package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BoardingAddOnDetailDynamicSqlSupport.boardingAddOnDetail;
import static org.assertj.core.api.Assertions.assertThat;

import com.moego.svc.online.booking.entity.BoardingAddOnDetail;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@Disabled("For local test")
class BoardingAddOnDetailMapperIT {

    @Autowired
    BoardingAddOnDetailMapper mapper;

    @Test
    @Transactional
    void testPostgresUsesDateAndJavaTypeIsLocalDate() {
        long id = mapper.selectOne(c -> c.orderBy(boardingAddOnDetail.id).limit(1))
                .map(BoardingAddOnDetail::getId)
                .orElseThrow();

        var updateBean = new BoardingAddOnDetail();
        updateBean.setId(id);
        updateBean.setStartDate(LocalDate.now());
        mapper.updateByPrimaryKeySelective(updateBean);

        var updated = mapper.selectByPrimaryKey(id).orElseThrow();
        assertThat(updated.getStartDate()).isEqualTo(LocalDate.now());
    }

    @Test
    @Transactional
    void testPostgresUsesJsonbAndJavaTypeIsLocalDateList() {
        long id = mapper.selectOne(c -> c.orderBy(boardingAddOnDetail.id).limit(1))
                .map(BoardingAddOnDetail::getId)
                .orElseThrow();

        var updateBean = new BoardingAddOnDetail();
        updateBean.setId(id);
        updateBean.setSpecificDates(List.of(LocalDate.now()));
        mapper.updateByPrimaryKeySelective(updateBean);

        var updated = mapper.selectByPrimaryKey(id).orElseThrow();
        assertThat(updated.getSpecificDates()).isEqualTo(List.of(LocalDate.now()));
    }
}
