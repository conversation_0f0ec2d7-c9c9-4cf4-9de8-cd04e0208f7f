package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.service.AvailabilitySettingService.EVALUATION_DEFAULT_PET_CAPACITY;
import static com.moego.svc.online.booking.service.AvailabilitySettingService.TIME_RANGE_DEFAULT_END_AT;
import static com.moego.svc.online.booking.service.AvailabilitySettingService.TIME_RANGE_DEFAULT_START_AT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.models.online_booking.v1.DateLimitType;
import com.moego.idl.models.online_booking.v1.DayOfWeekTimeRangeDef;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import com.moego.idl.models.online_booking.v1.DogWalkingServiceAvailabilityModel;
import com.moego.idl.models.online_booking.v1.TimeRangeType;
import com.moego.lib.common.exception.BizException;
import com.moego.svc.online.booking.client.OrganizationClient;
import com.moego.svc.online.booking.entity.AcceptCustomerSetting;
import com.moego.svc.online.booking.entity.BookingDateRangeSetting;
import com.moego.svc.online.booking.entity.BookingTimeRangeDetail;
import com.moego.svc.online.booking.entity.BookingTimeRangeOverride;
import com.moego.svc.online.booking.entity.BookingTimeRangeSetting;
import com.moego.svc.online.booking.entity.LodgingCapacitySetting;
import com.moego.svc.online.booking.mapper.AcceptCustomerSettingMapper;
import com.moego.svc.online.booking.mapper.BookingDateRangeSettingMapper;
import com.moego.svc.online.booking.mapper.BookingTimeRangeDetailMapper;
import com.moego.svc.online.booking.mapper.BookingTimeRangeOverrideMapper;
import com.moego.svc.online.booking.mapper.BookingTimeRangeSettingMapper;
import com.moego.svc.online.booking.mapper.LodgingCapacitySettingMapper;
import com.moego.svc.online.booking.mapstruct.DateRangeConverter;
import com.moego.svc.online.booking.mapstruct.TimeRangeConverter;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;

@ExtendWith(MockitoExtension.class)
class AvailabilitySettingServiceTest {

    @Mock
    private OrganizationClient organizationClient;

    @Mock
    private BookingTimeRangeSettingMapper bookingTimeRangeSettingMapper;

    @Mock
    private BookingTimeRangeDetailMapper bookingTimeRangeDetailMapper;

    @Mock
    private BookingTimeRangeOverrideMapper bookingTimeRangeOverrideMapper;

    @Mock
    private LodgingCapacitySettingMapper lodgingCapacitySettingMapper;

    @Mock
    private AcceptCustomerSettingMapper acceptCustomerSettingMapper;

    @Mock
    private BookingDateRangeSettingMapper bookingDateRangeSettingMapper;

    @InjectMocks
    private AvailabilitySettingService service;

    @Test
    void buildDefaultLodgingCapacitySetting_boardingSetsCorrect() {
        long companyId = 1L;
        long businessId = 2L;
        ServiceItemType serviceItemType = ServiceItemType.BOARDING;

        LodgingCapacitySetting result =
                AvailabilitySettingService.buildDefaultLodgingCapacitySetting(companyId, businessId, serviceItemType);

        LodgingCapacitySetting expect = new LodgingCapacitySetting();
        expect.setCompanyId(companyId);
        expect.setBusinessId(businessId);
        expect.setServiceItemType(serviceItemType.getNumber());
        expect.setIsCapacityLimited(true);
        expect.setCapacityLimit(100);
        expect.setAllowWaitlistSignups(false);
        assertThat(result).usingRecursiveComparison().isEqualTo(expect);
    }

    @Test
    void buildDefaultLodgingCapacitySetting_daycareSetsCorrect() {
        long companyId = 1L;
        long businessId = 2L;
        ServiceItemType serviceItemType = ServiceItemType.DAYCARE;

        LodgingCapacitySetting result =
                AvailabilitySettingService.buildDefaultLodgingCapacitySetting(companyId, businessId, serviceItemType);

        LodgingCapacitySetting expect = new LodgingCapacitySetting();
        expect.setCompanyId(companyId);
        expect.setBusinessId(businessId);
        expect.setServiceItemType(serviceItemType.getNumber());
        expect.setIsCapacityLimited(false);
        expect.setCapacityLimit(100);
        expect.setAllowWaitlistSignups(false);
        assertThat(result).usingRecursiveComparison().isEqualTo(expect);
    }

    @Test
    void buildDefaultBookingTimeRangeDetail_setsCorrectTimeRangeForBoardingArrivalTime() {
        BookingTimeRangeDetail result = AvailabilitySettingService.buildDefaultBookingTimeRangeDetail(
                1L, ServiceItemType.BOARDING, TimeRangeType.ARRIVAL_TIME);

        String weekDefStr = TimeRangeConverter.INSTANCE.toString(DayOfWeekTimeRangeDef.newBuilder()
                .addMonday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addTuesday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addWednesday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addThursday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addFriday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addSaturday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addSunday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .build());
        BookingTimeRangeDetail expect = new BookingTimeRangeDetail();
        expect.setSettingId(1L);
        expect.setTimeRangeType(TimeRangeType.ARRIVAL_TIME_VALUE);
        expect.setFirstWeek(weekDefStr);
        expect.setSecondWeek(weekDefStr);
        expect.setThirdWeek(weekDefStr);
        expect.setForthWeek(weekDefStr);

        assertThat(result).usingRecursiveComparison().isEqualTo(expect);
    }

    @Test
    void buildDefaultBookingTimeRangeDetail_setsCorrectTimeRangeForEvaluationArrivalTime() {
        BookingTimeRangeDetail result = AvailabilitySettingService.buildDefaultBookingTimeRangeDetail(
                1L, ServiceItemType.EVALUATION, TimeRangeType.ARRIVAL_TIME);

        String weekDefStr = TimeRangeConverter.INSTANCE.toString(DayOfWeekTimeRangeDef.newBuilder()
                .addMonday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT)
                        .setPetCapacity(EVALUATION_DEFAULT_PET_CAPACITY))
                .addTuesday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT)
                        .setPetCapacity(EVALUATION_DEFAULT_PET_CAPACITY))
                .addWednesday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT)
                        .setPetCapacity(EVALUATION_DEFAULT_PET_CAPACITY))
                .addThursday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT)
                        .setPetCapacity(EVALUATION_DEFAULT_PET_CAPACITY))
                .addFriday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT)
                        .setPetCapacity(EVALUATION_DEFAULT_PET_CAPACITY))
                .addSaturday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT)
                        .setPetCapacity(EVALUATION_DEFAULT_PET_CAPACITY))
                .addSunday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT)
                        .setPetCapacity(EVALUATION_DEFAULT_PET_CAPACITY))
                .build());
        BookingTimeRangeDetail expect = new BookingTimeRangeDetail();
        expect.setSettingId(1L);
        expect.setTimeRangeType(TimeRangeType.ARRIVAL_TIME_VALUE);
        expect.setFirstWeek(weekDefStr);
        expect.setSecondWeek(weekDefStr);
        expect.setThirdWeek(weekDefStr);
        expect.setForthWeek(weekDefStr);

        assertThat(result).usingRecursiveComparison().isEqualTo(expect);
    }

    @Test
    void buildDefaultBookingTimeRangeDetail_setsCorrectTimeRangeForBoardingPickUpTime() {
        BookingTimeRangeDetail result = AvailabilitySettingService.buildDefaultBookingTimeRangeDetail(
                1L, ServiceItemType.BOARDING, TimeRangeType.PICK_UP_TIME);

        String weekDefStr = TimeRangeConverter.INSTANCE.toString(DayOfWeekTimeRangeDef.newBuilder()
                .addMonday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addTuesday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addWednesday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addThursday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addFriday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addSaturday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addSunday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .build());
        BookingTimeRangeDetail expect = new BookingTimeRangeDetail();
        expect.setSettingId(1L);
        expect.setTimeRangeType(TimeRangeType.PICK_UP_TIME_VALUE);
        expect.setFirstWeek(weekDefStr);
        expect.setSecondWeek(weekDefStr);
        expect.setThirdWeek(weekDefStr);
        expect.setForthWeek(weekDefStr);

        assertThat(result).usingRecursiveComparison().isEqualTo(expect);
    }

    @Test
    void buildDefaultBookingTimeRangeDetail_setsCorrectTimeRangeForEvaluationPickUpTime() {
        BookingTimeRangeDetail result = AvailabilitySettingService.buildDefaultBookingTimeRangeDetail(
                1L, ServiceItemType.EVALUATION, TimeRangeType.PICK_UP_TIME);

        String weekDefStr = TimeRangeConverter.INSTANCE.toString(DayOfWeekTimeRangeDef.newBuilder()
                .addMonday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addTuesday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addWednesday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addThursday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addFriday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addSaturday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .addSunday(DayTimeRangeDef.newBuilder()
                        .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                        .setEndTime(TIME_RANGE_DEFAULT_END_AT))
                .build());
        BookingTimeRangeDetail expect = new BookingTimeRangeDetail();
        expect.setSettingId(1L);
        expect.setTimeRangeType(TimeRangeType.PICK_UP_TIME_VALUE);
        expect.setFirstWeek(weekDefStr);
        expect.setSecondWeek(weekDefStr);
        expect.setThirdWeek(weekDefStr);
        expect.setForthWeek(weekDefStr);

        assertThat(result).usingRecursiveComparison().isEqualTo(expect);
    }

    @Test
    void getLodgingCapacitySetting_returnsExistingSetting() {
        long companyId = 1L;
        long businessId = 2L;
        ServiceItemType serviceItemType = ServiceItemType.BOARDING;
        LodgingCapacitySetting expectedSetting = new LodgingCapacitySetting();
        expectedSetting.setCompanyId(companyId);
        expectedSetting.setBusinessId(businessId);
        expectedSetting.setServiceItemType(serviceItemType.getNumber());

        when(lodgingCapacitySettingMapper.selectOne(any(SelectDSLCompleter.class)))
                .thenReturn(Optional.of(expectedSetting));

        LodgingCapacitySetting result = service.getLodgingCapacitySetting(companyId, businessId, serviceItemType);

        assertThat(result).isEqualTo(expectedSetting);
    }

    @Test
    void getLodgingCapacitySetting_createsDefaultSettingWhenNoneExists() {
        long companyId = 1L;
        long businessId = 2L;
        ServiceItemType serviceItemType = ServiceItemType.BOARDING;

        when(lodgingCapacitySettingMapper.selectOne(any(SelectDSLCompleter.class)))
                .thenReturn(Optional.empty());

        LodgingCapacitySetting result = service.getLodgingCapacitySetting(companyId, businessId, serviceItemType);

        LodgingCapacitySetting expect = new LodgingCapacitySetting();
        expect.setCompanyId(companyId);
        expect.setBusinessId(businessId);
        expect.setServiceItemType(serviceItemType.getNumber());
        expect.setIsCapacityLimited(true);
        expect.setCapacityLimit(100);
        expect.setAllowWaitlistSignups(false);
        assertThat(result).usingRecursiveComparison().isEqualTo(expect);
    }

    @Test
    void getLodgingCapacitySetting_createsDefaultSettingForDaycare() {
        long companyId = 1L;
        long businessId = 2L;
        ServiceItemType serviceItemType = ServiceItemType.DAYCARE;

        when(lodgingCapacitySettingMapper.selectOne(any(SelectDSLCompleter.class)))
                .thenReturn(Optional.empty());

        LodgingCapacitySetting result = service.getLodgingCapacitySetting(companyId, businessId, serviceItemType);

        LodgingCapacitySetting expect = new LodgingCapacitySetting();
        expect.setCompanyId(companyId);
        expect.setBusinessId(businessId);
        expect.setServiceItemType(serviceItemType.getNumber());
        expect.setIsCapacityLimited(false);
        expect.setCapacityLimit(100);
        expect.setAllowWaitlistSignups(false);
        assertThat(result).usingRecursiveComparison().isEqualTo(expect);
    }

    @Test
    void batchCreateArrivalPickUpOverrides_withValidOverrides_shouldInsertOverrides() {

        try (MockedStatic<LocalDateTime> mockedLocalDateTime = mockStatic(LocalDateTime.class)) {
            LocalDateTime fixedDateTime = LocalDateTime.of(2023, 1, 1, 0, 0);
            mockedLocalDateTime.when(LocalDateTime::now).thenReturn(fixedDateTime);

            List<BookingTimeRangeOverride> overrides =
                    List.of(new BookingTimeRangeOverride(), new BookingTimeRangeOverride());
            overrides.forEach(override -> {
                override.setCreatedAt(null);
                override.setUpdatedAt(null);
            });

            when(bookingTimeRangeOverrideMapper.insertMultiple(any())).thenReturn(1);

            service.batchCreateArrivalPickUpOverrides(overrides);

            List<BookingTimeRangeOverride> expect = List.of(
                    new BookingTimeRangeOverride() {
                        {
                            setCreatedAt(LocalDateTime.of(2023, 1, 1, 0, 0));
                            setUpdatedAt(LocalDateTime.of(2023, 1, 1, 0, 0));
                        }
                    },
                    new BookingTimeRangeOverride() {
                        {
                            setCreatedAt(LocalDateTime.of(2023, 1, 1, 0, 0));
                            setUpdatedAt(LocalDateTime.of(2023, 1, 1, 0, 0));
                        }
                    });

            assertThat(overrides).usingRecursiveComparison().isEqualTo(expect);
        }
    }

    @Test
    void batchCreateArrivalPickUpOverrides_withEmptyOverrides_shouldDoNothing() {
        List<BookingTimeRangeOverride> overrides = List.of();

        service.batchCreateArrivalPickUpOverrides(overrides);
    }

    @Test
    void batchDeleteArrivalPickUpOverrides_withEmptyIds_shouldDoNothing() {
        List<Long> ids = List.of();

        service.batchDeleteArrivalPickUpOverrides(123L, ids);
    }

    @Test
    void batchUpdateArrivalPickUpOverrides_withEmptyUpdates_shouldReturnEmptyList() {
        List<BookingTimeRangeOverride> updates = List.of();

        List<BookingTimeRangeOverride> result = service.batchUpdateArrivalPickUpOverrides(123L, updates);

        assertThat(result).isEmpty();
    }

    @Test
    void batchUpdateArrivalPickUpOverrides_withValidUpdates_shouldUpdateOverrides() {
        List<BookingTimeRangeOverride> updates = List.of(
                new BookingTimeRangeOverride() {
                    {
                        setId(1L);
                    }
                },
                new BookingTimeRangeOverride() {
                    {
                        setId(2L);
                    }
                });
        List<BookingTimeRangeOverride> existingOverrides = List.of(
                new BookingTimeRangeOverride() {
                    {
                        setId(1L);
                        setSettingId(1L);
                    }
                },
                new BookingTimeRangeOverride() {
                    {
                        setId(2L);
                        setSettingId(2L);
                    }
                });
        when(bookingTimeRangeSettingMapper.select(any()))
                .thenReturn(List.of(
                        new BookingTimeRangeSetting() {
                            {
                                setId(1L);
                            }
                        },
                        new BookingTimeRangeSetting() {
                            {
                                setId(2L);
                            }
                        }));
        when(bookingTimeRangeOverrideMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

        AvailabilitySettingService spyService = spy(service);
        doReturn(existingOverrides).when(spyService).getArrivalPickUpOverrideByIds(any());
        List<BookingTimeRangeOverride> result = spyService.batchUpdateArrivalPickUpOverrides(123L, updates);

        assertThat(result).usingRecursiveComparison().isEqualTo(existingOverrides);
    }

    @Test
    void batchUpdateArrivalPickUpOverrides_withInvalidSettingId_shouldThrowException() {
        List<BookingTimeRangeOverride> updates = List.of(
                new BookingTimeRangeOverride() {
                    {
                        setId(1L);
                    }
                },
                new BookingTimeRangeOverride() {
                    {
                        setId(2L);
                    }
                });
        List<BookingTimeRangeOverride> existingOverrides = List.of(
                new BookingTimeRangeOverride() {
                    {
                        setId(1L);
                        setSettingId(1L);
                    }
                },
                new BookingTimeRangeOverride() {
                    {
                        setId(2L);
                        setSettingId(3L);
                    }
                });
        when(bookingTimeRangeSettingMapper.select(any()))
                .thenReturn(List.of(
                        new BookingTimeRangeSetting() {
                            {
                                setId(1L);
                            }
                        },
                        new BookingTimeRangeSetting() {
                            {
                                setId(2L);
                            }
                        }));

        AvailabilitySettingService spyService = spy(service);
        doReturn(existingOverrides).when(spyService).getArrivalPickUpOverrideByIds(any());

        assertThrows(BizException.class, () -> spyService.batchUpdateArrivalPickUpOverrides(123L, updates));
    }

    @Test
    void batchUpdateArrivalPickUpOverrides_withNullCompanyId_shouldUpdateOverrides() {
        List<BookingTimeRangeOverride> updates = List.of(
                new BookingTimeRangeOverride() {
                    {
                        setId(1L);
                    }
                },
                new BookingTimeRangeOverride() {
                    {
                        setId(1L);
                    }
                });
        List<BookingTimeRangeOverride> existingOverrides =
                List.of(new BookingTimeRangeOverride(), new BookingTimeRangeOverride());

        when(bookingTimeRangeOverrideMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

        AvailabilitySettingService spyService = spy(service);
        doReturn(existingOverrides).when(spyService).getArrivalPickUpOverrideByIds(any());
        List<BookingTimeRangeOverride> result = spyService.batchUpdateArrivalPickUpOverrides(null, updates);

        assertThat(result).usingRecursiveComparison().isEqualTo(existingOverrides);
    }

    @Test
    void getArrivalPickUpOverrideBySettingIds_withValidSettingIds_shouldReturnOverrides() {
        List<Long> settingIds = List.of(1L, 2L);
        List<BookingTimeRangeOverride> expectedOverrides =
                List.of(new BookingTimeRangeOverride(), new BookingTimeRangeOverride());
        when(bookingTimeRangeOverrideMapper.select(any())).thenReturn(expectedOverrides);

        List<BookingTimeRangeOverride> result = service.getArrivalPickUpOverrideBySettingIds(settingIds);

        assertThat(result).isEqualTo(expectedOverrides);
    }

    @Test
    void getArrivalPickUpOverrideBySettingIds_withEmptySettingIds_shouldReturnEmptyList() {
        List<Long> settingIds = List.of();

        List<BookingTimeRangeOverride> result = service.getArrivalPickUpOverrideBySettingIds(settingIds);

        assertThat(result).isEmpty();
    }

    @Test
    void getArrivalPickUpOverrideByIds_withValidIds_shouldReturnOverrides() {
        List<Long> ids = List.of(1L, 2L);
        List<BookingTimeRangeOverride> expectedOverrides =
                List.of(new BookingTimeRangeOverride(), new BookingTimeRangeOverride());
        when(bookingTimeRangeOverrideMapper.select(any())).thenReturn(expectedOverrides);

        List<BookingTimeRangeOverride> result = service.getArrivalPickUpOverrideByIds(ids);

        assertThat(result).isEqualTo(expectedOverrides);
    }

    @Test
    void getArrivalPickUpOverrideByIds_withEmptyIds_shouldReturnEmptyList() {
        List<Long> ids = List.of();

        List<BookingTimeRangeOverride> result = service.getArrivalPickUpOverrideByIds(ids);

        assertThat(result).isEmpty();
    }

    @Test
    void getBookingTimeRangeSettingByIds_withValidIds_shouldReturnSettings() {
        List<Long> ids = List.of(1L, 2L);
        List<BookingTimeRangeSetting> expectedSettings =
                List.of(new BookingTimeRangeSetting(), new BookingTimeRangeSetting());
        when(bookingTimeRangeSettingMapper.select(any())).thenReturn(expectedSettings);

        List<BookingTimeRangeSetting> result = service.getBookingTimeRangeSettingByIds(ids);

        assertThat(result).usingRecursiveComparison().isEqualTo(expectedSettings);
    }

    @Test
    void getBookingTimeRangeSettingByIds_withEmptyIds_shouldReturnEmptyList() {
        List<Long> ids = List.of();

        List<BookingTimeRangeSetting> result = service.getBookingTimeRangeSettingByIds(ids);

        assertThat(result).isEmpty();
    }

    @Test
    void getDogWalkingServiceAvailability() {
        // Arrange
        var companyId = 1L;
        var businessId = 2L;
        var serviceItemType = ServiceItemType.DOG_WALKING;

        var bookingDateRangeSettingEntity = new BookingDateRangeSetting();
        bookingDateRangeSettingEntity.setCompanyId(companyId);
        bookingDateRangeSettingEntity.setBusinessId(businessId);
        bookingDateRangeSettingEntity.setServiceItemType(serviceItemType.getNumber());
        bookingDateRangeSettingEntity.setStartDateType(DateLimitType.DATE_TYPE_OFFSET_VALUE);
        bookingDateRangeSettingEntity.setEndDateType(DateLimitType.DATE_TYPE_OFFSET_VALUE);

        var acceptCustomerSettingEntity = new AcceptCustomerSetting();
        acceptCustomerSettingEntity.setCompanyId(companyId);
        acceptCustomerSettingEntity.setBusinessId(businessId);
        acceptCustomerSettingEntity.setServiceItemType(serviceItemType.getNumber());
        acceptCustomerSettingEntity.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);

        when(bookingDateRangeSettingMapper.selectOne(any(SelectDSLCompleter.class)))
                .thenReturn(Optional.of(bookingDateRangeSettingEntity));
        when(acceptCustomerSettingMapper.selectOne(any(SelectDSLCompleter.class)))
                .thenReturn(Optional.of(acceptCustomerSettingEntity));

        // Act
        var result = service.getDogWalkingServiceAvailability(companyId, businessId);

        // Assert
        var expected = DogWalkingServiceAvailabilityModel.newBuilder()
                .addAcceptedPetTypes(PetType.PET_TYPE_DOG) // only accept dog
                .setBookingDateRange(DateRangeConverter.INSTANCE.entityToDef(bookingDateRangeSettingEntity))
                .setAcceptCustomerType(
                        AcceptCustomerType.forNumber(acceptCustomerSettingEntity.getAcceptedCustomerType()))
                .build();
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }
}
