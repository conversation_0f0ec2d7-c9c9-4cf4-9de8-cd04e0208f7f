package com.moego.svc.online.booking.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.DaycareAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.DogWalkingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import com.moego.idl.service.appointment.v1.GetLastPetDetailRequest;
import com.moego.idl.service.appointment.v1.GetLastPetDetailResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PetDetailServiceTest {

    @InjectMocks
    private PetDetailService petDetailService;

    @Mock
    private PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailServiceStub;

    @Test
    void testGetBoardingServiceDetails() {
        BookingRequestModel bookingRequest = BookingRequestModel.newBuilder()
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setBoarding(BookingRequestModel.BoardingService.newBuilder()
                                .setService(BoardingServiceDetailModel.newBuilder()
                                        .setPetId(1L)
                                        .build())
                                .build())
                        .build())
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setDaycare(
                                BookingRequestModel.DaycareService.newBuilder().build())
                        .build())
                .build();

        List<BoardingServiceDetailModel> result = PetDetailService.getBoardingServiceDetails(bookingRequest);

        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getPetId());
    }

    @Test
    void testGetEvaluationServiceDetails() {
        var bookingRequest = BookingRequestModel.newBuilder()
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setEvaluation(BookingRequestModel.EvaluationService.newBuilder()
                                .setService(EvaluationTestDetailModel.newBuilder()
                                        .setPetId(1L)
                                        .build())
                                .build())
                        .build())
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setDaycare(
                                BookingRequestModel.DaycareService.newBuilder().build())
                        .build())
                .build();

        var result = PetDetailService.getEvaluationServiceDetails(bookingRequest);

        var expect = List.of(EvaluationTestDetailModel.newBuilder().setPetId(1L).build());
        assertThat(result).isEqualTo(expect);
    }

    @Test
    void testGetPetIds() {
        BookingRequestModel bookingRequest = BookingRequestModel.newBuilder()
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setBoarding(BookingRequestModel.BoardingService.newBuilder()
                                .setService(BoardingServiceDetailModel.newBuilder()
                                        .setPetId(1L)
                                        .build())
                                .build())
                        .build())
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setDaycare(BookingRequestModel.DaycareService.newBuilder()
                                .setService(DaycareServiceDetailModel.newBuilder()
                                        .setPetId(2L)
                                        .build())
                                .build())
                        .build())
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setGrooming(BookingRequestModel.GroomingService.newBuilder()
                                .setService(GroomingServiceDetailModel.newBuilder()
                                        .setPetId(3L)
                                        .build())
                                .build())
                        .build())
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setEvaluation(BookingRequestModel.EvaluationService.newBuilder()
                                .setService(EvaluationTestDetailModel.newBuilder()
                                        .setPetId(4L)
                                        .build())
                                .build())
                        .build())
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setDogWalking(BookingRequestModel.DogWalkingService.newBuilder()
                                .setService(DogWalkingServiceDetailModel.newBuilder()
                                        .setPetId(5L)
                                        .build())
                                .build())
                        .build())
                .build();

        List<Long> result = PetDetailService.getPetIds(bookingRequest);

        assertEquals(List.of(1L, 2L, 3L, 4L, 5L), result);
    }

    @Test
    void testGetServiceIds() {
        BookingRequestModel bookingRequest = BookingRequestModel.newBuilder()
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setBoarding(BookingRequestModel.BoardingService.newBuilder()
                                .setService(BoardingServiceDetailModel.newBuilder()
                                        .setServiceId(1L)
                                        .build())
                                .addAddons(BoardingAddOnDetailModel.newBuilder()
                                        .setAddOnId(2L)
                                        .build())
                                .build())
                        .build())
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setDaycare(BookingRequestModel.DaycareService.newBuilder()
                                .setService(DaycareServiceDetailModel.newBuilder()
                                        .setServiceId(3L)
                                        .build())
                                .addAddons(DaycareAddOnDetailModel.newBuilder()
                                        .setAddOnId(4L)
                                        .build())
                                .build())
                        .build())
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setGrooming(BookingRequestModel.GroomingService.newBuilder()
                                .setService(GroomingServiceDetailModel.newBuilder()
                                        .setServiceId(5L)
                                        .build())
                                .addAddons(GroomingAddOnDetailModel.newBuilder()
                                        .setAddOnId(6L)
                                        .build())
                                .build())
                        .build())
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setDogWalking(BookingRequestModel.DogWalkingService.newBuilder()
                                .setService(DogWalkingServiceDetailModel.newBuilder()
                                        .setServiceId(7L)
                                        .build())
                                .build())
                        .build())
                .build();

        List<Long> result = PetDetailService.getServiceIds(bookingRequest);

        assertEquals(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L), result);
    }

    @ParameterizedTest
    @CsvSource({
        "10.4, 1", "20.5, 2", "30.6, 3",
    })
    void testGetPetSizeId(String weight, Long expectedSizeId) {
        List<BusinessPetSizeModel> petSizeList = List.of(
                BusinessPetSizeModel.newBuilder()
                        .setId(1L)
                        .setWeightLow(0)
                        .setWeightHigh(15)
                        .build(),
                BusinessPetSizeModel.newBuilder()
                        .setId(2L)
                        .setWeightLow(16)
                        .setWeightHigh(25)
                        .build(),
                BusinessPetSizeModel.newBuilder()
                        .setId(3L)
                        .setWeightLow(26)
                        .setWeightHigh(35)
                        .build());

        Long result = PetDetailService.getPetSizeId(weight, petSizeList);

        assertEquals(expectedSizeId, result);
    }

    @Test
    void testGetPetSizeId_InvalidWeight() {
        List<BusinessPetSizeModel> petSizeList = List.of(BusinessPetSizeModel.newBuilder()
                .setId(1L)
                .setWeightLow(0)
                .setWeightHigh(15)
                .build());

        assertNull(PetDetailService.getPetSizeId("invalid", petSizeList));
    }

    @Test
    @DisplayName("Should return mapping when all pet details are valid")
    void getLastLodgingUnitId_WhenAllPetDetailsValid_ShouldReturnMapping() {
        long companyId = 1L;
        long businessId = 2L;
        long customerId = 3L;
        Collection<Long> petIds = Arrays.asList(101L, 102L);
        Collection<Long> serviceIds = Arrays.asList(201L, 202L);

        // Arrange
        GetLastPetDetailResponse response = GetLastPetDetailResponse.newBuilder()
                .addPetDetails(createPetDetail(101L, 301L))
                .addPetDetails(createPetDetail(102L, 302L))
                .build();

        when(petDetailServiceStub.getLastPetDetail(any(GetLastPetDetailRequest.class)))
                .thenReturn(response);

        // Act
        Map<Long, Long> result =
                petDetailService.getLastLodgingUnitId(companyId, businessId, customerId, petIds, serviceIds);

        // Assert
        assertEquals(2, result.size());
        assertEquals(301L, result.get(101L));
        assertEquals(302L, result.get(102L));

        verify(petDetailServiceStub)
                .getLastPetDetail(argThat(request -> request.getCompanyId() == companyId
                        && request.getCustomerId(0) == customerId
                        && request.getPetIdCount() == petIds.size()
                        && request.getFilter().getBusinessId() == businessId
                        && request.getFilter().getServiceIdsCount() == serviceIds.size()));
    }

    private PetDetailModel createPetDetail(Long petId, Long lodgingId) {
        return PetDetailModel.newBuilder()
                .setPetId(petId)
                .setLodgingId(lodgingId)
                .build();
    }
}
