package com.moego.svc.online.booking.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class ScheduleFormatterUtilsTest {

    @Test
    public void testMorning() {
        String result = ScheduleFormatterUtils.format(540, "AM"); // 09:00
        assertEquals("AM 09:00 am", result);
    }

    @Test
    public void testNoon() {
        String result = ScheduleFormatterUtils.format(720, "Noon"); // 12:00
        assertEquals("Noon 12:00 pm", result);
    }

    @Test
    public void testEvening() {
        String result = ScheduleFormatterUtils.format(1080, "PM"); // 18:00
        assertEquals("PM 06:00 pm", result);
    }

    @Test
    public void testMidnight() {
        String result = ScheduleFormatterUtils.format(0, "AM"); // 00:00
        assertEquals("AM 12:00 am", result);
    }

    @Test
    public void testEdgeBeforeNoon() {
        String result = ScheduleFormatterUtils.format(719, "AM"); // 11:59
        assertEquals("AM 11:59 am", result);
    }

    @Test
    public void testEdgeAfterNoon() {
        String result = ScheduleFormatterUtils.format(721, "PM"); // 12:01
        assertEquals("PM 12:01 pm", result);
    }
}
