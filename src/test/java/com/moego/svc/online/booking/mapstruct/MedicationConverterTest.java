package com.moego.svc.online.booking.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import com.moego.idl.service.online_booking.v1.CreateMedicationRequest;
import com.moego.svc.online.booking.entity.Medication;
import java.math.BigDecimal;
import java.util.List;
import org.junit.jupiter.api.Test;

class MedicationConverterTest {

    @Test
    void toAmountStr_OnlyHaveAmountStr() {
        // Arrange
        var entity = new Medication();
        entity.setAmountStr("1/2");

        // Act
        var result = MedicationConverter.INSTANCE.toAmountStr(entity);

        // Assert
        var expected = "1/2";
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void toAmountStr_OnlyHaveAmount() {
        // Arrange
        var entity = new Medication();
        entity.setAmount(BigDecimal.valueOf(1));

        // Act
        var result = MedicationConverter.INSTANCE.toAmountStr(entity);

        // Assert
        var expected = "1";
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void toAmountStr_NeitherIsNull() {
        // Arrange
        var entity = new Medication();
        entity.setAmountStr("1/2");
        entity.setAmount(BigDecimal.valueOf(1));

        // Act
        var result = MedicationConverter.INSTANCE.toAmountStr(entity);

        // Assert
        var expected = "1/2";
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void toAmountStr_BothAreNull_DefaultZero() {
        // Arrange
        var entity = new Medication();
        entity.setAmount(BigDecimal.valueOf(0));

        // Act
        var result = MedicationConverter.INSTANCE.toAmountStr(entity);

        // Assert
        var expected = "0";
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void entityToModel_EmptyList() {
        var result = MedicationConverter.INSTANCE.entityToModel(List.of());
        assertThat(result).isEmpty();
    }

    @Test
    void createRequestToEntity_EmptyList() {
        // Act
        var result = MedicationConverter.INSTANCE.createRequestToEntity(List.of(), 1L, 2L, ServiceItemType.BOARDING);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void createRequestToEntity_NonEmptyList() {
        // Arrange
        var createRequest = CreateMedicationRequest.newBuilder()
                .setUnit("unit")
                .setMedicationName("medicationName")
                .setNotes("notes")
                .setAmountStr("1/2")
                .addTime(MedicationModel.MedicationSchedule.newBuilder()
                        .setLabel("label")
                        .setTime(540)
                        .build())
                .build();

        // Act
        var result = MedicationConverter.INSTANCE.createRequestToEntity(
                List.of(createRequest), 1L, 2L, ServiceItemType.BOARDING);

        var expect = new Medication();
        expect.setUnit("unit");
        expect.setMedicationName("medicationName");
        expect.setNotes("notes");
        expect.setAmountStr("1/2");
        expect.setTime("[{\"label\":\"label\",\"time\":540}]");
        expect.setBookingRequestId(1L);
        expect.setServiceDetailId(2L);
        expect.setServiceDetailType(ServiceItemType.BOARDING.getNumber());

        // Assert
        assertThat(result).isEqualTo(List.of(expect));
    }

    @Test
    void createRequestToEntity_NonEmptyListWithDefaultValue() {
        // Arrange
        var createRequest = CreateMedicationRequest.getDefaultInstance();

        // Act
        var result = MedicationConverter.INSTANCE.createRequestToEntity(
                List.of(createRequest), 1L, 2L, ServiceItemType.BOARDING);

        var expect = new Medication();
        expect.setTime("[]");
        expect.setBookingRequestId(1L);
        expect.setServiceDetailId(2L);
        expect.setServiceDetailType(ServiceItemType.BOARDING.getNumber());

        // Assert
        assertThat(result).isEqualTo(List.of(expect));
    }
}
