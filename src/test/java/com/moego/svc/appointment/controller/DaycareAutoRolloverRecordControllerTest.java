package com.moego.svc.appointment.controller;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@link DaycareAutoRolloverRecordController}
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Disabled("local test")
class DaycareAutoRolloverRecordControllerTest {

    @Autowired
    DaycareAutoRolloverRecordController c;

    @Test
    void getService() {
        c.getService(100870, 100986, 1110548, 17574293);
    }
}
