package com.moego.svc.appointment.listener;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.google.protobuf.Timestamp;
import com.moego.idl.models.organization.v1.TimeZone;
import org.junit.jupiter.api.Test;

class AppointmentEventListenerTest {

    @Test
    void getTimestamp() {
        // Arrange
        TimeZone timeZone = TimeZone.newBuilder().setName("Asia/Shanghai").build();
        String date = "2024-11-10";
        Integer minutes = 1230;

        // Act
        var result = AppointmentEventListener.getTimestamp(timeZone, date, minutes);

        // Assert
        final var expected = Timestamp.newBuilder().setSeconds(1731241800L).build();

        assertEquals(expected, result);
    }
}
