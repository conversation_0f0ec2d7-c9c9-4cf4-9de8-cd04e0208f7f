package com.moego.svc.appointment.listener.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.idl.models.order.v1.ServiceChargeExceedHourRule;
import com.moego.idl.models.order.v1.SurchargeType;
import com.moego.idl.models.organization.v1.TimeZone;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.lib.featureflag.FeatureFlag;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.svc.appointment.client.OrganizationClient;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import com.moego.svc.appointment.service.remote.OrderRemoteService;
import com.moego.svc.appointment.service.remote.ServiceChargeHelper;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ServiceChargeHandlerTest {

    @InjectMocks
    private ServiceChargeHandler serviceChargeHandler;

    @Mock
    private OrderRemoteService orderRemoteService;

    @Mock
    private PetDetailServiceProxy petDetailService;

    @Mock
    private OrderServiceGrpc.OrderServiceBlockingStub orderStub;

    @Mock
    private FeatureFlagApi featureFlagApi;

    @Mock
    private ServiceChargeHelper serviceChargeHelper;

    @Mock
    private OrganizationClient organizationClient;

    final String defaultTimeZoneName = "UTC";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testApplyCharge24Hour_FixRate_WhenExceeds24Hours_ShouldAddLineItem() {
        // Arrange
        MoeGroomingAppointment appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getId()).thenReturn(1);
        when(appointment.getCheckInTime()).thenReturn(0L); // check-in 时间为 0
        when(appointment.getCheckOutTime()).thenReturn(25 * 60 * 60L); // 超过 24 小时
        when(appointment.getServiceTypeInclude()).thenReturn(ServiceItemEnum.DAYCARE.getBitValue());

        List<MoeGroomingPetDetail> petDetails = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
            petDetail.setId(i + 1);
            petDetail.setPetId(i + 1);
            petDetail.setServiceItemType((byte) ServiceItemType.DAYCARE.getNumber());
            petDetails.add(petDetail);
        }
        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setId(100)
                .setName("24 Hour Fee")
                .setPrice(10.0)
                .setSurchargeType(SurchargeType.CHARGE_24_HOUR)
                .setTimeBasedPricingType(ServiceCharge.TimeBasedPricingType.FLAT_RATE)
                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                .addServiceItemTypes(ServiceItemType.DAYCARE)
                .addAllHourlyExceedRules(Collections.emptyList())
                .setAutoApplyStatus(ServiceCharge.AutoApplyStatus.AUTO_APPLY_ENABLED_WITH_CONDITION)
                .setApplyType(ServiceCharge.ApplyType.PER_APPOINTMENT)
                .build();

        // 模拟远程返回服务费用配置
        when(serviceChargeHelper.listConditionServiceCharge(anyLong(), anyLong()))
                .thenReturn(List.of(serviceCharge));

        // 模拟订单详情 - 构造一个 OrderDetailModel 包含空的 lineItemsList
        OrderModel order = OrderModel.newBuilder().setId(1).build();
        OrderDetailModel orderDetail = OrderDetailModel.newBuilder()
                .setOrder(order)
                .addAllLineItems(Collections.emptyList()) // 空的 line items
                .build();

        when(orderRemoteService.getOrderDetail(anyInt())).thenReturn(orderDetail);
        // 模拟宠物详情
        when(petDetailService.getPetDetailList(anyLong())).thenReturn(petDetails);

        when(featureFlagApi.isOn(any(FeatureFlag.class), any(FeatureFlagContext.class)))
                .thenReturn(false);

        when(organizationClient.getTimeZone(anyLong()))
                .thenReturn(TimeZone.newBuilder().setName(defaultTimeZoneName).build());

        // Act
        serviceChargeHandler.applyCharge24Hour(appointment, appointment);

        // Assert
        verify(orderStub).updateOrderIncremental(argThat(request -> {
            return request.getLineItemsList().size() == 1
                    && request.getLineItemsList().get(0).getName().equals("24 Hour Fee")
                    && request.getLineItemsList().get(0).getUnitPrice() == 10.0;
        }));
    }

    @Test
    void testApplyCharge24Hour_WhenExceeds24Hours_ShouldAddLineItem() {
        // Arrange
        MoeGroomingAppointment appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getId()).thenReturn(1);
        when(appointment.getCheckInTime()).thenReturn(0L); // check-in 时间为 0
        when(appointment.getCheckOutTime()).thenReturn(25 * 60 * 60L); // 超过 24 小时
        when(appointment.getServiceTypeInclude()).thenReturn(ServiceItemEnum.DAYCARE.getBitValue());

        List<MoeGroomingPetDetail> petDetails = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
            petDetail.setId(i + 1);
            petDetail.setPetId(i + 1);
            petDetail.setServiceItemType((byte) ServiceItemType.DAYCARE.getNumber());
            petDetails.add(petDetail);
        }
        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setId(100)
                .setName("24 Hour Fee")
                .setPrice(0)
                .setSurchargeType(SurchargeType.CHARGE_24_HOUR)
                .setTimeBasedPricingType(ServiceCharge.TimeBasedPricingType.HOURLY_EXCEED_TIERED_RATE)
                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                .addServiceItemTypes(ServiceItemType.DAYCARE)
                .addAllHourlyExceedRules(Arrays.asList(
                        ServiceChargeExceedHourRule.newBuilder()
                                .setFeeName("24H Rule")
                                .setHour(0)
                                .setBasePrice(10.0)
                                .build(),
                        ServiceChargeExceedHourRule.newBuilder()
                                .setFeeName("26H Rule")
                                .setHour(2)
                                .setBasePrice(20.0)
                                .build()))
                .setAutoApplyStatus(ServiceCharge.AutoApplyStatus.AUTO_APPLY_ENABLED_WITH_CONDITION)
                .setApplyType(ServiceCharge.ApplyType.PER_APPOINTMENT)
                .build();

        // 模拟远程返回服务费用配置
        when(serviceChargeHelper.listConditionServiceCharge(anyLong(), anyLong()))
                .thenReturn(List.of(serviceCharge));

        // 模拟订单详情 - 构造一个 OrderDetailModel 包含空的 lineItemsList
        OrderModel order = OrderModel.newBuilder().setId(1).build();
        OrderDetailModel orderDetail = OrderDetailModel.newBuilder()
                .setOrder(order)
                .addAllLineItems(Collections.emptyList()) // 空的 line items
                .build();

        when(orderRemoteService.getOrderDetail(anyInt())).thenReturn(orderDetail);
        // 模拟宠物详情
        when(petDetailService.getPetDetailList(anyLong())).thenReturn(petDetails);
        when(featureFlagApi.isOn(any(FeatureFlag.class), any(FeatureFlagContext.class)))
                .thenReturn(false);
        when(organizationClient.getTimeZone(anyLong()))
                .thenReturn(TimeZone.newBuilder().setName(defaultTimeZoneName).build());
        // Act
        serviceChargeHandler.applyCharge24Hour(appointment, appointment);

        // Assert
        verify(orderStub).updateOrderIncremental(argThat(request -> {
            return request.getLineItemsList().size() == 1
                    && request.getLineItemsList().get(0).getName().equals("24H Rule")
                    && request.getLineItemsList().get(0).getUnitPrice() == 50.0;
        }));
    }

    @Test
    void testApplyCharge24Hour_WhenNotExceed24Hours_ShouldNotAddLineItem() {
        // Arrange
        MoeGroomingAppointment appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getId()).thenReturn(1);
        when(appointment.getCheckInTime()).thenReturn(0L);
        when(appointment.getCheckOutTime()).thenReturn(23 * 60 * 60L); // 少于 24 小时
        when(appointment.getServiceTypeInclude()).thenReturn(ServiceItemEnum.DAYCARE.getBitValue());

        List<MoeGroomingPetDetail> petDetails = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
            petDetail.setId(i + 1);
            petDetail.setPetId(i + 1);
            petDetail.setServiceItemType((byte) ServiceItemType.DAYCARE.getNumber());
            petDetails.add(petDetail);
        }

        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setId(100)
                .setName("24 Hour Fee")
                .setPrice(0)
                .setSurchargeType(SurchargeType.CHARGE_24_HOUR)
                .setTimeBasedPricingType(ServiceCharge.TimeBasedPricingType.HOURLY_EXCEED_TIERED_RATE)
                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                .addServiceItemTypes(ServiceItemType.DAYCARE)
                .addAllHourlyExceedRules(Collections.singletonList(ServiceChargeExceedHourRule.newBuilder()
                        .setFeeName("24H Rule")
                        .setHour(0)
                        .setBasePrice(10.0)
                        .build()))
                .setAutoApplyStatus(ServiceCharge.AutoApplyStatus.AUTO_APPLY_ENABLED_WITH_CONDITION)
                .setApplyType(ServiceCharge.ApplyType.PER_APPOINTMENT)
                .build();

        // 模拟远程返回服务费用配置
        when(serviceChargeHelper.listConditionServiceCharge(anyLong(), anyLong()))
                .thenReturn(List.of(serviceCharge));

        OrderModel order = OrderModel.newBuilder().setId(1).build();
        OrderDetailModel orderDetail = OrderDetailModel.newBuilder()
                .setOrder(order)
                .addAllLineItems(Collections.emptyList())
                .build();
        when(orderRemoteService.getOrderDetail(anyInt())).thenReturn(orderDetail);

        when(petDetailService.getPetDetailList(anyLong())).thenReturn(petDetails);

        when(organizationClient.getTimeZone(anyLong()))
                .thenReturn(TimeZone.newBuilder().setName(defaultTimeZoneName).build());

        // Act
        serviceChargeHandler.applyCharge24Hour(appointment, appointment);

        // Assert
        verify(orderStub, times(0)).updateOrderIncremental(any());
    }

    @Test
    void testApplyCharge24Hour_WithExactly24Hours_ShouldAddLineItem() {
        // Arrange
        MoeGroomingAppointment appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getId()).thenReturn(1);
        when(appointment.getCheckInTime()).thenReturn(0L);
        when(appointment.getCheckOutTime()).thenReturn(24 * 60 * 60L + 30 * 60L); // 24 小时 30 分钟
        when(appointment.getServiceTypeInclude()).thenReturn(ServiceItemEnum.DAYCARE.getBitValue());

        List<MoeGroomingPetDetail> petDetails = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
            petDetail.setId(i + 1);
            petDetail.setPetId(i + 1);
            petDetail.setServiceItemType((byte) ServiceItemType.DAYCARE.getNumber());
            petDetails.add(petDetail);
        }

        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setId(100)
                .setName("24 Hour Fee")
                .setPrice(0)
                .setSurchargeType(SurchargeType.CHARGE_24_HOUR)
                .setTimeBasedPricingType(ServiceCharge.TimeBasedPricingType.HOURLY_EXCEED_TIERED_RATE)
                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                .addServiceItemTypes(ServiceItemType.DAYCARE)
                .addAllHourlyExceedRules(Collections.singletonList(ServiceChargeExceedHourRule.newBuilder()
                        .setHour(0)
                        .setBasePrice(10.0)
                        .build()))
                .setAutoApplyStatus(ServiceCharge.AutoApplyStatus.AUTO_APPLY_ENABLED_WITH_CONDITION)
                .setApplyType(ServiceCharge.ApplyType.PER_APPOINTMENT)
                .build();

        // 模拟远程返回服务费用配置
        when(serviceChargeHelper.listConditionServiceCharge(anyLong(), anyLong()))
                .thenReturn(List.of(serviceCharge));

        OrderModel order = OrderModel.newBuilder().setId(1).build();
        OrderDetailModel orderDetail = OrderDetailModel.newBuilder()
                .setOrder(order)
                .addAllLineItems(Collections.emptyList())
                .build();
        when(orderRemoteService.getOrderDetail(anyInt())).thenReturn(orderDetail);

        when(petDetailService.getPetDetailList(anyLong())).thenReturn(petDetails);
        when(featureFlagApi.isOn(any(FeatureFlag.class), any(FeatureFlagContext.class)))
                .thenReturn(false);

        when(organizationClient.getTimeZone(anyLong()))
                .thenReturn(TimeZone.newBuilder().setName(defaultTimeZoneName).build());
        // Act
        serviceChargeHandler.applyCharge24Hour(appointment, appointment);

        // Assert
        verify(orderStub)
                .updateOrderIncremental(
                        argThat(request -> request.getLineItemsList().size() == 1));
    }

    @Test
    void testApplyCharge24Hour_WithDifferentPetsChargeType_ShouldCalculateCorrectPrice() {
        // Arrange
        MoeGroomingAppointment appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getId()).thenReturn(1);
        when(appointment.getCheckInTime()).thenReturn(0L);
        when(appointment.getCheckOutTime()).thenReturn(25 * 60 * 60L);
        when(appointment.getServiceTypeInclude()).thenReturn(ServiceItemEnum.DAYCARE.getBitValue());

        List<MoeGroomingPetDetail> petDetails = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
            petDetail.setId(i + 1);
            petDetail.setPetId(i + 1);
            petDetail.setServiceItemType((byte) ServiceItemType.DAYCARE.getNumber());
            petDetails.add(petDetail);
        }

        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setId(100)
                .setName("24 Hour Fee")
                .setPrice(0)
                .setSurchargeType(SurchargeType.CHARGE_24_HOUR)
                .setTimeBasedPricingType(ServiceCharge.TimeBasedPricingType.HOURLY_EXCEED_TIERED_RATE)
                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS)
                .addServiceItemTypes(ServiceItemType.DAYCARE)
                .addAllHourlyExceedRules(Arrays.asList(
                        ServiceChargeExceedHourRule.newBuilder()
                                .setHour(0)
                                .setBasePrice(10.0)
                                .setAdditionalPetPrice(5.0)
                                .build(),
                        ServiceChargeExceedHourRule.newBuilder()
                                .setHour(2)
                                .setBasePrice(20.0)
                                .setAdditionalPetPrice(10.0)
                                .build()))
                .setAutoApplyStatus(ServiceCharge.AutoApplyStatus.AUTO_APPLY_ENABLED_WITH_CONDITION)
                .setApplyType(ServiceCharge.ApplyType.PER_APPOINTMENT)
                .build();

        // 模拟远程返回服务费用配置
        when(serviceChargeHelper.listConditionServiceCharge(anyLong(), anyLong()))
                .thenReturn(List.of(serviceCharge));

        OrderModel order = OrderModel.newBuilder().setId(1).build();
        OrderDetailModel orderDetail = OrderDetailModel.newBuilder()
                .setOrder(order)
                .addAllLineItems(Collections.emptyList())
                .build();
        when(orderRemoteService.getOrderDetail(anyInt())).thenReturn(orderDetail);

        when(petDetailService.getPetDetailList(anyLong())).thenReturn(petDetails);
        when(featureFlagApi.isOn(any(FeatureFlag.class), any(FeatureFlagContext.class)))
                .thenReturn(false);

        when(organizationClient.getTimeZone(anyLong()))
                .thenReturn(TimeZone.newBuilder().setName(defaultTimeZoneName).build());
        // Act
        serviceChargeHandler.applyCharge24Hour(appointment, appointment);

        // Assert
        verify(orderStub).updateOrderIncremental(argThat(request -> {
            OrderLineItemModel item = request.getLineItemsList().get(0);
            return item.getUnitPrice() == 30.0;
        }));
    }

    @Test
    void testApplyCharge24Hour_WithNoMatchingServiceItemType_ShouldNotApply() {
        // Arrange
        MoeGroomingAppointment appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getId()).thenReturn(1);
        when(appointment.getCheckInTime()).thenReturn(0L);
        when(appointment.getCheckOutTime()).thenReturn(25 * 60 * 60L);
        when(appointment.getServiceTypeInclude()).thenReturn(ServiceItemEnum.BOARDING.getBitValue()); // 不匹配 DAYCARE

        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setId(100)
                .setName("24 Hour Fee")
                .addServiceItemTypes(ServiceItemType.DAYCARE) // 只匹配 DAYCARE
                .build();

        // 模拟远程返回服务费用配置
        when(serviceChargeHelper.listConditionServiceCharge(anyLong(), anyLong()))
                .thenReturn(List.of(serviceCharge));

        when(petDetailService.getPetDetailList(anyLong())).thenReturn(Collections.emptyList());

        when(organizationClient.getTimeZone(anyLong()))
                .thenReturn(TimeZone.newBuilder().setName(defaultTimeZoneName).build());

        // Act
        serviceChargeHandler.applyCharge24Hour(appointment, appointment);

        // Assert
        verify(orderStub, times(0)).updateOrderIncremental(any());
    }

    @Test
    void testApplyCharge24Hour_WithNoPets_ShouldNotAddLineItem() {
        // Arrange
        MoeGroomingAppointment appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getId()).thenReturn(1);
        when(appointment.getCheckInTime()).thenReturn(0L);
        when(appointment.getCheckOutTime()).thenReturn(25 * 60 * 60L);
        when(appointment.getServiceTypeInclude()).thenReturn(ServiceItemEnum.DAYCARE.getBitValue());

        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setId(100)
                .setName("24 Hour Fee")
                .addServiceItemTypes(ServiceItemType.DAYCARE)
                .build();

        // 模拟远程返回服务费用配置
        when(serviceChargeHelper.listConditionServiceCharge(anyLong(), anyLong()))
                .thenReturn(List.of(serviceCharge));

        OrderModel order = OrderModel.newBuilder().setId(1).build();
        OrderDetailModel orderDetail = OrderDetailModel.newBuilder()
                .setOrder(order)
                .addAllLineItems(Collections.emptyList())
                .build();
        when(orderRemoteService.getOrderDetail(anyInt())).thenReturn(orderDetail);

        when(petDetailService.getPetDetailList(anyLong())).thenReturn(Collections.emptyList());

        when(organizationClient.getTimeZone(anyLong()))
                .thenReturn(TimeZone.newBuilder().setName(defaultTimeZoneName).build());

        // Act
        serviceChargeHandler.applyCharge24Hour(appointment, appointment);

        // Assert
        verify(orderStub, times(0)).updateOrderIncremental(any());
    }

    @Test
    void testApplyCharge24Hour_WithAutoApplyDisabled_ShouldNotAddLineItem() {
        MoeGroomingAppointment appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getId()).thenReturn(1);
        when(appointment.getCheckInTime()).thenReturn(0L);
        when(appointment.getCheckOutTime()).thenReturn(25 * 60 * 60L);
        when(appointment.getServiceTypeInclude()).thenReturn(ServiceItemEnum.DAYCARE.getBitValue());

        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setId(100)
                .setName("24 Hour Fee")
                .addServiceItemTypes(ServiceItemType.DAYCARE)
                .setAutoApplyStatus(ServiceCharge.AutoApplyStatus.AUTO_APPLY_DISABLED)
                .build();

        // 模拟远程返回服务费用配置
        when(serviceChargeHelper.listConditionServiceCharge(anyLong(), anyLong()))
                .thenReturn(List.of(serviceCharge));

        OrderModel order = OrderModel.newBuilder().setId(1).build();
        OrderDetailModel orderDetail = OrderDetailModel.newBuilder()
                .setOrder(order)
                .addAllLineItems(Collections.emptyList())
                .build();
        when(orderRemoteService.getOrderDetail(anyInt())).thenReturn(orderDetail);

        when(petDetailService.getPetDetailList(anyLong()))
                .thenReturn(Collections.singletonList(new MoeGroomingPetDetail()));

        when(organizationClient.getTimeZone(anyLong()))
                .thenReturn(TimeZone.newBuilder().setName(defaultTimeZoneName).build());

        serviceChargeHandler.applyCharge24Hour(appointment, appointment);

        verify(orderStub, times(0)).updateOrderIncremental(any());
    }

    @Test
    void testApplyCharge24Hour_WhenSameChargeAlreadyExists_ShouldNotAddAgain() {
        MoeGroomingAppointment appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getId()).thenReturn(1);
        when(appointment.getCheckInTime()).thenReturn(0L);
        when(appointment.getCheckOutTime()).thenReturn(25 * 60 * 60L);
        when(appointment.getServiceTypeInclude()).thenReturn(ServiceItemEnum.DAYCARE.getBitValue());

        List<MoeGroomingPetDetail> petDetails = Collections.singletonList(new MoeGroomingPetDetail());
        when(petDetailService.getPetDetailList(anyLong())).thenReturn(petDetails);

        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setId(100)
                .setName("24 Hour Fee")
                .addServiceItemTypes(ServiceItemType.DAYCARE)
                .build();

        // 模拟远程返回服务费用配置
        when(serviceChargeHelper.listConditionServiceCharge(anyLong(), anyLong()))
                .thenReturn(List.of(serviceCharge));

        OrderLineItemModel existingItem = OrderLineItemModel.newBuilder()
                .setName("24 Hour Fee")
                .setUnitPrice(10.0)
                .build();

        OrderModel order = OrderModel.newBuilder().setId(1).build();
        OrderDetailModel orderDetail = OrderDetailModel.newBuilder()
                .setOrder(order)
                .addLineItems(existingItem)
                .build();
        when(orderRemoteService.getOrderDetail(anyInt())).thenReturn(orderDetail);

        when(organizationClient.getTimeZone(anyLong()))
                .thenReturn(TimeZone.newBuilder().setName(defaultTimeZoneName).build());

        serviceChargeHandler.applyCharge24Hour(appointment, appointment);

        verify(orderStub, times(0)).updateOrderIncremental(any());
    }

    @Test
    void testApplyCharge24Hour_ShouldUseCorrectCompanyIdForTimeZone() {
        // Arrange
        MoeGroomingAppointment appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getId()).thenReturn(123);
        when(appointment.getCompanyId()).thenReturn(456L);
        when(appointment.getCheckInTime()).thenReturn(0L);
        when(appointment.getCheckOutTime()).thenReturn(25 * 60 * 60L); // 超过 24 小时
        when(appointment.getServiceTypeInclude()).thenReturn(ServiceItemEnum.DAYCARE.getBitValue());

        List<MoeGroomingPetDetail> petDetails = new ArrayList<>();
        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setPetId(1);
        petDetail.setServiceItemType((byte) ServiceItemType.DAYCARE.getNumber());
        petDetails.add(petDetail);

        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setId(100)
                .setName("24 Hour Fee")
                .setPrice(10.0)
                .setSurchargeType(SurchargeType.CHARGE_24_HOUR)
                .setTimeBasedPricingType(ServiceCharge.TimeBasedPricingType.FLAT_RATE)
                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                .addServiceItemTypes(ServiceItemType.DAYCARE)
                .setAutoApplyStatus(ServiceCharge.AutoApplyStatus.AUTO_APPLY_ENABLED_WITH_CONDITION)
                .setApplyType(ServiceCharge.ApplyType.PER_APPOINTMENT)
                .build();

        // 模拟远程返回服务费用配置
        when(serviceChargeHelper.listConditionServiceCharge(anyLong(), anyLong()))
                .thenReturn(List.of(serviceCharge));

        // 模拟订单详情
        OrderModel order = OrderModel.newBuilder().setId(1).build();
        OrderDetailModel orderDetail = OrderDetailModel.newBuilder()
                .setOrder(order)
                .addAllLineItems(Collections.emptyList())
                .build();

        when(orderRemoteService.getOrderDetail(anyInt())).thenReturn(orderDetail);
        when(petDetailService.getPetDetailList(anyLong())).thenReturn(petDetails);
        when(featureFlagApi.isOn(any(FeatureFlag.class), any(FeatureFlagContext.class)))
                .thenReturn(false);

        // 针对正确的 companyId (456L) mock 时区返回
        when(organizationClient.getTimeZone(456L))
                .thenReturn(TimeZone.newBuilder().setName(defaultTimeZoneName).build());

        // Act
        serviceChargeHandler.applyCharge24Hour(appointment, appointment);

        // Assert - 如果代码正确使用了 companyId，应该会成功添加 line item
        // 如果代码错误使用了 appointmentId，会抛出异常导致测试失败
        verify(orderStub).updateOrderIncremental(argThat(request -> {
            return request.getLineItemsList().size() == 1
                    && request.getLineItemsList().get(0).getName().equals("24 Hour Fee")
                    && request.getLineItemsList().get(0).getUnitPrice() == 10.0;
        }));
    }

    @ParameterizedTest
    @MethodSource("provideConfigureTieredRateCases")
    void testConfigureTieredRate_Parameterized(
            String testName,
            ServiceCharge serviceCharge,
            List<MoeGroomingPetDetail> petDetails,
            long usedTime,
            String expectedName,
            double expectedPrice) {

        OrderLineItemModel.Builder builder = OrderLineItemModel.newBuilder();

        when(organizationClient.getTimeZone(anyLong()))
                .thenReturn(TimeZone.newBuilder().setName(defaultTimeZoneName).build());

        serviceChargeHandler.configureTieredRate(builder, serviceCharge, petDetails.size(), usedTime);

        assertEquals(expectedName, builder.getName(), testName + " - name mismatch");
        assertEquals(expectedPrice, builder.getUnitPrice(), 0.001, testName + " - price mismatch");
    }

    static Stream<Arguments> provideConfigureTieredRateCases() {
        return Stream.of(

                // ✅ 匹配规则 (usedTime >= 24h + rule.hour)
                Arguments.of(
                        "Match rule with hour=2",
                        ServiceCharge.newBuilder()
                                .addAllHourlyExceedRules(Arrays.asList(
                                        ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(0)
                                                .setFeeName("Base Rule")
                                                .setBasePrice(10.0)
                                                .build(),
                                        ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(2)
                                                .setFeeName("Extra 2H Rule")
                                                .setBasePrice(30.0)
                                                .build()))
                                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                                .build(),
                        Arrays.asList(new MoeGroomingPetDetail(), new MoeGroomingPetDetail()),
                        2 * 60 * 60L + 1, // exceed over 2h
                        "Extra 2H Rule",
                        60.0), // 30 * 2 pets

                // ✅ 精确匹配 24h + rule.hour
                Arguments.of(
                        "Exact match at 24h + 2h",
                        ServiceCharge.newBuilder()
                                .addAllHourlyExceedRules(
                                        Collections.singletonList(ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(2)
                                                .setFeeName("2H Rule")
                                                .setBasePrice(20.0)
                                                .build()))
                                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                                .build(),
                        Collections.singletonList(new MoeGroomingPetDetail()),
                        2 * 60 * 60L, // exceed 2h
                        "2H Rule",
                        20.0),

                // ✅ 时间介于两条规则之间（应选最大满足条件的）
                Arguments.of(
                        "Between two rules: 25h matches 1h rule",
                        ServiceCharge.newBuilder()
                                .addAllHourlyExceedRules(Arrays.asList(
                                        ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(0)
                                                .setFeeName("Base Rule")
                                                .setBasePrice(10.0)
                                                .build(),
                                        ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(1)
                                                .setFeeName("1H Rule")
                                                .setBasePrice(20.0)
                                                .build(),
                                        ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(3)
                                                .setFeeName("3H Rule")
                                                .setBasePrice(30.0)
                                                .build()))
                                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                                .build(),
                        Arrays.asList(
                                new MoeGroomingPetDetail(), new MoeGroomingPetDetail(), new MoeGroomingPetDetail()),
                        60 * 60L, // exceed 1h
                        "1H Rule",
                        60.0), // 20 * 3 pets

                // ❌ 没有满足的规则（时间小于最小规则）
                Arguments.of(
                        "Used time less than any rule",
                        ServiceCharge.newBuilder()
                                .addAllHourlyExceedRules(
                                        Collections.singletonList(ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(2)
                                                .setFeeName("2H Rule")
                                                .setBasePrice(20.0)
                                                .build()))
                                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                                .build(),
                        Arrays.asList(new MoeGroomingPetDetail(), new MoeGroomingPetDetail()),
                        60 * 60L, // exceed 1h
                        "",
                        0.0),

                // ✅ 多条规则无序排序，内部应自动处理
                Arguments.of(
                        "Unsorted rules should be handled correctly",
                        ServiceCharge.newBuilder()
                                .addAllHourlyExceedRules(Arrays.asList(
                                        ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(3)
                                                .setFeeName("3H Rule")
                                                .setBasePrice(30.0)
                                                .build(),
                                        ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(1)
                                                .setFeeName("1H Rule")
                                                .setBasePrice(10.0)
                                                .build(),
                                        ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(2)
                                                .setFeeName("2H Rule")
                                                .setBasePrice(20.0)
                                                .build()))
                                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                                .build(),
                        Collections.singletonList(new MoeGroomingPetDetail()),
                        2 * 60 * 60L + 1, // exceed over 2h
                        "2H Rule",
                        20.0),

                // ✅ 规则为空时不应设置任何费用项
                Arguments.of(
                        "Empty rules list",
                        ServiceCharge.newBuilder().build(),
                        Collections.emptyList(),
                        6 * 60 * 60L,
                        "",
                        0.0),

                // ✅ 不同宠物计费类型 - DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS
                Arguments.of(
                        "Different charge per pet type",
                        ServiceCharge.newBuilder()
                                .addAllHourlyExceedRules(
                                        Collections.singletonList(ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(0)
                                                .setFeeName("Base Rule")
                                                .setBasePrice(10.0)
                                                .setAdditionalPetPrice(5.0)
                                                .build()))
                                .setMultiplePetsChargeType(
                                        ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS)
                                .build(),
                        Arrays.asList(
                                new MoeGroomingPetDetail(),
                                new MoeGroomingPetDetail(),
                                new MoeGroomingPetDetail()), // 3 只宠物
                        60 * 60L, // exceed 1h
                        "Base Rule",
                        10.0 + 5.0 * 2), // 10 + 5*2 = 20

                // ✅ 异常输入：负数时间
                Arguments.of(
                        "Negative usedTime",
                        ServiceCharge.newBuilder()
                                .addAllHourlyExceedRules(
                                        Collections.singletonList(ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(0)
                                                .setFeeName("Base Rule")
                                                .setBasePrice(10.0)
                                                .build()))
                                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                                .build(),
                        Collections.singletonList(new MoeGroomingPetDetail()),
                        -1 * 60 * 60L,
                        "",
                        0.0),

                // ✅ 多个规则都满足，应取最大的一个（排序后第一个匹配）
                Arguments.of(
                        "Multiple matching rules",
                        ServiceCharge.newBuilder()
                                .addAllHourlyExceedRules(Arrays.asList(
                                        ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(0)
                                                .setFeeName("Base Rule")
                                                .setBasePrice(10.0)
                                                .build(),
                                        ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(1)
                                                .setFeeName("1H Rule")
                                                .setBasePrice(15.0)
                                                .build(),
                                        ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(2)
                                                .setFeeName("2H Rule")
                                                .setBasePrice(20.0)
                                                .build()))
                                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                                .build(),
                        Arrays.asList(new MoeGroomingPetDetail(), new MoeGroomingPetDetail()),
                        60 * 60L + 30 * 60L, // exceed 1.5h
                        "1H Rule",
                        30.0), // 15 * 2 pets

                // ✅ 没有超过任何规则
                Arguments.of(
                        "No rule matched",
                        ServiceCharge.newBuilder()
                                .addAllHourlyExceedRules(
                                        Collections.singletonList(ServiceChargeExceedHourRule.newBuilder()
                                                .setHour(2)
                                                .setFeeName("2H Rule")
                                                .setBasePrice(20.0)
                                                .build()))
                                .setMultiplePetsChargeType(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET)
                                .build(),
                        Arrays.asList(new MoeGroomingPetDetail(), new MoeGroomingPetDetail()),
                        60 * 60L, // exceed 1h
                        "",
                        0.0));
    }
}
