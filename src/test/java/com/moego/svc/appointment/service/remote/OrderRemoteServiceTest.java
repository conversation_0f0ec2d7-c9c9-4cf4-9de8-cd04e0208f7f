package com.moego.svc.appointment.service.remote;

import static com.moego.idl.service.order.v1.OrderServiceGrpc.OrderServiceBlockingStub;
import static java.util.concurrent.TimeUnit.SECONDS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.common.enums.order.LineApplyType;
import com.moego.common.enums.order.OrderItemType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import com.moego.idl.models.order.v1.OrderLineTaxModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.service.order.v1.GetOrderListResponse;
import com.moego.idl.service.order.v1.UpdateOrderIncrResponse;
import com.moego.lib.common.exception.BizException;
import com.moego.server.business.dto.MoeBusinessTaxDto;
import com.moego.server.grooming.api.IInvoiceApplyPackageService;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.dto.PricingRuleItemDTO;
import com.moego.svc.appointment.service.AppointmentExtraInfoService;
import com.moego.svc.appointment.service.BoardingSplitLodgingService;
import com.moego.svc.appointment.service.EvaluationServiceDetailService;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import com.moego.svc.appointment.service.PricingRuleRecordApplyService;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * {@link OrderRemoteService}
 */
@ExtendWith(MockitoExtension.class)
class OrderRemoteServiceTest {

    @Mock
    private PetDetailServiceProxy petDetailService;

    @Mock
    private EvaluationServiceDetailService evaluationService;

    @Mock
    private OfferingRemoteService offeringRemoteService;

    @Mock
    private BusinessTaxRateRemoteService taxRateRemoteService;

    @Mock
    private IInvoiceApplyPackageService invoiceApplyPackageApi;

    @Mock
    private OrderServiceBlockingStub orderServiceBlockingStub;

    @Mock
    private AppointmentExtraInfoService extraInfoService;

    @Mock
    private PricingRuleRecordApplyService pricingRuleApplyService;

    @Mock
    private BoardingSplitLodgingService boardingSplitLodgingService;

    @InjectMocks
    private OrderRemoteService orderRemoteService;

    /**
     * {@link OrderRemoteService#updateOrder(MoeGroomingAppointment)}
     */
    @Test
    void updateOrder_whenAnyCondition_thenMustCallUpdateAppliedPackageForOrder() {
        // Mock
        var appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setCompanyId(100L);
        appointment.setBusinessId(200);

        when(petDetailService.getPetDetailList(anyLong())).thenReturn(List.of());
        when(orderServiceBlockingStub.getOrderDetail(any()))
                .thenReturn(OrderDetailModel.newBuilder()
                        .setOrder(OrderModel.newBuilder().setId(2).build())
                        .build());
        when(offeringRemoteService.listService(anyLong(), anyLong(), anyMap())).thenReturn(Map.of());
        when(evaluationService.getPetEvaluationList(anyLong())).thenReturn(List.of());
        when(taxRateRemoteService.listServiceTax(anyList(), anyMap())).thenReturn(Map.of());
        when(orderServiceBlockingStub.updateOrderIncremental(any()))
                .thenReturn(UpdateOrderIncrResponse.getDefaultInstance());
        when(extraInfoService.isNewOrder(anyLong())).thenReturn(false);

        // Act
        orderRemoteService.updateOrder(appointment);

        // Assert
        await().atMost(2, SECONDS)
                .untilAsserted(() -> verify(invoiceApplyPackageApi, times(1)).updateAppliedPackageForOrder(any()));
    }

    /**
     * {@link OrderRemoteService#getBySource(OrderSourceType, long)}
     */
    @Test
    void getBySource_whenSourceNotFound_thenReturnNull() {
        // Mock
        when(orderServiceBlockingStub.getOrderList(any()))
                .thenReturn(GetOrderListResponse.newBuilder().build());

        // Act
        var actual = orderRemoteService.getBySource(OrderSourceType.BOOKING_REQUEST, 1);

        // Assert
        assertThat(actual).isNull();
    }

    /**
     * {@link OrderRemoteService#getBySource(OrderSourceType, long)}
     */
    @Test
    void getBySource_whenSourceExists_thenReturnIt() {
        // Mock
        var order = OrderDetailModel.newBuilder()
                .setOrder(OrderModel.newBuilder().setId(1).build())
                .build();
        when(orderServiceBlockingStub.getOrderList(any()))
                .thenReturn(
                        GetOrderListResponse.newBuilder().addOrderList(order).build());

        // Act
        var actual = orderRemoteService.getBySource(OrderSourceType.BOOKING_REQUEST, 1);

        // Assert
        assertThat(actual).isNotNull();
    }

    /**
     * {@link OrderRemoteService#getOrderDetail(Long)}
     */
    @Test
    void getOrderDetail_whenOrderExists_thenReturnIt() {
        // Mock
        var order = OrderDetailModel.newBuilder().build();
        when(orderServiceBlockingStub.getOrderDetail(any())).thenReturn(order);

        // Act
        var actual = orderRemoteService.getOrderDetail(1L);

        // Assert
        var expected = OrderDetailModel.newBuilder().build();
        assertThat(actual).isEqualTo(expected);
    }

    /**
     * {@link OrderRemoteService#getOrderDetail(Long)}
     */
    @Test
    void getOrderDetail_whenOrderNotExists_thenThrowException() {
        // Mock
        when(orderServiceBlockingStub.getOrderDetail(any())).thenReturn(null);

        // Act
        var result = assertThatCode(() -> orderRemoteService.getOrderDetail(1L));

        // Assert
        result.isInstanceOfSatisfying(BizException.class, e -> {
            assertThat(e.getCode()).isEqualTo(Code.CODE_INVOICE_NOT_FOUND_VALUE);
        });
    }

    @Test
    void buildItemKey_whenValidPetDetail_thenReturnCorrectKey() {
        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setServiceId(1);
        petDetail.setServicePrice(new BigDecimal("10.5"));
        petDetail.setPetId(100);

        String result = OrderRemoteService.buildItemKey(petDetail);

        assertThat(result).isEqualTo("1-10.5-100");
    }

    @Test
    void buildItemKey_whenZeroServicePrice_thenReturnCorrectKey() {
        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setServiceId(1);
        petDetail.setServicePrice(BigDecimal.ZERO);
        petDetail.setPetId(100);

        String result = OrderRemoteService.buildItemKey(petDetail);

        assertThat(result).isEqualTo("1-0.0-100");
    }

    @Test
    void buildItemKey_whenValidOrderLineItem_thenReturnCorrectKey() {
        OrderLineItemModel item = OrderLineItemModel.newBuilder()
                .setObjectId(1)
                .setUnitPrice(10.5)
                .setPetId(100)
                .build();

        String result = OrderRemoteService.buildItemKey(item);

        assertThat(result).isEqualTo("1-10.5-100");
    }

    @Test
    void buildItemKey_whenNullPetId_thenReturnCorrectKey() {
        OrderLineItemModel item = OrderLineItemModel.newBuilder()
                .setObjectId(1)
                .setUnitPrice(10.5)
                .setPetId(0)
                .build();

        String result = OrderRemoteService.buildItemKey(item);

        assertThat(result).isEqualTo("1-10.5-0");
    }

    @Test
    void buildOrderLineTaxModel_whenValidTax_thenReturnCorrectModel() {
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setBusinessId(1);
        appointment.setCreatedById(2);

        MoeBusinessTaxDto tax = new MoeBusinessTaxDto();
        tax.setId(3);
        tax.setTaxRate(0.1);
        tax.setTaxName("VAT");

        OrderLineTaxModel result = orderRemoteService.buildOrderLineTaxModel(appointment, tax);

        assertThat(result.getBusinessId()).isEqualTo(1);
        assertThat(result.getApplyType()).isEqualTo(LineApplyType.TYPE_ITEM.getType());
        assertThat(result.getTaxId()).isEqualTo(3);
        assertThat(result.getTaxRate()).isEqualTo(0.1);
        assertThat(result.getTaxName()).isEqualTo("VAT");
        assertThat(result.getApplyBy()).isEqualTo(2);
    }

    private OrderLineItemModel productItem;
    private OrderLineItemModel serviceItem;
    private OrderLineItemModel evalServiceItem;
    private OrderLineItemModel deletedServiceItem;

    @BeforeEach
    void setUp() {
        productItem = OrderLineItemModel.newBuilder()
                .setType(OrderItemType.ITEM_TYPE_PRODUCT.getType())
                .setName("Product Item")
                .setIsDeleted(false)
                .build();

        serviceItem = OrderLineItemModel.newBuilder()
                .setType(OrderItemType.ITEM_TYPE_SERVICE.getType())
                .setName("Service Item")
                .setIsDeleted(false)
                .build();

        evalServiceItem = OrderLineItemModel.newBuilder()
                .setType(OrderItemType.ITEM_TYPE_EVALUATION_SERVICE.getType())
                .setName("Evaluation Service")
                .setIsDeleted(false)
                .build();

        deletedServiceItem = OrderLineItemModel.newBuilder()
                .setType(OrderItemType.ITEM_TYPE_SERVICE.getType())
                .setName("Already Deleted Service")
                .setIsDeleted(true)
                .build();
    }

    @Test
    void should_combine_new_items_and_mark_old_service_items_as_deleted() {
        // Arrange
        var newItems = List.of(productItem);
        var oldItems = List.of(serviceItem, evalServiceItem);

        var expectedServiceItem = serviceItem.toBuilder().setIsDeleted(true).build();
        var expectedEvalServiceItem =
                evalServiceItem.toBuilder().setIsDeleted(true).build();
        var expected = List.of(productItem, expectedServiceItem, expectedEvalServiceItem);

        // Act
        var result = OrderRemoteService.buildCombinedItemModels(newItems, oldItems);

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void should_handle_empty_new_items() {
        // Arrange
        var newItems = List.<OrderLineItemModel>of();
        var oldItems = List.of(serviceItem);

        var expectedServiceItem = serviceItem.toBuilder().setIsDeleted(true).build();
        var expected = List.of(expectedServiceItem);

        // Act
        var result = OrderRemoteService.buildCombinedItemModels(newItems, oldItems);

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void should_handle_empty_old_items() {
        // Arrange
        var newItems = List.of(productItem);
        var oldItems = List.<OrderLineItemModel>of();

        var expected = List.of(productItem);

        // Act
        var result = OrderRemoteService.buildCombinedItemModels(newItems, oldItems);

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void should_handle_both_empty_lists() {
        // Arrange
        var newItems = List.<OrderLineItemModel>of();
        var oldItems = List.<OrderLineItemModel>of();

        var expected = List.<OrderLineItemModel>of();

        // Act
        var result = OrderRemoteService.buildCombinedItemModels(newItems, oldItems);

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void should_ignore_non_service_items_in_old_list() {
        // Arrange
        var newItems = List.of(productItem);
        var oldItems = List.of(productItem, serviceItem);

        var expectedServiceItem = serviceItem.toBuilder().setIsDeleted(true).build();
        var expected = List.of(productItem, expectedServiceItem);

        // Act
        var result = OrderRemoteService.buildCombinedItemModels(newItems, oldItems);

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void should_keep_already_deleted_service_items() {
        // Arrange
        var newItems = List.of(productItem);
        var oldItems = List.of(deletedServiceItem);

        var expected = List.of(productItem, deletedServiceItem);

        // Act
        var result = OrderRemoteService.buildCombinedItemModels(newItems, oldItems);

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void buildPetDetailLineItems_whenPetDetailsAreEmpty_thenReturnEmptyList() {
        // Arrange
        var appointment = new MoeGroomingAppointment();
        var petDetails = List.<MoeGroomingPetDetail>of();
        var serviceMap = Map.<Long, com.moego.idl.models.offering.v1.CustomizedServiceView>of();
        var serviceTaxMap = Map.<Integer, MoeBusinessTaxDto>of();

        // Act
        List<OrderLineItemModel> result =
                orderRemoteService.buildPetDetailLineItems(appointment, petDetails, serviceMap, serviceTaxMap);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void buildPetDetailLineItems_whenServiceTypeIsUnspecified_thenSetFromServiceMap() {
        // Arrange
        var appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setBusinessId(1);

        var petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setServiceId(1);
        petDetail.setGroomingId(1);
        petDetail.setPetId(1);
        petDetail.setStartDate("2025-07-16");
        petDetail.setStartTime(600L);
        petDetail.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        petDetail.setServiceItemType((byte) 1);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail.setServicePrice(new BigDecimal("50.0"));

        var serviceView = com.moego.idl.models.offering.v1.CustomizedServiceView.newBuilder()
                .setTypeValue(com.moego.idl.models.offering.v1.ServiceType.SERVICE_TYPE_UNSPECIFIED_VALUE)
                .setName("Test Service")
                .setDescription("Test Description")
                .build();

        var petDetails = List.of(petDetail);
        var serviceMap = Map.of(1L, serviceView);
        var serviceTaxMap = Map.<Integer, MoeBusinessTaxDto>of();

        when(pricingRuleApplyService.getPricingRuleUsingMap(any(), anyList())).thenReturn(Map.of());
        when(boardingSplitLodgingService.getBoardingSplitLodgings(anyList())).thenReturn(List.of());

        // Act
        List<OrderLineItemModel> result =
                orderRemoteService.buildPetDetailLineItems(appointment, petDetails, serviceMap, serviceTaxMap);

        // Assert
        assertThat(result).hasSize(1);
        OrderLineItemModel item = result.get(0);
        assertThat(item.getUnitPrice()).isEqualTo(50.0);
        assertThat(item.getQuantity()).isEqualTo(1);
        assertThat(item.getName()).isEqualTo("Test Service");
    }

    @Test
    void buildPetDetailLineItems_withTax_thenLineItemContainsTaxInfo() {
        // Arrange
        var appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setBusinessId(1);
        appointment.setCreatedById(1);

        var petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setServiceId(1);
        petDetail.setGroomingId(1);
        petDetail.setPetId(1);
        petDetail.setStartDate("2025-07-16");
        petDetail.setStartTime(600L);
        petDetail.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        petDetail.setServiceItemType((byte) 1);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail.setServicePrice(new BigDecimal("100.0"));

        var serviceView = com.moego.idl.models.offering.v1.CustomizedServiceView.newBuilder()
                .setName("Taxable Service")
                .build();

        var taxDto = new MoeBusinessTaxDto();
        taxDto.setId(1);
        taxDto.setTaxName("GST");
        taxDto.setTaxRate(0.1);

        var petDetails = List.of(petDetail);
        var serviceMap = Map.of(1L, serviceView);
        var serviceTaxMap = Map.of(1, taxDto);

        when(pricingRuleApplyService.getPricingRuleUsingMap(any(), anyList())).thenReturn(Map.of());
        when(boardingSplitLodgingService.getBoardingSplitLodgings(anyList())).thenReturn(List.of());

        // Act
        List<OrderLineItemModel> result =
                orderRemoteService.buildPetDetailLineItems(appointment, petDetails, serviceMap, serviceTaxMap);

        // Assert
        assertThat(result).hasSize(1);
        OrderLineItemModel item = result.get(0);
        assertThat(item.getTaxId()).isEqualTo(1);
        assertThat(item.getTaxName()).isEqualTo("GST");
        assertThat(item.getTaxRate().getValue()).isEqualTo("0.1");
    }

    @Test
    void buildPetDetailLineItems_withPricingRule_thenUseRulePriceAndQuantity() {
        // Arrange
        var appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setBusinessId(1);

        var petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setServiceId(1);
        petDetail.setGroomingId(1);
        petDetail.setPetId(1);
        petDetail.setStartDate("2025-07-16");
        petDetail.setStartTime(600L);
        petDetail.setServiceItemType((byte) 1);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        petDetail.setServicePrice(new BigDecimal("100.0")); // Original price

        var serviceView = com.moego.idl.models.offering.v1.CustomizedServiceView.newBuilder()
                .setName("Service with Rule")
                .build();

        var pricingRuleEntry = new PricingRuleItemDTO(new BigDecimal("80.0"), 2, List.of()); // Rule price and quantity
        var pricingRuleMap = Map.of(new java.util.AbstractMap.SimpleEntry<>(1L, 1L), List.of(pricingRuleEntry));

        when(pricingRuleApplyService.getPricingRuleUsingMap(any(), anyList())).thenReturn(pricingRuleMap);

        var petDetails = List.of(petDetail);
        var serviceMap = Map.of(1L, serviceView);
        var serviceTaxMap = Map.<Integer, MoeBusinessTaxDto>of();

        // Act
        List<OrderLineItemModel> result =
                orderRemoteService.buildPetDetailLineItems(appointment, petDetails, serviceMap, serviceTaxMap);

        // Assert
        assertThat(result).hasSize(1);
        OrderLineItemModel item = result.get(0);
        assertThat(item.getUnitPrice()).isEqualTo(80.0);
        assertThat(item.getQuantity()).isEqualTo(2);
    }

    @Test
    void buildPetDetailLineItems_withBoardingSplitLodging_perNight_thenUseSplitLodgingPriceAndQuantity() {
        // Arrange
        var appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setBusinessId(1);

        var petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setServiceId(1);
        petDetail.setGroomingId(1);
        petDetail.setPetId(1);
        petDetail.setStartDate("2025-07-16");
        petDetail.setStartTime(600L);
        petDetail.setServiceItemType((byte) 1);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        petDetail.setServicePrice(new BigDecimal("100.0"));

        var serviceView = com.moego.idl.models.offering.v1.CustomizedServiceView.newBuilder()
                .setName("Boarding Service")
                .build();

        var splitLodging = new com.moego.svc.appointment.domain.BoardingSplitLodging();
        splitLodging.setPetDetailId(1L);
        splitLodging.setPrice(new BigDecimal("90.0"));
        splitLodging.setStartDateTime(java.time.LocalDateTime.of(2025, 7, 16, 10, 0));
        splitLodging.setEndDateTime(java.time.LocalDateTime.of(2025, 7, 18, 10, 0)); // 2 nights

        when(boardingSplitLodgingService.getBoardingSplitLodgings(anyList())).thenReturn(List.of(splitLodging));
        when(pricingRuleApplyService.getPricingRuleUsingMap(any(), anyList())).thenReturn(Map.of());

        var petDetails = List.of(petDetail);
        var serviceMap = Map.of(1L, serviceView);
        var serviceTaxMap = Map.<Integer, MoeBusinessTaxDto>of();

        // Act
        List<OrderLineItemModel> result =
                orderRemoteService.buildPetDetailLineItems(appointment, petDetails, serviceMap, serviceTaxMap);

        // Assert
        assertThat(result).hasSize(1);
        OrderLineItemModel item = result.get(0);
        assertThat(item.getUnitPrice()).isEqualTo(90.0);
        assertThat(item.getQuantity()).isEqualTo(2);
    }

    @Test
    void buildPetDetailLineItems_withBoardingSplitLodging_perDay_thenUseSplitLodgingPriceAndQuantity() {
        // Arrange
        var appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setBusinessId(1);

        var petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setServiceId(1);
        petDetail.setGroomingId(1);
        petDetail.setPetId(1);
        petDetail.setStartDate("2025-07-16");
        petDetail.setStartTime(600L);
        petDetail.setServiceItemType((byte) 1);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail.setPriceUnit(ServicePriceUnit.PER_DAY_VALUE);
        petDetail.setServicePrice(new BigDecimal("100.0"));

        var serviceView = com.moego.idl.models.offering.v1.CustomizedServiceView.newBuilder()
                .setName("Boarding Service")
                .build();

        var splitLodging = new com.moego.svc.appointment.domain.BoardingSplitLodging();
        splitLodging.setPetDetailId(1L);
        splitLodging.setPrice(new BigDecimal("90.0"));
        splitLodging.setStartDateTime(java.time.LocalDateTime.of(2025, 7, 16, 10, 0));
        splitLodging.setEndDateTime(java.time.LocalDateTime.of(2025, 7, 18, 10, 0)); // 3 days

        when(boardingSplitLodgingService.getBoardingSplitLodgings(anyList())).thenReturn(List.of(splitLodging));
        when(pricingRuleApplyService.getPricingRuleUsingMap(any(), anyList())).thenReturn(Map.of());

        var petDetails = List.of(petDetail);
        var serviceMap = Map.of(1L, serviceView);
        var serviceTaxMap = Map.<Integer, MoeBusinessTaxDto>of();

        // Act
        List<OrderLineItemModel> result =
                orderRemoteService.buildPetDetailLineItems(appointment, petDetails, serviceMap, serviceTaxMap);

        // Assert
        assertThat(result).hasSize(1);
        OrderLineItemModel item = result.get(0);
        assertThat(item.getUnitPrice()).isEqualTo(90.0);
        assertThat(item.getQuantity()).isEqualTo(2);
    }

    @Test
    void buildPetDetailLineItems_withMixedServicesAndSplitLodging_thenProcessAllDetails() {
        // Arrange
        var appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setBusinessId(1);

        // 1. Boarding pet detail with split lodging
        var boardingPetDetail = new MoeGroomingPetDetail();
        boardingPetDetail.setId(1);
        boardingPetDetail.setServiceId(101);
        boardingPetDetail.setGroomingId(1);
        boardingPetDetail.setPetId(1);
        boardingPetDetail.setStartDate("2025-07-16");
        boardingPetDetail.setEndDate("2025-07-18");
        boardingPetDetail.setStartTime(600L);
        boardingPetDetail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        boardingPetDetail.setServiceType(ServiceType.SERVICE_VALUE);
        boardingPetDetail.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        boardingPetDetail.setServicePrice(new BigDecimal("100.0"));

        var boardingServiceView = com.moego.idl.models.offering.v1.CustomizedServiceView.newBuilder()
                .setName("Boarding Service")
                .build();

        var splitLodging = new com.moego.svc.appointment.domain.BoardingSplitLodging();
        splitLodging.setPetDetailId(1L);
        splitLodging.setPrice(new BigDecimal("90.0"));
        splitLodging.setStartDateTime(java.time.LocalDateTime.of(2025, 7, 16, 10, 0));
        splitLodging.setEndDateTime(java.time.LocalDateTime.of(2025, 7, 18, 10, 0)); // 2 nights

        // 2. Grooming pet detail (non-boarding)
        var groomingPetDetail = new MoeGroomingPetDetail();
        groomingPetDetail.setId(2);
        groomingPetDetail.setServiceId(102);
        groomingPetDetail.setGroomingId(1);
        groomingPetDetail.setPetId(1);
        groomingPetDetail.setStartDate("2025-07-16");
        groomingPetDetail.setEndDate("2025-07-16");
        groomingPetDetail.setStartTime(600L);
        groomingPetDetail.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);
        groomingPetDetail.setServiceType(ServiceType.SERVICE_VALUE);
        groomingPetDetail.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        groomingPetDetail.setServicePrice(new BigDecimal("50.0"));

        var groomingServiceView = com.moego.idl.models.offering.v1.CustomizedServiceView.newBuilder()
                .setName("Grooming Service")
                .build();

        // Mocks
        when(boardingSplitLodgingService.getBoardingSplitLodgings(any())).thenReturn(List.of(splitLodging));
        when(pricingRuleApplyService.getPricingRuleUsingMap(any(), anyList())).thenReturn(Map.of());

        var petDetails = List.of(boardingPetDetail, groomingPetDetail);
        var serviceMap = Map.of(101L, boardingServiceView, 102L, groomingServiceView);
        var serviceTaxMap = Map.<Integer, MoeBusinessTaxDto>of();

        // Act
        List<OrderLineItemModel> result =
                orderRemoteService.buildPetDetailLineItems(appointment, petDetails, serviceMap, serviceTaxMap);

        // Assert
        assertThat(result).hasSize(2);

        OrderLineItemModel boardingItem = result.stream()
                .filter(i -> i.getName().equals("Boarding Service"))
                .findFirst()
                .orElseThrow();
        assertThat(boardingItem.getUnitPrice()).isEqualTo(90.0);
        assertThat(boardingItem.getQuantity()).isEqualTo(2);

        OrderLineItemModel groomingItem = result.stream()
                .filter(i -> i.getName().equals("Grooming Service"))
                .findFirst()
                .orElseThrow();
        assertThat(groomingItem.getUnitPrice()).isEqualTo(50.0);
        assertThat(groomingItem.getQuantity()).isEqualTo(1);
    }
}
