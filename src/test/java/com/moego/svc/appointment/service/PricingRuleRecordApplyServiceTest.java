package com.moego.svc.appointment.service;

import static com.moego.idl.models.appointment.v2.PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT;
import static com.moego.idl.models.appointment.v2.PricingRuleApplySourceType.SOURCE_TYPE_BOOKING_REQUEST;
import static com.moego.svc.appointment.service.PricingRuleRecordApplyService.getServicePrice;
import static com.moego.svc.appointment.service.PricingRuleRecordApplyService.isMatchPetDetailCalculateDef;
import static com.moego.svc.appointment.service.PricingRuleRecordApplyService.isMatchPetDetailCalculateResultDef;
import static com.moego.svc.appointment.service.PricingRuleRecordApplyService.setPriceIfNeedUpdate;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.appointment.v2.PricingRuleApplySourceType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceScopeType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.offering.v2.PetDetailCalculateDef;
import com.moego.idl.models.offering.v2.PetDetailCalculateResultDef;
import com.moego.idl.models.offering.v2.PricingRule;
import com.moego.idl.service.offering.v2.CalculatePricingRuleRequest;
import com.moego.idl.service.offering.v2.CalculatePricingRuleResponse;
import com.moego.idl.service.offering.v2.ListPricingRulesRequest;
import com.moego.idl.service.offering.v2.ListPricingRulesResponse;
import com.moego.idl.service.offering.v2.PricingRuleServiceGrpc;
import com.moego.svc.appointment.domain.BoardingSplitLodging;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.PricingRuleRecordApplyLog;
import com.moego.svc.appointment.dto.PetDetailDTO;
import com.moego.svc.appointment.dto.PricingRuleItemDTO;
import com.moego.svc.appointment.mapper.pg.PricingRuleRecordApplyLogMapper;
import com.moego.svc.appointment.service.remote.MetadataRemoteService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.AbstractMap.SimpleEntry;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.DoubleSummaryStatistics;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;

@ExtendWith(MockitoExtension.class)
class PricingRuleRecordApplyServiceTest {

    @Mock
    private PricingRuleRecordApplyLogMapper pricingRuleRecordApplyLogMapper;

    @Mock
    private PricingRuleServiceGrpc.PricingRuleServiceBlockingStub pricingRuleService;

    @Mock
    private PetDetailServiceProxy petDetailService;

    @Mock
    private MetadataRemoteService metadataRemoteService;

    @Mock
    BoardingSplitLodgingService boardingSplitLodgingService;

    @InjectMocks
    private PricingRuleRecordApplyService pricingRuleApplyService;

    private static final Long APPOINTMENT_ID = 1L;
    private static final Long BOOKING_REQUEST_ID = 2L;
    private static final Long COMPANY_ID = 100L;
    private static final Long BUSINESS_ID = 200L;
    private static final Long PET_ID_1 = 300L;
    private static final Long PET_ID_2 = 400L;
    private static final Long SERVICE_ID = 400L;
    private static final Long SOURCE_ID = 1L;

    @Test
    void applyPricingRule_WithSinglePetDetailDef_Success() {
        // Arrange
        var petDetailDefs = List.of(createPetDetailDef());
        List<MoeGroomingPetDetail> petDetails = createPetDetails();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(petDetailService.getPetDetailList(APPOINTMENT_ID)).thenReturn(petDetails);
        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());

        // Act
        pricingRuleApplyService.applyPricingRule(APPOINTMENT_ID, COMPANY_ID, BUSINESS_ID, petDetailDefs);

        // Assert
        verify(petDetailService).updatePetDetailById(any());
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    @Test
    void applyPricingRule_WithMultiPetDetailDefs_Success() {
        // Arrange
        var petDetailDefs = List.of(createPetDetailDef(), createBoardingPetDetailDef());
        List<MoeGroomingPetDetail> petDetails = createPetDetails();
        List<PetDetailCalculateResultDef> calculateResults = Stream.concat(
                        createCalculateResults().stream(), createCalculateBoardingResults().stream())
                .toList();

        when(petDetailService.getPetDetailList(APPOINTMENT_ID)).thenReturn(petDetails);
        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());
        when(boardingSplitLodgingService.getBoardingSplitLodgings(anyList())).thenReturn(List.of());

        // Act
        pricingRuleApplyService.applyPricingRule(APPOINTMENT_ID, COMPANY_ID, BUSINESS_ID, petDetailDefs);

        // Assert
        verify(petDetailService).updatePetDetailById(any());
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    @Test
    void applyPricingRule_WithoutPetDetailDef_Success() {
        // Arrange
        List<MoeGroomingPetDetail> petDetails = createPetDetails();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(petDetailService.getPetDetailList(APPOINTMENT_ID)).thenReturn(petDetails);
        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());
        when(boardingSplitLodgingService.getBoardingSplitLodgings(anyList())).thenReturn(List.of());

        // Act
        pricingRuleApplyService.applyPricingRule(APPOINTMENT_ID, COMPANY_ID, BUSINESS_ID);

        // Assert
        verify(petDetailService).updatePetDetailById(any());
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    @Test
    void applyPricingRuleByNewService_WithPetDetailDef_Success() {
        // Arrange
        ServiceModel serviceModel = createServiceModel();
        List<MoeGroomingPetDetail> petDetails = createPetDetails();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(petDetailService.getPetDetailList(APPOINTMENT_ID)).thenReturn(petDetails);
        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());

        // Act
        pricingRuleApplyService.applyPricingRuleByNewService(APPOINTMENT_ID, COMPANY_ID, BUSINESS_ID, serviceModel);

        // Assert
        verify(petDetailService).updatePetDetailById(any());
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    @Test
    void applyPricingRuleByNewService_WithoutPetDetailDef_Success() {
        // Arrange
        ServiceModel serviceModel = createServiceModel();
        List<MoeGroomingPetDetail> petDetails = createPetDetails();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(petDetailService.getPetDetailList(APPOINTMENT_ID)).thenReturn(petDetails);
        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());
        when(boardingSplitLodgingService.getBoardingSplitLodgings(anyList())).thenReturn(List.of());

        // Act
        pricingRuleApplyService.applyPricingRuleByNewService(APPOINTMENT_ID, COMPANY_ID, BUSINESS_ID, serviceModel);

        // Assert
        verify(petDetailService).updatePetDetailById(any());
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    @Test
    void applyPricingRule_WithBoardingPetDetailDef_Success() {
        // Arrange
        var petDetailDefs = List.of(createBoardingPetDetailDef());
        List<MoeGroomingPetDetail> petDetails = createBoardingPetDetails();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateBoardingResults();

        when(petDetailService.getPetDetailList(APPOINTMENT_ID)).thenReturn(petDetails);
        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());
        when(boardingSplitLodgingService.getBoardingSplitLodgings(anyList())).thenReturn(List.of());

        // Act
        pricingRuleApplyService.applyPricingRule(APPOINTMENT_ID, COMPANY_ID, BUSINESS_ID, petDetailDefs);

        // Assert
        verify(petDetailService).updatePetDetailById(any());
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    @Test
    void getTotalAmountUsingPricingRule_Success() {
        // Arrange
        List<MoeGroomingPetDetail> petDetails = createPetDetails();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(metadataRemoteService.isAllowBoardingAndDaycare(anyLong())).thenReturn(true);

        // Act
        var result =
                pricingRuleApplyService.getTotalAmountUsingPricingRule(COMPANY_ID, BUSINESS_ID, petDetails, List.of());

        // Assert
        assertTrue(result.isPresent());
        assertEquals(0, BigDecimal.valueOf(190).compareTo(result.get()));
    }

    @Test
    void getTotalAmountUsingPricingRule_TwoDates_Success() {
        // Arrange
        List<MoeGroomingPetDetail> petDetails = createPetDetails_WithTwoDates();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(metadataRemoteService.isAllowBoardingAndDaycare(anyLong())).thenReturn(true);

        // Act
        var result =
                pricingRuleApplyService.getTotalAmountUsingPricingRule(COMPANY_ID, BUSINESS_ID, petDetails, List.of());

        // Assert
        assertTrue(result.isPresent());
        assertEquals(0, BigDecimal.valueOf(390).compareTo(result.get()));
    }

    @Test
    void getTotalAmountUsingPricingRule_EmptyPricingRule() {
        // Arrange
        List<MoeGroomingPetDetail> petDetails = createPetDetails();

        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.getDefaultInstance());
        when(metadataRemoteService.isAllowBoardingAndDaycare(anyLong())).thenReturn(true);

        // Act
        var result =
                pricingRuleApplyService.getTotalAmountUsingPricingRule(COMPANY_ID, BUSINESS_ID, petDetails, List.of());

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void getTotalAmountUsingPricingRule_WithoutPricingRule() {
        // Arrange
        List<MoeGroomingPetDetail> petDetails = createPetDetails();
        List<PetDetailCalculateResultDef> calculateResults = List.of(PetDetailCalculateResultDef.newBuilder()
                .setPetId(PET_ID_1)
                .setServiceId(500)
                .setAdjustedPrice(100.00)
                .addAppliedRuleIds(1L)
                .build());

        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(metadataRemoteService.isAllowBoardingAndDaycare(anyLong())).thenReturn(true);

        // Act
        var result =
                pricingRuleApplyService.getTotalAmountUsingPricingRule(COMPANY_ID, BUSINESS_ID, petDetails, List.of());

        // Assert
        assertTrue(result.isPresent());
        assertEquals(0, BigDecimal.valueOf(200).compareTo(result.get()));
    }

    @Test
    void getUsingRuleApplyLogByAppointmentId_Success() {
        // Arrange
        List<PricingRuleRecordApplyLog> expectedLogs = createPricingRuleRecordApplyLogs();
        when(pricingRuleRecordApplyLogMapper.select(any())).thenReturn(expectedLogs);

        // Act
        List<PricingRuleRecordApplyLog> result =
                pricingRuleApplyService.getUsingRuleApplyLogByAppointmentId(COMPANY_ID, APPOINTMENT_ID);

        // Assert
        assertEquals(expectedLogs.size(), result.size());
        assertEquals(expectedLogs.get(0).getSourceId(), result.get(0).getSourceId());
    }

    @Test
    void getApplyLog_Success() {
        // Arrange
        List<PricingRuleRecordApplyLog> expectedLogs = createPricingRuleRecordApplyLogs();
        when(pricingRuleRecordApplyLogMapper.select(any())).thenReturn(expectedLogs);

        // Act
        List<PricingRuleRecordApplyLog> result =
                pricingRuleApplyService.getApplyLog(COMPANY_ID, SOURCE_ID, SOURCE_TYPE_APPOINTMENT);

        // Assert
        assertEquals(expectedLogs.size(), result.size());
        assertEquals(expectedLogs.get(0).getSourceId(), result.get(0).getSourceId());
    }

    @Test
    void applyPricingRule_EmptyPetDetails_NoAction() {
        // Arrange
        when(petDetailService.getPetDetailList(APPOINTMENT_ID)).thenReturn(new ArrayList<>());
        when(boardingSplitLodgingService.getBoardingSplitLodgings(anyList())).thenReturn(List.of());

        // Act
        pricingRuleApplyService.applyPricingRule(APPOINTMENT_ID, COMPANY_ID, BUSINESS_ID);

        // Assert
        verify(pricingRuleService, never()).calculatePricingRule(any());
        verify(petDetailService, never()).updatePetDetailById(any());
    }

    @Test
    void applyPricingRule_NoPricingRuleResults_UseOriginalPrices() {
        // Arrange
        List<MoeGroomingPetDetail> petDetails = createPetDetails();
        when(petDetailService.getPetDetailList(APPOINTMENT_ID)).thenReturn(petDetails);
        when(pricingRuleService.calculatePricingRule(any()))
                .thenReturn(CalculatePricingRuleResponse.newBuilder().build());
        when(boardingSplitLodgingService.getBoardingSplitLodgings(anyList())).thenReturn(List.of());

        // Act
        pricingRuleApplyService.applyPricingRule(APPOINTMENT_ID, COMPANY_ID, BUSINESS_ID);

        // Assert
        verify(petDetailService, never()).updatePetDetailById(any());
        verify(pricingRuleRecordApplyLogMapper).update(any(UpdateDSLCompleter.class));
    }

    @Test
    void applyPricingRule_NoPricingRuleResults() {
        // Arrange
        List<PetDetailCalculateDef> defs = createPetDetailCalculateDef();
        when(pricingRuleService.calculatePricingRule(any()))
                .thenReturn(CalculatePricingRuleResponse.newBuilder().build());

        // Act
        pricingRuleApplyService.applyPricingRule(SOURCE_ID, SOURCE_TYPE_APPOINTMENT, COMPANY_ID, BUSINESS_ID, defs);

        // Assert
        verify(pricingRuleRecordApplyLogMapper).update(any(UpdateDSLCompleter.class));
    }

    @Test
    void applyPricingRule_WithPricingRuleResults() {
        // Arrange
        List<PetDetailCalculateDef> defs = createPetDetailCalculateDef();
        when(pricingRuleService.calculatePricingRule(any()))
                .thenReturn(CalculatePricingRuleResponse.newBuilder().build());
        List<PetDetailCalculateResultDef> calculateResults = List.of(PetDetailCalculateResultDef.newBuilder()
                .setPetId(PET_ID_1)
                .setServiceId(500)
                .setAdjustedPrice(100.00)
                .addAppliedRuleIds(1L)
                .build());

        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());

        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());

        // Act
        pricingRuleApplyService.applyPricingRule(SOURCE_ID, SOURCE_TYPE_APPOINTMENT, COMPANY_ID, BUSINESS_ID, defs);

        // Assert
        verify(pricingRuleRecordApplyLogMapper).update(any(UpdateDSLCompleter.class));
    }

    @Test
    @DisplayName("Should apply pricing rule with SOURCE_TYPE_APPOINTMENT and update pet details")
    void applyPricingRule_WithAppointmentSourceType_ShouldUpdatePetDetails() {
        // Arrange
        List<PetDetailCalculateDef> petDetailDefs = createPetDetailCalculateDef();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());

        // Act
        List<PetDetailCalculateResultDef> result = pricingRuleApplyService.applyPricingRule(
                SOURCE_ID, SOURCE_TYPE_APPOINTMENT, COMPANY_ID, BUSINESS_ID, petDetailDefs);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getPetId()).isEqualTo(PET_ID_1);
        assertThat(result.get(0).getAdjustedPrice()).isEqualTo(90.00);

        // Verify that pet details are NOT updated for non-appointment source types
        verify(petDetailService, never()).updatePetDetailById(any());

        // Verify pricing rule calculation and logging
        verify(pricingRuleService).calculatePricingRule(any(CalculatePricingRuleRequest.class));
        verify(pricingRuleService).listPricingRules(any(ListPricingRulesRequest.class));
        verify(pricingRuleRecordApplyLogMapper).update(any(UpdateDSLCompleter.class));
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    @Test
    @DisplayName("Should apply pricing rule with SOURCE_TYPE_BOOKING_REQUEST without updating pet details")
    void applyPricingRule_WithBookingRequestSourceType_ShouldNotUpdatePetDetails() {
        // Arrange
        List<PetDetailCalculateDef> petDetailDefs = createPetDetailCalculateDef();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());

        // Act
        List<PetDetailCalculateResultDef> result = pricingRuleApplyService.applyPricingRule(
                SOURCE_ID, SOURCE_TYPE_BOOKING_REQUEST, COMPANY_ID, BUSINESS_ID, petDetailDefs);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getPetId()).isEqualTo(PET_ID_1);
        assertThat(result.get(0).getAdjustedPrice()).isEqualTo(90.00);

        // Verify that pet details are NOT updated for booking request source type
        verify(petDetailService, never()).updatePetDetailById(any());

        // Verify pricing rule calculation and logging still occur
        verify(pricingRuleService).calculatePricingRule(any(CalculatePricingRuleRequest.class));
        verify(pricingRuleService).listPricingRules(any(ListPricingRulesRequest.class));
        verify(pricingRuleRecordApplyLogMapper).update(any(UpdateDSLCompleter.class));
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    @Test
    @DisplayName("Should apply pricing rule with SOURCE_TYPE_ONLINE_BOOKING without updating pet details")
    void applyPricingRule_WithOnlineBookingSourceType_ShouldNotUpdatePetDetails() {
        // Arrange
        List<PetDetailCalculateDef> petDetailDefs = createPetDetailCalculateDef();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());

        // Act
        List<PetDetailCalculateResultDef> result = pricingRuleApplyService.applyPricingRule(
                SOURCE_ID, SOURCE_TYPE_BOOKING_REQUEST, COMPANY_ID, BUSINESS_ID, petDetailDefs);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);

        // Verify that pet details are NOT updated for online booking source type
        verify(petDetailService, never()).updatePetDetailById(any());
        verify(pricingRuleService).calculatePricingRule(any(CalculatePricingRuleRequest.class));
        verify(pricingRuleRecordApplyLogMapper).update(any(UpdateDSLCompleter.class));
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    @Test
    @DisplayName("Should handle appointment source with empty pet detail defs and update pet details")
    void applyPricingRule_WithAppointmentSourceAndEmptyDefs_ShouldFetchAndUpdatePetDetails() {
        // Arrange
        List<MoeGroomingPetDetail> originPetDetails = createPetDetails();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(petDetailService.getPetDetailList(SOURCE_ID)).thenReturn(originPetDetails);
        when(boardingSplitLodgingService.getBoardingSplitLodgings(List.of(SOURCE_ID)))
                .thenReturn(List.of());
        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());

        // Act
        List<PetDetailCalculateResultDef> result = pricingRuleApplyService.applyPricingRule(
                SOURCE_ID, SOURCE_TYPE_APPOINTMENT, COMPANY_ID, BUSINESS_ID, List.of() // Empty pet detail defs
                );

        // Assert
        assertThat(result).isNotNull();

        // Verify that for appointment source, pet details are fetched and updated
        verify(petDetailService).getPetDetailList(SOURCE_ID);
        verify(boardingSplitLodgingService).getBoardingSplitLodgings(List.of(SOURCE_ID));
        verify(petDetailService)
                .updatePetDetailById(argThat(petDetails -> petDetails.size() > 0
                        && petDetails.stream()
                                .allMatch(detail -> detail.getPetId().equals(PET_ID_1.intValue())
                                        || detail.getPetId().equals(PET_ID_2.intValue()))));

        verify(pricingRuleService).calculatePricingRule(any(CalculatePricingRuleRequest.class));
        verify(pricingRuleRecordApplyLogMapper).update(any(UpdateDSLCompleter.class));
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    @Test
    @DisplayName("Should handle booking request source with empty pet detail defs without updating pet details")
    void applyPricingRule_WithBookingRequestSourceAndEmptyDefs_ShouldNotFetchOrUpdatePetDetails() {
        // Act
        List<PetDetailCalculateResultDef> result = pricingRuleApplyService.applyPricingRule(
                SOURCE_ID, SOURCE_TYPE_BOOKING_REQUEST, COMPANY_ID, BUSINESS_ID, List.of() // Empty pet detail defs
                );

        // Assert
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();

        // Verify that for non-appointment source, pet details are NOT fetched or updated
        verify(petDetailService, never()).getPetDetailList(anyList());
        verify(boardingSplitLodgingService, never()).getBoardingSplitLodgings(any());
        verify(petDetailService, never()).updatePetDetailById(any());

        verify(pricingRuleService, never()).calculatePricingRule(any(CalculatePricingRuleRequest.class));
        verify(pricingRuleRecordApplyLogMapper).update(any(UpdateDSLCompleter.class));
        verify(pricingRuleRecordApplyLogMapper, never()).insertMultiple(any());
    }

    @Test
    @DisplayName("Should verify correct source type parameter passed to saveApplyLog")
    void applyPricingRule_ShouldPassCorrectSourceTypeToSaveApplyLog() {
        // Arrange
        List<PetDetailCalculateDef> petDetailDefs = createPetDetailCalculateDef();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());

        // Act
        pricingRuleApplyService.applyPricingRule(
                SOURCE_ID, SOURCE_TYPE_BOOKING_REQUEST, COMPANY_ID, BUSINESS_ID, petDetailDefs);

        // Assert
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(argThat(logs -> logs.stream()
                .allMatch(log -> log.getSourceType().equals(SOURCE_TYPE_BOOKING_REQUEST.getNumber())
                        && log.getSourceId().equals(SOURCE_ID)
                        && log.getCompanyId().equals(COMPANY_ID)
                        && log.getBusinessId().equals(BUSINESS_ID))));
    }

    @Test
    @DisplayName("Should handle multiple source types with different update behaviors")
    void applyPricingRule_WithDifferentSourceTypes_ShouldHandleUpdatesBehaviorCorrectly() {
        // Test data setup
        List<PetDetailCalculateDef> petDetailDefs = createPetDetailCalculateDef();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());

        // Test different source types
        PricingRuleApplySourceType[] nonUpdateSourceTypes = {SOURCE_TYPE_BOOKING_REQUEST, SOURCE_TYPE_APPOINTMENT};

        for (PricingRuleApplySourceType sourceType : nonUpdateSourceTypes) {
            // Reset mocks for each iteration
            reset(petDetailService);

            // Act
            List<PetDetailCalculateResultDef> result = pricingRuleApplyService.applyPricingRule(
                    SOURCE_ID, sourceType, COMPANY_ID, BUSINESS_ID, petDetailDefs);

            // Assert
            assertThat(result).isNotNull();

            // Verify no pet detail updates for non-appointment source types
            verify(petDetailService, never()).updatePetDetailById(any());
            verify(petDetailService, never()).getPetDetailList(anyList());
        }
    }

    @Test
    @DisplayName("Should handle appointment source type with boarding split lodgings and update pet details")
    void applyPricingRule_WithAppointmentSourceAndBoardingSplitLodgings_ShouldUpdatePetDetails() {
        // Arrange
        List<MoeGroomingPetDetail> originPetDetails = createBoardingPetDetails();
        List<BoardingSplitLodging> boardingSplitLodgings = createBoardingSplitLodgings();
        List<PetDetailCalculateResultDef> calculateResults = createCalculateResults();

        when(petDetailService.getPetDetailList(SOURCE_ID)).thenReturn(originPetDetails);
        when(boardingSplitLodgingService.getBoardingSplitLodgings(List.of(SOURCE_ID)))
                .thenReturn(boardingSplitLodgings);
        when(pricingRuleService.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addAllPetDetails(calculateResults)
                        .build());
        when(pricingRuleService.listPricingRules(any(ListPricingRulesRequest.class)))
                .thenReturn(createPricingRuleResponse());

        // Act
        List<PetDetailCalculateResultDef> result = pricingRuleApplyService.applyPricingRule(
                SOURCE_ID, SOURCE_TYPE_APPOINTMENT, COMPANY_ID, BUSINESS_ID, List.of());

        // Assert
        assertThat(result).isNotNull();

        // Verify all appointment-specific operations
        verify(petDetailService).getPetDetailList(SOURCE_ID);
        verify(boardingSplitLodgingService).getBoardingSplitLodgings(List.of(SOURCE_ID));
        verify(petDetailService).updatePetDetailById(any());
        verify(pricingRuleService).calculatePricingRule(any(CalculatePricingRuleRequest.class));
        verify(pricingRuleRecordApplyLogMapper).update(any(UpdateDSLCompleter.class));
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    private List<BoardingSplitLodging> createBoardingSplitLodgings() {
        // Return empty list or create mock boarding split lodgings as needed
        return List.of();
    }

    private MoeGroomingAppointment createAppointment() {
        MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();
        moeGroomingAppointment.setId(APPOINTMENT_ID.intValue());
        moeGroomingAppointment.setServiceTypeInclude(2);
        return moeGroomingAppointment;
    }

    private PetDetailDef createPetDetailDef() {
        return PetDetailDef.newBuilder()
                .setPetId(PET_ID_1)
                .addServices(SelectedServiceDef.newBuilder()
                        .setServiceId(SERVICE_ID)
                        .setServicePrice(150.00)
                        .build())
                .build();
    }

    private PetDetailDef createBoardingPetDetailDef() {
        return PetDetailDef.newBuilder()
                .setPetId(PET_ID_1)
                .addServices(SelectedServiceDef.newBuilder()
                        .setStartDate("2024-01-01")
                        .setEndDate("2024-01-03")
                        .setServiceId(SERVICE_ID)
                        .setServicePrice(150.00)
                        .build())
                .build();
    }

    private ServiceModel createServiceModel() {
        return ServiceModel.newBuilder()
                .setServiceId(SERVICE_ID)
                .setPrice(100.00)
                .build();
    }

    private List<MoeGroomingPetDetail> createPetDetails() {
        MoeGroomingPetDetail detail1 = new MoeGroomingPetDetail();
        detail1.setPetId(PET_ID_1.intValue());
        detail1.setServiceId(SERVICE_ID.intValue());
        detail1.setServiceType(ServiceType.SERVICE_VALUE);
        detail1.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        detail1.setScopeTypePrice(ServiceScopeType.DO_NOT_SAVE_VALUE);
        detail1.setServicePrice(BigDecimal.valueOf(100.00));
        detail1.setStartDate("2024-01-01");
        detail1.setEndDate("2024-01-02");
        detail1.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        detail1.setTotalPrice(BigDecimal.valueOf(100.00));
        MoeGroomingPetDetail detail2 = new MoeGroomingPetDetail();
        detail2.setPetId(PET_ID_2.intValue());
        detail2.setServiceId(SERVICE_ID.intValue());
        detail2.setServiceType(ServiceType.SERVICE_VALUE);
        detail2.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        detail2.setScopeTypePrice(ServiceScopeType.DO_NOT_SAVE_VALUE);
        detail2.setServicePrice(BigDecimal.valueOf(100.00));
        detail2.setStartDate("2024-01-01");
        detail2.setEndDate("2024-01-02");
        detail2.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        detail2.setTotalPrice(BigDecimal.valueOf(100.00));
        return List.of(detail1, detail2);
    }

    private List<MoeGroomingPetDetail> createPetDetails_WithTwoDates() {
        MoeGroomingPetDetail detail1 = new MoeGroomingPetDetail();
        detail1.setPetId(PET_ID_1.intValue());
        detail1.setServiceId(SERVICE_ID.intValue());
        detail1.setServiceType(ServiceType.SERVICE_VALUE);
        detail1.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        detail1.setScopeTypePrice(ServiceScopeType.DO_NOT_SAVE_VALUE);
        detail1.setServicePrice(BigDecimal.valueOf(100.00));
        detail1.setStartDate("2024-01-01");
        detail1.setEndDate("2024-01-03");
        detail1.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        detail1.setTotalPrice(BigDecimal.valueOf(200.00));
        MoeGroomingPetDetail detail2 = new MoeGroomingPetDetail();
        detail2.setPetId(PET_ID_2.intValue());
        detail2.setServiceId(SERVICE_ID.intValue());
        detail2.setServiceType(ServiceType.SERVICE_VALUE);
        detail2.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        detail2.setScopeTypePrice(ServiceScopeType.DO_NOT_SAVE_VALUE);
        detail2.setServicePrice(BigDecimal.valueOf(100.00));
        detail2.setStartDate("2024-01-01");
        detail2.setEndDate("2024-01-03");
        detail2.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        detail2.setTotalPrice(BigDecimal.valueOf(200.00));
        return List.of(detail1, detail2);
    }

    private List<MoeGroomingPetDetail> createBoardingPetDetails() {
        MoeGroomingPetDetail detail1 = new MoeGroomingPetDetail();
        detail1.setPetId(PET_ID_1.intValue());
        detail1.setServiceId(SERVICE_ID.intValue());
        detail1.setServiceType(ServiceType.SERVICE_VALUE);
        detail1.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        detail1.setScopeTypePrice(ServiceScopeType.DO_NOT_SAVE_VALUE);
        detail1.setServicePrice(BigDecimal.valueOf(100.00));
        detail1.setStartDate("2024-01-01");
        detail1.setEndDate("2024-01-03");
        detail1.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        detail1.setTotalPrice(BigDecimal.valueOf(200.00));
        MoeGroomingPetDetail detail2 = new MoeGroomingPetDetail();
        detail2.setPetId(PET_ID_2.intValue());
        detail2.setServiceId(SERVICE_ID.intValue());
        detail2.setServiceType(ServiceType.SERVICE_VALUE);
        detail2.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        detail2.setScopeTypePrice(ServiceScopeType.DO_NOT_SAVE_VALUE);
        detail2.setServicePrice(BigDecimal.valueOf(100.00));
        detail2.setStartDate("2024-01-01");
        detail2.setEndDate("2024-01-03");
        detail2.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        detail2.setTotalPrice(BigDecimal.valueOf(200.00));
        return List.of(detail1, detail2);
    }

    private List<PetDetailDTO> createPetDetailDTOs() {
        MoeGroomingPetDetail detail1 = new MoeGroomingPetDetail();
        detail1.setPetId(PET_ID_1.intValue());
        detail1.setServiceId(SERVICE_ID.intValue());
        detail1.setServiceType(ServiceType.SERVICE_VALUE);
        detail1.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        detail1.setScopeTypePrice(ServiceScopeType.DO_NOT_SAVE_VALUE);
        detail1.setServicePrice(BigDecimal.valueOf(100.00));
        detail1.setStartDate("2024-01-01");
        detail1.setEndDate("2024-01-03");
        detail1.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        detail1.setTotalPrice(BigDecimal.valueOf(200.00));
        MoeGroomingPetDetail detail2 = new MoeGroomingPetDetail();
        detail2.setPetId(PET_ID_2.intValue());
        detail2.setServiceId(SERVICE_ID.intValue());
        detail2.setServiceType(ServiceType.SERVICE_VALUE);
        detail2.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        detail2.setScopeTypePrice(ServiceScopeType.DO_NOT_SAVE_VALUE);
        detail2.setServicePrice(BigDecimal.valueOf(100.00));
        detail2.setStartDate("2024-01-01");
        detail2.setEndDate("2024-01-03");
        detail2.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        detail2.setTotalPrice(BigDecimal.valueOf(200.00));

        PetDetailDTO dto1 = new PetDetailDTO();
        dto1.setPetId(PET_ID_1);
        dto1.setPetDetail(detail1);
        PetDetailDTO dto2 = new PetDetailDTO();
        dto2.setPetId(PET_ID_2);
        dto2.setPetDetail(detail2);

        return List.of(dto1, dto2);
    }

    private List<PetDetailCalculateResultDef> createCalculateResults() {
        return List.of(PetDetailCalculateResultDef.newBuilder()
                .setPetId(PET_ID_1)
                .setServiceId(SERVICE_ID)
                .setAdjustedPrice(90.00)
                .setServiceDate("2024-01-01")
                .addAppliedRuleIds(1L)
                .build());
    }

    private List<PetDetailCalculateResultDef> createCalculateBoardingResults() {
        return List.of(PetDetailCalculateResultDef.newBuilder()
                .setPetId(PET_ID_1)
                .setServiceId(SERVICE_ID)
                .setAdjustedPrice(90.00)
                .setServiceDate("2024-01-01")
                .addAppliedRuleIds(1L)
                .build());
    }

    private ListPricingRulesResponse createPricingRuleResponse() {
        return ListPricingRulesResponse.newBuilder()
                .addPricingRules(PricingRule.newBuilder().setId(1L).build())
                .build();
    }

    private List<PricingRuleRecordApplyLog> createPricingRuleRecordApplyLogs() {
        PricingRuleRecordApplyLog log = new PricingRuleRecordApplyLog();
        log.setSourceId(APPOINTMENT_ID);
        log.setCompanyId(COMPANY_ID);
        log.setBusinessId(BUSINESS_ID);
        log.setPetId(PET_ID_1);
        log.setServiceId(SERVICE_ID);
        log.setOriginalPrice(BigDecimal.valueOf(100.00));
        log.setAdjustedPrice(BigDecimal.valueOf(90.00));
        log.setServiceDate("2024-01-01");
        log.setCreatedAt(new Date());
        log.setIsUsingRule(true);
        return List.of(log);
    }

    private List<PetDetailCalculateDef> createPetDetailCalculateDef() {
        PetDetailCalculateDef def = PetDetailCalculateDef.newBuilder()
                .setPetId(PET_ID_1)
                .setServiceId(SERVICE_ID)
                .setServicePrice(100.00)
                .setServiceDate("2024-01-01")
                .build();
        return List.of(def);
    }

    @Test
    void testGetPricingRuleUsingMap_PricingRuleApplicable() {
        List<PricingRuleRecordApplyLog> expectedLogs = createPricingRuleRecordApplyLogs();
        when(pricingRuleRecordApplyLogMapper.select(any())).thenReturn(expectedLogs);

        Map<SimpleEntry<Long, Long>, List<PricingRuleItemDTO>> result =
                pricingRuleApplyService.getPricingRuleUsingMap(createAppointment(), createPetDetails());
        assertEquals(1, result.size());

        Map<SimpleEntry<Long, Long>, List<PricingRuleItemDTO>> expectedMap = Map.of(
                new SimpleEntry<>(PET_ID_1, SERVICE_ID),
                List.of(new PricingRuleItemDTO(BigDecimal.valueOf(90.0), 1, List.of("2024-01-01"))));

        for (Map.Entry<SimpleEntry<Long, Long>, List<PricingRuleItemDTO>> entry : expectedMap.entrySet()) {
            assertTrue(result.containsKey(entry.getKey()));
            assertEquals(entry.getValue().size(), result.get(entry.getKey()).size());
            for (int i = 0; i < entry.getValue().size(); i++) {
                assertEquals(
                        entry.getValue().get(i).servicePrice(),
                        result.get(entry.getKey()).get(i).servicePrice());
                assertEquals(
                        entry.getValue().get(i).quantity(),
                        result.get(entry.getKey()).get(i).quantity());
                assertEquals(
                        entry.getValue().get(i).serviceDates(),
                        result.get(entry.getKey()).get(i).serviceDates());
            }
        }
    }

    @Test
    void testCopyApplyLog() {
        // Arrange
        List<PricingRuleRecordApplyLog> expectedLogs = createPricingRuleRecordApplyLogs();
        when(pricingRuleRecordApplyLogMapper.select(any())).thenReturn(expectedLogs);

        // Act
        pricingRuleApplyService.copyApplyLog(
                COMPANY_ID,
                BUSINESS_ID,
                BOOKING_REQUEST_ID,
                SOURCE_TYPE_BOOKING_REQUEST,
                APPOINTMENT_ID,
                SOURCE_TYPE_APPOINTMENT,
                "2025-05-20",
                "2025-05-20");

        // Assert
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(anyList());
    }

    @Test
    @DisplayName("正常情况：成功拷贝日期范围内的记录")
    void copyApplyLog_shouldCopyLogsWithinDateRange() {
        // Given
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";
        List<PricingRuleRecordApplyLog> mockLogs = createMockLogs();

        when(pricingRuleRecordApplyLogMapper.select(any())).thenReturn(mockLogs);

        // When
        pricingRuleApplyService.copyApplyLog(
                1L, 1L, 100L, SOURCE_TYPE_BOOKING_REQUEST, 200L, SOURCE_TYPE_APPOINTMENT, startDate, endDate);

        // Then
        verify(pricingRuleRecordApplyLogMapper).select(any());
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(argThat(logs -> logs.stream()
                .allMatch(log -> log.getSourceId().equals(200L)
                        && log.getSourceType().equals(SOURCE_TYPE_APPOINTMENT.getNumber()))));
    }

    @Test
    @DisplayName("边界情况：查询结果为空时不执行插入")
    void copyApplyLog_shouldNotInsertWhenNoLogsFound() {
        // Given
        when(pricingRuleRecordApplyLogMapper.select(any())).thenReturn(Collections.emptyList());

        // When
        pricingRuleApplyService.copyApplyLog(
                1L, 1L, 100L, SOURCE_TYPE_BOOKING_REQUEST, 200L, SOURCE_TYPE_APPOINTMENT, "2024-01-01", "2024-01-31");

        // Then
        verify(pricingRuleRecordApplyLogMapper).select(any());
        verify(pricingRuleRecordApplyLogMapper, never()).insertMultiple(any());
    }

    @Test
    @DisplayName("边界情况：开始日期等于结束日期")
    void copyApplyLog_shouldWorkWithSameDateRange() {
        // Given
        String sameDate = "2024-01-15";
        List<PricingRuleRecordApplyLog> mockLogs = createMockLogs();

        when(pricingRuleRecordApplyLogMapper.select(any())).thenReturn(mockLogs);

        // When
        pricingRuleApplyService.copyApplyLog(
                1L, 1L, 100L, SOURCE_TYPE_BOOKING_REQUEST, 200L, SOURCE_TYPE_APPOINTMENT, sameDate, sameDate);

        // Then
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    @Test
    @DisplayName("字符串日期格式：测试不同日期格式")
    void copyApplyLog_shouldHandleDifferentDateFormats() {
        // Given - 测试各种可能的日期格式
        String startDate = "2024-01-01";
        String endDate = "2024-12-31";
        List<PricingRuleRecordApplyLog> mockLogs = createMockLogsWithDifferentDates();

        when(pricingRuleRecordApplyLogMapper.select(any())).thenReturn(mockLogs);

        // When
        pricingRuleApplyService.copyApplyLog(
                1L, 1L, 100L, SOURCE_TYPE_BOOKING_REQUEST, 200L, SOURCE_TYPE_APPOINTMENT, startDate, endDate);

        // Then
        verify(pricingRuleRecordApplyLogMapper).insertMultiple(any());
    }

    @Test
    @DisplayName("空值处理：测试日期参数为null或空字符串")
    void copyApplyLog_shouldHandleNullOrEmptyDates() {
        // Given
        List<PricingRuleRecordApplyLog> mockLogs = createMockLogs();
        when(pricingRuleRecordApplyLogMapper.select(any())).thenReturn(mockLogs);

        // When & Then - 测试各种空值情况
        assertDoesNotThrow(() -> pricingRuleApplyService.copyApplyLog(
                1L, 1L, 100L, SOURCE_TYPE_BOOKING_REQUEST, 200L, SOURCE_TYPE_APPOINTMENT, null, null));

        assertDoesNotThrow(() -> pricingRuleApplyService.copyApplyLog(
                1L, 1L, 100L, SOURCE_TYPE_BOOKING_REQUEST, 200L, SOURCE_TYPE_APPOINTMENT, "", ""));
    }

    private List<PricingRuleRecordApplyLog> createMockLogs() {
        PricingRuleRecordApplyLog log1 = new PricingRuleRecordApplyLog();
        log1.setId(1L);
        log1.setSourceId(100L);
        log1.setSourceType(SOURCE_TYPE_BOOKING_REQUEST.getNumber());
        log1.setServiceDate("2024-01-15"); // String类型的日期

        PricingRuleRecordApplyLog log2 = new PricingRuleRecordApplyLog();
        log2.setId(2L);
        log2.setSourceId(100L);
        log2.setSourceType(SOURCE_TYPE_BOOKING_REQUEST.getNumber());
        log2.setServiceDate("2024-01-20"); // String类型的日期

        return Arrays.asList(log1, log2);
    }

    private List<PricingRuleRecordApplyLog> createMockLogsWithDifferentDates() {
        PricingRuleRecordApplyLog log1 = new PricingRuleRecordApplyLog();
        log1.setId(1L);
        log1.setServiceDate("2024-01-01"); // 边界日期

        PricingRuleRecordApplyLog log2 = new PricingRuleRecordApplyLog();
        log2.setId(2L);
        log2.setServiceDate("2024-12-31"); // 边界日期

        PricingRuleRecordApplyLog log3 = new PricingRuleRecordApplyLog();
        log3.setId(3L);
        log3.setServiceDate("2024-06-15"); // 中间日期

        return Arrays.asList(log1, log2, log3);
    }

    @Test
    void testRemoveApplyLog() {
        // Act
        pricingRuleApplyService.removeApplyLog(COMPANY_ID, BUSINESS_ID, APPOINTMENT_ID, SOURCE_TYPE_APPOINTMENT);

        // Assert
        verify(pricingRuleRecordApplyLogMapper).update(any(UpdateDSLCompleter.class));
    }

    @Test
    void testUpdatePetDetailByOnlineBooking() {
        // Arrange
        List<PetDetailDTO> petDetailDTOs = createPetDetailDTOs();
        List<PricingRuleRecordApplyLog> expectedLogs = createPricingRuleRecordApplyLogs();
        when(pricingRuleRecordApplyLogMapper.select(any())).thenReturn(expectedLogs);

        // Act
        List<PetDetailDTO> result = pricingRuleApplyService.updatePetDetailByOnlineBooking(
                COMPANY_ID, SOURCE_ID, SOURCE_TYPE_BOOKING_REQUEST, petDetailDTOs);

        // Assert
        Assertions.assertEquals(petDetailDTOs.size(), result.size());
    }

    @Test
    @DisplayName("当结果列表中有匹配的服务日期时，getServicePrice应返回调整后的价格")
    void getServicePrice_WithMatchingServiceDate_ReturnsAdjustedPrice() {
        // Arrange
        PetDetailCalculateDef def = PetDetailCalculateDef.newBuilder()
                .setServiceDate("2025-05-20")
                .setServicePrice(100.0)
                .build();

        PetDetailCalculateResultDef result1 = PetDetailCalculateResultDef.newBuilder()
                .setServiceDate("2025-05-19")
                .setAdjustedPrice(150.0)
                .build();

        PetDetailCalculateResultDef result2 = PetDetailCalculateResultDef.newBuilder()
                .setServiceDate("2025-05-20")
                .setAdjustedPrice(200.0)
                .build();

        List<PetDetailCalculateResultDef> results = Arrays.asList(result1, result2);

        // Act
        double price = getServicePrice(def, results);

        // Assert
        assertThat(price).isEqualTo(200.0);
    }

    @Test
    @DisplayName("当定义没有服务日期时，getServicePrice应返回第一个结果的调整价格")
    void getServicePrice_WithoutServiceDate_ReturnsFirstResultAdjustedPrice() {
        // Arrange
        PetDetailCalculateDef def =
                PetDetailCalculateDef.newBuilder().setServicePrice(100.0).build();

        PetDetailCalculateResultDef result1 = PetDetailCalculateResultDef.newBuilder()
                .setServiceDate("2025-05-19")
                .setAdjustedPrice(150.0)
                .build();

        PetDetailCalculateResultDef result2 = PetDetailCalculateResultDef.newBuilder()
                .setServiceDate("2025-05-20")
                .setAdjustedPrice(200.0)
                .build();

        List<PetDetailCalculateResultDef> results = Arrays.asList(result1, result2);

        // Act
        double price = getServicePrice(def, results);

        // Assert
        assertThat(price).isEqualTo(150.0);
    }

    @Test
    @DisplayName("当结果列表为空时，getServicePrice应返回定义中的服务价格")
    void getServicePrice_WithEmptyResults_ReturnsDefinitionServicePrice() {
        // Arrange
        PetDetailCalculateDef def = PetDetailCalculateDef.newBuilder()
                .setServiceDate("2025-05-20")
                .setServicePrice(100.0)
                .build();

        List<PetDetailCalculateResultDef> results = Collections.emptyList();

        // Act
        double price = getServicePrice(def, results);

        // Assert
        assertThat(price).isEqualTo(100.0);
    }

    @Test
    @DisplayName("当价格需要更新时，setPriceIfNeedUpdate应返回true并更新价格")
    void setPriceIfNeedUpdate_WhenPriceNeedsUpdate_ReturnsTrue() {
        // Arrange
        DoubleSummaryStatistics statistics = new DoubleSummaryStatistics();
        statistics.accept(50.0);
        statistics.accept(70.0);

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setServicePrice(new BigDecimal("55.00"));
        petDetail.setTotalPrice(new BigDecimal("110.00"));

        // Act
        boolean needUpdate = setPriceIfNeedUpdate(statistics, petDetail);

        // Assert
        assertThat(needUpdate).isTrue();
        assertThat(petDetail.getServicePrice()).isEqualTo(new BigDecimal("60.00").setScale(2, RoundingMode.HALF_EVEN));
        assertThat(petDetail.getTotalPrice()).isEqualTo(new BigDecimal("120.00").setScale(2, RoundingMode.HALF_EVEN));
    }

    @Test
    @DisplayName("当价格不需要更新时，setPriceIfNeedUpdate应返回false")
    void setPriceIfNeedUpdate_WhenPriceDoesNotNeedUpdate_ReturnsFalse() {
        // Arrange
        DoubleSummaryStatistics statistics = new DoubleSummaryStatistics();
        statistics.accept(60.0);
        statistics.accept(60.0);

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setServicePrice(new BigDecimal("60.00"));
        petDetail.setTotalPrice(new BigDecimal("120.00"));

        // Act
        boolean needUpdate = setPriceIfNeedUpdate(statistics, petDetail);

        // Assert
        assertThat(needUpdate).isFalse();
        assertThat(petDetail.getServicePrice()).isEqualTo(new BigDecimal("60.00").setScale(2, RoundingMode.HALF_EVEN));
        assertThat(petDetail.getTotalPrice()).isEqualTo(new BigDecimal("120.00").setScale(2, RoundingMode.HALF_EVEN));
    }

    @Test
    @DisplayName("当只有服务价格需要更新时，setPriceIfNeedUpdate应返回true")
    void setPriceIfNeedUpdate_WhenOnlyServicePriceNeedsUpdate_ReturnsTrue() {
        // Arrange
        DoubleSummaryStatistics statistics = new DoubleSummaryStatistics();
        statistics.accept(50.0);
        statistics.accept(70.0);

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setServicePrice(new BigDecimal("55.00")); // 不等于统计平均值60.00
        petDetail.setTotalPrice(new BigDecimal("120.00")); // 等于统计总和120.00

        // Act
        boolean needUpdate = setPriceIfNeedUpdate(statistics, petDetail);

        // Assert
        assertThat(needUpdate).isTrue();
        assertThat(petDetail.getServicePrice()).isEqualTo(new BigDecimal("60.00").setScale(2, RoundingMode.HALF_EVEN));
        assertThat(petDetail.getTotalPrice()).isEqualTo(new BigDecimal("120.00").setScale(2, RoundingMode.HALF_EVEN));
    }

    @Test
    @DisplayName("当只有总价需要更新时，setPriceIfNeedUpdate应返回true")
    void setPriceIfNeedUpdate_WhenOnlyTotalPriceNeedsUpdate_ReturnsTrue() {
        // Arrange
        DoubleSummaryStatistics statistics = new DoubleSummaryStatistics();
        statistics.accept(50.0);
        statistics.accept(70.0);

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setServicePrice(new BigDecimal("60.00")); // 等于统计平均值60.00
        petDetail.setTotalPrice(new BigDecimal("110.00")); // 不等于统计总和120.00

        // Act
        boolean needUpdate = setPriceIfNeedUpdate(statistics, petDetail);

        // Assert
        assertThat(needUpdate).isTrue();
        assertThat(petDetail.getServicePrice()).isEqualTo(new BigDecimal("60.00").setScale(2, RoundingMode.HALF_EVEN));
        assertThat(petDetail.getTotalPrice()).isEqualTo(new BigDecimal("120.00").setScale(2, RoundingMode.HALF_EVEN));
    }

    @Test
    @DisplayName("当宠物ID和服务ID匹配时，isMatchPetDetailCalculateDef应返回true")
    void isMatchPetDetailCalculateDef_WhenPetIdAndServiceIdMatch_ReturnsTrue() {
        // Arrange
        PetDetailCalculateDef def =
                PetDetailCalculateDef.newBuilder().setPetId(1L).setServiceId(2L).build();

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setPetId(1);
        petDetail.setServiceId(2);

        // Act
        boolean isMatch = isMatchPetDetailCalculateDef(def, petDetail);

        // Assert
        assertThat(isMatch).isTrue();
    }

    @Test
    @DisplayName("当宠物ID或服务ID不匹配时，isMatchPetDetailCalculateDef应返回false")
    void isMatchPetDetailCalculateDef_WhenPetIdOrServiceIdDoNotMatch_ReturnsFalse() {
        // Arrange
        PetDetailCalculateDef def =
                PetDetailCalculateDef.newBuilder().setPetId(1L).setServiceId(2L).build();

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setPetId(1);
        petDetail.setServiceId(3);

        // Act
        boolean isMatch = isMatchPetDetailCalculateDef(def, petDetail);

        // Assert
        assertThat(isMatch).isFalse();
    }

    @Test
    @DisplayName("当宠物ID和服务ID匹配时，isMatchPetDetailCalculateResultDef应返回true")
    void isMatchPetDetailCalculateResultDef_WhenPetIdAndServiceIdMatch_ReturnsTrue() {
        // Arrange
        PetDetailCalculateResultDef resultDef = PetDetailCalculateResultDef.newBuilder()
                .setPetId(1L)
                .setServiceId(2L)
                .build();

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setPetId(1);
        petDetail.setServiceId(2);

        // Act
        boolean isMatch = isMatchPetDetailCalculateResultDef(resultDef, petDetail);

        // Assert
        assertThat(isMatch).isTrue();
    }

    @Test
    @DisplayName("当宠物ID或服务ID不匹配时，isMatchPetDetailCalculateResultDef应返回false")
    void isMatchPetDetailCalculateResultDef_WhenPetIdOrServiceIdDoNotMatch_ReturnsFalse() {
        // Arrange
        PetDetailCalculateResultDef resultDef = PetDetailCalculateResultDef.newBuilder()
                .setPetId(1L)
                .setServiceId(2L)
                .build();

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setPetId(3);
        petDetail.setServiceId(2);

        // Act
        boolean isMatch = isMatchPetDetailCalculateResultDef(resultDef, petDetail);

        // Assert
        assertThat(isMatch).isFalse();
    }
}
