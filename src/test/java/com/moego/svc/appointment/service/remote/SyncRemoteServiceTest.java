package com.moego.svc.appointment.service.remote;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.moego.server.grooming.client.IGoogleCalendarClient;
import com.moego.server.grooming.client.IQuickBooksClient;
import com.moego.server.grooming.params.SyncAppointmentParams;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SyncRemoteServiceTest {

    @Mock
    private IGoogleCalendarClient googleCalendarClient;

    @Mock
    private IQuickBooksClient quickBooksClient;

    @InjectMocks
    private SyncRemoteService syncRemoteService;

    @Test
    void syncToGoogleCalendar_shouldCallGoogleCalendarClient() {
        // Arrange
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setBusinessId(100);
        appointment.setAppointmentDate("2024-02-20");

        // Act
        syncRemoteService.syncToGoogleCalendar(appointment);

        // Assert
        verify(googleCalendarClient)
                .checkBusinessHaveGoogleCalendarSync(
                        argThat(params -> params.getBusinessId().equals(appointment.getBusinessId())
                                && params.getAppointmentId().equals(appointment.getId())
                                && params.getAppointmentDate().equals(appointment.getAppointmentDate())
                                && params.getIsDelay()));
        verify(googleCalendarClient, times(1)).checkBusinessHaveGoogleCalendarSync(any(SyncAppointmentParams.class));
    }

    @Test
    void syncToQuickBook_shouldCallQuickBooksClient() {
        // Arrange
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setBusinessId(100);
        appointment.setAppointmentDate("2024-02-20");

        // Act
        syncRemoteService.syncToQuickBook(appointment);

        // Assert
        verify(quickBooksClient)
                .addRedisSyncGroomingData(
                        argThat(params -> params.getBusinessId().equals(appointment.getBusinessId())
                                && params.getAppointmentId().equals(appointment.getId())
                                && params.getAppointmentDate().equals(appointment.getAppointmentDate())));
        verify(quickBooksClient, times(1)).addRedisSyncGroomingData(any());
    }
}
