package com.moego.svc.appointment.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class EvaluationServiceDetailServiceTest {

    @InjectMocks
    EvaluationServiceDetailService service;

    @Test
    void isBelongsToAppointment_withEmptyPetEvaluationIds_returnsTrue() {
        boolean result = service.isBelongsToAppointment(1L, List.of());
        assertThat(result).isTrue();
    }

    @Test
    void isBelongsToAppointment_withNonExistingPetEvaluationIds_returnsFalse() {

        var spyService = spy(service);
        doReturn(List.of(
                        new EvaluationServiceDetail() {
                            {
                                setId(1L);
                            }
                        },
                        new EvaluationServiceDetail() {
                            {
                                setId(2L);
                            }
                        }))
                .when(spyService)
                .getPetEvaluationList(anyLong());

        boolean result = spyService.isBelongsToAppointment(1L, List.of(3L));
        assertThat(result).isFalse();
    }

    @Test
    void isBelongsToAppointment_withExistingPetEvaluationIds_returnsTrue() {

        var spyService = spy(service);
        doReturn(List.of(
                        new EvaluationServiceDetail() {
                            {
                                setId(1L);
                            }
                        },
                        new EvaluationServiceDetail() {
                            {
                                setId(2L);
                            }
                        }))
                .when(spyService)
                .getPetEvaluationList(anyLong());

        boolean result = spyService.isBelongsToAppointment(1L, List.of(1L, 2L));
        assertThat(result).isTrue();
    }

    @Test
    void isBelongsToAppointment_withPartialExistingPetEvaluationIds_returnsFalse() {
        var spyService = spy(service);
        doReturn(List.of(
                        new EvaluationServiceDetail() {
                            {
                                setId(1L);
                            }
                        },
                        new EvaluationServiceDetail() {
                            {
                                setId(2L);
                            }
                        }))
                .when(spyService)
                .getPetEvaluationList(anyLong());

        boolean result = spyService.isBelongsToAppointment(1L, List.of(1L, 3L));
        assertThat(result).isFalse();
    }
}
