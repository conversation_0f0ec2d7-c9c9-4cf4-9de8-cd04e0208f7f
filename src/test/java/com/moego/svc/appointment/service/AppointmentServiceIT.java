package com.moego.svc.appointment.service;

import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.id;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.moeGroomingAppointment;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.status;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mybatis.dynamic.sql.SqlBuilder.and;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

import com.moego.idl.models.appointment.v1.AppointmentStatus;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.DisabledIfEnvironmentVariable;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mybatis.dynamic.sql.exception.InvalidSqlException;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.transaction.annotation.Transactional;

/**
 * {@link AppointmentService}
 */
@SpringBootTest
@DisabledIfEnvironmentVariable(named = "CI", matches = "true")
@ExtendWith(OutputCaptureExtension.class)
class AppointmentServiceIT {

    @Autowired
    AppointmentServiceProxy appointmentService;

    /**
     * {@link AppointmentService#countAppointmentForPets(Collection)}
     */
    @Test
    void testCountAppointmentForPets(CapturedOutput output) {
        var notExistPetId = -111111111L;

        var actual = appointmentService.countAppointmentForPets(List.of(notExistPetId));
        var expected = Map.of(notExistPetId, 0);
        assertThat(actual).isEqualTo(expected);

        assertThat(output)
                .containsIgnoringWhitespaces(
                        """
                    select moe_grooming_pet_detail.pet_id, count(distinct moe_grooming_pet_detail.grooming_id) as cnt
                    from moe_grooming_pet_detail join moe_grooming_appointment on moe_grooming_pet_detail.grooming_id = moe_grooming_appointment.id
                    where moe_grooming_pet_detail.pet_id in (?)
                      and moe_grooming_pet_detail.status = ?
                      and moe_grooming_appointment.status in (?,?,?,?,?)
                      and moe_grooming_appointment.is_deprecate = ?
                    group by moe_grooming_pet_detail.pet_id
                    """);
    }

    /**
     * {@link AppointmentService#update(List, UpdateDSL, Long)}
     *
     * @see <a href="https://moegoworkspace.slack.com/archives/C035E7FV21X/p1742904850206189">Slack</a>
     */
    @Test
    @Transactional
    void testUpdate_whenReuseUpdateDSL() {

        // 复用 UpdateDSL where 查询条件不会清空，会报错：You cannot specify more than one "where" clause in a statement
        var cond = List.of(and(id, isIn(1)), and(status, isIn((byte) AppointmentStatus.UNCONFIRMED_VALUE)));

        var dsl = newUpdateDSL();

        assertThatCode(() -> appointmentService.update(cond, dsl, 1L)).doesNotThrowAnyException(); // dsl 使用一次
        assertThatCode(() -> appointmentService.update(cond, dsl, 1L)) // dsl 重复使用，boom！
                .isInstanceOf(InvalidSqlException.class)
                .hasMessageContaining("You cannot specify more than one \"where\" clause in a statement");

        // 修复方式：一个 UpdateDSL 对象只能使用一次
        assertThatCode(() -> appointmentService.update(cond, newUpdateDSL(), 1L))
                .doesNotThrowAnyException();
        assertThatCode(() -> appointmentService.update(cond, newUpdateDSL(), 1L))
                .doesNotThrowAnyException();
    }

    private static UpdateDSL<UpdateModel> newUpdateDSL() {
        return UpdateDSL.update(moeGroomingAppointment).set(status).equalTo((byte) AppointmentStatus.CHECKED_IN_VALUE);
    }
}
