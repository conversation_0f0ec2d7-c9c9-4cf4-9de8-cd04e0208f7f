package com.moego.svc.appointment.utils;

import static com.moego.svc.appointment.utils.ServiceChargeUtil.calculateApplyAmount;
import static com.moego.svc.appointment.utils.ServiceChargeUtil.calculateExceed24hTime;
import static com.moego.svc.appointment.utils.ServiceChargeUtil.calculatePriceByMultiplePets;
import static com.moego.svc.appointment.utils.ServiceChargeUtil.getApplyCount;
import static com.moego.svc.appointment.utils.ServiceChargeUtil.getApplyCountByFeedingMedication;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.AppointmentPetFeedingScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import com.moego.idl.models.business_customer.v1.FeedingMedicationScheduleDateType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.order.v1.ChargeMethod;
import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.idl.models.order.v1.ServiceChargeExceedHourRule;
import com.moego.idl.models.order.v1.SurchargeType;
import com.moego.idl.models.organization.v1.TimeZone;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class ServiceChargeUtilTest {
    @Test
    public void testGetApplyCount_NoMatchingServiceItemType() {
        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setApplyType(ServiceCharge.ApplyType.PER_APPOINTMENT)
                .setAutoApplyStatus(ServiceCharge.AutoApplyStatus.AUTO_APPLY_ENABLED)
                .addAllServiceItemTypes(Collections.singletonList(ServiceItemType.GROOMING))
                .build();

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);

        long count = getApplyCount(Collections.singletonList(petDetail), serviceCharge);

        // 验证结果 - 由于ServiceItemType不匹配，应该返回0
        assertEquals(0, count);
    }

    @Test
    public void testGetApplyCount_PerAppointment() {
        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setApplyType(ServiceCharge.ApplyType.PER_APPOINTMENT)
                .setAutoApplyStatus(ServiceCharge.AutoApplyStatus.AUTO_APPLY_ENABLED)
                .addAllServiceItemTypes(Collections.singletonList(ServiceItemType.GROOMING))
                .build();

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);

        var excepted = 1;

        long count = getApplyCount(Collections.singletonList(petDetail), serviceCharge);

        assertEquals(excepted, count);
    }

    @Test
    public void testGetApplyCount_PerPet() {
        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setApplyType(ServiceCharge.ApplyType.PER_PET)
                .setAutoApplyStatus(ServiceCharge.AutoApplyStatus.AUTO_APPLY_ENABLED)
                .addAllServiceItemTypes(List.of(ServiceItemType.GROOMING, ServiceItemType.DAYCARE))
                .build();

        MoeGroomingPetDetail petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setPetId(1);
        petDetail1.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail1.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);

        MoeGroomingPetDetail petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setPetId(2);
        petDetail2.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail2.setServiceItemType((byte) ServiceItemType.DAYCARE_VALUE);

        MoeGroomingPetDetail petDetail3 = new MoeGroomingPetDetail();
        petDetail3.setPetId(3);
        petDetail3.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail3.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);

        MoeGroomingPetDetail petDetail4 = new MoeGroomingPetDetail();
        petDetail4.setPetId(1);
        petDetail4.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail4.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);

        var excepted = 2;

        long count = getApplyCount(Arrays.asList(petDetail1, petDetail2, petDetail3, petDetail4), serviceCharge);

        assertEquals(excepted, count);
    }

    @Test
    public void testGetApplyCount_PerPricingUnit_AllUnits() {
        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setApplyType(ServiceCharge.ApplyType.PER_PRICING_UNIT)
                .setAutoApplyStatus(ServiceCharge.AutoApplyStatus.AUTO_APPLY_ENABLED)
                .addAllServiceItemTypes(
                        Arrays.asList(ServiceItemType.BOARDING, ServiceItemType.DAYCARE, ServiceItemType.GROOMING))
                .build();

        // 构建不同价格单位的PetDetail
        // price unit, 1 - per session, 2 - per night, 3 - per hour, 4 - per day
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setPetId(1);
        petDetail1.setServiceId(1);
        petDetail1.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        petDetail1.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail1.setPriceUnit(2);
        petDetail1.setStartDate("2024-03-20");
        petDetail1.setEndDate("2024-03-22");

        // PER_DAY - 3天 (3.20-3.22)
        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setPetId(2);
        petDetail2.setServiceId(2);
        petDetail2.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        petDetail2.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail2.setPriceUnit(4);
        petDetail2.setStartDate("2024-03-20");
        petDetail2.setEndDate("2024-03-22");

        // PER_HOUR - 2.5 * 3 = 7.5 -> 8小时
        var petDetail3 = new MoeGroomingPetDetail();
        petDetail3.setPetId(1);
        petDetail3.setServiceId(3);
        petDetail3.setServiceItemType((byte) ServiceItemType.DAYCARE_VALUE);
        petDetail3.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail3.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY_VALUE);
        petDetail3.setPriceUnit(3);
        petDetail3.setStartTime(540L);
        petDetail3.setEndTime(690L);

        // PER_SESSION - 返回2
        var petDetail4 = new MoeGroomingPetDetail();
        petDetail4.setPetId(2);
        petDetail4.setServiceId(4);
        petDetail4.setServiceItemType((byte) ServiceItemType.DAYCARE_VALUE);
        petDetail4.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail4.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);
        petDetail4.setPriceUnit(1);
        petDetail4.setSpecificDates("[\"2025-03-20\", \"2025-03-21\"]");
        petDetail4.setStartTime(540L);
        petDetail4.setEndTime(690L);

        // PER_SESSION - 返回1
        var petDetail5 = new MoeGroomingPetDetail();
        petDetail5.setPetId(1);
        petDetail5.setServiceId(5);
        petDetail5.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);
        petDetail5.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail5.setPriceUnit(1);
        petDetail5.setStartDate("2024-03-20");
        petDetail5.setEndDate("2024-03-20");

        // PER_SESSION - 返回1
        var petDetail6 = new MoeGroomingPetDetail();
        petDetail6.setPetId(2);
        petDetail6.setServiceId(5);
        petDetail6.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);
        petDetail6.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail6.setPriceUnit(1);
        petDetail6.setStartDate("2024-03-20");
        petDetail6.setEndDate("2024-03-20");

        List<MoeGroomingPetDetail> petDetails =
                Arrays.asList(petDetail1, petDetail2, petDetail3, petDetail4, petDetail5, petDetail6);

        // 2(晚) + 3(天) + 8(小时) + 2(次) + 1(次) + 1(次) = 17
        var expected = 17;

        long count = getApplyCount(petDetails, serviceCharge);

        assertEquals(expected, count);
    }

    /**
     * 测试Feeding charge apply count - 有foodSource - 按顿收费
     */
    @Test
    void testGetApplyCountByFeedingMedication_FeedingWithFoodSource() {
        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setSurchargeType(SurchargeType.FEEDING_FEE)
                .setFoodSource(ServiceCharge.FoodSource.newBuilder()
                        .setIsAllFoodSource(false)
                        .addAllFoodSourceIds(Collections.singletonList(123L)) // 特定的食物来源ID
                        .build())
                .addAllServiceItemTypes(List.of(ServiceItemType.BOARDING, ServiceItemType.DAYCARE))
                .setChargeMethod(ChargeMethod.PER_ADMINISTRATION)
                .build();

        Map<String, Long> foodSourceMap = new HashMap<>();
        foodSourceMap.put("House Food", 123L);
        foodSourceMap.put("Brought From Home", 456L);

        AppointmentPetFeedingScheduleDef feeding1 = AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingSource("House Food") // 匹配
                .addAllFeedingTimes(List.of(
                        BusinessPetScheduleTimeDef.getDefaultInstance(),
                        BusinessPetScheduleTimeDef.getDefaultInstance())) // 每日两餐
                .build();

        AppointmentPetFeedingScheduleDef feeding2 = AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingSource("Brought From Home") // 不匹配
                .addAllFeedingTimes(List.of(BusinessPetScheduleTimeDef.getDefaultInstance()))
                .build();

        AppointmentPetFeedingScheduleDef feeding3 = AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingSource("House Food") // 匹配
                .addAllFeedingTimes(List.of(BusinessPetScheduleTimeDef.getDefaultInstance()))
                .build();

        List<AppointmentPetFeedingScheduleDef> feedingSchedules = Arrays.asList(feeding1, feeding2, feeding3);
        List<AppointmentPetMedicationScheduleDef> medicationSchedules = Collections.emptyList();

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setStartDate("2024-03-01");
        petDetail.setEndDate("2024-03-02");
        petDetail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        List<MoeGroomingPetDetail> petDetails = List.of(petDetail);

        // 2 天 * 3 餐 = 6
        var expect = 6;

        long result = getApplyCountByFeedingMedication(
                feedingSchedules, medicationSchedules, foodSourceMap, petDetails, serviceCharge);

        assertEquals(expect, result);
    }

    /**
     * 测试Feeding charge apply count - ServiceCharge的isAllFoodSource为true的情况
     */
    @Test
    void testGetApplyCountByFeedingMedication_FeedingWithAllFoodSources() {
        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setSurchargeType(SurchargeType.FEEDING_FEE)
                .setFoodSource(ServiceCharge.FoodSource.newBuilder()
                        .setIsAllFoodSource(true)
                        .addAllFoodSourceIds(List.of(123L))
                        .build())
                .addAllServiceItemTypes(List.of(ServiceItemType.BOARDING, ServiceItemType.DAYCARE))
                .setChargeMethod(ChargeMethod.PER_ADMINISTRATION)
                .build();

        Map<String, Long> foodSourceMap = new HashMap<>();
        foodSourceMap.put("House Food", 123L);
        foodSourceMap.put("Brought From Home", 456L);

        AppointmentPetFeedingScheduleDef feeding1 = AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingSource("House Food") // 匹配
                .addAllFeedingTimes(List.of(
                        BusinessPetScheduleTimeDef.getDefaultInstance(),
                        BusinessPetScheduleTimeDef.getDefaultInstance())) // 每日两餐
                .build();

        AppointmentPetFeedingScheduleDef feeding2 = AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingSource("Brought From Home") // 匹配
                .addAllFeedingTimes(List.of(BusinessPetScheduleTimeDef.getDefaultInstance()))
                .build();

        AppointmentPetFeedingScheduleDef feeding3 = AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingSource("") // 匹配
                .addAllFeedingTimes(List.of(BusinessPetScheduleTimeDef.getDefaultInstance()))
                .build();

        List<AppointmentPetFeedingScheduleDef> feedingSchedules = Arrays.asList(feeding1, feeding2, feeding3);
        List<AppointmentPetMedicationScheduleDef> medicationSchedules = Collections.emptyList();

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setStartDate("2024-03-01");
        petDetail.setEndDate("2024-03-02");
        petDetail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        List<MoeGroomingPetDetail> petDetails = List.of(petDetail);

        // 2 天 * 4 餐 = 8
        var expect = 8;

        long result = getApplyCountByFeedingMedication(
                feedingSchedules, medicationSchedules, foodSourceMap, petDetails, serviceCharge);

        assertEquals(expect, result);
    }

    /**
     * 测试 charge apply count - 有foodSource - 按天收费
     */
    @Test
    void testGetApplyCountByFeedingMedication_FeedingWithPerDay() {
        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setSurchargeType(SurchargeType.FEEDING_FEE)
                .setFoodSource(ServiceCharge.FoodSource.newBuilder()
                        .setIsAllFoodSource(false)
                        .addAllFoodSourceIds(Collections.singletonList(123L)) // 特定的食物来源ID
                        .build())
                .addAllServiceItemTypes(List.of(ServiceItemType.BOARDING, ServiceItemType.DAYCARE))
                .setChargeMethod(ChargeMethod.PER_DAY)
                .build();

        Map<String, Long> foodSourceMap = new HashMap<>();
        foodSourceMap.put("House Food", 123L);
        foodSourceMap.put("Brought From Home", 456L);

        AppointmentPetFeedingScheduleDef feeding1 = AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingSource("House Food") // 匹配
                .addAllFeedingTimes(List.of(
                        BusinessPetScheduleTimeDef.getDefaultInstance(),
                        BusinessPetScheduleTimeDef.getDefaultInstance())) // 每日两餐
                .build();

        AppointmentPetFeedingScheduleDef feeding2 = AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingSource("Brought From Home") // 不匹配
                .addAllFeedingTimes(List.of(BusinessPetScheduleTimeDef.getDefaultInstance()))
                .build();

        AppointmentPetFeedingScheduleDef feeding3 = AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingSource("House Food") // 匹配
                .addAllFeedingTimes(List.of(BusinessPetScheduleTimeDef.getDefaultInstance()))
                .build();

        List<AppointmentPetFeedingScheduleDef> feedingSchedules = Arrays.asList(feeding1, feeding2, feeding3);
        List<AppointmentPetMedicationScheduleDef> medicationSchedules = Collections.emptyList();

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setStartDate("2024-03-01");
        petDetail.setEndDate("2024-03-02");
        petDetail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        List<MoeGroomingPetDetail> petDetails = List.of(petDetail);

        // 2 天 * 2 = 4
        var expect = 4;

        long result = getApplyCountByFeedingMedication(
                feedingSchedules, medicationSchedules, foodSourceMap, petDetails, serviceCharge);

        assertEquals(expect, result);
    }

    /**
     * 测试Medication费用计算 - 所有日期类型 - 按顿收费
     */
    @Test
    void testGetApplyCountByFeedingMedication_MedicationWithDateType_PerAdministration() {
        // 准备测试数据
        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setSurchargeType(SurchargeType.MEDICATION_FEE)
                .addAllServiceItemTypes(List.of(ServiceItemType.BOARDING, ServiceItemType.DAYCARE))
                .setChargeMethod(ChargeMethod.PER_ADMINISTRATION)
                .build();
        // 创建用药安排 - 使用常规日期类型
        AppointmentPetMedicationScheduleDef medication1 =
                AppointmentPetMedicationScheduleDef.newBuilder().build();

        // SelectedDate - EXCLUDE_CHECKOUT_DAY
        AppointmentPetMedicationScheduleDef.SelectedDateDef selectedDate1 =
                AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                        .setDateTypeValue(FeedingMedicationScheduleDateType.EVERYDAY_EXCEPT_CHECKOUT_DATE_VALUE)
                        .build();

        medication1 = medication1.toBuilder()
                .setSelectedDate(selectedDate1)
                .addAllMedicationTimes(List.of(
                        BusinessPetScheduleTimeDef.getDefaultInstance(),
                        BusinessPetScheduleTimeDef.getDefaultInstance())) // 每日两次
                .build();

        // 创建第二个用药安排 - INCLUDE_CHECKOUT_DAY类型 (3天)
        AppointmentPetMedicationScheduleDef medication2 =
                AppointmentPetMedicationScheduleDef.newBuilder().build();

        AppointmentPetMedicationScheduleDef.SelectedDateDef selectedDate2 =
                AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                        .setDateTypeValue(FeedingMedicationScheduleDateType.EVERYDAY_INCLUDE_CHECKOUT_DATE_VALUE)
                        .build();

        medication2 = medication2.toBuilder()
                .setSelectedDate(selectedDate2)
                .addAllMedicationTimes(List.of(
                        BusinessPetScheduleTimeDef.getDefaultInstance(),
                        BusinessPetScheduleTimeDef.getDefaultInstance())) // 每日两次
                .build();

        // SPECIFIC_DATE
        AppointmentPetMedicationScheduleDef medication3 =
                AppointmentPetMedicationScheduleDef.newBuilder().build();

        AppointmentPetMedicationScheduleDef.SelectedDateDef selectedDate3 =
                AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                        .setDateType(FeedingMedicationScheduleDateType.SPECIFIC_DATE)
                        .addAllSpecificDates(List.of("2024-03-01", "2024-03-02", "2024-03-04")) // 3天
                        .build();

        medication3 = medication3.toBuilder()
                .setSelectedDate(selectedDate3)
                .addAllMedicationTimes(List.of(
                        BusinessPetScheduleTimeDef.getDefaultInstance(),
                        BusinessPetScheduleTimeDef.getDefaultInstance())) // 每日两次
                .build();

        List<AppointmentPetFeedingScheduleDef> feedingSchedules = Collections.emptyList();
        List<AppointmentPetMedicationScheduleDef> medicationSchedules =
                Arrays.asList(medication1, medication2, medication3);

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setStartDate("2024-03-01");
        petDetail.setEndDate("2024-03-05");
        petDetail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        List<MoeGroomingPetDetail> petDetails = List.of(petDetail);

        var expect = 24; // 4*2 + 5*2 + 3*2 = 8 + 10 + 6 = 24

        long result = getApplyCountByFeedingMedication(
                feedingSchedules, medicationSchedules, Collections.emptyMap(), petDetails, serviceCharge);

        assertEquals(expect, result);
    }

    /**
     * 测试Medication charge apply count - 所有日期类型 - 按天收费
     */
    @Test
    void testGetApplyCountByFeedingMedication_MedicationWithDateType_PerDay() {
        // 准备测试数据
        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setSurchargeType(SurchargeType.MEDICATION_FEE)
                .addAllServiceItemTypes(List.of(ServiceItemType.BOARDING, ServiceItemType.DAYCARE))
                .setChargeMethod(ChargeMethod.PER_DAY)
                .build();

        // 创建用药安排 - 使用常规日期类型
        AppointmentPetMedicationScheduleDef medication1 =
                AppointmentPetMedicationScheduleDef.newBuilder().build();

        // SelectedDate - EXCLUDE_CHECKOUT_DAY
        AppointmentPetMedicationScheduleDef.SelectedDateDef selectedDate1 =
                AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                        .setDateTypeValue(FeedingMedicationScheduleDateType.EVERYDAY_EXCEPT_CHECKOUT_DATE_VALUE)
                        .build();

        medication1 = medication1.toBuilder()
                .setSelectedDate(selectedDate1)
                .addAllMedicationTimes(List.of(
                        BusinessPetScheduleTimeDef.getDefaultInstance(),
                        BusinessPetScheduleTimeDef.getDefaultInstance())) // 每日两次
                .build();

        // 创建第二个用药安排 - INCLUDE_CHECKOUT_DAY类型 (3天)
        AppointmentPetMedicationScheduleDef medication2 =
                AppointmentPetMedicationScheduleDef.newBuilder().build();

        AppointmentPetMedicationScheduleDef.SelectedDateDef selectedDate2 =
                AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                        .setDateTypeValue(FeedingMedicationScheduleDateType.EVERYDAY_INCLUDE_CHECKOUT_DATE_VALUE)
                        .build();

        medication2 = medication2.toBuilder()
                .setSelectedDate(selectedDate2)
                .addAllMedicationTimes(List.of(
                        BusinessPetScheduleTimeDef.getDefaultInstance(),
                        BusinessPetScheduleTimeDef.getDefaultInstance())) // 每日两次
                .build();

        // SPECIFIC_DATE
        AppointmentPetMedicationScheduleDef medication3 =
                AppointmentPetMedicationScheduleDef.newBuilder().build();

        AppointmentPetMedicationScheduleDef.SelectedDateDef selectedDate3 =
                AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                        .setDateType(FeedingMedicationScheduleDateType.SPECIFIC_DATE)
                        .addAllSpecificDates(List.of("2024-03-01", "2024-03-02", "2024-03-04")) // 3天
                        .build();

        medication3 = medication3.toBuilder()
                .setSelectedDate(selectedDate3)
                .addAllMedicationTimes(List.of(
                        BusinessPetScheduleTimeDef.getDefaultInstance(),
                        BusinessPetScheduleTimeDef.getDefaultInstance())) // 每日两次
                .build();

        List<AppointmentPetFeedingScheduleDef> feedingSchedules = Collections.emptyList();
        List<AppointmentPetMedicationScheduleDef> medicationSchedules =
                Arrays.asList(medication1, medication2, medication3);

        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setStartDate("2024-03-01");
        petDetail.setEndDate("2024-03-05");
        petDetail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
        List<MoeGroomingPetDetail> petDetails = List.of(petDetail);

        var expect = 12; // 4*1 + 5*1 + 3*1 = 12

        long result = getApplyCountByFeedingMedication(
                feedingSchedules, medicationSchedules, Collections.emptyMap(), petDetails, serviceCharge);

        assertEquals(expect, result);
    }

    @Test
    @DisplayName("应该正确计算单个服务费用")
    void shouldCalculateSingleServiceCharge() {
        // Arrange
        Long serviceChargeId = 1L;
        ServiceCharge serviceCharge = ServiceCharge.newBuilder()
                .setId(serviceChargeId)
                .setPrice(100.50)
                .build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceCharge);
        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(serviceChargeId, 2);
        BigDecimal expected = BigDecimal.valueOf(201.00);

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("应该正确计算多个服务费用的总和")
    void shouldCalculateMultipleServiceCharges() {
        // Arrange
        ServiceCharge serviceCharge1 =
                ServiceCharge.newBuilder().setId(1L).setPrice(50.00).build();
        ServiceCharge serviceCharge2 =
                ServiceCharge.newBuilder().setId(2L).setPrice(75.25).build();
        ServiceCharge serviceCharge3 =
                ServiceCharge.newBuilder().setId(3L).setPrice(100.50).build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceCharge1, serviceCharge2, serviceCharge3);

        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(
                1L, 2, // 50.00 * 2 = 100.00
                2L, 1, // 75.25 * 1 = 75.25
                3L, 3 // 100.50 * 3 = 301.50
                );
        BigDecimal expected = BigDecimal.valueOf(476.75);

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("应该正确处理小数计算精度")
    void shouldHandleDecimalPrecisionCorrectly() {
        // Arrange
        ServiceCharge serviceCharge =
                ServiceCharge.newBuilder().setId(1L).setPrice(33.333).build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceCharge);
        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(1L, 3);
        BigDecimal expected = BigDecimal.valueOf(99.999);

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("应该正确处理大数值计算")
    void shouldHandleLargeNumbers() {
        // Arrange
        ServiceCharge serviceCharge =
                ServiceCharge.newBuilder().setId(1L).setPrice(999999.99).build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceCharge);
        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(1L, 1000);
        BigDecimal expected = BigDecimal.valueOf(999999990.00);

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("当应用次数为0时应该返回0")
    void shouldReturnZeroWhenApplyCountIsZero() {
        // Arrange
        ServiceCharge serviceCharge =
                ServiceCharge.newBuilder().setId(1L).setPrice(100.00).build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceCharge);
        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(1L, 0);
        BigDecimal expected = BigDecimal.ZERO;

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("应该正确处理负数应用次数")
    void shouldHandleNegativeApplyCount() {
        // Arrange
        ServiceCharge serviceCharge =
                ServiceCharge.newBuilder().setId(1L).setPrice(100.00).build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceCharge);
        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(1L, -2);
        BigDecimal expected = BigDecimal.valueOf(-200.00);

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("当价格为0时应该返回0")
    void shouldReturnZeroWhenPriceIsZero() {
        // Arrange
        ServiceCharge serviceCharge =
                ServiceCharge.newBuilder().setId(1L).setPrice(0.0).build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceCharge);
        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(1L, 5);
        BigDecimal expected = BigDecimal.ZERO;

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("当输入Map为空时应该返回0")
    void shouldReturnZeroWhenInputMapIsEmpty() {
        // Arrange
        ServiceCharge serviceCharge =
                ServiceCharge.newBuilder().setId(1L).setPrice(100.00).build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceCharge);
        Map<Long, Integer> serviceChargeIdToApplyCount = Collections.emptyMap();
        BigDecimal expected = BigDecimal.ZERO;

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("当服务费用列表为空时应该返回0")
    void shouldReturnZeroWhenServiceChargeListIsEmpty() {
        // Arrange
        List<ServiceCharge> serviceCharges = Collections.emptyList();
        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(1L, 2);
        BigDecimal expected = BigDecimal.ZERO;

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("当服务费用ID不存在时应该跳过该项并继续计算")
    void shouldSkipNonExistentServiceChargeId() {
        // Arrange
        ServiceCharge serviceCharge1 =
                ServiceCharge.newBuilder().setId(1L).setPrice(50.00).build();
        ServiceCharge serviceCharge2 =
                ServiceCharge.newBuilder().setId(2L).setPrice(75.25).build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceCharge1, serviceCharge2);

        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(
                1L, 2, // 50.00 * 2 = 100.00 (存在)
                999L, 5, // 不存在的ID，应该被跳过
                2L, 1 // 75.25 * 1 = 75.25 (存在)
                );
        BigDecimal expected = BigDecimal.valueOf(175.25);

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("当所有请求的服务费用ID都不存在时应该返回0")
    void shouldReturnZeroWhenAllRequestedIdsDoNotExist() {
        // Arrange
        ServiceCharge serviceCharge =
                ServiceCharge.newBuilder().setId(1L).setPrice(100.00).build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceCharge);
        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(
                999L, 2,
                888L, 3);
        BigDecimal expected = BigDecimal.ZERO;

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("当服务费用列表包含重复ID时应该使用第一个")
    void shouldUseFirstServiceChargeWhenDuplicateIds() {
        // Arrange
        ServiceCharge serviceCharge1 =
                ServiceCharge.newBuilder().setId(1L).setPrice(100.00).build();
        ServiceCharge serviceCharge2 = ServiceCharge.newBuilder()
                .setId(1L)
                .setPrice(200.00) // 重复ID，不同价格
                .build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceCharge1, serviceCharge2);
        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(1L, 2);
        BigDecimal expected = BigDecimal.valueOf(200.00); // 应该使用第一个ServiceCharge的价格

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("应该正确处理protobuf默认值")
    void shouldHandleProtobufDefaultValues() {
        // Arrange
        ServiceCharge serviceChargeWithDefaultId = ServiceCharge.newBuilder()
                .setPrice(50.00)
                // 不设置ID，使用默认值0L
                .build();
        ServiceCharge normalServiceCharge =
                ServiceCharge.newBuilder().setId(1L).setPrice(100.00).build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceChargeWithDefaultId, normalServiceCharge);
        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(
                0L, 2, // 默认ID
                1L, 1 // 正常ID
                );
        BigDecimal expected = BigDecimal.valueOf(200.00); // 50.00*2 + 100.00*1

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("应该正确处理protobuf价格默认值为0的情况")
    void shouldHandleProtobufDefaultPriceValue() {
        // Arrange
        ServiceCharge serviceChargeWithDefaultPrice = ServiceCharge.newBuilder()
                .setId(1L)
                // 不设置price，使用默认值0.0
                .build();
        List<ServiceCharge> serviceCharges = Arrays.asList(serviceChargeWithDefaultPrice);
        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(1L, 5);
        BigDecimal expected = BigDecimal.ZERO;

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("应该能处理大量服务费用数据")
    void shouldHandleLargeServiceChargeList() {
        // Arrange
        List<ServiceCharge> serviceCharges = new ArrayList<>();
        Map<Long, Integer> serviceChargeIdToApplyCount = new HashMap<>();

        for (long i = 1; i <= 1000; i++) {
            serviceCharges.add(
                    ServiceCharge.newBuilder().setId(i).setPrice(i * 10.0).build());
            serviceChargeIdToApplyCount.put(i, 1);
        }

        // 计算期望值: 10 + 20 + 30 + ... + 10000 = 10 * (1 + 2 + ... + 1000) = 10 * 500500 = 5005000
        BigDecimal expected = BigDecimal.valueOf(5005000.0);

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    @DisplayName("应该能处理混合正负应用次数的复杂场景")
    void shouldHandleComplexMixedScenario() {
        // Arrange
        ServiceCharge charge1 =
                ServiceCharge.newBuilder().setId(1L).setPrice(100.0).build();
        ServiceCharge charge2 =
                ServiceCharge.newBuilder().setId(2L).setPrice(50.0).build();
        ServiceCharge charge3 =
                ServiceCharge.newBuilder().setId(3L).setPrice(25.5).build();
        List<ServiceCharge> serviceCharges = Arrays.asList(charge1, charge2, charge3);

        Map<Long, Integer> serviceChargeIdToApplyCount = Map.of(
                1L, 3, // 100 * 3 = 300
                2L, -2, // 50 * -2 = -100
                3L, 4, // 25.5 * 4 = 102
                999L, 10 // 不存在，跳过
                );
        BigDecimal expected = BigDecimal.valueOf(302.0); // 300 - 100 + 102 = 302

        // Act
        BigDecimal result = calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);

        // Assert
        assertThat(result).isEqualByComparingTo(expected);
    }

    @Test
    void testCalculateExceed24hTime() {
        var defaultTimeZoneName = "Asia/Shanghai";
        com.moego.idl.models.organization.v1.TimeZone timeZone =
                TimeZone.newBuilder().setName(defaultTimeZoneName).build();

        // Test case 1: 未超过24小时的情况
        Long checkIn1 = DateUtil.timestamp("2024-01-01 10:00:00", defaultTimeZoneName) / 1000;
        Long checkOut1 = DateUtil.timestamp("2024-01-01 15:00:00", defaultTimeZoneName) / 1000;
        assertEquals(0, calculateExceed24hTime(checkIn1, checkOut1, timeZone));

        // Test case 2: 刚好24小时的情况
        Long checkIn2 = DateUtil.timestamp("2024-01-01 10:00:00", defaultTimeZoneName) / 1000;
        Long checkOut2 = DateUtil.timestamp("2024-01-02 10:00:00", defaultTimeZoneName) / 1000;
        assertEquals(0, calculateExceed24hTime(checkIn2, checkOut2, timeZone));

        // Test case 3: 超过24小时的情况
        Long checkIn3 = DateUtil.timestamp("2024-01-01 10:00:00", defaultTimeZoneName) / 1000;
        Long checkOut3 = DateUtil.timestamp("2024-01-02 18:00:00", defaultTimeZoneName) / 1000;
        assertEquals(8 * 60 * 60, calculateExceed24hTime(checkIn3, checkOut3, timeZone));

        // Test case 4: 跨天且超过24小时但退房时间点早于入住时间点的情况
        Long checkIn4 = DateUtil.timestamp("2024-01-01 23:00:00", defaultTimeZoneName) / 1000;
        Long checkOut4 = DateUtil.timestamp("2024-01-03 01:00:00", defaultTimeZoneName) / 1000;
        assertEquals(0, calculateExceed24hTime(checkIn4, checkOut4, timeZone));

        // Test case 5: 超过48小时的情况
        Long checkIn5 = DateUtil.timestamp("2024-01-01 10:00:00", defaultTimeZoneName) / 1000;
        Long checkOut5 = DateUtil.timestamp("2024-01-03 15:00:00", defaultTimeZoneName) / 1000;
        assertEquals(5 * 60 * 60, calculateExceed24hTime(checkIn5, checkOut5, timeZone));

        // Test case 6: 退房时间早于入住时间的情况（异常情况）
        Long checkIn6 = DateUtil.timestamp("2024-01-02 10:00:00", defaultTimeZoneName) / 1000;
        Long checkOut6 = DateUtil.timestamp("2024-01-01 15:00:00", defaultTimeZoneName) / 1000;
        assertEquals(0, calculateExceed24hTime(checkIn6, checkOut6, timeZone));
    }

    @ParameterizedTest
    @MethodSource("provideCalculatePriceByMultiplePetsCases")
    void testCalculatePriceByMultiplePets_Parameterized(
            String testName,
            ServiceCharge serviceCharge,
            ServiceChargeExceedHourRule rule,
            long petCount,
            double expectedPrice) {

        double actualPrice = calculatePriceByMultiplePets(rule, petCount, serviceCharge);
        assertEquals(expectedPrice, actualPrice, 0.001, testName);
    }

    private static ServiceCharge buildServiceCharge(ServiceCharge.MultiplePetsChargeType chargeType) {
        return ServiceCharge.newBuilder().setMultiplePetsChargeType(chargeType).build();
    }

    private static ServiceChargeExceedHourRule buildRule(double basePrice, double additionalPrice) {
        return ServiceChargeExceedHourRule.newBuilder()
                .setBasePrice(basePrice)
                .setAdditionalPetPrice(additionalPrice)
                .build();
    }

    static Stream<Arguments> provideCalculatePriceByMultiplePetsCases() {
        return Stream.of(

                // ✅ SAME_CHARGE_PER_PET 类型
                Arguments.of(
                        "Same per pet - 0 pets",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET),
                        buildRule(10.0, 5.0),
                        0,
                        0.0),
                Arguments.of(
                        "Same per pet - 1 pet",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET),
                        buildRule(10.0, 5.0),
                        1,
                        10.0),
                Arguments.of(
                        "Same per pet - 3 pets",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET),
                        buildRule(10.0, 5.0),
                        3,
                        30.0),
                Arguments.of(
                        "Same per pet - negative pet count",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET),
                        buildRule(10.0, 5.0),
                        -1,
                        0.0),

                // ✅ DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS 类型
                Arguments.of(
                        "Different for additional - 1 pet",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(10.0, 5.0),
                        1,
                        10.0),
                Arguments.of(
                        "Different for additional - 2 pets",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(10.0, 5.0),
                        2,
                        15.0), // 10 + 5×1
                Arguments.of(
                        "Different for additional - 3 pets",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(10.0, 5.0),
                        3,
                        20.0), // 10 + 5×2
                Arguments.of(
                        "Different for additional - 0 pets",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(10.0, 5.0),
                        0,
                        0.0),
                Arguments.of(
                        "Different for additional - negative pets",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(10.0, 5.0),
                        -1,
                        0.0),

                // 🧪 特殊数值测试
                Arguments.of(
                        "Large number of pets",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET),
                        buildRule(15.5, 0.0),
                        10,
                        155.0),
                Arguments.of(
                        "Zero base price",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET),
                        buildRule(0.0, 5.0),
                        4,
                        0.0),
                Arguments.of(
                        "Zero additional price",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(10.0, 0.0),
                        5,
                        10.0),
                Arguments.of(
                        "Negative additional price",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(20.0, -2.0),
                        4,
                        14.0), // 20 + (-2)*3 = 14

                // 🔁 边界值测试
                Arguments.of(
                        "Exactly one pet with different type",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(25.0, 10.0),
                        1,
                        25.0),
                Arguments.of(
                        "Two pets with different type",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(25.0, 10.0),
                        2,
                        35.0), // 25 + 10×1
                Arguments.of(
                        "Three pets with different type",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(25.0, 10.0),
                        3,
                        45.0), // 25 + 10×2

                // 🧪 浮点精度测试
                Arguments.of(
                        "Floating point prices",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(7.5, 2.5),
                        4,
                        15.0), // 7.5 + 2.5*3

                // 🧹 非法/边缘输入
                Arguments.of(
                        "Additional price only",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(0.0, 5.0),
                        4,
                        15.0), // 0 + 5×3
                Arguments.of(
                        "Base price zero and no additional",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS),
                        buildRule(0.0, 0.0),
                        4,
                        0.0),
                Arguments.of(
                        "Very large numbers",
                        buildServiceCharge(ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET),
                        buildRule(999999.0, 0.0),
                        1000,
                        999999000.0));
    }
}
