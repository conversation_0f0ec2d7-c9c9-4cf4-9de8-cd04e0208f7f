package com.moego.api.v3.appointment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.type.Date;
import com.moego.api.v3.offering.service.ServiceService;
import com.moego.idl.api.appointment.v1.ListAppointmentTaskCountByCategoryParams;
import com.moego.idl.api.appointment.v1.ListAppointmentTaskCountByCategoryResult;
import com.moego.idl.models.appointment.v1.AppointmentTaskCategory;
import com.moego.idl.models.appointment.v1.AppointmentTaskStatus;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.service.appointment.v1.CountAppointmentTasksResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentTaskGroupsResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AppointmentTaskControllerTest {

    @Mock
    private ServiceService service;

    @InjectMocks
    private AppointmentTaskController appointmentTaskController;

    @Test
    void isSelectedAllStatuses_WhenNoStatusSelected_ShouldReturnTrue() {
        // Arrange
        var params = ListAppointmentTaskCountByCategoryParams.newBuilder()
                .setFilter(ListAppointmentTaskCountByCategoryParams.Filter.getDefaultInstance())
                .build();

        // Act
        var result = AppointmentTaskController.isSelectedAllStatuses(params);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSelectedAllStatuses_WhenTwoStatusesSelected_ShouldReturnTrue() {
        // Arrange
        var params = ListAppointmentTaskCountByCategoryParams.newBuilder()
                .setFilter(ListAppointmentTaskCountByCategoryParams.Filter.newBuilder()
                        .addStatuses(AppointmentTaskStatus.COMPLETED)
                        .addStatuses(AppointmentTaskStatus.INCOMPLETE)
                        .build())
                .build();

        // Act
        var result = AppointmentTaskController.isSelectedAllStatuses(params);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSelectedAllStatuses_WhenOneStatusSelected_ShouldReturnFalse() {
        // Arrange
        var params = ListAppointmentTaskCountByCategoryParams.newBuilder()
                .setFilter(ListAppointmentTaskCountByCategoryParams.Filter.newBuilder()
                        .addStatuses(AppointmentTaskStatus.COMPLETED)
                        .build())
                .build();

        // Act
        var result = AppointmentTaskController.isSelectedAllStatuses(params);

        // Assert
        assertFalse(result);
    }

    @Test
    void isSelectedCompleted_WhenCompletedStatusSelected_ShouldReturnTrue() {
        // Arrange
        var params = ListAppointmentTaskCountByCategoryParams.newBuilder()
                .setFilter(ListAppointmentTaskCountByCategoryParams.Filter.newBuilder()
                        .addStatuses(AppointmentTaskStatus.COMPLETED)
                        .build())
                .build();

        // Act
        var result = AppointmentTaskController.isSelectedCompleted(params);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSelectedCompleted_WhenCompletedStatusNotSelected_ShouldReturnFalse() {
        // Arrange
        var params = ListAppointmentTaskCountByCategoryParams.newBuilder()
                .setFilter(ListAppointmentTaskCountByCategoryParams.Filter.newBuilder()
                        .addStatuses(AppointmentTaskStatus.INCOMPLETE)
                        .build())
                .build();

        // Act
        var result = AppointmentTaskController.isSelectedCompleted(params);

        // Assert
        assertFalse(result);
    }

    @Test
    void buildCompletedParams_ShouldOnlyContainCompletedStatus() {
        // Arrange
        var params = ListAppointmentTaskCountByCategoryParams.newBuilder()
                .setBusinessId(1000L)
                .setDate(Date.newBuilder().setYear(2025).setMonth(1).setDay(15).build())
                .setFilter(ListAppointmentTaskCountByCategoryParams.Filter.newBuilder()
                        .addStatuses(AppointmentTaskStatus.INCOMPLETE)
                        .addStatuses(AppointmentTaskStatus.COMPLETED)
                        .build())
                .build();

        // Act
        var result = AppointmentTaskController.buildCompletedParams(params);

        // Assert
        var expected = ListAppointmentTaskCountByCategoryParams.newBuilder()
                .setBusinessId(1000L)
                .setDate(Date.newBuilder().setYear(2025).setMonth(1).setDay(15).build())
                .setFilter(ListAppointmentTaskCountByCategoryParams.Filter.newBuilder()
                        .addStatuses(AppointmentTaskStatus.COMPLETED)
                        .build())
                .build();
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void count_WithMultipleCategories_ShouldReturnTotalCount() {
        // Arrange
        var categories = Arrays.asList(
                ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem.newBuilder()
                        .setCount(5)
                        .build(),
                ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem.newBuilder()
                        .setCount(3)
                        .build(),
                ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem.newBuilder()
                        .setCount(2)
                        .build());

        // Act
        int result = AppointmentTaskController.count(categories);

        // Assert
        assertThat(result).isEqualTo(10);
    }

    @Test
    void buildGroups_WithMultiCategories_ShouldReturnMultiCategories() {
        // Arrange
        var response = CountAppointmentTasksResponse.newBuilder()
                .addCategoryCounts(CountAppointmentTasksResponse.CategoryCount.newBuilder()
                        .setCategory(AppointmentTaskCategory.FEEDING)
                        .setCount(10))
                .addCategoryCounts(CountAppointmentTasksResponse.CategoryCount.newBuilder()
                        .setCategory(AppointmentTaskCategory.MEDICATION)
                        .setCount(20))
                .build();

        var completedResponse = CountAppointmentTasksResponse.newBuilder()
                .addCategoryCounts(CountAppointmentTasksResponse.CategoryCount.newBuilder()
                        .setCategory(AppointmentTaskCategory.FEEDING)
                        .setCount(6))
                .build();

        // Act
        var result = AppointmentTaskController.buildGroups(response, completedResponse);

        // Assert
        var expected = List.of(
                ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem.newBuilder()
                        .setCategory(AppointmentTaskCategory.FEEDING)
                        .setCategoryName("Feeding")
                        .setCount(10)
                        .setCompletedCount(6)
                        .build(),
                ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem.newBuilder()
                        .setCategory(AppointmentTaskCategory.MEDICATION)
                        .setCategoryName("Medication")
                        .setCount(20)
                        .setCompletedCount(0)
                        .build());
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void buildGroups_WithMultiAddOns_ShouldReturnMultiCategories() {
        // Arrange
        var response = ListAppointmentTaskGroupsResponse.newBuilder()
                .addGroups(ListAppointmentTaskGroupsResponse.Group.newBuilder()
                        .setAddOn(ListAppointmentTaskGroupsResponse.AddOnGroup.newBuilder()
                                .setAddOnId(100L)
                                .setCount(20)))
                .addGroups(ListAppointmentTaskGroupsResponse.Group.newBuilder()
                        .setAddOn(ListAppointmentTaskGroupsResponse.AddOnGroup.newBuilder()
                                .setAddOnId(200L)
                                .setCount(10)))
                .build();

        var completedResponse = ListAppointmentTaskGroupsResponse.newBuilder()
                .addGroups(ListAppointmentTaskGroupsResponse.Group.newBuilder()
                        .setAddOn(ListAppointmentTaskGroupsResponse.AddOnGroup.newBuilder()
                                .setAddOnId(100L)
                                .setCount(10)))
                .build();
        Mockito.when(service.getServiceMap(null, List.of(100L, 200L)))
                .thenReturn(Map.of(
                        100L,
                                ServiceBriefView.newBuilder()
                                        .setId(100L)
                                        .setName("Test Add-on-100")
                                        .build(),
                        200L,
                                ServiceBriefView.newBuilder()
                                        .setId(200L)
                                        .setName("Test Add-on-200")
                                        .build()));

        // Act
        var result = appointmentTaskController.buildGroups(response, completedResponse);

        // Assert
        var expected = List.of(
                ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem.newBuilder()
                        .setCategory(AppointmentTaskCategory.ADD_ONS)
                        .setCategoryName("Test Add-on-100")
                        .setAddOnId(100L)
                        .setCount(20)
                        .setCompletedCount(10)
                        .build(),
                ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem.newBuilder()
                        .setCategory(AppointmentTaskCategory.ADD_ONS)
                        .setCategoryName("Test Add-on-200")
                        .setAddOnId(200L)
                        .setCount(10)
                        .setCompletedCount(0)
                        .build());
        assertThat(result).isEqualTo(expected);
    }
}
