package com.moego.api.v3.permission.controller;

import static com.moego.api.v3.utils.TestUtils.AU;
import static com.moego.api.v3.utils.TestUtils.mockStub;
import static com.moego.api.v3.utils.TestUtils.noAuth;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.moego.api.v3.utils.MockStubs;
import com.moego.idl.api.permission.v1.GetRoleDetailParams;
import com.moego.idl.api.permission.v1.GetRoleDetailResult;
import com.moego.idl.api.permission.v1.PermissionServiceGrpc;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.models.permission.v1.PermissionCategoryModel;
import com.moego.idl.models.permission.v1.PermissionModel;
import com.moego.idl.models.permission.v1.PermissionScopeAvailableRule;
import com.moego.idl.models.permission.v1.PermissionScopeModel;
import com.moego.idl.models.permission.v1.RoleModel;
import com.moego.idl.service.organization.v1.GetLocationListResponse;
import com.moego.idl.service.organization.v1.IsMoegoPayEnableResponse;
import com.moego.idl.service.permission.v1.GetRoleDetailResponse;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.lib.permission.PermissionEnums;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@ExtendWith(MockitoExtension.class)
@SpringBootTest
public class PermissionControllerTest extends MockStubs {
    @Autowired
    private PermissionServiceGrpc.PermissionServiceBlockingStub permissionServiceBlockingStub;

    private static final RoleModel DEFAULT_ROLE = RoleModel.newBuilder()
            .setId(1L)
            .setName("R1")
            .setDescription("D1")
            .addAllPermissionCategoryList(List.of(
                    PermissionCategoryModel.newBuilder()
                            .setId(11L)
                            .setName("C1")
                            .addAllPermissionList(List.of(
                                    PermissionModel.newBuilder()
                                            .setId(111L)
                                            .setName(PermissionEnums.ACCESS_AUTO_REMINDERS.getPermissionName())
                                            .build(),
                                    PermissionModel.newBuilder()
                                            .setId(112L)
                                            .setName(
                                                    PermissionEnums.REMOVE_PROCESSING_FEE_BY_CLIENT.getPermissionName())
                                            .build(),
                                    PermissionModel.newBuilder()
                                            .setId(113L)
                                            .setName(PermissionEnums.ACCESS_REPORT.getPermissionName())
                                            .build(),
                                    PermissionModel.newBuilder()
                                            .setId(114L)
                                            .setName(PermissionEnums.ACCESS_CLIENT.getPermissionName())
                                            .addAllSubPermissionList(List.of(
                                                    PermissionModel.newBuilder()
                                                            .setId(1141L)
                                                            .setName(
                                                                    PermissionEnums.ACCESS_CLIENT_MEMBERSHIPS
                                                                            .getPermissionName())
                                                            .build(),
                                                    PermissionModel.newBuilder()
                                                            .setId(1142L)
                                                            .setName(PermissionEnums.EXPORT_CLIENT.getPermissionName())
                                                            .build()))
                                            .build()))
                            .build(),
                    PermissionCategoryModel.newBuilder()
                            .setId(12L)
                            .setName("C2")
                            .addAllPermissionList(List.of(
                                    PermissionModel.newBuilder()
                                            .setId(121L)
                                            .setName(PermissionEnums.ACCESS_LODGING_SETTING.getPermissionName())
                                            .build(),
                                    PermissionModel.newBuilder()
                                            .setId(122L)
                                            .setName(PermissionEnums.ACCESS_MEMBERSHIP.getPermissionName())
                                            .build()))
                            .build(),
                    PermissionCategoryModel.newBuilder()
                            .setId(13L)
                            .setName("C3")
                            .addAllPermissionList(List.of(
                                    PermissionModel.newBuilder()
                                            .setId(131L)
                                            .setName(PermissionEnums.SELL_MEMBERSHIP.getPermissionName())
                                            .build(),
                                    PermissionModel.newBuilder()
                                            .setId(132L)
                                            .setName(PermissionEnums.ACCESS_CLIENT_MEMBERSHIPS.getPermissionName())
                                            .build(),
                                    PermissionModel.newBuilder()
                                            .setId(133L)
                                            .setName(PermissionEnums.OPERATE_MEMBERSHIP.getPermissionName())
                                            .build()))
                            .build(),
                    PermissionCategoryModel.newBuilder()
                            .setId(14L)
                            .setName("C4")
                            .addAllPermissionList(List.of(PermissionModel.newBuilder()
                                    .setId(141L)
                                    .setName(PermissionEnums.ACCESS_AGREEMENT_SETTINGS.getPermissionName())
                                    .addAllScopeList(List.of(
                                            PermissionScopeModel.newBuilder()
                                                    .setName("only single location")
                                                    .setIndex(1L)
                                                    .setAvailableRule(PermissionScopeAvailableRule.ONLY_SINGLE_LOCATION)
                                                    .build(),
                                            PermissionScopeModel.newBuilder()
                                                    .setName("only multi location")
                                                    .setIndex(2L)
                                                    .setAvailableRule(PermissionScopeAvailableRule.ONLY_MULTI_LOCATION)
                                                    .build(),
                                            PermissionScopeModel.newBuilder()
                                                    .setName("both")
                                                    .setIndex(3L)
                                                    .setAvailableRule(
                                                            PermissionScopeAvailableRule
                                                                    .PERMISSION_SCOPE_AVAILABLE_RULE_UNSPECIFIED)
                                                    .build()))
                                    .build()))
                            .build()))
            .build();

    private static final RoleModel ROLE_PERMISSION_AFTER_HIDE = RoleModel.newBuilder()
            .setId(1L)
            .setName("R1")
            .setDescription("D1")
            .addAllPermissionCategoryList(List.of(
                    PermissionCategoryModel.newBuilder()
                            .setId(11L)
                            .setName("C1")
                            .addAllPermissionList(List.of(
                                    PermissionModel.newBuilder()
                                            .setId(113L)
                                            .setName(PermissionEnums.ACCESS_REPORT.getPermissionName())
                                            .build(),
                                    PermissionModel.newBuilder()
                                            .setId(114L)
                                            .setName(PermissionEnums.ACCESS_CLIENT.getPermissionName())
                                            .addAllSubPermissionList(List.of(PermissionModel.newBuilder()
                                                    .setId(1142L)
                                                    .setName(PermissionEnums.EXPORT_CLIENT.getPermissionName())
                                                    .build()))
                                            .build()))
                            .build(),
                    PermissionCategoryModel.newBuilder()
                            .setId(12L)
                            .setName("C2")
                            .addAllPermissionList(List.of())
                            .build(),
                    PermissionCategoryModel.newBuilder()
                            .setId(13L)
                            .setName("C3")
                            .addAllPermissionList(List.of())
                            .build(),
                    PermissionCategoryModel.newBuilder()
                            .setId(14L)
                            .setName("C4")
                            .addAllPermissionList(List.of(PermissionModel.newBuilder()
                                    .setId(141L)
                                    .setName(PermissionEnums.ACCESS_AGREEMENT_SETTINGS.getPermissionName())
                                    .addAllScopeList(List.of(
                                            PermissionScopeModel.newBuilder()
                                                    .setName("only multi location")
                                                    .setIndex(2L)
                                                    .setAvailableRule(PermissionScopeAvailableRule.ONLY_MULTI_LOCATION)
                                                    .build(),
                                            PermissionScopeModel.newBuilder()
                                                    .setName("both")
                                                    .setIndex(3L)
                                                    .setAvailableRule(
                                                            PermissionScopeAvailableRule
                                                                    .PERMISSION_SCOPE_AVAILABLE_RULE_UNSPECIFIED)
                                                    .build()))
                                    .build()))
                            .build()))
            .build();

    @Test
    public void getRoleDetail() {
        final var params = GetRoleDetailParams.newBuilder().setRoleId(1L).build();

        noAuth(() -> permissionServiceBlockingStub.getRoleDetail(params));

        mockStub(permissionServiceClient::getRoleDetail, () -> GetRoleDetailResponse.newBuilder()
                .setRoleDetail(DEFAULT_ROLE)
                .build());
        mockStub(businessServiceClient::getLocationList, () -> GetLocationListResponse.newBuilder()
                .addLocation(LocationBriefView.getDefaultInstance())
                .addLocation(LocationBriefView.getDefaultInstance())
                .build());
        mockStub(companyServiceClient::isMoegoPayEnable, () -> IsMoegoPayEnableResponse.newBuilder()
                .setIsMoegoPayEnable(false)
                .build());

        mockStub(featureFlagApi::isOn, (feature, context) -> feature != FeatureFlags.ENABLE_MEMBERSHIP);

        final var result = AU(permissionServiceBlockingStub).getRoleDetail(params);
        final var exp = GetRoleDetailResult.newBuilder()
                .setRoleDetail(ROLE_PERMISSION_AFTER_HIDE)
                .build();
        assertEquals(exp, result);
    }
}
