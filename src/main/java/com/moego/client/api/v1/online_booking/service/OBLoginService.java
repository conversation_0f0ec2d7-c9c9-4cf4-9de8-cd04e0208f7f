package com.moego.client.api.v1.online_booking.service;

import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.organization.v1.LocationModel;
import com.moego.lib.common.util.Env;
import com.moego.server.message.client.IOBVerificationCodeClient;
import com.moego.server.message.dto.SendPhoneDTO;
import com.moego.server.message.dto.VerifyCodeDTO;
import com.moego.server.message.enums.VerificationCodeScenarioEnum;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class OBLoginService {
    private final IOBVerificationCodeClient verificationCodeClient;
    private final Environment env;

    public String sendPhoneVerificationCode(
            LocationModel business, BusinessCustomerInfoModel customer, String phoneNumber) {
        SendPhoneDTO sendPhoneDTO = new SendPhoneDTO()
                .setScenario(VerificationCodeScenarioEnum.OB_CHANGE_PHONE_NUMBER)
                .setBusinessId((int) business.getId())
                .setBusinessName(business.getName())
                .setCustomerId((int) customer.getId())
                .setFirstName(customer.getFirstName())
                .setLastName(customer.getLastName())
                .setPhoneNumber(phoneNumber);
        return verificationCodeClient.sendPhoneVerificationCode(sendPhoneDTO);
    }

    public void verifyPhoneCode(VerifyCodeDTO verifyCodeDTO) {
        // 测试环境魔法验证码
        if (env.matchesProfiles(Env.TEST2.getValue()) && Objects.equals(verifyCodeDTO.getCode(), "000000")) {
            return;
        }
        verificationCodeClient.verifyPhoneCode(verifyCodeDTO);
    }
}
