package com.moego.client.api.v1.online_booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.client.api.v1.shared.helper.EvaluationHelper;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.client.online_booking.v1.BoardingAddon;
import com.moego.idl.client.online_booking.v1.DaycareAddon;
import com.moego.idl.client.online_booking.v1.GroomingAddon;
import com.moego.idl.client.online_booking.v1.Pet;
import com.moego.idl.client.online_booking.v1.PetServices;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.offering.v2.PetDetailCalculateDef;
import com.moego.idl.models.offering.v2.PetDetailCalculateResultDef;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.offering.v2.CalculatePricingRuleRequest;
import com.moego.idl.service.offering.v2.PricingRuleServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class CashierService {

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementServiceClient;
    private final PricingRuleServiceGrpc.PricingRuleServiceBlockingStub pricingRuleStub;
    private final EvaluationHelper evaluationHelper;

    public BigDecimal getCustomizedServicePrice(Long companyId, Integer businessId, List<PetServices> petDatas) {
        boolean hasUnsupportedService = petDatas.stream()
                .flatMap(petServices -> petServices.getServicesList().stream())
                .noneMatch(service -> service.hasBoarding() || service.hasDaycare() || service.hasEvaluation());

        if (hasUnsupportedService) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "pet services not support");
        }

        // Calculate price for each service type
        var servicePrice = getPriceForService(companyId, businessId, petDatas);
        var evaluationPrice = getPriceForEvaluation(petDatas);

        return BigDecimal.ZERO.add(servicePrice).add(evaluationPrice);
    }

    private BigDecimal getPriceForService(Long companyId, Integer businessId, List<PetServices> petDatas) {
        var petDataForService = petDatas.stream()
                .map(petServices -> {
                    var pet = petServices.getPet();
                    var services = petServices.getServicesList().stream()
                            .filter(e -> !e.hasEvaluation())
                            .toList();
                    return PetServices.newBuilder()
                            .setPet(pet)
                            .addAllServices(services)
                            .build();
                })
                .filter(petServices -> !petServices.getServicesList().isEmpty())
                .toList();

        var customizedServiceList = listCustomizedService(companyId, businessId, petDataForService);
        if (customizedServiceList.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // pricing rule 的计算依赖 pet id，给 new pet 生成一个 virtual id
        List<PetServiceDTO> petData = generateVirtualIdForNewPets(petDataForService);

        Collection<CalculateServiceAmount> services = convertServices(petData, customizedServiceList);

        Collection<CalculateServiceAmount> addOns = convertAddOns(petData, customizedServiceList);

        Collection<CalculateServiceAmount> usingPricingRuleServices = applyPricingRule(services, companyId, businessId);

        return Stream.of(usingPricingRuleServices, addOns)
                .flatMap(Collection::stream)
                .map(CalculateServiceAmount::servicePrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getPriceForEvaluation(List<PetServices> petDatas) {

        var evaluationDetails = petDatas.stream()
                .flatMap(e -> e.getServicesList().stream()
                        .filter(com.moego.idl.client.online_booking.v1.Service::hasEvaluation))
                .map(com.moego.idl.client.online_booking.v1.Service::getEvaluation)
                .toList();

        var evaluationIds = evaluationDetails.stream()
                .map(com.moego.idl.client.online_booking.v1.Service.Evaluation::getServiceId)
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());

        var evaluationIdToEvaluation = evaluationHelper.listEvaluation(evaluationIds);

        return evaluationDetails.stream()
                .map(e -> Optional.ofNullable(evaluationIdToEvaluation.get(e.getServiceId()))
                        .map(evaluation -> BigDecimal.valueOf(evaluation.getPrice()))
                        .orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Builder(toBuilder = true)
    private record CalculateServiceAmount(
            Long serviceId,
            ServiceType serviceType,
            ServiceItemType serviceItemType,
            BigDecimal servicePrice,
            Long petId,
            ServiceOverrideType priceOverrideType,
            @Nullable String date) {}

    private List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> listCustomizedService(
            long companyId, Integer businessId, List<PetServices> petData) {

        var conditions = petData.stream()
                .flatMap(petAndService -> petAndService.getServicesList().stream()
                        .flatMap(service -> getQueryConditionStream(businessId, service, petAndService.getPet())))
                .toList();

        if (conditions.isEmpty()) {
            return List.of();
        }

        return serviceManagementServiceClient
                .batchGetCustomizedService(BatchGetCustomizedServiceRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllQueryConditionList(conditions)
                        .build())
                .getCustomizedServiceListList();
    }

    @NotNull
    private static Stream<CustomizedServiceQueryCondition> getQueryConditionStream(
            Integer businessId, com.moego.idl.client.online_booking.v1.Service service, Pet pet) {
        var serviceId = getServiceId(service);
        if (serviceId == null) {
            return Stream.empty();
        }

        var serviceCondition = CustomizedServiceQueryCondition.newBuilder()
                .setServiceId(serviceId)
                .setBusinessId(businessId);

        if (isNormal(pet.getPetId())) {
            serviceCondition.setPetId(pet.getPetId());
        }

        var addOnConditions = getAddIds(service).stream().map(addId -> {
            CustomizedServiceQueryCondition.Builder builder = CustomizedServiceQueryCondition.newBuilder()
                    .setServiceId(addId)
                    .setBusinessId(businessId);
            if (isNormal(pet.getPetId())) {
                builder.setPetId(pet.getPetId());
            }
            return builder.build();
        });

        return Stream.concat(Stream.of(serviceCondition.build()), addOnConditions);
    }

    @NotNull
    private static List<Long> getAddIds(com.moego.idl.client.online_booking.v1.Service service) {
        return switch (service.getServiceCase()) {
            case BOARDING -> service.getBoarding().getAddonsList().stream()
                    .map(BoardingAddon::getId)
                    .distinct()
                    .toList();
            case DAYCARE -> service.getDaycare().getAddonsList().stream()
                    .map(DaycareAddon::getId)
                    .distinct()
                    .toList();
            case GROOMING -> service.getGrooming().getAddonsList().stream()
                    .map(GroomingAddon::getId)
                    .distinct()
                    .toList();
            default -> List.of();
        };
    }

    @Nullable
    private static Long getServiceId(com.moego.idl.client.online_booking.v1.Service service) {
        return switch (service.getServiceCase()) {
            case BOARDING -> service.getBoarding().getServiceId();
            case DAYCARE -> service.getDaycare().getServiceId();
            case GROOMING -> service.getGrooming().getServiceId();
            case DOG_WALKING -> service.getDogWalking().getServiceId();
            default -> null;
        };
    }

    private static List<PetServiceDTO> generateVirtualIdForNewPets(List<PetServices> petDetails) {
        var nextVirtualId = petDetails.stream()
                        .mapToLong(petService -> petService.getPet().getPetId())
                        .filter(CommonUtil::isNormal)
                        .max()
                        .orElse(0)
                + 1;

        var idCounter = new AtomicLong(nextVirtualId);

        return petDetails.stream()
                .map(petService -> {
                    long petId = petService.getPet().getPetId();
                    boolean isVirtualPetId = false;
                    if (!isNormal(petId)) {
                        petId = idCounter.getAndIncrement();
                        isVirtualPetId = true;
                    }
                    return new PetServiceDTO(new PetDTO(petId, isVirtualPetId), petService.getServicesList());
                })
                .toList();
    }

    private record PetDTO(Long petId, boolean isVirtualPetId) {}

    private record PetServiceDTO(PetDTO pet, List<com.moego.idl.client.online_booking.v1.Service> servicesList) {}

    private static Collection<CalculateServiceAmount> convertServices(
            List<PetServiceDTO> petData,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList) {
        return petData.stream()
                .flatMap(petAndServices -> {
                    var pet = petAndServices.pet();
                    return petAndServices.servicesList().stream().flatMap(service -> {
                        var serviceId = getServiceId(service);
                        if (serviceId == null) {
                            return Stream.empty();
                        }

                        var petId = pet.isVirtualPetId() ? null : pet.petId();
                        var customizedService = findCustomizedService(customizedServiceList, serviceId, petId);
                        if (customizedService == null) {
                            return Stream.empty();
                        }

                        return switch (service.getServiceCase()) {
                            case BOARDING -> generateBoardingServiceAmounts(
                                    service.getBoarding(), customizedService, pet.petId());
                            case DAYCARE -> generateDaycareServiceAmounts(
                                    service.getDaycare(), customizedService, pet.petId());
                            case GROOMING -> generateGroomingServiceAmounts(
                                    service.getGrooming(), customizedService, pet.petId());
                            case DOG_WALKING -> generateDogWalkingServiceAmounts(
                                    service.getDogWalking(), customizedService, pet.petId());
                            default -> Stream.empty();
                        };
                    });
                })
                .toList();
    }

    private static Stream<CalculateServiceAmount> generateDogWalkingServiceAmounts(
            com.moego.idl.client.online_booking.v1.Service.DogWalking dogWalking,
            CustomizedServiceView customizedService,
            long petId) {

        var amount = CalculateServiceAmount.builder()
                .serviceId(customizedService.getId())
                .serviceType(customizedService.getType())
                .serviceItemType(customizedService.getServiceItemType())
                .servicePrice(BigDecimal.valueOf(customizedService.getPrice()))
                .petId(petId)
                .priceOverrideType(customizedService.getPriceOverrideType())
                .date(dogWalking.getDate())
                .build();

        return Stream.of(amount);
    }

    private static Stream<CalculateServiceAmount> generateGroomingServiceAmounts(
            com.moego.idl.client.online_booking.v1.Service.Grooming grooming,
            CustomizedServiceView customizedService,
            long petId) {

        var amount = CalculateServiceAmount.builder()
                .serviceId(customizedService.getId())
                .serviceType(customizedService.getType())
                .serviceItemType(customizedService.getServiceItemType())
                .servicePrice(BigDecimal.valueOf(customizedService.getPrice()))
                .petId(petId)
                .priceOverrideType(customizedService.getPriceOverrideType())
                .date(grooming.getStartDate())
                .build();

        return Stream.of(amount);
    }

    private static Stream<CalculateServiceAmount> generateBoardingServiceAmounts(
            com.moego.idl.client.online_booking.v1.Service.Boarding boarding,
            CustomizedServiceView customizedService,
            Long petId) {
        var start = boarding.getStartDate();
        var end = boarding.getEndDate();
        var endDate = isPerDayBoardingService(customizedService)
                ? LocalDate.parse(end).plusDays(1)
                : LocalDate.parse(end);

        return LocalDate.parse(start).datesUntil(endDate).map(date -> CalculateServiceAmount.builder()
                .serviceId(customizedService.getId())
                .serviceType(customizedService.getType())
                .serviceItemType(customizedService.getServiceItemType())
                .servicePrice(BigDecimal.valueOf(customizedService.getPrice()))
                .petId(petId)
                .priceOverrideType(customizedService.getPriceOverrideType())
                .date(date.toString())
                .build());
    }

    private static Stream<CalculateServiceAmount> generateDaycareServiceAmounts(
            com.moego.idl.client.online_booking.v1.Service.Daycare daycare,
            CustomizedServiceView customizedService,
            Long petId) {
        return daycare.getDatesList().stream().map(date -> CalculateServiceAmount.builder()
                .serviceId(customizedService.getId())
                .serviceType(customizedService.getType())
                .serviceItemType(customizedService.getServiceItemType())
                .servicePrice(BigDecimal.valueOf(customizedService.getPrice()))
                .petId(petId)
                .priceOverrideType(customizedService.getPriceOverrideType())
                .date(date)
                .build());
    }

    private static Collection<CalculateServiceAmount> convertAddOns(
            List<PetServiceDTO> petData,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList) {
        return petData.stream()
                .flatMap(petAndServices -> {
                    var pet = petAndServices.pet();
                    return petAndServices.servicesList().stream().flatMap(service -> {
                        var petId = pet.isVirtualPetId() ? null : pet.petId();
                        return switch (service.getServiceCase()) {
                            case BOARDING -> generateBoardingAddonAmounts(
                                    service.getBoarding(), customizedServiceList, pet, petId);
                            case DAYCARE -> generateDaycareAddonAmounts(
                                    service.getDaycare(), customizedServiceList, pet, petId);
                            case GROOMING -> generateGroomingAddonAmounts(
                                    service.getGrooming(), customizedServiceList, pet, petId);
                            case DOG_WALKING -> generateDogWalkingAddonAmounts();
                            default -> Stream.empty();
                        };
                    });
                })
                .toList();
    }

    private static Stream<CalculateServiceAmount> generateDogWalkingAddonAmounts() {
        // no addon for dog walking
        return Stream.of();
    }

    private static Stream<CalculateServiceAmount> generateGroomingAddonAmounts(
            com.moego.idl.client.online_booking.v1.Service.Grooming grooming,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            PetDTO pet,
            @Nullable Long petId) {
        return grooming.getAddonsList().stream()
                .map(e -> {
                    var customizedService = findCustomizedService(customizedServiceList, e.getId(), petId);
                    if (customizedService == null) {
                        return null;
                    }
                    return CalculateServiceAmount.builder()
                            .serviceId(customizedService.getId())
                            .serviceType(customizedService.getType())
                            .serviceItemType(customizedService.getServiceItemType())
                            .servicePrice(BigDecimal.valueOf(customizedService.getPrice()))
                            .petId(pet.petId())
                            .priceOverrideType(customizedService.getPriceOverrideType())
                            .date(grooming.getStartDate())
                            .build();
                })
                .filter(Objects::nonNull);
    }

    private static Stream<CalculateServiceAmount> generateBoardingAddonAmounts(
            com.moego.idl.client.online_booking.v1.Service.Boarding boarding,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            PetDTO pet,
            Long petId) {
        return boarding.getAddonsList().stream().flatMap(addon -> {
            var customizedService = findCustomizedService(customizedServiceList, addon.getId(), petId);
            if (customizedService == null) {
                return Stream.empty();
            }
            return Stream.generate(() -> CalculateServiceAmount.builder()
                            .serviceId(customizedService.getId())
                            .serviceType(customizedService.getType())
                            .serviceItemType(customizedService.getServiceItemType())
                            .servicePrice(BigDecimal.valueOf(customizedService.getPrice()))
                            .petId(pet.petId())
                            .priceOverrideType(customizedService.getPriceOverrideType())
                            .date(null)
                            .build())
                    .limit(calculateCount(boarding, addon));
        });
    }

    private static Stream<CalculateServiceAmount> generateDaycareAddonAmounts(
            com.moego.idl.client.online_booking.v1.Service.Daycare daycare,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            PetDTO pet,
            Long petId) {
        return daycare.getAddonsList().stream().flatMap(addon -> {
            var customizedService = findCustomizedService(customizedServiceList, addon.getId(), petId);
            if (customizedService == null) {
                return Stream.empty();
            }
            return Stream.generate(() -> CalculateServiceAmount.builder()
                            .serviceId(customizedService.getId())
                            .serviceType(customizedService.getType())
                            .serviceItemType(customizedService.getServiceItemType())
                            .servicePrice(BigDecimal.valueOf(customizedService.getPrice()))
                            .petId(pet.petId())
                            .priceOverrideType(customizedService.getPriceOverrideType())
                            .date(null)
                            .build())
                    .limit(calculateCount(daycare, addon));
        });
    }

    private static boolean isPerDayBoardingService(CustomizedServiceView customizedService) {
        return customizedService.getServiceItemType() == ServiceItemType.BOARDING
                && customizedService.getPriceUnit() == ServicePriceUnit.PER_DAY;
    }

    @Nullable
    private static CustomizedServiceView findCustomizedService(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            Long serviceId,
            @Nullable Long petId) {
        return customizedServiceList.stream()
                .filter(e -> {
                    var cond = e.getQueryCondition();
                    return serviceId == cond.getServiceId()
                            && (!isNormal(petId) && !isNormal(cond.getPetId())
                                    || isNormal(petId) && petId == cond.getPetId());
                })
                .findFirst()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .orElse(null);
    }

    private static long calculateCount(
            com.moego.idl.client.online_booking.v1.Service.Boarding service, BoardingAddon addon) {
        var start = service.getStartDate();
        var end = service.getEndDate();

        if (!addon.hasDateType()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "date type is required for boarding service add-on");
        }

        long days =
                switch (addon.getDateType()) {
                    case PET_DETAIL_DATE_EVERYDAY, PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> LocalDate.parse(start)
                            .datesUntil(LocalDate.parse(end))
                            .count();
                    case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> LocalDate.parse(start)
                            .datesUntil(LocalDate.parse(end).plusDays(1))
                            .count();
                    case PET_DETAIL_DATE_LAST_DAY -> 1;
                    case PET_DETAIL_DATE_FIRST_DAY -> 1;
                    case PET_DETAIL_DATE_SPECIFIC_DATE -> addon.getDatesCount();
                    case PET_DETAIL_DATE_DATE_POINT -> 1;
                    default -> throw bizException(
                            Code.CODE_PARAMS_ERROR, "date type is invalid: " + addon.getDateType());
                };

        return days * getQuantityPerDay(addon);
    }

    private static int calculateCount(
            com.moego.idl.client.online_booking.v1.Service.Daycare service, DaycareAddon addon) {
        if (addon.getIsEveryDay()) {
            return service.getDatesCount() * getQuantityPerDay(addon);
        }
        return addon.getDatesCount() * getQuantityPerDay(addon);
    }

    private static int getQuantityPerDay(BoardingAddon addon) {
        return addon.hasQuantityPerDay() ? addon.getQuantityPerDay() : 1;
    }

    private static int getQuantityPerDay(DaycareAddon addon) {
        return addon.hasQuantityPerDay() ? addon.getQuantityPerDay() : 1;
    }

    private Collection<CalculateServiceAmount> applyPricingRule(
            Collection<CalculateServiceAmount> serviceAmountDTOList, long companyId, int businessId) {

        var petDetails = listPetDetailWithPricingRuleApplied(companyId, businessId, serviceAmountDTOList);
        if (petDetails.isEmpty()) {
            return serviceAmountDTOList;
        }

        return getUsingPricingRuleService(serviceAmountDTOList, petDetails);
    }

    @NotNull
    private static List<CalculateServiceAmount> getUsingPricingRuleService(
            Collection<CalculateServiceAmount> serviceAmountDTOList, List<PetDetailCalculateResultDef> petDetails) {
        return serviceAmountDTOList.stream()
                .map(amount -> {
                    if (amount.priceOverrideType() == ServiceOverrideType.CLIENT) {
                        // override by client 优先级比 pricing rule 更高
                        // See
                        // https://moego.atlassian.net/wiki/spaces/~************************/pages/*********/Service+by+Staff+-+PRD#Detail
                        return amount;
                    }

                    var petDetail = findMatchingPetDetail(petDetails, amount);
                    if (petDetail == null) {
                        return amount;
                    }

                    return amount.toBuilder()
                            .servicePrice(BigDecimal.valueOf(petDetail.getAdjustedPrice()))
                            .build();
                })
                .toList();
    }

    private static PetDetailCalculateResultDef findMatchingPetDetail(
            List<PetDetailCalculateResultDef> petDetails, CalculateServiceAmount amount) {
        return petDetails.stream()
                .filter(detail -> Objects.equals(detail.getPetId(), amount.petId())
                        && Objects.equals(detail.getServiceId(), amount.serviceId())
                        && Objects.equals(detail.getServiceDate(), amount.date()))
                .findFirst()
                .orElse(null);
    }

    private List<PetDetailCalculateResultDef> listPetDetailWithPricingRuleApplied(
            long companyId, int businessId, Collection<CalculateServiceAmount> serviceAmountDTOList) {

        var pricingRuleEligibleServices = serviceAmountDTOList.stream()
                .filter(CashierService::isPricingRuleEligible)
                .map(amount -> PetDetailCalculateDef.newBuilder()
                        .setPetId(amount.petId())
                        .setServiceId(amount.serviceId())
                        .setServicePrice(amount.servicePrice().doubleValue())
                        .setServiceDate(amount.date())
                        .build())
                .toList();

        if (pricingRuleEligibleServices.isEmpty()) {
            return List.of();
        }

        return pricingRuleStub
                .calculatePricingRule(CalculatePricingRuleRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllPetDetails(pricingRuleEligibleServices)
                        .build())
                .getPetDetailsList();
    }

    private static boolean isPricingRuleEligible(CalculateServiceAmount amount) {
        return (amount.serviceItemType() == ServiceItemType.BOARDING
                        || amount.serviceItemType() == ServiceItemType.DAYCARE)
                && !Objects.equals(ServiceOverrideType.CLIENT, amount.priceOverrideType());
    }
}
