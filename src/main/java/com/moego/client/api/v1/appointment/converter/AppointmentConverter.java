package com.moego.client.api.v1.appointment.converter;

import static com.moego.idl.models.online_booking.v1.BookingRequestModel.PaymentStatus.NO_PAYMENT;
import static com.moego.idl.models.online_booking.v1.BookingRequestModel.PaymentStatus.PROCESSING;
import static com.moego.idl.models.online_booking.v1.BookingRequestModel.PaymentStatus.SUCCESS;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.idl.client.appointment.v1.AppointmentDetailView;
import com.moego.idl.client.appointment.v1.ClientAppointmentStatus;
import com.moego.idl.client.appointment.v1.OrderApplyPackage;
import com.moego.idl.client.appointment.v1.OrderItem;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentPaymentStatus;
import com.moego.idl.models.appointment.v1.AppointmentPetFeedingScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.AppointmentTrackingView;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.PetDetailModelClientView;
import com.moego.idl.models.appointment.v1.PetEvaluationDetailClientView;
import com.moego.idl.models.business.v1.StaffModelClientView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModelClientView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModelClientView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModelClientView;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import com.moego.idl.models.marketing.v1.DiscountCodeCompositeView;
import com.moego.idl.models.marketing.v1.DiscountCodeModelOnlineBookingView;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.EvaluationServiceClientView;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceClientView;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.FeedingModel;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.dto.PackageServiceDTO;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @since 2024/6/22
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AppointmentConverter {
    // also used by moego-server-message/src/main/java/com/moego/server/message/consts/AppointmentStatusConvert.java
    // plz check it after changing
    Map<AppointmentStatus, ClientAppointmentStatus> STATUS_MAP = Map.of(
            AppointmentStatus.UNCONFIRMED, ClientAppointmentStatus.UNCONFIRMED,
            AppointmentStatus.CONFIRMED, ClientAppointmentStatus.UPCOMING,
            AppointmentStatus.CHECKED_IN, ClientAppointmentStatus.IN_PROGRESS,
            AppointmentStatus.READY, ClientAppointmentStatus.READY_TO_PICK_UP,
            AppointmentStatus.FINISHED, ClientAppointmentStatus.FINISHED,
            AppointmentStatus.CANCELED, ClientAppointmentStatus.CANCELLED);

    default ClientAppointmentStatus toClientStatus(AppointmentModel model) {
        if (Objects.equals(model.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB.intValue())) {
            return ClientAppointmentStatus.PENDING;
        }
        return STATUS_MAP.get(model.getStatus());
    }

    @Named("convertBookingRequestPaymentStatus")
    static AppointmentPaymentStatus convertBookingRequestPaymentStatus(BookingRequestModel.PaymentStatus status) {
        return switch (status) {
            case NO_PAYMENT -> AppointmentPaymentStatus.UNPAID;
            case PROCESSING, SUCCESS -> AppointmentPaymentStatus.PREPAID;
            default -> AppointmentPaymentStatus.APPOINTMENT_PAYMENT_STATUS_UNSPECIFIED;
        };
    }

    @Mapping(target = "status", expression = "java(toClientStatus(model))")
    AppointmentDetailView toView(AppointmentModel model);

    @Mapping(
            target = "status",
            expression = "java(com.moego.idl.client.appointment.v1.ClientAppointmentStatus.PENDING)")
    @Mapping(target = "id", source = "appointmentId")
    @Mapping(target = "bookingRequestId", source = "id")
    @Mapping(target = "appointmentDate", source = "startDate")
    @Mapping(target = "appointmentEndDate", source = "endDate")
    @Mapping(target = "appointmentStartTime", source = "startTime")
    @Mapping(target = "appointmentEndTime", source = "endTime")
    @Mapping(target = "paymentStatus", source = "paymentStatus", qualifiedByName = "convertBookingRequestPaymentStatus")
    AppointmentDetailView toView(BookingRequestModel model);

    // use afterMapping
    @Mapping(target = "specificDates", ignore = true)
    PetDetailModelClientView toView(PetDetailModel model);

    ServiceClientView toView(ServiceBriefView view);

    StaffModelClientView toView(StaffModel model);

    BusinessCustomerPetModelClientView toView(BusinessCustomerPetInfoModel model);

    BusinessCustomerModelClientView toView(BusinessCustomerInfoModel model);

    BusinessCustomerAddressModelClientView toView(BusinessCustomerAddressModel model);

    DiscountCodeModelOnlineBookingView toView(DiscountCodeCompositeView view);

    OrderItem toView(OrderLineItemModel model);

    OrderApplyPackage toView(PackageServiceDTO dto);

    AppointmentTrackingView toView(com.moego.idl.models.appointment.v1.AppointmentTracking model);

    EvaluationServiceClientView toView(EvaluationBriefView model);

    List<EvaluationServiceClientView> toView(List<EvaluationBriefView> models);

    PetEvaluationDetailClientView toView(EvaluationServiceModel model);

    @Mapping(target = ".", source = "service")
    PetEvaluationDetailClientView toView(BookingRequestModel.EvaluationService model);

    default AppointmentPetFeedingScheduleDef toAppointmentPetFeedingScheduleDef(FeedingModel model) {
        return AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingAmount(model.getAmountStr())
                .setFeedingUnit(model.getUnit())
                .setFeedingType(model.getFoodType())
                .setFeedingSource(model.getFoodSource())
                .setFeedingNote(model.getNote())
                .setFeedingInstruction(model.getInstruction())
                .addAllFeedingTimes(model.getTimeList().stream()
                        .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(time.getTime())
                                .putExtraJson("label", time.getLabel())
                                .build())
                        .toList())
                .build();
    }

    List<AppointmentPetFeedingScheduleDef> toAppointmentPetFeedingScheduleDefs(List<FeedingModel> models);

    default AppointmentPetMedicationScheduleDef toAppointmentPetMedicationScheduleDef(MedicationModel model) {
        return AppointmentPetMedicationScheduleDef.newBuilder()
                .setMedicationName(model.getMedicationName())
                .setMedicationAmount(model.getAmountStr())
                .setMedicationUnit(model.getUnit())
                .setMedicationNote(model.getNotes())
                .addAllMedicationTimes(model.getTimeList().stream()
                        .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(time.getTime())
                                .putExtraJson("label", time.getLabel())
                                .build())
                        .toList())
                .build();
    }

    List<AppointmentPetMedicationScheduleDef> toAppointmentPetMedicationScheduleDefs(List<MedicationModel> models);

    static List<String> jsonToStringList(String json) {
        if (json == null || json.isEmpty()) {
            return List.of();
        }
        Logger logger = LoggerFactory.getLogger(AppointmentConverter.class);
        try {
            return JsonUtil.toBean(json, new TypeRef<>() {});
        } catch (Exception e) {
            logger.error("Failed to convert json to string array", e);
            return List.of();
        }
    }

    @AfterMapping
    default void afterMapping(PetDetailModel source, @MappingTarget PetDetailModelClientView.Builder target) {
        if (target != null) {
            target.addAllSpecificDates(jsonToStringList(source.getSpecificDates()))
                    .build();
        }
    }
}
