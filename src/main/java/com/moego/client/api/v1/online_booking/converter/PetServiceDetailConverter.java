package com.moego.client.api.v1.online_booking.converter;

import com.moego.idl.client.online_booking.v1.PetServices;
import com.moego.idl.client.online_booking.v1.Service;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetail;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetail;
import com.moego.idl.models.online_booking.v1.DogWalkingServiceDetail;
import com.moego.idl.models.online_booking.v1.EvaluationServiceDetail;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetail;
import com.moego.idl.models.online_booking.v1.GroupClassServiceDetail;
import com.moego.idl.models.online_booking.v1.Pet;
import com.moego.idl.models.online_booking.v1.PetServiceDetails;
import com.moego.idl.models.online_booking.v1.ServiceDetail;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface PetServiceDetailConverter {
    PetServiceDetailConverter INSTANCE = Mappers.getMapper(PetServiceDetailConverter.class);

    default List<PetServiceDetails> toModels(List<PetServices> petServices) {
        if (CollectionUtils.isEmpty(petServices)) {
            return null;
        }

        List<PetServiceDetails> list = new ArrayList<PetServiceDetails>(petServices.size());
        for (PetServices petService : petServices) {
            var nps = serviceListToModel(petService);
            if (nps != null) {
                list.add(nps);
            }
        }

        return list;
    }

    private PetServiceDetails serviceListToModel(PetServices petServices) {
        if (petServices == null) {
            return null;
        }

        PetServiceDetails.Builder petServiceDetails = PetServiceDetails.newBuilder();

        for (Service service : petServices.getServicesList()) {
            var newService = serviceToModel(service);
            if (newService != null) {
                petServiceDetails.addServiceDetails(newService);
            }
        }
        if (petServices.hasPet()) {
            petServiceDetails.setPet(toModel(petServices.getPet()));
        }

        petServiceDetails.setIsNewPet(
                !com.moego.common.utils.CommonUtil.isNormal(petServices.getPet().getPetId()));

        return petServiceDetails.build();
    }

    @Nullable
    private ServiceDetail serviceToModel(Service service) {
        if (service == null) {
            return null;
        }
        ServiceDetail.Builder builder = ServiceDetail.newBuilder();
        return switch (service.getServiceCase()) {
            case GROOMING -> builder.setGrooming(toModel(service.getGrooming())).build();
            case BOARDING -> {
                if (service.getBoarding().hasWaitlist()) {
                    yield null;
                }
                yield builder.setBoarding(toModel(service.getBoarding())).build();
            }
            case DAYCARE -> {
                if (service.getDaycare().hasWaitlist()) {
                    yield null;
                }
                yield builder.setDaycare(toModel(service.getDaycare())).build();
            }
            case EVALUATION -> builder.setEvaluation(toModel(service.getEvaluation()))
                    .build();
            case DOG_WALKING -> builder.setDogWalking(toModel(service.getDogWalking()))
                    .build();
            case GROUP_CLASS -> builder.setGroupClass(toModel(service.getGroupClass()))
                    .build();
            case SERVICE_NOT_SET -> null;
        };
    }

    @Mapping(
            target = "isNewPet",
            expression = "java(!com.moego.common.utils.CommonUtil.isNormal(petServices.getPet().getPetId()))")
    @Mapping(target = "serviceDetails", source = "services")
    PetServiceDetails toModel(PetServices petServices);

    Pet toModel(com.moego.idl.client.online_booking.v1.Pet pet);

    @Mapping(
            target = "grooming",
            conditionExpression = "java(service.getServiceCase() == Service.ServiceCase.GROOMING)")
    @Mapping(
            target = "boarding",
            conditionExpression = "java(service.getServiceCase() == Service.ServiceCase.BOARDING)")
    @Mapping(target = "daycare", conditionExpression = "java(service.getServiceCase() == Service.ServiceCase.DAYCARE)")
    @Mapping(
            target = "evaluation",
            conditionExpression = "java(service.getServiceCase() == Service.ServiceCase.EVALUATION)")
    @Mapping(
            target = "dogWalking",
            conditionExpression = "java(service.getServiceCase() == Service.ServiceCase.DOG_WALKING)")
    @Mapping(
            target = "groupClass",
            conditionExpression = "java(service.getServiceCase() == Service.ServiceCase.GROUP_CLASS)")
    ServiceDetail toModel(Service service);

    GroomingServiceDetail toModel(Service.Grooming grooming);

    BoardingServiceDetail toModel(Service.Boarding boarding);

    DaycareServiceDetail toModel(Service.Daycare daycare);

    EvaluationServiceDetail toModel(Service.Evaluation evaluation);

    DogWalkingServiceDetail toModel(Service.DogWalking dogwalking);

    GroupClassServiceDetail toModel(Service.GroupClass groupClass);
}
