package com.moego.client.api.v1.shared.helper;

import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetAppointmentRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/6/17
 */
@Component
@RequiredArgsConstructor
public class AppointmentHelper {

    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;

    /**
     * Get appointment by id, throw exception if not found.
     *
     * @param appointmentId appointment id
     * @return appointment
     */
    public AppointmentModel mustGetAppointment(long appointmentId) {
        var resp = appointmentStub.getAppointment(GetAppointmentRequest.newBuilder()
                .setAppointmentId(appointmentId)
                .build());
        if (!resp.hasAppointment()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Appointment not found: " + appointmentId);
        }
        return resp.getAppointment();
    }
}
