package com.moego.client.api.v1.online_booking.utils;

import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
public class PaymentUtil {

    /**
     * Calculate the estimated total price of the booking request
     *
     * @param services the booking request services
     * @return the estimated total price
     */
    public static double calculateEstimatedTotalPrice(List<BookingRequestModel.Service> services) {
        return services.stream()
                .map(service -> switch (service.getServiceCase()) {
                    case GROOMING -> calculateGroomingPrice(service.getGrooming());
                    case BOARDING -> calculateBoardingPrice(service.getBoarding());
                    case DAYCARE -> calculateDaycarePrice(service.getDaycare());
                    case EVALUATION -> calculateEvaluationPrice(service.getEvaluation());
                    default -> BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .doubleValue();
    }

    private static BigDecimal calculateGroomingPrice(BookingRequestModel.GroomingService grooming) {
        var total = new ArrayList<BigDecimal>();

        total.add(BigDecimal.valueOf(grooming.getService().getServicePrice()));

        grooming.getAddonsList().forEach(addOn -> total.add(BigDecimal.valueOf(addOn.getServicePrice())));

        return total.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static BigDecimal calculateDaycarePrice(BookingRequestModel.DaycareService daycare) {
        var total = new ArrayList<BigDecimal>();
        var service = daycare.getService();

        total.add(BigDecimal.valueOf(service.getServicePrice())
                .multiply(BigDecimal.valueOf(service.getSpecificDatesCount())));

        daycare.getAddonsList()
                .forEach(addOn -> total.add(BigDecimal.valueOf(addOn.getServicePrice())
                        .multiply(BigDecimal.valueOf(calculateDays(
                                addOn.getIsEveryday(), addOn.getSpecificDatesCount(), service.getSpecificDatesCount())))
                        .multiply(BigDecimal.valueOf(addOn.getQuantityPerDay()))));

        return total.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static BigDecimal calculateBoardingPrice(BookingRequestModel.BoardingService boarding) {
        var total = new ArrayList<BigDecimal>();
        var service = boarding.getService();
        var serviceDays =
                ChronoUnit.DAYS.between(LocalDate.parse(service.getStartDate()), LocalDate.parse(service.getEndDate()));
        total.add(BigDecimal.valueOf(service.getServicePrice()).multiply(BigDecimal.valueOf(serviceDays)));

        boarding.getAddonsList()
                .forEach(addOn -> total.add(BigDecimal.valueOf(addOn.getServicePrice())
                        .multiply(BigDecimal.valueOf(
                                calculateDays(addOn.getIsEveryday(), addOn.getSpecificDatesCount(), serviceDays)))
                        .multiply(BigDecimal.valueOf(addOn.getQuantityPerDay()))));

        return total.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static long calculateDays(boolean isEveryday, int addOnDays, long serviceDays) {
        if (isEveryday) {
            return serviceDays;
        } else if (addOnDays > 0) {
            return addOnDays;
        } else {
            return 1;
        }
    }

    private static BigDecimal calculateEvaluationPrice(BookingRequestModel.EvaluationService evaluation) {
        return BigDecimal.valueOf(evaluation.getService().getServicePrice());
    }
}
