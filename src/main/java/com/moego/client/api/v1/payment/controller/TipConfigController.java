package com.moego.client.api.v1.payment.controller;

import com.moego.client.api.v1.payment.mapper.TipConfigMapper;
import com.moego.idl.client.payment.v1.GetTipConfigRequest;
import com.moego.idl.client.payment.v1.GetTipConfigResponse;
import com.moego.idl.client.payment.v1.TipConfigServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.payment.client.IPaymentSettingClient;
import com.moego.server.payment.dto.SmartTipConfigDTO;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/10/13
 */
@GrpcService
@RequiredArgsConstructor
public class TipConfigController extends TipConfigServiceGrpc.TipConfigServiceImplBase {

    private final IPaymentSettingClient paymentSettingClient;

    private final TipConfigMapper tipConfigMapper;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getTipConfig(GetTipConfigRequest request, StreamObserver<GetTipConfigResponse> responseObserver) {
        SmartTipConfigDTO dto = paymentSettingClient.getSmartTipConfig(Math.toIntExact(request.getBusinessId()));

        responseObserver.onNext(GetTipConfigResponse.newBuilder()
                .setConfig(tipConfigMapper.dtoToView(dto))
                .build());
        responseObserver.onCompleted();
    }
}
