package com.moego.client.api.v1.online_booking.service;

import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.lib.common.auth.AuthContext;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ContextService {
    private final IGroomingOnlineBookingService onlineBookingApi;

    public BaseBusinessCustomerIdDTO buildOnlineBookingContext(String domain, String obName) {
        var obBusiness = onlineBookingApi.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(domain).setName(obName));
        BaseBusinessCustomerIdDTO result = new BaseBusinessCustomerIdDTO();
        result.setCompanyId(obBusiness.getCompanyId());
        result.setBusinessId(obBusiness.getBusinessId());
        result.setCustomerId(AuthContext.get().customerId().intValue());
        return result;
    }
}
