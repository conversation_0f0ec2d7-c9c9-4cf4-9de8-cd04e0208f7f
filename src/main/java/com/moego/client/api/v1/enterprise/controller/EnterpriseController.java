package com.moego.client.api.v1.enterprise.controller;

import static java.util.stream.Collectors.groupingBy;

import com.moego.client.api.v1.branded_app.BrandedAppService;
import com.moego.client.api.v1.business.service.BusinessService;
import com.moego.client.api.v1.enterprise.converter.BrandedAppConfigConverter;
import com.moego.client.api.v1.enterprise.converter.BusinessConverter;
import com.moego.client.api.v1.enterprise.service.CompanyService;
import com.moego.client.api.v1.online_booking.converter.BookingQuestionConverter;
import com.moego.idl.client.enterprise.v1.EnterpriseServiceGrpc;
import com.moego.idl.client.enterprise.v1.GetBrandedAppConfigParams;
import com.moego.idl.client.enterprise.v1.GetBrandedAppConfigResult;
import com.moego.idl.client.enterprise.v1.ListBrandedQuestionsParams;
import com.moego.idl.client.enterprise.v1.ListBrandedQuestionsResult;
import com.moego.idl.client.enterprise.v1.ListCompaniesParams;
import com.moego.idl.client.enterprise.v1.ListCompaniesResult;
import com.moego.idl.models.branded_app.v1.BrandedLocationModel;
import com.moego.idl.models.organization.v1.CompanyBriefView;
import com.moego.idl.models.organization.v1.LocationModel;
import com.moego.idl.service.branded_app.v1.BrandedAppConfigServiceGrpc.BrandedAppConfigServiceBlockingStub;
import com.moego.idl.service.branded_app.v1.GetBrandedAppPackConfigRequest;
import com.moego.idl.service.branded_app.v1.GetDefaultThemeConfigRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.ListLocationsRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IBookOnlineQuestionService;
import com.moego.server.grooming.client.IBookOnlineQuestionClient;
import io.grpc.stub.StreamObserver;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/6/6
 */
@GrpcService
@RequiredArgsConstructor
public class EnterpriseController extends EnterpriseServiceGrpc.EnterpriseServiceImplBase {

    private final BusinessService businessService;
    private final CompanyService companyService;
    private final BrandedAppConfigConverter brandedAppConfigConverter;
    private final BookingQuestionConverter bookingQuestionConverter;
    private final IBookOnlineQuestionClient bookOnlineQuestionClient;
    private final BrandedAppService brandedAppService;
    private final BrandedAppConfigServiceBlockingStub brandedAppConfigService;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void listCompanies(ListCompaniesParams request, StreamObserver<ListCompaniesResult> responseObserver) {
        String brandedAppId = AuthContext.get().brandedAppId();
        var brandedAppConfig = brandedAppService.mustGetBrandedAppConfig(brandedAppId);
        var locations = companyService.listAvailableLocations(brandedAppId);
        var businesses = businessServiceBlockingStub
                .listLocations(ListLocationsRequest.newBuilder()
                        .setFilter(ListLocationsRequest.Filter.newBuilder()
                                .addAllCompanyIds(locations.stream()
                                        .map(BrandedLocationModel::getCompanyId)
                                        .distinct()
                                        .toList())
                                .build())
                        .build())
                .getLocationsList();
        Map<Long, ListCompaniesResult.BusinessList> companyToBusinesses = new HashMap<>();
        var cToB = businesses.stream()
                .collect(groupingBy(
                        LocationModel::getCompanyId,
                        Collectors.mapping(BusinessConverter.INSTANCE::toView, Collectors.toList())));
        cToB.forEach((k, v) -> {
            companyToBusinesses.put(
                    k,
                    ListCompaniesResult.BusinessList.newBuilder()
                            .addAllBusinesses(v)
                            .build());
        });

        responseObserver.onNext(ListCompaniesResult.newBuilder()
                .addAllCompanies(locations.stream()
                        .map(location -> CompanyBriefView.newBuilder()
                                .setId(location.getCompanyId())
                                .setName(location.getLocationName())
                                .setEnterpriseId(brandedAppConfig.getBrandedId())
                                .build())
                        .toList())
                .putAllCompanyToBusinesses(companyToBusinesses)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void getBrandedAppConfig(
            GetBrandedAppConfigParams request, StreamObserver<GetBrandedAppConfigResult> responseObserver) {
        var config = brandedAppService.mustGetBrandedAppConfig(AuthContext.get().brandedAppId());

        var defaultTheme = brandedAppConfigService
                .getDefaultThemeConfig(GetDefaultThemeConfigRequest.newBuilder()
                        .setBrandedAppId(AuthContext.get().brandedAppId())
                        .build())
                .getDefaultTheme();
        var packConfig = brandedAppConfigService
                .getBrandedAppPackConfig(GetBrandedAppPackConfigRequest.newBuilder()
                        .setBrandedAppId(AuthContext.get().brandedAppId())
                        .build())
                .getPackConfig();

        responseObserver.onNext(GetBrandedAppConfigResult.newBuilder()
                .setConfig(brandedAppConfigConverter.toView(config))
                .setDefaultTheme(defaultTheme)
                .setPackConfig(brandedAppConfigConverter.toView(packConfig))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void listBrandedQuestions(
            ListBrandedQuestionsParams request, StreamObserver<ListBrandedQuestionsResult> responseObserver) {
        var businessId = businessService.getFirstBusinessId(request.getCompanyId());

        var questions =
                bookOnlineQuestionClient.listByCondition(IBookOnlineQuestionService.ListByConditionParam.builder()
                        .businessId(businessId.intValue())
                        .type(request.getTypeValue())
                        .build());

        responseObserver.onNext(ListBrandedQuestionsResult.newBuilder()
                .addAllQuestions(questions.stream()
                        .map(bookingQuestionConverter::toModel)
                        .toList())
                .build());
        responseObserver.onCompleted();
    }
}
