package com.moego.client.api.v1.online_booking.converter;

import com.moego.idl.client.online_booking.v1.AppointmentSummaryItem;
import com.moego.idl.client.online_booking.v1.GetAppointmentDetailResult;
import com.moego.idl.client.online_booking.v1.ReschedulePetFeedingMedicationParams;
import com.moego.idl.client.online_booking.v1.UpdateAppointmentParams;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.service.online_booking.v1.CreateFeedingRequestList;
import com.moego.idl.service.online_booking.v1.CreateMedicationRequestList;
import com.moego.idl.service.online_booking.v1.UpdateBoardingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateBoardingServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.UpdateDaycareAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateDaycareServiceDetailRequest;
import com.moego.idl.utils.v1.StringListValue;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        uses = AppointmentConverter.class)
public interface BookingRequestConverter {

    BookingRequestConverter INSTANCE = Mappers.getMapper(BookingRequestConverter.class);

    @Mapping(target = "appointmentId", ignore = true)
    @Mapping(target = "bookingRequestId", source = "id")
    @Mapping(target = "isBookingRequest", expression = "java(true)")
    @Mapping(target = "mainCareType", source = "serviceTypeInclude", qualifiedByName = "toMainCareType")
    AppointmentSummaryItem toAppointmentSummary(BookingRequestModel model);

    @Mapping(target = "appointmentId", ignore = true)
    @Mapping(target = "bookingRequestId", source = "id")
    @Mapping(target = "isBookingRequest", expression = "java(true)")
    @Mapping(target = "mainCareType", source = "serviceTypeInclude", qualifiedByName = "toMainCareType")
    GetAppointmentDetailResult.AppointmentItem toAppointmentDetail(BookingRequestModel model);

    default BookingRequestStatus toBookingRequestStatus(int value) {
        return BookingRequestStatus.forNumber(value);
    }

    static UpdateBookingRequestRequest toUpdateBookingRequestRequest(
            ReschedulePetFeedingMedicationParams request, BookingRequestModel bookingRequest) {
        // petDetailId -> schedule
        var boardingSchedules = request.getSchedulesList().stream()
                .filter(k -> k.getCareType() == ServiceItemType.BOARDING)
                .collect(Collectors.toMap(
                        ReschedulePetFeedingMedicationParams.PetScheduleDef::getPetDetailId, k -> k, (k1, k2) -> k1));

        var daycareSchedules = request.getSchedulesList().stream()
                .filter(k -> k.getCareType() == ServiceItemType.DAYCARE)
                .collect(Collectors.toMap(
                        ReschedulePetFeedingMedicationParams.PetScheduleDef::getPetDetailId, k -> k, (k1, k2) -> k1));

        var builder = UpdateBookingRequestRequest.newBuilder().setId(request.getBookingRequestId());

        for (var petService : bookingRequest.getServicesList()) {
            switch (petService.getServiceCase()) {
                case BOARDING -> {
                    var boardingDetail = petService.getBoarding().getService();
                    var tmp = boardingSchedules.get(boardingDetail.getId());
                    if (tmp != null) {
                        builder.addServices(toBoardingUpdateService(tmp, boardingDetail.getId()));
                    }
                }
                case DAYCARE -> {
                    var daycareDetail = petService.getDaycare().getService();
                    var tmp = daycareSchedules.get(daycareDetail.getId());
                    if (tmp != null) {
                        builder.addServices(toDaycareUpdateService(tmp, daycareDetail.getId()));
                    }
                }
                default -> {}
            }
        }
        return builder.build();
    }

    static UpdateBookingRequestRequest toUpdateBookingRequestRequest(
            UpdateAppointmentParams request, BookingRequestModel bookingRequest) {
        Map<Long, UpdateAppointmentParams.UpdateServiceDetailParams> daycareServiceDetailToUpdate = new HashMap<>();
        Map<Long, UpdateAppointmentParams.UpdateServiceDetailParams> boardingServiceDetailToUpdate = new HashMap<>();
        Map<Long, UpdateAppointmentParams.UpdateAddOnDetailParams> daycareAddOnDetailToUpdate = new HashMap<>();
        Map<Long, UpdateAppointmentParams.UpdateAddOnDetailParams> boardingAddOnDetailToUpdate = new HashMap<>();
        for (var petServiceUpdate : request.getPetAndServicesList()) {
            for (var serviceDetail : petServiceUpdate.getServicesList()) {
                switch (serviceDetail.getCareType()) {
                    case DAYCARE -> daycareServiceDetailToUpdate.put(serviceDetail.getPetDetailId(), serviceDetail);
                    case BOARDING -> boardingServiceDetailToUpdate.put(serviceDetail.getPetDetailId(), serviceDetail);
                    default -> {}
                }
            }
            for (var addOnDetail : petServiceUpdate.getAddOnsList()) {
                switch (addOnDetail.getCareType()) {
                    case DAYCARE -> daycareAddOnDetailToUpdate.put(addOnDetail.getPetDetailId(), addOnDetail);
                    case BOARDING -> boardingAddOnDetailToUpdate.put(addOnDetail.getPetDetailId(), addOnDetail);
                    default -> {}
                }
            }
        }

        var updateBuilder = com.moego.idl.service.online_booking.v1.UpdateBookingRequestRequest.newBuilder()
                .setId(request.getBookingRequestId());
        for (var service : bookingRequest.getServicesList()) {
            switch (service.getServiceCase()) {
                case BOARDING -> {
                    var serviceToUpdate = boardingServiceDetailToUpdate.get(
                            service.getBoarding().getService().getId());
                    var addOnsToUpdate = service.getBoarding().getAddonsList().stream()
                            .map(k -> boardingAddOnDetailToUpdate.get(k.getId()))
                            .filter(Objects::nonNull)
                            .toList();
                    if (serviceToUpdate != null || !CollectionUtils.isEmpty(addOnsToUpdate)) {
                        updateBuilder.addServices(BookingRequestConverter.toUpdateBookingRequestService(
                                ServiceItemType.BOARDING, serviceToUpdate, addOnsToUpdate));
                    }
                }
                case DAYCARE -> {
                    var serviceToUpdate = daycareServiceDetailToUpdate.get(
                            service.getDaycare().getService().getId());
                    var addOnsToUpdate = service.getDaycare().getAddonsList().stream()
                            .map(k -> daycareAddOnDetailToUpdate.get(k.getId()))
                            .filter(Objects::nonNull)
                            .toList();
                    if (serviceToUpdate != null || !CollectionUtils.isEmpty(addOnsToUpdate)) {
                        updateBuilder.addServices(BookingRequestConverter.toUpdateBookingRequestService(
                                ServiceItemType.DAYCARE, serviceToUpdate, addOnsToUpdate));
                    }
                }
                default -> {}
            }
        }
        return updateBuilder.build();
    }

    static UpdateBookingRequestRequest.Service toUpdateBookingRequestService(
            ServiceItemType serviceItemType,
            UpdateAppointmentParams.UpdateServiceDetailParams serviceDetailParam,
            List<UpdateAppointmentParams.UpdateAddOnDetailParams> addOnDetailParams) {
        var serviceBuilder = UpdateBookingRequestRequest.Service.newBuilder();
        switch (serviceItemType) {
            case BOARDING -> {
                var boardingServiceBuilder = UpdateBookingRequestRequest.BoardingService.newBuilder();
                if (serviceDetailParam != null) {
                    boardingServiceBuilder.setService(toUpdateBoardingServiceDetailRequest(serviceDetailParam));
                }
                if (!CollectionUtils.isEmpty(addOnDetailParams)) {
                    boardingServiceBuilder.addAllAddons(addOnDetailParams.stream()
                            .map(BookingRequestConverter::toUpdateBoardingAddOnDetailRequest)
                            .toList());
                }
                serviceBuilder.setBoarding(boardingServiceBuilder.build());
            }
            case DAYCARE -> {
                var daycareServiceBuilder = UpdateBookingRequestRequest.DaycareService.newBuilder();
                if (serviceDetailParam != null) {
                    daycareServiceBuilder.setService(toUpdateDaycareServiceDetailRequest(serviceDetailParam));
                }
                if (!CollectionUtils.isEmpty(addOnDetailParams)) {
                    daycareServiceBuilder.addAllAddons(addOnDetailParams.stream()
                            .map(BookingRequestConverter::toUpdateDaycareAddOnDetailRequest)
                            .toList());
                }
                serviceBuilder.setDaycare(daycareServiceBuilder.build());
            }
            default -> {}
        }
        return serviceBuilder.build();
    }

    static UpdateDaycareServiceDetailRequest toUpdateDaycareServiceDetailRequest(
            UpdateAppointmentParams.UpdateServiceDetailParams params) {
        var builder = UpdateDaycareServiceDetailRequest.newBuilder().setId(params.getPetDetailId());
        if (params.hasDateType()) {
            builder.setSpecificDates(StringListValue.newBuilder()
                    .addAllValues(params.getSpecificDatesList())
                    .build());
        }
        if (params.hasStartTime()) {
            builder.setStartTime(params.getStartTime());
        }
        if (params.hasEndTime()) {
            builder.setEndTime(params.getEndTime());
        }
        return builder.build();
    }

    static UpdateBoardingServiceDetailRequest toUpdateBoardingServiceDetailRequest(
            UpdateAppointmentParams.UpdateServiceDetailParams params) {
        var builder = UpdateBoardingServiceDetailRequest.newBuilder().setId(params.getPetDetailId());
        if (params.hasStartDate()) {
            builder.setStartDate(params.getStartDate());
        }
        if (params.hasEndDate()) {
            builder.setEndDate(params.getEndDate());
        }
        if (params.hasStartTime()) {
            builder.setStartTime(params.getStartTime());
        }
        if (params.hasEndTime()) {
            builder.setEndTime(params.getEndTime());
        }
        return builder.build();
    }

    static UpdateDaycareAddOnDetailRequest toUpdateDaycareAddOnDetailRequest(
            UpdateAppointmentParams.UpdateAddOnDetailParams params) {
        var builder = UpdateDaycareAddOnDetailRequest.newBuilder().setId(params.getPetDetailId());
        if (params.hasDateType()) {
            if (params.getDateType() == PetDetailDateType.PET_DETAIL_DATE_EVERYDAY) {
                builder.setIsEveryday(true);
            } else {
                builder.setSpecificDates(StringListValue.newBuilder()
                        .addAllValues(params.getSpecificDatesList())
                        .build());
            }
        }
        if (params.hasQuantityPerDay()) {
            builder.setQuantityPerDay(params.getQuantityPerDay());
        }
        return builder.build();
    }

    static UpdateBoardingAddOnDetailRequest toUpdateBoardingAddOnDetailRequest(
            UpdateAppointmentParams.UpdateAddOnDetailParams params) {
        var builder = UpdateBoardingAddOnDetailRequest.newBuilder().setId(params.getPetDetailId());
        if (params.hasDateType()) {
            builder.setDateType(params.getDateType());
            if (params.getDateType() == PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE) {
                builder.setSpecificDates(StringListValue.newBuilder()
                        .addAllValues(params.getSpecificDatesList())
                        .build());
            }
        }
        if (params.hasQuantityPerDay()) {
            builder.setQuantityPerDay(params.getQuantityPerDay());
        }
        if (params.hasStartDate()) {
            builder.setStartDate(params.getStartDate());
        }
        return builder.build();
    }

    static UpdateBookingRequestRequest.Service toBoardingUpdateService(
            ReschedulePetFeedingMedicationParams.PetScheduleDef def, Long petDetailId) {
        return UpdateBookingRequestRequest.Service.newBuilder()
                .setBoarding(UpdateBookingRequestRequest.BoardingService.newBuilder()
                        .setService(UpdateBoardingServiceDetailRequest.newBuilder()
                                .setId(petDetailId)
                                .build())
                        .setFeedingsUpsert(CreateFeedingRequestList.newBuilder()
                                .addAllValues(def.getFeedingsList().stream()
                                        .map(FeedingConverter::toCreateFeedingRequest)
                                        .toList())
                                .build())
                        .setMedicationsUpsert(CreateMedicationRequestList.newBuilder()
                                .addAllValues(def.getMedicationsList().stream()
                                        .map(MedicationConverter::toCreateMedicationRequest)
                                        .toList())
                                .build())
                        .build())
                .build();
    }

    static UpdateBookingRequestRequest.Service toDaycareUpdateService(
            ReschedulePetFeedingMedicationParams.PetScheduleDef def, Long petDetailId) {
        return UpdateBookingRequestRequest.Service.newBuilder()
                .setDaycare(UpdateBookingRequestRequest.DaycareService.newBuilder()
                        .setService(UpdateDaycareServiceDetailRequest.newBuilder()
                                .setId(petDetailId)
                                .build())
                        .setFeedingsUpsert(CreateFeedingRequestList.newBuilder()
                                .addAllValues(def.getFeedingsList().stream()
                                        .map(FeedingConverter::toCreateFeedingRequest)
                                        .toList())
                                .build())
                        .setMedicationsUpsert(CreateMedicationRequestList.newBuilder()
                                .addAllValues(def.getMedicationsList().stream()
                                        .map(MedicationConverter::toCreateMedicationRequest)
                                        .toList())
                                .build())
                        .build())
                .build();
    }
}
