package com.moego.client.api.v1.payment.controller;

import com.moego.client.api.v1.appointment.service.AccountAppointmentService;
import com.moego.client.api.v1.branded_app.BrandedAppService;
import com.moego.common.enums.PaymentSettingConst;
import com.moego.common.enums.PaymentStripeStatus;
import com.moego.idl.client.payment.v1.CancellationServiceGrpc;
import com.moego.idl.client.payment.v1.GetCancellationFeeParams;
import com.moego.idl.client.payment.v1.GetCancellationFeeResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.dto.ConvenienceFeeDTO;
import com.moego.server.payment.params.ConvenienceFeeParams;
import io.grpc.stub.StreamObserver;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/8/21
 */
@GrpcService
@RequiredArgsConstructor
public class CancellationController extends CancellationServiceGrpc.CancellationServiceImplBase {

    private final IPaymentPaymentClient paymentClient;
    private final AccountAppointmentService accountAppointmentService;
    private final BrandedAppService brandedAppService;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getCancellationFee(
            GetCancellationFeeParams request, StreamObserver<GetCancellationFeeResult> responseObserver) {
        var appointment = accountAppointmentService.getAccountBelongsAppointment(
                AuthContext.get().accountId(), request.getAppointmentId());

        var brandedAppConfig =
                brandedAppService.mustGetBrandedAppConfig(AuthContext.get().brandedAppId());

        var convenienceFee = paymentClient
                .listConvenienceFees(List.of(new ConvenienceFeeParams()
                        .setBusinessId(appointment.getBusinessId())
                        .setRemainAmount(BigDecimal.valueOf(brandedAppConfig.getCancellationFee()))
                        .setPaymentMethod(PaymentStripeStatus.CARD_PAY)))
                .stream()
                .filter(result -> Objects.equals(
                        result.getProcessingFeePayBy(), PaymentSettingConst.PROCESSING_FEE_PAY_BY_CLIENT))
                .findFirst()
                .map(ConvenienceFeeDTO::getConvenienceFee)
                .orElse(BigDecimal.ZERO);

        var total = BigDecimal.valueOf(brandedAppConfig.getCancellationFee()).add(convenienceFee);

        responseObserver.onNext(GetCancellationFeeResult.newBuilder()
                .setCancellationFee(brandedAppConfig.getCancellationFee())
                .setTaxAndFees(convenienceFee.doubleValue())
                .setTotal(total.doubleValue())
                .build());
        responseObserver.onCompleted();
    }
}
