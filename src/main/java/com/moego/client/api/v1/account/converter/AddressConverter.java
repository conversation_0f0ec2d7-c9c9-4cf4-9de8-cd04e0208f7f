package com.moego.client.api.v1.account.converter;

import com.moego.idl.client.account.v1.AccountAddressDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressUpdateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressView;
import com.moego.server.grooming.params.ob.ServiceAreaParams;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @since 2024/6/6
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        unmappedTargetPolicy = ReportingPolicy.WARN)
public interface AddressConverter {

    BusinessCustomerAddressCreateDef toCreateDef(AccountAddressDef def);

    BusinessCustomerAddressUpdateDef toUpdateDef(AccountAddressDef def);

    BusinessCustomerAddressView toView(BusinessCustomerAddressModel model);

    @Mapping(target = "addressId", source = "id")
    @Mapping(target = "lat", source = "coordinate.latitude")
    @Mapping(target = "lng", source = "coordinate.longitude")
    ServiceAreaParams.ClientAddressParams toParams(BusinessCustomerAddressModel model);
}
