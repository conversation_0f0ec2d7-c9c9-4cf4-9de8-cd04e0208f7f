package com.moego.client.api.v1.online_booking.service;

import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetPetDetailListRequest;
import com.moego.idl.service.appointment.v1.LodgingAssignInfoRequest;
import com.moego.idl.service.appointment.v1.LodgingServiceGrpc;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.lib.utils.model.Pair;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class AppointmentService {

    private final LodgingServiceGrpc.LodgingServiceBlockingStub lodgingClient;

    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentService;

    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailService;
    public static final List<AppointmentStatus> IN_PROGRESS_STATUS_VALUE_SET = List.of(
            AppointmentStatus.UNCONFIRMED,
            AppointmentStatus.CONFIRMED,
            AppointmentStatus.READY,
            AppointmentStatus.CHECKED_IN);

    public List<LodgingAssignInfo> getLodgingAssignInfo(
            Long companyId, Long businessId, LocalDate startDate, LocalDate endDate) {
        return lodgingClient
                .lodgingAssignInfo(LodgingAssignInfoRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .setStartDate(startDate.toString())
                        .setEndDate(endDate.toString())
                        .build())
                .getLodgingAssignInfoList();
    }

    public Pair<List<PetDetailModel>, List<EvaluationServiceModel>> listAllPetDetails(
            long companyId, List<Long> appointmentIds) {
        if (ObjectUtils.isEmpty(appointmentIds)) {
            return Pair.of(List.of(), List.of());
        }
        var response = petDetailService.getPetDetailList(GetPetDetailListRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllAppointmentIds(appointmentIds)
                .build());
        return Pair.of(response.getPetDetailsList(), response.getPetEvaluationsList());
    }
}
