package com.moego.client.api.v1.appointment;

import com.moego.client.api.v1.appointment.service.GroupClassService;
import com.moego.idl.client.appointment.v1.GetInstanceDetailParams;
import com.moego.idl.client.appointment.v1.GetInstanceDetailResult;
import com.moego.idl.client.appointment.v1.ListInstancesParams;
import com.moego.idl.client.appointment.v1.ListInstancesResult;
import com.moego.idl.client.appointment.v1.ListSessionsParams;
import com.moego.idl.client.appointment.v1.ListSessionsResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class GroupClassController
        extends com.moego.idl.client.appointment.v1.GroupClassServiceGrpc.GroupClassServiceImplBase {
    private final GroupClassService groupClassService;
    private final IGroomingOnlineBookingService onlineBookingService;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listInstances(ListInstancesParams request, StreamObserver<ListInstancesResult> responseObserver) {
        OBBusinessDTO biz = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        final var result = groupClassService.listInstances(
                biz.getCompanyId(),
                biz.getBusinessId(),
                AuthContext.get().customerId().intValue());
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listSessions(ListSessionsParams request, StreamObserver<ListSessionsResult> responseObserver) {
        OBBusinessDTO biz = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));
        var result = groupClassService.listSessions(
                biz.getCompanyId(),
                biz.getBusinessId(),
                AuthContext.get().customerId().intValue(),
                request.getStatus(),
                request.getPagination());
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB)
    public void getInstanceDetail(
            GetInstanceDetailParams request, StreamObserver<GetInstanceDetailResult> responseObserver) {
        OBBusinessDTO biz = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));
        var result = groupClassService.getInstanceDetail(
                biz.getCompanyId(), request.getGroupInstanceId(), request.getPetIdsList());
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }
}
