package com.moego.client.api.v1.online_booking.server;

import static com.moego.client.api.v1.appointment.utils.AppointmentUtil.buildFilterAndOrderBys;
import static com.moego.client.api.v1.appointment.utils.AppointmentUtil.buildPaginationRequest;

import com.google.type.Date;
import com.moego.client.api.v1.enterprise.service.CompanyService;
import com.moego.client.api.v1.online_booking.service.ContextService;
import com.moego.client.api.v1.online_booking.utils.DateComparator;
import com.moego.client.api.v1.shared.helper.CustomerHelper;
import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.idl.client.online_booking.v1.AppointmentCardType;
import com.moego.idl.client.online_booking.v1.CreatePetParams;
import com.moego.idl.client.online_booking.v1.CreatePetResult;
import com.moego.idl.client.online_booking.v1.GetPetParams;
import com.moego.idl.client.online_booking.v1.GetPetResult;
import com.moego.idl.client.online_booking.v1.ListPetVaccineExpirationStatusParams;
import com.moego.idl.client.online_booking.v1.ListPetVaccineExpirationStatusResult;
import com.moego.idl.client.online_booking.v1.ListPetsParams;
import com.moego.idl.client.online_booking.v1.ListPetsResult;
import com.moego.idl.client.online_booking.v1.PetServiceGrpc;
import com.moego.idl.client.online_booking.v1.PetVaccineExpirationStatus;
import com.moego.idl.client.online_booking.v1.SubmitPetVaccineRequestParams;
import com.moego.idl.client.online_booking.v1.SubmitPetVaccineRequestResult;
import com.moego.idl.client.online_booking.v1.UpdatePetParams;
import com.moego.idl.client.online_booking.v1.UpdatePetResult;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordBindingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordUpdateDef;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRequestBindingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRequestCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRequestModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRequestUpdateDef;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.business_customer.v1.BatchCreatePetVaccineRecordRequest;
import com.moego.idl.service.business_customer.v1.BatchListVaccineRecordRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessPetVaccineRecordServiceGrpc.BusinessPetVaccineRecordServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessPetVaccineRequestServiceGrpc.BusinessPetVaccineRequestServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreatePetVaccineRequestRequest;
import com.moego.idl.service.business_customer.v1.CreatePetWithAdditionalInfoRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.GetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.GetPetRequest;
import com.moego.idl.service.business_customer.v1.GetPetVaccineRequestRequest;
import com.moego.idl.service.business_customer.v1.ListPetInfoRequest;
import com.moego.idl.service.business_customer.v1.ListPetRequest;
import com.moego.idl.service.business_customer.v1.ListPetVaccineRequestsRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetVaccineRecordsRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetVaccineRequestRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.utils.PageUtil;
import com.moego.lib.utils.model.Pair;
import com.moego.server.message.api.INotificationService;
import com.moego.server.message.dto.NotificationClientUpdatePetParams;
import com.moego.server.message.params.notification.NotificationPendingPetVaccineRequestParams;
import io.grpc.stub.StreamObserver;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class PetServer extends PetServiceGrpc.PetServiceImplBase {
    private final ContextService contextService;
    private final BusinessCustomerServiceBlockingStub customerService;
    private final BusinessCustomerPetServiceBlockingStub petService;
    private final BusinessPetVaccineRecordServiceBlockingStub petVaccineRecordService;
    private final BusinessPetVaccineRequestServiceBlockingStub petVaccineRequestService;
    private final INotificationService notificationService;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;
    private final CompanyService companyService;
    private final INotificationService iNotificationService;
    private final CustomerHelper customerHelper;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void createPet(CreatePetParams request, StreamObserver<CreatePetResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var req = CreatePetWithAdditionalInfoRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(ctx.getCompanyId()).build())
                .setCustomerId(ctx.getCustomerId())
                .setPetWithAdditionalInfo(request.getDef())
                .build();
        var rsp = petService.createPetWithAdditionalInfo(req);
        var petInfo = petService.getPet(GetPetRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(ctx.getCompanyId()).build())
                .setId(rsp.getPetId())
                .build());
        responseObserver.onNext(
                CreatePetResult.newBuilder().setPet(petInfo.getPet()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void getPet(GetPetParams request, StreamObserver<GetPetResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var rsp = petService.getPet(GetPetRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(ctx.getCompanyId()).build())
                .setId(request.getId())
                .build());
        if (rsp.getPet().getCustomerId() != ctx.getCustomerId()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Pet Not Found");
        }
        responseObserver.onNext(GetPetResult.newBuilder().setPet(rsp.getPet()).build());
        responseObserver.onCompleted();
    }

    /**
     * ListPets lists all pets.
     */
    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listPets(ListPetsParams request, StreamObserver<ListPetsResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var rsp = petService.listPet(ListPetRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(ctx.getCompanyId()).build())
                .setCustomerId(ctx.getCustomerId())
                .setIncludePassedAway(request.getIncludePassedAway())
                .build());

        var pets = rsp.getPetsList();
        if (CollectionUtils.isEmpty(pets)) {
            responseObserver.onNext(ListPetsResult.getDefaultInstance());
            responseObserver.onCompleted();
        }

        var resultBuilder = ListPetsResult.newBuilder().addAllPets(rsp.getPetsList());

        // include vaccine records
        var petIds = pets.stream().map(BusinessCustomerPetModel::getId).toList();
        if (request.getIncludeVaccineRecords()) {
            var records = petVaccineRecordService
                    .batchListVaccineRecord(BatchListVaccineRecordRequest.newBuilder()
                            .setCompanyId(ctx.getCompanyId())
                            .addAllPetIds(petIds)
                            .setIncludeVaccine(false)
                            .build())
                    .getBindingsList();

            resultBuilder.addAllVaccineRecords(records);
        }

        // include pending vaccine requests
        if (request.getIncludePendingVaccineRequests()) {
            var requests = listAllPendingPetVaccineRequests(petIds).stream()
                    .collect(Collectors.groupingBy(BusinessPetVaccineRequestModel::getVaccineRecordId))
                    .entrySet()
                    .stream()
                    .map(entry -> {
                        var builder = BusinessPetVaccineRequestBindingModel.newBuilder()
                                .addAllRequests(entry.getValue());
                        // 等于 0 的时候表示新增数据, 没有跟任何 vaccine record id 绑定, 不要把 0 返回给前端
                        if (entry.getKey() > 0) {
                            builder.setVaccineRecordId(entry.getKey());
                        }
                        return builder.build();
                    })
                    .sorted(Comparator.comparing(BusinessPetVaccineRequestBindingModel::getVaccineRecordId))
                    .toList();

            resultBuilder.addAllPendingVaccineRequests(requests);
        }

        responseObserver.onNext(resultBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * Updates a pet by id.
     */
    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void updatePet(UpdatePetParams request, StreamObserver<UpdatePetResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var rsp = petService.getPet(GetPetRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(ctx.getCompanyId()).build())
                .setId(request.getId())
                .build());
        if (rsp.getPet().getCustomerId() != ctx.getCustomerId()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Pet Not Found");
        }
        petService.updatePet(UpdatePetRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(ctx.getCompanyId()).build())
                .setPet(request.getDef())
                .setId(request.getId())
                .setPet(request.getDef())
                .build());
        if (request.getVaccinesCount() > 0) {
            var vaccinesToCreate = new ArrayList<BusinessPetVaccineRecordCreateDef>();
            var vaccinesToUpdate = new ArrayList<BusinessPetVaccineRecordUpdateDef>();
            request.getVaccinesList().forEach(v -> {
                var date = LocalDate.parse(v.getExpirationDate());
                if (v.hasVaccineBindingId()) {
                    vaccinesToUpdate.add(BusinessPetVaccineRecordUpdateDef.newBuilder()
                            .setId(v.getVaccineBindingId())
                            .setVaccineId(v.getVaccineId())
                            .setExpirationDate(Date.newBuilder()
                                    .setYear(date.getYear())
                                    .setMonth(date.getMonthValue())
                                    .setDay(date.getDayOfMonth())
                                    .build())
                            .addAllDocumentUrls(v.getDocumentUrlsList())
                            .build());
                } else {
                    vaccinesToCreate.add(BusinessPetVaccineRecordCreateDef.newBuilder()
                            .setVaccineId(v.getVaccineId())
                            .setExpirationDate(Date.newBuilder()
                                    .setYear(date.getYear())
                                    .setMonth(date.getMonthValue())
                                    .setDay(date.getDayOfMonth())
                                    .build())
                            .addAllDocumentUrls(v.getDocumentUrlsList())
                            .build());
                }
            });
            if (!CollectionUtils.isEmpty(vaccinesToCreate)) {
                petVaccineRecordService.batchCreatePetVaccineRecord(BatchCreatePetVaccineRecordRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(ctx.getCompanyId())
                                .build())
                        .setPetId(request.getId())
                        .addAllVaccineRecords(vaccinesToCreate)
                        .build());
            }
            if (!CollectionUtils.isEmpty(vaccinesToUpdate)) {
                petVaccineRecordService.updatePetVaccineRecords(UpdatePetVaccineRecordsRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(ctx.getCompanyId())
                                .build())
                        .setPetId(request.getId())
                        .addAllVaccineRecords(vaccinesToUpdate)
                        .build());
            }
        }
        var petInfo = petService.getPet(GetPetRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(ctx.getCompanyId()).build())
                .setId(request.getId())
                .build());

        if (request.getSendNotification()) {
            ThreadPool.execute(() -> sendNotificationForUpdatePet(ctx, petInfo.getPet()));
        }

        responseObserver.onNext(
                UpdatePetResult.newBuilder().setPet(petInfo.getPet()).build());
        responseObserver.onCompleted();
    }

    private void sendNotificationForUpdatePet(BaseBusinessCustomerIdDTO ctx, BusinessCustomerPetModel pet) {

        var customer = customerHelper.mustGetCustomer(pet.getCustomerId());

        var params = new NotificationClientUpdatePetParams();
        params.setBusinessId(ctx.getBusinessId());
        params.setWebPushDto(NotificationClientUpdatePetParams.Data.builder()
                .petId(Math.toIntExact(pet.getId()))
                .petName(pet.getPetName())
                .customerId(Math.toIntExact(pet.getCustomerId()))
                .customerFirstName(customer.getFirstName())
                .customerLastName(customer.getLastName())
                .updateTime(Instant.now().getEpochSecond())
                .build());

        iNotificationService.sendClientUpdatePetNotification(params);
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void submitPetVaccineRequest(
            SubmitPetVaccineRequestParams request, StreamObserver<SubmitPetVaccineRequestResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var tenant = Tenant.newBuilder().setCompanyId(ctx.getCompanyId()).build();

        // check customer exists
        var customerId = ctx.getCustomerId().longValue();
        var customer = customerService
                .getCustomerInfo(GetCustomerInfoRequest.newBuilder()
                        .setTenant(tenant)
                        .setId(customerId)
                        .build())
                .getCustomer();
        if (customer.getDeleted()) {
            throw ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND);
        }

        // check pet exists and belongs to customer
        var petId = request.getPetId();
        var pet = petService
                .getPetInfo(GetPetInfoRequest.newBuilder()
                        .setTenant(tenant)
                        .setId(petId)
                        .build())
                .getPet();
        if (pet.getDeleted() || pet.getCustomerId() != customerId) {
            throw ExceptionUtil.bizException(Code.CODE_PET_NOT_FOUND);
        }

        // override existing pet vaccine request
        // override 不需要发送新的 notification，原来的 notification 打开会看到 override 的结果
        if (request.hasId()) {
            var petVaccineRequest = petVaccineRequestService
                    .getPetVaccineRequest(GetPetVaccineRequestRequest.newBuilder()
                            .setId(request.getId())
                            .build())
                    .getPetVaccineRequest();

            if (petVaccineRequest.getPetId() != petId) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "pet id mismatch");
            }

            // 如果不是 pending 状态，说明 request 已经被商家处理。用户不能再修改，需要重新提交
            if (petVaccineRequest.getStatus() != BusinessPetVaccineRequestModel.Status.PENDING) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "Your submission has expired, please refresh the page and try again");
            }

            petVaccineRequestService.updatePetVaccineRequest(UpdatePetVaccineRequestRequest.newBuilder()
                    .setId(request.getId())
                    .setPetVaccineRequest(toUpdateDef(request.getPetVaccineRequest()))
                    .build());
            responseObserver.onNext(SubmitPetVaccineRequestResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // create pet vaccine request
        var petVaccineRequest = petVaccineRequestService
                .createPetVaccineRequest(CreatePetVaccineRequestRequest.newBuilder()
                        .setPetId(petId)
                        .setPetVaccineRequest(request.getPetVaccineRequest())
                        .build())
                .getPetVaccineRequest();

        // send notification
        var extra = new NotificationPendingPetVaccineRequestParams.ExtraDTO();
        extra.setCustomerFirstName(customer.getFirstName());
        extra.setCustomerLastName(customer.getLastName());
        extra.setPetVaccineRequestId(petVaccineRequest.getId());
        extra.setCreateTime(petVaccineRequest.getCreateTime().getSeconds());
        var params = new NotificationPendingPetVaccineRequestParams();
        params.setWebPushDto(extra);
        params.setBusinessId(ctx.getBusinessId());
        params.setIsSendMobilePush(false); // mobile 暂不支持
        notificationService.sendNotification(params);

        responseObserver.onNext(SubmitPetVaccineRequestResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listPetVaccineExpirationStatus(
            ListPetVaccineExpirationStatusParams request,
            StreamObserver<ListPetVaccineExpirationStatusResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        var tenant = Tenant.newBuilder().setCompanyId(ctx.getCompanyId()).build();

        // check customer exists
        var customerId = ctx.getCustomerId().longValue();
        var customer = customerService
                .getCustomerInfo(GetCustomerInfoRequest.newBuilder()
                        .setTenant(tenant)
                        .setId(customerId)
                        .build())
                .getCustomer();
        if (customer.getDeleted()) {
            throw ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND);
        }

        // 查询没有去世的 pets
        var petIds = petService
                .listPetInfo(ListPetInfoRequest.newBuilder()
                        .setFilter(ListPetInfoRequest.Filter.newBuilder()
                                .addCompanyIds(ctx.getCompanyId())
                                .addCustomerIds(customerId)
                                .setIncludePassedAway(false)
                                .build())
                        .build())
                .getPetsList()
                .stream()
                .map(BusinessCustomerPetInfoModel::getId)
                .toList();
        if (CollectionUtils.isEmpty(petIds)) {
            responseObserver.onNext(ListPetVaccineExpirationStatusResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // 查询 vaccine records
        var petVaccineRecords = petVaccineRecordService
                .batchListVaccineRecord(BatchListVaccineRecordRequest.newBuilder()
                        .setCompanyId(ctx.getCompanyId())
                        .addAllPetIds(petIds)
                        .setIncludeVaccine(false)
                        .build())
                .getBindingsList();
        if (CollectionUtils.isEmpty(petVaccineRecords)) {
            var result = petIds.stream()
                    .collect(Collectors.toMap(
                            id -> id, id -> PetVaccineExpirationStatus.PET_VACCINE_EXPIRATION_STATUS_NO_RECORD));
            responseObserver.onNext(ListPetVaccineExpirationStatusResult.newBuilder()
                    .putAllPetVaccineExpirationStatus(result)
                    .build());
            responseObserver.onCompleted();
            return;
        }

        // 查询 vaccine requests
        var petVaccineRequests = listAllPendingPetVaccineRequests(petIds);

        // 查询 customer 的 next appointment (company 维度)
        // 或许这个接口的场景查 business 维度更合适？并且应该用 pet 去查会更合适？
        var nextAppointment = getUpcomingAppointments(ctx);

        // 计算每一只 pet 的 vaccine expiration status
        // TODO: 计算 record 维度的 expiration status
        var petVaccineExpirationStatus =
                getPetVaccineExpirationStatus(petIds, petVaccineRecords, petVaccineRequests, nextAppointment);

        var result = ListPetVaccineExpirationStatusResult.newBuilder()
                .putAllPetVaccineExpirationStatus(petVaccineExpirationStatus)
                .build();
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    private List<BusinessPetVaccineRequestModel> listAllPendingPetVaccineRequests(List<Long> petIds) {
        return PageUtil.fetchAllGrpc((pageNum, pageSize) -> {
            var pagination = PaginationRequest.newBuilder()
                    .setPageNum(pageNum)
                    .setPageSize(pageSize)
                    .build();
            var resp = petVaccineRequestService.listPetVaccineRequests(ListPetVaccineRequestsRequest.newBuilder()
                    .addAllPetIds(petIds)
                    .addStatuses(BusinessPetVaccineRequestModel.Status.PENDING)
                    .setPagination(pagination)
                    .build());

            return Pair.of(resp.getPetVaccineRequestsList(), resp.getPagination());
        });
    }

    private Map<Long, PetVaccineExpirationStatus> getPetVaccineExpirationStatus(
            List<Long> petId,
            List<BusinessPetVaccineRecordBindingModel> recordModels,
            List<BusinessPetVaccineRequestModel> requestModels,
            AppointmentModel nextAppointment) {
        var recordsMap = recordModels.stream()
                .collect(Collectors.toMap(
                        BusinessPetVaccineRecordBindingModel::getPetId,
                        BusinessPetVaccineRecordBindingModel::getRecordsList));
        var requestsMap =
                requestModels.stream().collect(Collectors.groupingBy(BusinessPetVaccineRequestModel::getPetId));

        var nextAppointmentDate = Optional.ofNullable(nextAppointment)
                .map(a -> {
                    var dateString = a.getAppointmentDate();
                    LocalDate date;
                    try {
                        date = LocalDate.parse(dateString);
                    } catch (Exception e) {
                        log.error(
                                "parse appointment date failed, id: {}, date: {}",
                                a.getId(),
                                a.getAppointmentDate(),
                                e);
                        return null;
                    }
                    return Date.newBuilder()
                            .setYear(date.getYear())
                            .setMonth(date.getMonthValue())
                            .setDay(date.getDayOfMonth())
                            .build();
                })
                .orElse(null);

        var next30Day = Optional.of(LocalDate.now(Clock.systemUTC()).plusDays(30))
                .map(d -> Date.newBuilder()
                        .setYear(d.getYear())
                        .setMonth(d.getMonthValue())
                        .setDay(d.getDayOfMonth())
                        .build())
                .get();

        var result = new HashMap<Long, PetVaccineExpirationStatus>();

        petId.forEach(id -> {
            var records = recordsMap.getOrDefault(id, List.of());
            var requests = requestsMap.getOrDefault(id, List.of());

            // 没有记录，返回 NO_RECORD
            if (CollectionUtils.isEmpty(records)) {
                result.put(id, PetVaccineExpirationStatus.PET_VACCINE_EXPIRATION_STATUS_NO_RECORD);
                return;
            }

            // 每个 vaccine 可能有多个 records / requests，取最晚的一个过期日期
            var recordExpirationDateByVaccine = records.stream()
                    .filter(BusinessPetVaccineRecordModel::hasExpirationDate)
                    .collect(Collectors.toMap(
                            BusinessPetVaccineRecordModel::getVaccineId,
                            BusinessPetVaccineRecordModel::getExpirationDate,
                            DateComparator::getMaxDate));

            var requestExpirationDateByVaccine = requests.stream()
                    .filter(BusinessPetVaccineRequestModel::hasExpirationDate)
                    .collect(Collectors.toMap(
                            BusinessPetVaccineRequestModel::getVaccineId,
                            BusinessPetVaccineRequestModel::getExpirationDate,
                            DateComparator::getMaxDate));

            // 如果 request 的过期日期比 record 的过期日期晚，用 request 的过期日期
            var expirationDateByVaccine = new HashMap<Long, Date>();
            for (var entry : recordExpirationDateByVaccine.entrySet()) {
                var vaccineId = entry.getKey();
                var recordDate = entry.getValue();
                var requestDate = requestExpirationDateByVaccine.get(vaccineId);
                if (requestDate == null) {
                    expirationDateByVaccine.put(vaccineId, recordDate);
                } else {
                    expirationDateByVaccine.put(vaccineId, DateComparator.getMaxDate(recordDate, requestDate));
                }
            }

            // 在所有 vaccine 里找到最先过期的一个
            var minExpirationDate = expirationDateByVaccine.values().stream()
                    .min(DateComparator.INSTANCE)
                    .orElse(null);

            var status = calculateExpirationStatus(minExpirationDate, nextAppointmentDate, next30Day);
            result.put(id, status);
        });
        return result;
    }

    private static PetVaccineExpirationStatus calculateExpirationStatus(
            Date expirationDate, Date nextAppointmentDate, Date next30Day) {
        // 如果没有过期日期，返回 NOT_EXPIRED
        if (expirationDate == null) {
            return PetVaccineExpirationStatus.PET_VACCINE_EXPIRATION_STATUS_NOT_EXPIRED;
        }

        // 如果过期日期小于今天，返回 EXPIRED
        if (DateComparator.beforeToday(expirationDate)) {
            return PetVaccineExpirationStatus.PET_VACCINE_EXPIRATION_STATUS_EXPIRED;
        }

        // 过期时间在今天以后，看是否 30 天内过期，以及是否在 next appt 之前过期
        var expiredIn30Days = DateComparator.INSTANCE.compare(expirationDate, next30Day) <= 0;

        // 没有 next appt，直接根据 expiredIn30Days 返回结果
        if (nextAppointmentDate == null) {
            return expiredIn30Days
                    ? PetVaccineExpirationStatus.PET_VACCINE_EXPIRATION_STATUS_EXPIRED_IN_30_DAYS
                    : PetVaccineExpirationStatus.PET_VACCINE_EXPIRATION_STATUS_NOT_EXPIRED;
        }

        // 有 next appt
        // 如果在 30 天内过期
        if (expiredIn30Days) {
            // 如果 next appt 在 30 天内，返回 EXPIRED_BEFORE_NEXT_APPT
            // 否则 返回 EXPIRED_IN_30_DAYS
            return DateComparator.INSTANCE.compare(nextAppointmentDate, next30Day) <= 0
                    ? PetVaccineExpirationStatus.PET_VACCINE_EXPIRATION_STATUS_EXPIRED_BEFORE_NEXT_APPOINTMENT
                    : PetVaccineExpirationStatus.PET_VACCINE_EXPIRATION_STATUS_EXPIRED_IN_30_DAYS;
        }

        // 在 30 天之后过期，直接跟 next appt 比较
        return DateComparator.INSTANCE.compare(expirationDate, nextAppointmentDate) <= 0
                ? PetVaccineExpirationStatus.PET_VACCINE_EXPIRATION_STATUS_EXPIRED_BEFORE_NEXT_APPOINTMENT
                : PetVaccineExpirationStatus.PET_VACCINE_EXPIRATION_STATUS_NOT_EXPIRED;
    }

    private BusinessPetVaccineRequestUpdateDef toUpdateDef(BusinessPetVaccineRequestCreateDef createDef) {
        var builder = BusinessPetVaccineRequestUpdateDef.newBuilder();
        builder.setVaccineId(createDef.getVaccineId());

        if (createDef.hasVaccineRecordId()) {
            builder.setVaccineRecordId(createDef.getVaccineRecordId());
        } else {
            builder.setVaccineRecordId(0);
        }

        if (createDef.hasExpirationDate()) {
            builder.setExpirationDate(createDef.getExpirationDate());
        } else {
            // 没填要清空
            builder.setExpirationDate(Date.getDefaultInstance());
        }

        var documentUrls = BusinessPetVaccineRequestUpdateDef.DocumentUrlList.newBuilder()
                .addAllUrls(createDef.getDocumentUrlsList())
                .build();
        builder.setDocumentUrls(documentUrls);

        return builder.build();
    }

    private AppointmentModel getUpcomingAppointments(BaseBusinessCustomerIdDTO ctx) {
        var zoneId = companyService.getZoneId(ctx.getCompanyId());
        var pairAndOrderBys = buildFilterAndOrderBys(ctx.getCustomerId(), AppointmentCardType.UPCOMING, zoneId);

        var filter = pairAndOrderBys.key();
        var orderBys = pairAndOrderBys.value();
        var pagination =
                PaginationRequest.newBuilder().setPageNum(1).setPageSize(1).build();

        var request = buildPaginationRequest(ctx, orderBys, pagination, filter);
        var response = appointmentStub.listAppointments(request);
        return CollectionUtils.firstElement(response.getAppointmentsList());
    }
}
