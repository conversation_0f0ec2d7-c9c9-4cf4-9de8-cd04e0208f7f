package com.moego.client.api.v1.notification;

import com.moego.idl.client.notification.v1.GetNotificationListParams;
import com.moego.idl.client.notification.v1.GetNotificationListResult;
import com.moego.idl.client.notification.v1.NotificationServiceGrpc;
import com.moego.idl.models.notification.v1.NotificationMethod;
import com.moego.idl.models.notification.v1.NotificationSource;
import com.moego.idl.service.notification.v1.GetNotificationListRequest;
import com.moego.idl.service.notification.v1.GetNotificationListResponse;
import com.moego.idl.service.notification.v1.NotificationServiceGrpc.NotificationServiceBlockingStub;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
@GrpcService
@RequiredArgsConstructor
public class NotificationController extends NotificationServiceGrpc.NotificationServiceImplBase {

    private final NotificationMapper notificationMapper;
    private final NotificationServiceBlockingStub notificationServiceBlockingStub;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getNotificationList(
            GetNotificationListParams request, StreamObserver<GetNotificationListResult> responseObserver) {
        GetNotificationListRequest getNotificationListRequest = GetNotificationListRequest.newBuilder()
                .addAllTypes(request.getTypesList())
                .addAllSources(List.of(
                        NotificationSource.NOTIFICATION_SOURCE_PLATFORM,
                        NotificationSource.NOTIFICATION_SOURCE_BUSINESS))
                .addAllMethods(List.of(NotificationMethod.NOTIFICATION_METHOD_PET_PARENT_APP))
                .setPagination(request.getPagination())
                .addAllSorts(request.getSortsList())
                .setReceiverId(AuthContext.get().accountId())
                .build();
        GetNotificationListResponse response =
                notificationServiceBlockingStub.getNotificationList(getNotificationListRequest);

        responseObserver.onNext(GetNotificationListResult.newBuilder()
                .addAllNotifications(notificationMapper.modelToView(response.getNotificationsList()))
                .setPagination(response.getPagination())
                .build());
        responseObserver.onCompleted();
    }
}
