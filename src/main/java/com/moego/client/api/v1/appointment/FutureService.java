package com.moego.client.api.v1.appointment;

import com.moego.common.dto.PaymentSummary;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.PaymentStripeStatus;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.idl.models.account.v1.AccountNamespaceType;
import com.moego.idl.models.agreement.v1.AgreementRecordSimpleView;
import com.moego.idl.models.appointment.v1.AppointmentPetScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentPetScheduleSettingModel;
import com.moego.idl.models.appointment.v1.AppointmentTracking;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.PetScheduleDef;
import com.moego.idl.models.branded_app.v1.BrandedAppConfigModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.marketing.v1.DiscountCodeCompositeView;
import com.moego.idl.models.marketing.v1.DiscountCodeStatus;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineDiscountModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.organization.v1.CompanyPreferenceSettingModel;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.agreement.v1.AgreementRecordServiceGrpc.AgreementRecordServiceBlockingStub;
import com.moego.idl.service.agreement.v1.GetRecordListByCompanyRequest;
import com.moego.idl.service.appointment.v1.AppointmentScheduleServiceGrpc.AppointmentScheduleServiceBlockingStub;
import com.moego.idl.service.appointment.v1.AppointmentTrackingServiceGrpc;
import com.moego.idl.service.appointment.v1.BatchGetPetFeedingMedicationSchedulesRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import com.moego.idl.service.appointment.v1.GetPetFeedingMedicationSchedulesRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTrackingRequest;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc.PetDetailServiceBlockingStub;
import com.moego.idl.service.branded_app.v1.BrandedAppConfigServiceGrpc.BrandedAppConfigServiceBlockingStub;
import com.moego.idl.service.branded_app.v1.GetBrandedAppConfigRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub;
import com.moego.idl.service.marketing.v1.DiscountCodeServiceGrpc.DiscountCodeServiceBlockingStub;
import com.moego.idl.service.marketing.v1.GetDiscountCodeListInput;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc.EvaluationServiceBlockingStub;
import com.moego.idl.service.offering.v1.GetEvaluationListWithEvaluationIdsRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc.BookingRequestServiceBlockingStub;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsRequest;
import com.moego.idl.service.order.v1.GetOrderListRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc.OrderServiceBlockingStub;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc.CompanyServiceBlockingStub;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub;
import com.moego.idl.utils.v1.Status;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.utils.model.Pair;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.params.CommonIdsParams;
import com.moego.server.grooming.client.IBookOnlineDepositClient;
import com.moego.server.grooming.client.IGroomingAppointmentClient;
import com.moego.server.grooming.client.IGroomingInvoiceClient;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.CustomerAppointmentNumInfoDTO;
import com.moego.server.grooming.dto.PackageServiceDTO;
import com.moego.server.message.client.IMessageClient;
import com.moego.server.message.dto.ArrivalWindowSettingDto;
import com.moego.server.message.dto.BusinessTwilioNumberDTO;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.client.IPaymentPreAuthClient;
import com.moego.server.payment.dto.ConvenienceFeeDTO;
import com.moego.server.payment.dto.PreAuthDTO;
import com.moego.server.payment.params.ConvenienceFeeParams;
import com.moego.server.payment.params.GetPaymentListParams;
import com.moego.server.payment.params.ListPreAuthParams;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024/6/22
 */
@Service
@RequiredArgsConstructor
public class FutureService {

    private final IBookOnlineDepositClient depositClient;
    private final IGroomingOnlineBookingClient onlineBookingClient;
    private final IGroomingAppointmentClient appointmentClient;
    private final IBusinessBusinessClient businessClient;
    private final IMessageClient messageClient;
    private final IGroomingInvoiceClient invoiceClient;
    private final IPaymentPaymentClient paymentClient;
    private final IPaymentPreAuthClient preAuthClient;
    private final PetDetailServiceBlockingStub petDetailService;
    private final BusinessCustomerPetServiceBlockingStub petService;
    private final BusinessCustomerServiceBlockingStub businessCustomerService;
    private final BusinessCustomerAddressServiceBlockingStub customerAddressService;
    private final AgreementRecordServiceBlockingStub agreementRecordService;
    private final ServiceManagementServiceBlockingStub serviceManagementService;
    private final EvaluationServiceBlockingStub evaluationService;
    private final StaffServiceBlockingStub staffService;
    private final OrderServiceBlockingStub orderService;
    private final DiscountCodeServiceBlockingStub discountCodeService;
    private final BookingRequestServiceBlockingStub bookingRequestService;
    private final CompanyServiceBlockingStub companyService;
    private final BrandedAppConfigServiceBlockingStub brandedAppConfigService;
    private final AppointmentScheduleServiceBlockingStub appointmentScheduleStub;
    private final AppointmentTrackingServiceGrpc.AppointmentTrackingServiceBlockingStub appointmentTrackingService;

    public CompletableFuture<Map<Long, OrderDetailModel>> listOrders(List<Long> sourceIds, OrderSourceType sourceType) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(sourceIds)) {
                        return Map.of();
                    }
                    return orderService
                            .getOrderList(GetOrderListRequest.newBuilder()
                                    .addAllSourceIds(sourceIds)
                                    .setSourceType(sourceType.getSource())
                                    .setQueryDetail(true)
                                    .build())
                            .getOrderListList()
                            .stream()
                            .collect(Collectors.toMap(
                                    detail -> detail.getOrder().getSourceId(), Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, OrderDetailModel>> listOrders(List<Long> sourceIds, String sourceType) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(sourceIds)) {
                        return Map.of();
                    }
                    return orderService
                            .getOrderList(GetOrderListRequest.newBuilder()
                                    .addAllSourceIds(sourceIds)
                                    .setSourceType(sourceType)
                                    .setQueryDetail(true)
                                    .build())
                            .getOrderListList()
                            .stream()
                            .collect(Collectors.toMap(
                                    detail -> detail.getOrder().getSourceId(), Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, ConvenienceFeeDTO>> listProcessingFees(
            CompletableFuture<Map<Long, OrderDetailModel>> ordersFuture) {
        return ordersFuture.thenApply(orders -> {
            if (ObjectUtils.isEmpty(orders)) {
                return Map.of();
            }
            var params = orders.values().stream()
                    .filter(OrderDetailModel::hasOrder)
                    .map(OrderDetailModel::getOrder)
                    .map(order -> new ConvenienceFeeParams()
                            .setOrderId(order.getId())
                            .setBusinessId(order.getBusinessId())
                            .setPaymentMethod(PaymentStripeStatus.CARD_PAY)
                            .setRemainAmount(BigDecimal.valueOf(order.getRemainAmount())))
                    .toList();
            return paymentClient.listConvenienceFees(params).stream()
                    .collect(Collectors.toMap(ConvenienceFeeDTO::getOrderId, Function.identity(), (a, b) -> a));
        });
    }

    public CompletableFuture<Map<Long, DiscountCodeCompositeView>> listDiscountCodes(
            long companyId, CompletableFuture<Map<Long, OrderDetailModel>> ordersFuture) {
        return ordersFuture.thenApply(orders -> {
            if (ObjectUtils.isEmpty(orders)) {
                return Map.of();
            }
            List<Long> discountCodeIds = orders.values().stream()
                    .map(OrderDetailModel::getLineDiscountsList)
                    .flatMap(List::stream)
                    .filter(OrderLineDiscountModel::hasDiscountCodeId)
                    .map(OrderLineDiscountModel::getDiscountCodeId)
                    .toList();
            if (CollectionUtils.isEmpty(discountCodeIds)) {
                return Map.of();
            }
            return discountCodeService
                    .getDiscountCodeList(GetDiscountCodeListInput.newBuilder()
                            .addAllIds(discountCodeIds)
                            .addAllStatus(List.of(
                                    DiscountCodeStatus.DISCOUNT_CODE_STATUS_ACTIVE,
                                    DiscountCodeStatus.DISCOUNT_CODE_STATUS_INACTIVE,
                                    DiscountCodeStatus.DISCOUNT_CODE_STATUS_ARCHIVED,
                                    DiscountCodeStatus.DISCOUNT_CODE_STATUS_DELETED))
                            .setCompanyId(companyId)
                            .setPagination(PaginationRequest.newBuilder()
                                    .setPageNum(1)
                                    .setPageSize(discountCodeIds.size())
                                    .build())
                            .build())
                    .getDiscountCodeCompositeViewsList()
                    .stream()
                    .collect(Collectors.toMap(DiscountCodeCompositeView::getId, Function.identity(), (a, b) -> a));
        });
    }

    public CompletableFuture<Map<Long, List<PackageServiceDTO>>> listApplyPackages(
            CompletableFuture<Map<Long, OrderDetailModel>> ordersFuture) {
        return ordersFuture.thenApply(orders -> {
            if (ObjectUtils.isEmpty(orders)) {
                return Map.of();
            }
            List<Long> orderIds = orders.values().stream()
                    .map(OrderDetailModel::getOrder)
                    .map(OrderModel::getId)
                    .toList();
            return invoiceClient.listApplyPackages(orderIds).stream()
                    .collect(Collectors.groupingBy(dto -> dto.getInvoiceId().longValue()));
        });
    }

    public CompletableFuture<Map<Long, PaymentSummary>> listPayments(
            CompletableFuture<Map<Long, OrderDetailModel>> ordersFuture,
            CompletableFuture<Map<Long, OrderDetailModel>> noShowOrdersFuture) {
        return ordersFuture.thenCombine(noShowOrdersFuture, (orders, noShowOrders) -> {
            if (ObjectUtils.isEmpty(orders) && ObjectUtils.isEmpty(noShowOrders)) {
                return Map.of();
            }
            List<Integer> orderIds = Stream.concat(
                            orders.values().stream()
                                    .map(OrderDetailModel::getOrder)
                                    .map(OrderModel::getId)
                                    .map(Long::intValue),
                            noShowOrders.values().stream()
                                    .map(OrderDetailModel::getOrder)
                                    .map(OrderModel::getId)
                                    .map(Long::intValue))
                    .toList();
            GetPaymentListParams params = new GetPaymentListParams();
            params.setModule(PaymentMethodEnum.MODULE_GROOMING);
            params.setInvoiceIds(orderIds);
            return paymentClient.getPaymentList(params).getData().stream()
                    .collect(Collectors.toMap(dto -> dto.getInvoiceId().longValue(), Function.identity(), (a, b) -> a));
        });
    }

    public CompletableFuture<Map<Long, PreAuthDTO>> listPreAuthRecords(long companyId, List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(appointmentIds)) {
                        return Map.of();
                    }
                    ListPreAuthParams params = new ListPreAuthParams();
                    params.setCompanyId(companyId);
                    params.setTicketIds(
                            appointmentIds.stream().map(Long::intValue).toList());
                    return preAuthClient.listPreAuthRecords(params).stream()
                            .collect(Collectors.toMap(
                                    dto -> dto.getTicketId().longValue(), Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<AgreementRecordSimpleView>>> listAgreements(
            long companyId, List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(appointmentIds)) {
                        return Map.of();
                    }
                    return agreementRecordService
                            .getRecordListByCompany(GetRecordListByCompanyRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .addAllTargetIds(appointmentIds)
                                    .setStatus(Status.STATUS_NORMAL)
                                    .build())
                            .getAgreementRecordSimpleViewList()
                            .stream()
                            .collect(Collectors.groupingBy(AgreementRecordSimpleView::getTargetId));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BookingRequestModel>> listBookingRequests(
            long companyId, List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(appointmentIds)) {
                        return Map.of();
                    }
                    return bookingRequestService
                            .listBookingRequests(ListBookingRequestsRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .addAllAppointmentIds(appointmentIds)
                                    .addAllAssociatedModels(List.of(
                                            BookingRequestAssociatedModel.SERVICE,
                                            BookingRequestAssociatedModel.ADD_ON,
                                            BookingRequestAssociatedModel.FEEDING,
                                            BookingRequestAssociatedModel.MEDICATION,
                                            BookingRequestAssociatedModel.AUTO_ASSIGN))
                                    .build())
                            .getBookingRequestsList()
                            .stream()
                            .collect(Collectors.toMap(
                                    BookingRequestModel::getAppointmentId, Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BookOnlineDepositDTO>> listBookOnlineDeposits(List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(appointmentIds)) {
                        return Map.of();
                    }
                    return depositClient
                            .getOBDepositByGroomingIds(
                                    appointmentIds.stream().map(Long::intValue).toList())
                            .stream()
                            .collect(Collectors.toMap(
                                    dto -> dto.getGroomingId().longValue(), Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BookOnlineDepositDTO>> listBookOnlineDepositsByRequestIds(
            List<Long> bookingRequestIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(bookingRequestIds)) {
                        return Map.of();
                    }
                    return depositClient.listOBDepositByBookingRequestIds(null, bookingRequestIds).stream()
                            .collect(Collectors.toMap(
                                    BookOnlineDepositDTO::getBookingRequestId, Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, OBBusinessInfoDTO>> listBusinessesInfo(List<Long> businessIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(businessIds)) {
                        return Map.of();
                    }
                    CommonIdsParams params = new CommonIdsParams();
                    params.setIds(businessIds.stream().map(Long::intValue).toList());
                    return businessClient.getBusinessInfoListForOB(params).stream()
                            .collect(
                                    Collectors.toMap(dto -> dto.getId().longValue(), Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BookOnlineDTO>> listBookOnlineConfig(List<Long> businessIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(businessIds)) {
                        return Map.of();
                    }
                    return onlineBookingClient
                            .listOBSetting(
                                    businessIds.stream().map(Long::intValue).toList())
                            .stream()
                            .collect(Collectors.toMap(
                                    dto -> dto.getBusinessId().longValue(), Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, ArrivalWindowSettingDto>> listArrivalWindowSettings(List<Long> businessIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(businessIds)) {
                        return Map.of();
                    }
                    return messageClient
                            .listArrivalWindow(
                                    businessIds.stream().map(Long::intValue).toList())
                            .stream()
                            .collect(Collectors.toMap(
                                    dto -> dto.getBusinessId().longValue(), Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BusinessTwilioNumberDTO>> listBusinessTwilioNumbers(List<Long> businessIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(businessIds)) {
                        return Map.of();
                    }
                    return messageClient
                            .listBusinessTwilioNumber(
                                    businessIds.stream().map(Long::intValue).toList())
                            .stream()
                            .collect(Collectors.toMap(
                                    dto -> dto.getBusinessId().longValue(), Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BusinessCustomerInfoModel>> listBusinessCustomers(List<Long> customerIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(customerIds)) {
                        return Map.of();
                    }
                    return businessCustomerService
                            .batchGetCustomerInfo(BatchGetCustomerInfoRequest.newBuilder()
                                    .addAllIds(customerIds)
                                    .build())
                            .getCustomersList()
                            .stream()
                            .collect(Collectors.toMap(
                                    BusinessCustomerInfoModel::getId, Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BusinessCustomerAddressModel>> listPrimaryAddresses(List<Long> customerIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(customerIds)) {
                        return Map.of();
                    }
                    return customerAddressService
                            .batchGetCustomerPrimaryAddress(BatchGetCustomerPrimaryAddressRequest.newBuilder()
                                    .addAllCustomerIds(customerIds)
                                    .build())
                            .getAddressesMap();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<GetPetDetailListResponse> listPetDetails(long companyId, List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(appointmentIds)) {
                        return GetPetDetailListResponse.getDefaultInstance();
                    }
                    return petDetailService.getPetDetailList(GetPetDetailListRequest.newBuilder()
                            .setCompanyId(companyId)
                            .addAllAppointmentIds(appointmentIds)
                            .build());
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Pair<List<PetDetailModel>, List<EvaluationServiceModel>>> listAllPetDetails(
            long companyId, List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(appointmentIds)) {
                        return Pair.of(List.of(), List.of());
                    }
                    var response = petDetailService.getPetDetailList(GetPetDetailListRequest.newBuilder()
                            .setCompanyId(companyId)
                            .addAllAppointmentIds(appointmentIds)
                            .build());
                    return Pair.of(response.getPetDetailsList(), response.getPetEvaluationsList());
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Pair<List<PetScheduleDef>, List<AppointmentPetScheduleSettingModel>>>
            listFeedingMedication(long companyId, long appointmentId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    var response = appointmentScheduleStub.getPetFeedingMedicationSchedules(
                            GetPetFeedingMedicationSchedulesRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .setAppointmentId(appointmentId)
                                    .build());

                    return Pair.of(response.getSchedulesList(), response.getScheduleSettingsList());
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, ServiceBriefView>> listServices(
            CompletableFuture<GetPetDetailListResponse> petDetailsFuture) {
        return petDetailsFuture.thenApply(petDetailsRes -> {
            if (ObjectUtils.isEmpty(petDetailsRes)) {
                return Map.of();
            }
            var serviceIds = petDetailsRes.getPetDetailsList().stream()
                    .map(PetDetailModel::getServiceId)
                    .collect(Collectors.toSet());
            return serviceManagementService
                    .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                            .addAllServiceIds(serviceIds)
                            .build())
                    .getServicesList()
                    .stream()
                    .collect(Collectors.toMap(ServiceBriefView::getId, Function.identity(), (a, b) -> a));
        });
    }

    public CompletableFuture<Map<Long, ServiceBriefView>> listServices(List<Long> serviceIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(serviceIds)) {
                        return Map.of();
                    }
                    return serviceManagementService
                            .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                                    .addAllServiceIds(serviceIds)
                                    .build())
                            .getServicesList()
                            .stream()
                            .collect(Collectors.toMap(ServiceBriefView::getId, Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, EvaluationBriefView>> listEvaluations(List<Long> evaluationIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(evaluationIds)) {
                        return Map.of();
                    }
                    return getEvaluation(evaluationIds);
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, StaffModel>> listStaffs(
            CompletableFuture<GetPetDetailListResponse> petDetailsFuture) {
        return petDetailsFuture.thenApply(petDetailsRes -> {
            if (ObjectUtils.isEmpty(petDetailsRes)) {
                return Map.of();
            }
            var staffIds = petDetailsRes.getPetDetailsList().stream()
                    .map(PetDetailModel::getStaffId)
                    .filter(staffId -> staffId > 0)
                    .collect(Collectors.toSet());
            staffIds.addAll(petDetailsRes.getPetEvaluationsList().stream()
                    .filter(detail -> detail.hasStaffId() && detail.getStaffId() > 0)
                    .map(EvaluationServiceModel::getStaffId)
                    .collect(Collectors.toSet()));
            if (staffIds.isEmpty()) {
                return Map.of();
            }
            return staffService
                    .queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                            .addAllStaffIds(staffIds)
                            .build())
                    .getStaffsList()
                    .stream()
                    .collect(Collectors.toMap(StaffModel::getId, Function.identity(), (a, b) -> a));
        });
    }

    public CompletableFuture<Map<Long, StaffModel>> listStaffs(List<Long> ids) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(ids)) {
                        return Map.of();
                    }
                    return staffService
                            .queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                                    .addAllStaffIds(ids.stream().distinct().toList())
                                    .build())
                            .getStaffsList()
                            .stream()
                            .collect(Collectors.toMap(StaffModel::getId, Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BusinessCustomerPetInfoModel>> listPets(
            CompletableFuture<GetPetDetailListResponse> petDetailsFuture) {
        return petDetailsFuture.thenApply(petDetailsRes -> {
            if (ObjectUtils.isEmpty(petDetailsRes)) {
                return Map.of();
            }
            var petIds = petDetailsRes.getPetDetailsList().stream()
                    .map(PetDetailModel::getPetId)
                    .collect(Collectors.toSet());
            petIds.addAll(petDetailsRes.getPetEvaluationsList().stream()
                    .map(EvaluationServiceModel::getPetId)
                    .collect(Collectors.toSet()));
            if (petIds.isEmpty()) {
                return Map.of();
            }
            return petService
                    .batchGetPetInfo(BatchGetPetInfoRequest.newBuilder()
                            .addAllIds(petIds)
                            .build())
                    .getPetsList()
                    .stream()
                    .collect(Collectors.toMap(BusinessCustomerPetInfoModel::getId, Function.identity(), (a, b) -> a));
        });
    }

    public CompletableFuture<Map<Long, BusinessCustomerPetInfoModel>> listPets(List<Long> petIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(petIds)) {
                        return Map.of();
                    }
                    return petService
                            .batchGetPetInfo(BatchGetPetInfoRequest.newBuilder()
                                    .addAllIds(new HashSet<>(petIds))
                                    .build())
                            .getPetsList()
                            .stream()
                            .collect(Collectors.toMap(
                                    BusinessCustomerPetInfoModel::getId, Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BusinessCustomerPetModel>> listPetFullInfos(List<Long> petIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(petIds)) {
                        return Map.of();
                    }
                    return petService
                            .batchGetPet(BatchGetPetRequest.newBuilder()
                                    .addAllIds(new HashSet<>(petIds))
                                    .build())
                            .getPetsList()
                            .stream()
                            .collect(Collectors.toMap(
                                    BusinessCustomerPetModel::getId, Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Integer, CustomerAppointmentNumInfoDTO>> listAppointmentCounts(
            long companyId, List<Integer> customerIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(customerIds)) {
                        return Map.of();
                    }
                    return appointmentClient.listCustomerAppointmentNum(companyId, customerIds).stream()
                            .collect(Collectors.toMap(
                                    CustomerAppointmentNumInfoDTO::getCustomerId, Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<CompanyPreferenceSettingModel> getCompanyPreferenceSetting(long companyId) {
        return CompletableFuture.supplyAsync(
                () -> companyService
                        .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                                .setCompanyId(companyId)
                                .build())
                        .getPreferenceSetting(),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<BrandedAppConfigModel> getBrandedAppConfig(long companyId) {
        return CompletableFuture.supplyAsync(
                () -> brandedAppConfigService
                        .mustGetBrandedAppConfig(GetBrandedAppConfigRequest.newBuilder()
                                .setBrandedEntity(GetBrandedAppConfigRequest.BrandedEntity.newBuilder()
                                        .setBrandedType(AccountNamespaceType.COMPANY)
                                        .setBrandedId(companyId)
                                        .build())
                                .build())
                        .getConfig(),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, AppointmentTracking>> listAppointmentTracking(List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(appointmentIds)) {
                        return Map.of();
                    }
                    return appointmentTrackingService
                            .listAppointmentTracking(ListAppointmentTrackingRequest.newBuilder()
                                    .setFilter(ListAppointmentTrackingRequest.Filter.newBuilder()
                                            .addAllAppointmentIds(appointmentIds)
                                            .build())
                                    .build())
                            .getAppointmentTrackingList()
                            .stream()
                            .collect(Collectors.toMap(
                                    AppointmentTracking::getAppointmentId, Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, AppointmentPetScheduleDef>> listAppointmentPetScheduleView(
            long companyId, List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(appointmentIds)) {
                        return Map.of();
                    }
                    return appointmentScheduleStub
                            .batchGetPetFeedingMedicationSchedules(
                                    BatchGetPetFeedingMedicationSchedulesRequest.newBuilder()
                                            .setCompanyId(companyId)
                                            .addAllAppointmentIds(appointmentIds)
                                            .build())
                            .getAppointmentPetSchedulesMap();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, EvaluationBriefView>> listEvaluations(
            CompletableFuture<GetPetDetailListResponse> petDetailListFuture) {
        return petDetailListFuture.thenApplyAsync(
                result -> getEvaluation(result.getPetEvaluationsList().stream()
                        .map(EvaluationServiceModel::getServiceId)
                        .toList()),
                ThreadPool.getSubmitExecutor());
    }

    private Map<Long, EvaluationBriefView> getEvaluation(List<Long> evaluationIds) {
        evaluationIds = evaluationIds.stream().filter(k -> k > 0).distinct().toList();
        if (CollectionUtils.isEmpty(evaluationIds)) {
            return Map.of();
        }
        return evaluationService
                .getEvaluationListWithEvaluationIds(GetEvaluationListWithEvaluationIdsRequest.newBuilder()
                        .addAllEvaluationIds(evaluationIds)
                        .build())
                .getEvaluationsList()
                .stream()
                .collect(Collectors.toMap(EvaluationBriefView::getId, Function.identity(), (a, b) -> a));
    }
}
