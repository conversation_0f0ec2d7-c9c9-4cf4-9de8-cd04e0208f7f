package com.moego.client.api.v1.online_booking.server;

import static com.moego.idl.client.online_booking.v1.BookingCareTypeServiceGrpc.BookingCareTypeServiceImplBase;

import com.moego.client.api.v1.online_booking.converter.BookingCareTypeConverter;
import com.moego.idl.client.online_booking.v1.ListBookingCareTypesParams;
import com.moego.idl.client.online_booking.v1.ListBookingCareTypesResult;
import com.moego.idl.models.online_booking.v1.BookingCareTypeView;
import com.moego.idl.service.online_booking.v1.BookingCareTypeServiceGrpc;
import com.moego.idl.service.online_booking.v1.ListBookingCareTypesRequest;
import com.moego.idl.service.online_booking.v1.ListBookingCareTypesResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class BookingCareTypeServer extends BookingCareTypeServiceImplBase {

    private final IGroomingOnlineBookingService onlineBookingService;

    private final BookingCareTypeServiceGrpc.BookingCareTypeServiceBlockingStub bookingCareTypeService;

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void listBookingCareTypes(
            ListBookingCareTypesParams request, StreamObserver<ListBookingCareTypesResult> responseObserver) {
        OBBusinessDTO biz = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        long businessId = biz.getBusinessId().longValue();
        Long companyId = biz.getCompanyId();

        ListBookingCareTypesRequest serviceRequest =
                BookingCareTypeConverter.INSTANCE.toListRequest(businessId, companyId);
        ListBookingCareTypesResponse response = bookingCareTypeService.listBookingCareTypes(serviceRequest);
        List<BookingCareTypeView> filteredList = new ArrayList<>(response.getBookingCareTypesList());
        filteredList.removeIf(
                bookingCareType -> !bookingCareType.getApplicableServices().getIsAllServiceApplicable()
                        && bookingCareType
                                .getApplicableServices()
                                .getSelectedServicesList()
                                .isEmpty());

        responseObserver.onNext(ListBookingCareTypesResult.newBuilder()
                .addAllBookingCareTypes(filteredList)
                .build());
        responseObserver.onCompleted();
    }
}
