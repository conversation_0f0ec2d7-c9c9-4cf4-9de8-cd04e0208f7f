package com.moego.api.v3.permission.service;

import com.moego.api.v3.permission.vo.PermissionFilterVO;
import com.moego.idl.models.permission.v1.PermissionCategoryModel;
import com.moego.idl.models.permission.v1.PermissionModel;
import com.moego.idl.models.permission.v1.PermissionScopeAvailableRule;
import com.moego.idl.models.permission.v1.PermissionScopeModel;
import com.moego.idl.models.permission.v1.RoleModel;
import com.moego.idl.service.organization.v1.IsMoegoPayEnableRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.permission.PermissionEnums;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class PermissionService {
    private final com.moego.idl.service.organization.v1.CompanyServiceGrpc.CompanyServiceBlockingStub companyClient;
    private final CompanyService companyService;

    public List<PermissionCategoryModel> hidePermissions(List<PermissionCategoryModel> permissionCategories) {
        Long companyId = AuthContext.get().companyId();
        var isMultiLocationFuture = CompletableFuture.supplyAsync(() -> companyService.isMultiLocation(companyId));
        var isMoeGoPayEnableFuture = CompletableFuture.supplyAsync(() -> companyClient
                .isMoegoPayEnable(IsMoegoPayEnableRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getIsMoegoPayEnable());
        var isInBoardingWhiteListFuture =
                CompletableFuture.supplyAsync(() -> companyService.isInBoardingWhiteList(companyId));
        var isInMembershipWhiteListFuture =
                CompletableFuture.supplyAsync(() -> companyService.isInMembershipWhiteList(companyId));

        CompletableFuture.allOf(
                        isMultiLocationFuture,
                        isMoeGoPayEnableFuture,
                        isInBoardingWhiteListFuture,
                        isInMembershipWhiteListFuture)
                .join();

        return permissionCategories.stream()
                .map(permissionCategory -> permissionCategory.toBuilder()
                        .clearPermissionList()
                        .addAllPermissionList(hidePermissions(
                                permissionCategory.getPermissionListList(),
                                new PermissionFilterVO(
                                        isMoeGoPayEnableFuture.join(),
                                        isInBoardingWhiteListFuture.join(),
                                        isInMembershipWhiteListFuture.join(),
                                        isMultiLocationFuture.join())))
                        .build())
                .toList();
    }

    private List<PermissionModel> hidePermissions(
            List<PermissionModel> permissions, PermissionFilterVO permissionFilterVO) {
        if (CollectionUtils.isEmpty(permissions)) {
            return permissions;
        }

        permissions = hidePermissions(permissions, PermissionEnums.ACCESS_AUTO_REMINDERS.getPermissionName());

        // 特殊逻辑：权限 removeProcessingFeeByClient，仅在 MoeGo Pay 开通的时候展示
        if (!permissionFilterVO.isMoeGoPayEnable()) {
            permissions =
                    hidePermissions(permissions, PermissionEnums.REMOVE_PROCESSING_FEE_BY_CLIENT.getPermissionName());
        }

        // 特殊逻辑：Care type 相关权限，Lodging 相关的权限只有 BD 白名单用户可见
        if (!permissionFilterVO.isInBoardingWhiteList()) {
            permissions = hidePermissions(permissions, PermissionEnums.ACCESS_LODGING_SETTING.getPermissionName());
            permissions = hidePermissions(permissions, PermissionEnums.EDIT_CARE_TYPE_NAME.getPermissionName());
        }

        // 特殊逻辑：membership 相关的权限只有 membership 白名单用户可见
        if (!permissionFilterVO.isInMembershipWhiteList()) {
            permissions = hidePermissions(permissions, PermissionEnums.ACCESS_MEMBERSHIP.getPermissionName());
            permissions = hidePermissions(permissions, PermissionEnums.SELL_MEMBERSHIP.getPermissionName());
            permissions = hidePermissions(permissions, PermissionEnums.ACCESS_CLIENT_MEMBERSHIPS.getPermissionName());
            permissions = hidePermissions(permissions, PermissionEnums.OPERATE_MEMBERSHIP.getPermissionName());
        }

        permissions = permissions.stream()
                .map(permission -> {
                    permission = hidePermissionScopes(permission, permissionFilterVO.isMultiLocation());
                    if (!CollectionUtils.isEmpty(permission.getSubPermissionListList())) {
                        PermissionModel.Builder permissionBuilder = permission.toBuilder();
                        permissionBuilder.clearSubPermissionList();
                        permissionBuilder.addAllSubPermissionList(permission.getSubPermissionListList().stream()
                                .map(subPermission ->
                                        hidePermissionScopes(subPermission, permissionFilterVO.isMultiLocation()))
                                .toList());
                        permission = permissionBuilder.build();
                    }
                    return permission;
                })
                .toList();

        return permissions;
    }

    private List<PermissionModel> hidePermissions(List<PermissionModel> permissions, String permissionName) {
        if (CollectionUtils.isEmpty(permissions)) {
            return permissions;
        }
        return permissions.stream()
                .filter(permission -> !permissionName.equals(permission.getName()))
                .map(permission -> permission.toBuilder()
                        .clearSubPermissionList()
                        .addAllSubPermissionList(hidePermissions(permission.getSubPermissionListList(), permissionName))
                        .build())
                .toList();
    }

    private List<String> getAllPermissionNames(List<PermissionModel> permissions) {
        if (CollectionUtils.isEmpty(permissions)) {
            return List.of();
        }
        var permissionNames = permissions.stream().map(PermissionModel::getName).collect(Collectors.toList());

        permissions.forEach(
                permission -> permissionNames.addAll(getAllPermissionNames(permission.getSubPermissionListList())));
        return permissionNames;
    }

    private PermissionModel hidePermissionScopes(PermissionModel permission, boolean isMultiLocation) {
        if (CollectionUtils.isEmpty(permission.getScopeListList())) {
            return permission;
        }
        var scopeAfterHide = hidePermissionScopes(permission.getScopeListList(), isMultiLocation);
        var maxScope = scopeAfterHide.stream()
                .map(PermissionScopeModel::getIndex)
                .max(Long::compareTo)
                .orElse(0L);
        // 由于 accessClient 的 scope index = 3 破坏了原有的 index 越大 scope 越大的逆天设计
        // 此处需要单独做一下特殊处理，在计算 maxScope 时，如果 selected index != 3，则排除 scope index = 3 并重新计算
        if (PermissionEnums.ACCESS_CLIENT.getPermissionName().equals(permission.getName())
                && permission.getSelectedScopeIndex() != 3) {
            maxScope = scopeAfterHide.stream()
                    .map(PermissionScopeModel::getIndex)
                    .filter(index -> index != 3)
                    .max(Long::compareTo)
                    .orElse(0L);
        }
        // 如果当前选择的 scope index 大于可用的 max scope index, 取可用的最大值
        // 需要兼容的场景有: role id 为 0 的 owner, 选了超过 max scope index 的脏数据等
        var result = permission.getSelectedScopeIndex() > maxScope ? maxScope : permission.getSelectedScopeIndex();
        return permission.toBuilder()
                .clearScopeList()
                .setSelectedScopeIndex(result)
                .addAllScopeList(scopeAfterHide)
                .build();
    }

    private List<PermissionScopeModel> hidePermissionScopes(
            List<PermissionScopeModel> permissionScopes, boolean isMultiLocation) {
        if (CollectionUtils.isEmpty(permissionScopes)) {
            return permissionScopes;
        }
        return permissionScopes.stream()
                .filter(permissionScope -> isMultiLocation
                        ? permissionScope.getAvailableRule() != PermissionScopeAvailableRule.ONLY_SINGLE_LOCATION
                        : permissionScope.getAvailableRule() != PermissionScopeAvailableRule.ONLY_MULTI_LOCATION)
                .toList();
    }

    public RoleModel hidePermissions(RoleModel role) {
        if (role == null) {
            return role;
        }
        var permissionCategoryList = hidePermissions(role.getPermissionCategoryListList());

        return role.toBuilder()
                .clearPermissionCategoryList()
                .addAllPermissionCategoryList(permissionCategoryList)
                .build();
    }
}
