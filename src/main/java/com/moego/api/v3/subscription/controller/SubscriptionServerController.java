package com.moego.api.v3.subscription.controller;

import com.moego.api.v3.subscription.service.SubscriptionService;
import com.moego.idl.api.subscription.v1.CreateSubscriptionParams;
import com.moego.idl.api.subscription.v1.CreateSubscriptionResult;
import com.moego.idl.api.subscription.v1.GetCreditParams;
import com.moego.idl.api.subscription.v1.GetCreditResult;
import com.moego.idl.api.subscription.v1.ListCreditChangeHistoryParams;
import com.moego.idl.api.subscription.v1.ListCreditChangeHistoryResult;
import com.moego.idl.api.subscription.v1.ListEntitlementsParams;
import com.moego.idl.api.subscription.v1.ListEntitlementsResult;
import com.moego.idl.api.subscription.v1.ListProductsParams;
import com.moego.idl.api.subscription.v1.ListProductsResult;
import com.moego.idl.api.subscription.v1.ListSubscriptionsParams;
import com.moego.idl.api.subscription.v1.ListSubscriptionsResult;
import com.moego.idl.api.subscription.v1.PreviewPurchasesParams;
import com.moego.idl.api.subscription.v1.PreviewPurchasesResult;
import com.moego.idl.api.subscription.v1.SubscriptionServiceGrpc;
import com.moego.idl.api.subscription.v1.UpdateCreditParams;
import com.moego.idl.api.subscription.v1.UpdateCreditResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@GrpcService
@Slf4j
public class SubscriptionServerController extends SubscriptionServiceGrpc.SubscriptionServiceImplBase {
    @Autowired
    private SubscriptionService subscriptionService;

    @Override
    @Auth(AuthType.COMPANY)
    public void listProducts(ListProductsParams request, StreamObserver<ListProductsResult> responseObserver) {
        var response = subscriptionService.listProducts(AuthContext.get().companyId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void createSubscription(
            CreateSubscriptionParams request, StreamObserver<CreateSubscriptionResult> responseObserver) {
        var response = subscriptionService.createSubscription(AuthContext.get().companyId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listSubscriptions(
            ListSubscriptionsParams request, StreamObserver<ListSubscriptionsResult> responseObserver) {
        var response = subscriptionService.listSubscriptions(AuthContext.get().companyId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void previewPurchases(
            PreviewPurchasesParams request, StreamObserver<PreviewPurchasesResult> responseObserver) {
        var response = subscriptionService.previewPurchases(AuthContext.get().companyId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listEntitlements(
            ListEntitlementsParams request, StreamObserver<ListEntitlementsResult> responseObserver) {
        var response = subscriptionService.listEntitlements(AuthContext.get().companyId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateCredit(UpdateCreditParams request, StreamObserver<UpdateCreditResult> responseObserver) {
        var response = subscriptionService.updateCredit(request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listCreditChangeHistory(
            ListCreditChangeHistoryParams request, StreamObserver<ListCreditChangeHistoryResult> responseObserver) {
        var response = subscriptionService.listCreditChangeHistory(request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getCredit(GetCreditParams request, StreamObserver<GetCreditResult> responseObserver) {
        var response = subscriptionService.getCredit(request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
