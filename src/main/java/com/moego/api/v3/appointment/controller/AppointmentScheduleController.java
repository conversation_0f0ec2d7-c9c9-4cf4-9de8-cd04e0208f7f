package com.moego.api.v3.appointment.controller;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.api.v3.appointment.converter.AppointmentConverter;
import com.moego.api.v3.appointment.service.AppointmentService;
import com.moego.api.v3.appointment.utils.FeedingMedicationUtil;
import com.moego.idl.api.appointment.v1.AppointmentScheduleServiceGrpc;
import com.moego.idl.api.appointment.v1.BatchRescheduleAppointmentParams;
import com.moego.idl.api.appointment.v1.BatchRescheduleAppointmentResult;
import com.moego.idl.api.appointment.v1.CalculateAppointmentScheduleParams;
import com.moego.idl.api.appointment.v1.CalculateAppointmentScheduleResult;
import com.moego.idl.api.appointment.v1.GetPetFeedingMedicationSchedulesParams;
import com.moego.idl.api.appointment.v1.GetPetFeedingMedicationSchedulesResult;
import com.moego.idl.api.appointment.v1.LodgingAssignParams;
import com.moego.idl.api.appointment.v1.LodgingAssignResult;
import com.moego.idl.api.appointment.v1.RescheduleAppointmentParams;
import com.moego.idl.api.appointment.v1.RescheduleAppointmentResult;
import com.moego.idl.api.appointment.v1.RescheduleBoardingServiceParams;
import com.moego.idl.api.appointment.v1.RescheduleBoardingServiceResult;
import com.moego.idl.api.appointment.v1.RescheduleCalendarCardParams;
import com.moego.idl.api.appointment.v1.RescheduleCalendarCardResult;
import com.moego.idl.api.appointment.v1.RescheduleDaycareServiceParams;
import com.moego.idl.api.appointment.v1.RescheduleDaycareServiceResult;
import com.moego.idl.api.appointment.v1.RescheduleEvaluationServiceParams;
import com.moego.idl.api.appointment.v1.RescheduleEvaluationServiceResult;
import com.moego.idl.api.appointment.v1.RescheduleGroomingServiceParams;
import com.moego.idl.api.appointment.v1.RescheduleGroomingServiceResult;
import com.moego.idl.api.appointment.v1.ReschedulePetDetailsParams;
import com.moego.idl.api.appointment.v1.ReschedulePetDetailsResult;
import com.moego.idl.api.appointment.v1.ReschedulePetFeedingMedicationParams;
import com.moego.idl.api.appointment.v1.ReschedulePetFeedingMedicationResult;
import com.moego.idl.api.appointment.v1.SwitchAllPetsStartAtSameTimeParams;
import com.moego.idl.api.appointment.v1.SwitchAllPetsStartAtSameTimeResult;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.CalendarCardType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.appointment.v1.AppointmentScheduleServiceGrpc.AppointmentScheduleServiceBlockingStub;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.BatchRescheduleAppointmentRequest;
import com.moego.idl.service.appointment.v1.CalculateAppointmentScheduleRequest;
import com.moego.idl.service.appointment.v1.CalculateAppointmentScheduleResponse;
import com.moego.idl.service.appointment.v1.GetAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetPetFeedingMedicationSchedulesRequest;
import com.moego.idl.service.appointment.v1.GetPetFeedingMedicationSchedulesResponse;
import com.moego.idl.service.appointment.v1.LodgingAssignRequest;
import com.moego.idl.service.appointment.v1.RescheduleBoardingServiceRequest;
import com.moego.idl.service.appointment.v1.RescheduleBoardingServiceResponse;
import com.moego.idl.service.appointment.v1.RescheduleDaycareServiceRequest;
import com.moego.idl.service.appointment.v1.RescheduleDaycareServiceResponse;
import com.moego.idl.service.appointment.v1.RescheduleEvaluationServiceRequest;
import com.moego.idl.service.appointment.v1.ReschedulePetFeedingMedicationRequest;
import com.moego.idl.service.appointment.v1.SwitchAllPetsStartAtSameTimeRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/2/18
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class AppointmentScheduleController extends AppointmentScheduleServiceGrpc.AppointmentScheduleServiceImplBase {

    private final AppointmentScheduleServiceBlockingStub appointmentScheduleService;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;
    private final PermissionHelper permissionHelper;
    private final FeedingMedicationUtil feedingMedicationUtil;
    private final AppointmentService appointmentService;

    private void checkReschedulePermission(long appointmentId) {
        var companyId = AuthContext.get().companyId();
        var businessId = AuthContext.get().businessId();
        var tokenStaffId = AuthContext.get().staffId();
        AppointmentModel appointment = appointmentStub
                .getAppointment(GetAppointmentRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .setAppointmentId(appointmentId)
                        .build())
                .getAppointment();

        PermissionEnums permissionEnums =
                appointment.getIsBlock() ? PermissionEnums.CREATE_AND_EDIT_BLOCK : PermissionEnums.EDIT_APPOINTMENT;

        permissionHelper.checkPermission(companyId, Set.of(appointment.getBusinessId()), tokenStaffId, permissionEnums);
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void rescheduleBoardingService(
            RescheduleBoardingServiceParams request, StreamObserver<RescheduleBoardingServiceResult> responseObserver) {
        checkReschedulePermission(request.getAppointmentId());

        RescheduleBoardingServiceRequest boardingServiceRequest =
                AppointmentConverter.INSTANCE.paramsToRequest(request).toBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(AuthContext.get().businessId())
                        .setStaffId(AuthContext.get().staffId())
                        .build();
        RescheduleBoardingServiceResponse response =
                appointmentScheduleService.rescheduleBoardingService(boardingServiceRequest);

        responseObserver.onNext(RescheduleBoardingServiceResult.newBuilder()
                .addAllConflictServiceNames(response.getConflictServiceNamesList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void rescheduleDaycareService(
            RescheduleDaycareServiceParams request, StreamObserver<RescheduleDaycareServiceResult> r) {

        checkAppointmentOwnership(request.getAppointmentId());

        RescheduleDaycareServiceRequest daycareServiceRequest = RescheduleDaycareServiceRequest.newBuilder()
                .setAppointmentId(request.getAppointmentId())
                .addAllDaycareServiceSchedules(AppointmentConverter.INSTANCE.rescheduleDaycareServiceToRequest(
                        request.getDaycareServiceSchedulesList()))
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .setStaffId(AuthContext.get().staffId())
                .build();
        RescheduleDaycareServiceResponse response =
                appointmentScheduleService.rescheduleDaycareService(daycareServiceRequest);

        r.onNext(RescheduleDaycareServiceResult.newBuilder()
                .addAllConflictServiceNames(response.getConflictServiceNamesList())
                .build());
        r.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void rescheduleGroomingService(
            RescheduleGroomingServiceParams request, StreamObserver<RescheduleGroomingServiceResult> responseObserver) {
        checkReschedulePermission(request.getAppointmentId());

        appointmentScheduleService.rescheduleGroomingService(
                AppointmentConverter.INSTANCE.paramsToRequest(request).toBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .build());

        responseObserver.onNext(RescheduleGroomingServiceResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void rescheduleEvaluationService(
            RescheduleEvaluationServiceParams request,
            StreamObserver<RescheduleEvaluationServiceResult> responseObserver) {
        checkReschedulePermission(request.getAppointmentId());

        RescheduleEvaluationServiceRequest evaluationServiceRequest =
                AppointmentConverter.INSTANCE.paramsToRequest(request).toBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(AuthContext.get().businessId())
                        .setStaffId(AuthContext.get().staffId())
                        .build();

        appointmentScheduleService.rescheduleEvaluationService(evaluationServiceRequest);
        responseObserver.onNext(RescheduleEvaluationServiceResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void lodgingAssign(LodgingAssignParams request, StreamObserver<LodgingAssignResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        long appointmentId = request.getAppointmentId();
        checkReschedulePermission(appointmentId);

        var req = LodgingAssignRequest.newBuilder()
                .setCompanyId(companyId)
                .setAppointmentId(appointmentId)
                .addAllServices(request.getServicesList())
                .addAllEvaluations(request.getEvaluationsList())
                .build();

        appointmentScheduleService.lodgingAssign(req);

        responseObserver.onNext(LodgingAssignResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getPetFeedingMedicationSchedules(
            GetPetFeedingMedicationSchedulesParams request,
            StreamObserver<GetPetFeedingMedicationSchedulesResult> responseObserver) {
        GetPetFeedingMedicationSchedulesRequest schedulesRequest = GetPetFeedingMedicationSchedulesRequest.newBuilder()
                .setAppointmentId(request.getAppointmentId())
                .setCompanyId(AuthContext.get().companyId())
                .build();
        GetPetFeedingMedicationSchedulesResponse response =
                appointmentScheduleService.getPetFeedingMedicationSchedules(schedulesRequest);

        responseObserver.onNext(GetPetFeedingMedicationSchedulesResult.newBuilder()
                .addAllSchedules(response.getSchedulesList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void reschedulePetFeedingMedication(
            ReschedulePetFeedingMedicationParams request,
            StreamObserver<ReschedulePetFeedingMedicationResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        checkReschedulePermission(request.getAppointmentId());

        ReschedulePetFeedingMedicationRequest feedingMedicationRequest =
                ReschedulePetFeedingMedicationRequest.newBuilder()
                        .setAppointmentId(request.getAppointmentId())
                        .addAllSchedules(request.getSchedulesList())
                        .setCompanyId(companyId)
                        .setBusinessId(AuthContext.get().businessId())
                        .setStaffId(AuthContext.get().staffId())
                        .build();
        appointmentScheduleService.reschedulePetFeedingMedication(feedingMedicationRequest);

        feedingMedicationUtil.syncPetScheduleDef(companyId, request.getSchedulesList());

        responseObserver.onNext(ReschedulePetFeedingMedicationResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void calculateAppointmentSchedule(
            CalculateAppointmentScheduleParams request,
            StreamObserver<CalculateAppointmentScheduleResult> responseObserver) {
        CalculateAppointmentScheduleRequest calculateAppointmentScheduleRequest =
                CalculateAppointmentScheduleRequest.newBuilder()
                        .setAppointment(request.getAppointment())
                        .addAllPetServiceSchedules(request.getPetServiceSchedulesList())
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(AuthContext.get().businessId())
                        .setStaffId(AuthContext.get().staffId())
                        .build();
        CalculateAppointmentScheduleResponse response =
                appointmentScheduleService.calculateAppointmentSchedule(calculateAppointmentScheduleRequest);

        responseObserver.onNext(CalculateAppointmentScheduleResult.newBuilder()
                .setAppointmentSchedule(response.getAppointmentSchedule())
                .addAllPetServiceSchedules(response.getPetServiceSchedulesList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void batchRescheduleAppointment(
            BatchRescheduleAppointmentParams request,
            StreamObserver<BatchRescheduleAppointmentResult> responseObserver) {
        if (!request.hasTargetDate() && !request.hasTargetStaffId()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "targetDate or targetStaffId must be provided");
        }

        List<Long> appointmentIds = appointmentService.getAppointmentByStaffAndDate(
                request.getSourceStaffId(), request.getSourceDate(), false);

        if (appointmentIds.isEmpty()) {
            responseObserver.onNext(BatchRescheduleAppointmentResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        BatchRescheduleAppointmentRequest.Builder builder = BatchRescheduleAppointmentRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setSourceStaffId(request.getSourceStaffId())
                .addAllAppointmentIds(appointmentIds)
                .setRescheduleBy(AuthContext.get().staffId());

        if (request.hasTargetStaffId()) {
            builder.setTargetStaffId(request.getTargetStaffId());
        }

        if (request.hasTargetDate()) {
            builder.setTargetDate(request.getTargetDate());
        }

        responseObserver.onNext(BatchRescheduleAppointmentResult.newBuilder()
                .addAllAppointments(appointmentScheduleService
                        .batchRescheduleAppointment(builder.build())
                        .getAppointmentsList())
                .build());
        responseObserver.onCompleted();
    }

    private void checkAppointmentOwnership(long appointmentId) {
        var appointment = appointmentStub.getAppointment(GetAppointmentRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setAppointmentId(appointmentId)
                .build());
        if (!appointment.hasAppointment()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "appointment not found: " + appointmentId);
        }
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void rescheduleCalendarCard(
            RescheduleCalendarCardParams request, StreamObserver<RescheduleCalendarCardResult> responseObserver) {
        checkReschedulePermission(request.getAppointmentId());

        var rescheduleCalendarCardRequest = AppointmentConverter.INSTANCE.paramsToRequest(request).toBuilder()
                .setUpdatedBy(AuthContext.get().staffId())
                .build();
        appointmentScheduleService.rescheduleCalendarCard(rescheduleCalendarCardRequest);

        responseObserver.onNext(RescheduleCalendarCardResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void rescheduleAppointment(
            RescheduleAppointmentParams request, StreamObserver<RescheduleAppointmentResult> responseObserver) {
        checkReschedulePermission(request.getAppointmentId());

        var rescheduleCalendarCardRequest = AppointmentConverter.INSTANCE.paramsToRequest(request).toBuilder()
                .setCardType(CalendarCardType.APPOINTMENT)
                .setMoveAllCards(true)
                .setUpdatedBy(AuthContext.get().staffId())
                .build();
        appointmentScheduleService.rescheduleCalendarCard(rescheduleCalendarCardRequest);

        responseObserver.onNext(RescheduleAppointmentResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void switchAllPetsStartAtSameTime(
            SwitchAllPetsStartAtSameTimeParams request,
            StreamObserver<SwitchAllPetsStartAtSameTimeResult> responseObserver) {
        checkReschedulePermission(request.getAppointmentId());

        var switchRequest = SwitchAllPetsStartAtSameTimeRequest.newBuilder()
                .setAppointmentId(request.getAppointmentId())
                .setAllPetsStartAtSameTime(request.getAllPetsStartAtSameTime())
                .build();
        appointmentScheduleService.switchAllPetsStartAtSameTime(switchRequest);

        responseObserver.onNext(SwitchAllPetsStartAtSameTimeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void reschedulePetDetails(
            ReschedulePetDetailsParams request, StreamObserver<ReschedulePetDetailsResult> responseObserver) {
        checkReschedulePermission(request.getAppointmentId());

        var rescheduleRequest = AppointmentConverter.INSTANCE.paramsToRequest(request);
        appointmentScheduleService.reschedulePetDetails(rescheduleRequest);

        responseObserver.onNext(ReschedulePetDetailsResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
