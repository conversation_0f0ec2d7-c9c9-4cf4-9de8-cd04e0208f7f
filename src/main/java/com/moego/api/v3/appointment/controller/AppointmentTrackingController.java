package com.moego.api.v3.appointment.controller;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.api.v3.appointment.converter.AppointmentTrackingConverter;
import com.moego.api.v3.branded_app.service.BrandedAppService;
import com.moego.idl.api.appointment.v1.AppointmentTrackingServiceGrpc;
import com.moego.idl.api.appointment.v1.GetAppointmentTrackingParams;
import com.moego.idl.api.appointment.v1.GetAppointmentTrackingResult;
import com.moego.idl.api.appointment.v1.ListStaffAppointmentTrackingParams;
import com.moego.idl.api.appointment.v1.ListStaffAppointmentTrackingResult;
import com.moego.idl.api.appointment.v1.UpdateAppointmentTrackingStatusParams;
import com.moego.idl.api.appointment.v1.UpdateAppointmentTrackingStatusResult;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetAppointmentListRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTrackingRequest;
import com.moego.idl.service.appointment.v1.UpdateStaffLocationStatusRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.map.v1.RoutesServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcRequestContext;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.Metadata;
import io.grpc.stub.StreamObserver;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class AppointmentTrackingController extends AppointmentTrackingServiceGrpc.AppointmentTrackingServiceImplBase {

    private final com.moego.idl.service.appointment.v1.AppointmentTrackingServiceGrpc
                    .AppointmentTrackingServiceBlockingStub
            appointmentTrackingServiceBlockingStub;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentServiceBlockingStub;
    private final RoutesServiceGrpc.RoutesServiceBlockingStub routesService;
    private final BrandedAppService brandedAppService;
    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub
            businessCustomerAddressServiceBlockingStub;
    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void getAppointmentTracking(
            GetAppointmentTrackingParams request, StreamObserver<GetAppointmentTrackingResult> responseObserver) {
        var appointment = appointmentServiceBlockingStub
                .getAppointment(GetAppointmentRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setAppointmentId(request.getAppointmentId())
                        .build())
                .getAppointment();
        if (AuthContext.get().companyId() != appointment.getCompanyId()) {
            // 下游查不到也会报错，额外防一手
            throw bizException(Code.CODE_APPOINTMENT_NOT_FOUND);
        }
        var appointmentTrackingList = appointmentTrackingServiceBlockingStub
                .listAppointmentTracking(ListAppointmentTrackingRequest.newBuilder()
                        .setFilter(ListAppointmentTrackingRequest.Filter.newBuilder()
                                .addAppointmentIds(request.getAppointmentId())
                                .build())
                        .build())
                .getAppointmentTrackingList();
        if (appointmentTrackingList.isEmpty()) {
            responseObserver.onNext(GetAppointmentTrackingResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        responseObserver.onNext(GetAppointmentTrackingResult.newBuilder()
                .setAppointmentTracking(
                        AppointmentTrackingConverter.INSTANCE.toAppointmentTrackingView(appointmentTrackingList.get(0)))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateAppointmentTrackingStatus(
            UpdateAppointmentTrackingStatusParams request,
            StreamObserver<UpdateAppointmentTrackingStatusResult> responseObserver) {

        if (!brandedAppService.isBrandedAppUser(AuthContext.get().companyId())) {
            responseObserver.onNext(UpdateAppointmentTrackingStatusResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        String deviceId = null;
        if (request.getLocationSharingDeviceId().isEmpty()) {
            var ctx = GrpcRequestContext.get();
            if (ctx != null) {
                Metadata headers = ctx.headers();
                deviceId = headers.get(Metadata.Key.of("mgdid", Metadata.ASCII_STRING_MARSHALLER));
            }
            if (!StringUtils.hasText(deviceId)) {
                deviceId = "";
            }
        } else {
            deviceId = request.getLocationSharingDeviceId();
        }
        var res = appointmentTrackingServiceBlockingStub
                .updateStaffLocationStatus(UpdateStaffLocationStatusRequest.newBuilder()
                        .setAppointmentId(request.getAppointmentId())
                        .setCompanyId(AuthContext.get().companyId())
                        .setStaffId(AuthContext.get().staffId())
                        .setStaffCoordinate(request.getStaffCoordinate())
                        .setDeviceId(deviceId)
                        .setStaffLocationStatus(request.getStaffLocationStatus())
                        .build())
                .getAppointmentTracking();
        responseObserver.onNext(UpdateAppointmentTrackingStatusResult.newBuilder()
                .setAppointmentTracking(AppointmentTrackingConverter.INSTANCE.toAppointmentTrackingView(res))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listStaffAppointmentTracking(
            ListStaffAppointmentTrackingParams request,
            StreamObserver<ListStaffAppointmentTrackingResult> responseObserver) {

        var result = appointmentTrackingServiceBlockingStub.listAppointmentTracking(
                ListAppointmentTrackingRequest.newBuilder()
                        .setPagination(request.getPagination())
                        .setFilter(AppointmentTrackingConverter.INSTANCE.toSvcFilter(request.getFilter()).toBuilder()
                                .addLocationSharingStaffIds(AuthContext.get().staffId())
                                .build())
                        .build());

        var resBuilder = ListStaffAppointmentTrackingResult.newBuilder()
                .addAllAppointmentTracking(AppointmentTrackingConverter.INSTANCE.toAppointmentTrackingView(
                        result.getAppointmentTrackingList()))
                .setPagination(result.getPagination());
        if (result.getAppointmentTrackingList().isEmpty()) {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        var appointmentIds = result.getAppointmentTrackingList().stream()
                .map(com.moego.idl.models.appointment.v1.AppointmentTracking::getAppointmentId)
                .toList();
        var appointments = appointmentServiceBlockingStub
                .getAppointmentList(GetAppointmentListRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .addAllAppointmentId(appointmentIds)
                        .build())
                .getAppointmentsList();
        var idToAppointment =
                appointments.stream().collect(Collectors.toMap(AppointmentModel::getId, Function.identity()));
        // filter by company id
        var appointmentTrackingList = result.getAppointmentTrackingList().stream()
                .filter(appointmentTracking -> idToAppointment.containsKey(appointmentTracking.getAppointmentId()))
                .toList();
        resBuilder
                .clearAppointmentTracking()
                .addAllAppointmentTracking(
                        AppointmentTrackingConverter.INSTANCE.toAppointmentTrackingView(appointmentTrackingList));
        if (!appointmentTrackingList.isEmpty()) {
            if (request.getExtraInfoRequest().getRequestAppointmentInfo()) {
                idToAppointment.forEach((key, value) -> resBuilder.putAppointmentInfo(
                        key,
                        ListStaffAppointmentTrackingResult.AppointmentView.newBuilder()
                                .setAppointment(value)
                                .build()));
            }
            if (request.getExtraInfoRequest().getRequestCustomerInfo()) {
                var clientIds = appointments.stream()
                        .map(AppointmentModel::getCustomerId)
                        .distinct()
                        .toList();
                var customersList = businessCustomerServiceBlockingStub
                        .batchGetCustomerInfo(BatchGetCustomerInfoRequest.newBuilder()
                                .addAllIds(clientIds)
                                .setTenant(Tenant.newBuilder()
                                        .setCompanyId(AuthContext.get().companyId())
                                        .build())
                                .build())
                        .getCustomersList();
                var idToCustomer = customersList.stream()
                        .collect(Collectors.toMap(BusinessCustomerInfoModel::getId, Function.identity()));
                idToAppointment.forEach((key, value) -> {
                    var customer = idToCustomer.get(value.getCustomerId());
                    if (customer != null) {
                        resBuilder.putCustomerInfo(
                                value.getCustomerId(),
                                ListStaffAppointmentTrackingResult.CustomerView.newBuilder()
                                        .setCustomerProfile(customer)
                                        .build());
                    }
                });
            }
        }
        responseObserver.onNext(resBuilder.build());
        responseObserver.onCompleted();
    }
}
