package com.moego.api.v3.appointment.utils;

import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v2.PricingRuleApplyLogModel;
import com.moego.idl.models.offering.v2.ConditionGroup;
import com.moego.idl.models.offering.v2.ConditionType;
import com.moego.idl.models.offering.v2.GenericValue;
import com.moego.idl.models.offering.v2.PricingRule;
import com.moego.idl.models.offering.v2.PricingRuleConfiguration;
import com.moego.idl.models.offering.v2.RuleType;
import com.moego.idl.utils.v2.StringDateRange;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.experimental.UtilityClass;
import org.springframework.util.StringUtils;

@UtilityClass
public class PricingRuleUtil {

    public static PricingRuleApplyLogModel process(PricingRuleApplyLogModel log, List<PetDetailModel> petDetails) {
        return processPeakDateRule(processAveragePrice(log, petDetails));
    }

    /**
     * Process average price for pricing rule
     *
     * @param log        The pricing rule apply log to process
     * @param petDetails The pet details to process
     * @return Processed pricing rule apply log
     */
    public static PricingRuleApplyLogModel processAveragePrice(
            PricingRuleApplyLogModel log, List<PetDetailModel> petDetails) {
        Optional<PetDetailModel> matchingPetDetail = petDetails.stream()
                .filter(detail -> Objects.equals(detail.getPetId(), log.getPetId())
                        && Objects.equals(detail.getServiceId(), log.getServiceId()))
                .findAny();
        if (matchingPetDetail.isPresent()) {
            PetDetailModel petDetail = matchingPetDetail.get();
            return log.toBuilder().setAdjustedPrice(petDetail.getServicePrice()).build();
        }
        return log;
    }

    /**
     * Process pricing rule for peak date type
     *
     * @param log The pricing rule apply log to process
     * @return Processed pricing rule apply log
     */
    public static PricingRuleApplyLogModel processPeakDateRule(PricingRuleApplyLogModel log) {
        if (!isPeakDateRuleEligible(log)) {
            return log;
        }

        var validPeakDates = extractValidPeakDates(log);
        return buildUpdatedLog(log, validPeakDates);
    }

    private static boolean isPeakDateRuleEligible(PricingRuleApplyLogModel log) {
        return Objects.equals(RuleType.PEAK_DATE, log.getPricingRule().getType()) && log.hasServiceDate();
    }

    private static List<ConditionGroup> extractValidPeakDates(PricingRuleApplyLogModel log) {
        LocalDate serviceDate = LocalDate.parse(log.getServiceDate());

        return Optional.of(log.getPricingRule())
                .map(PricingRule::getRuleConfiguration)
                .map(PricingRuleConfiguration::getConditionGroupsList)
                .stream()
                .flatMap(List::stream)
                .map(conditionGroup -> {
                    var list = conditionGroup.getConditionsList().stream()
                            .filter(item -> Objects.equals(ConditionType.DATE_RANGE, item.getType())
                                    || Objects.equals(ConditionType.REPEAT_DATES, item.getType()))
                            .filter(item -> isDateInRange(serviceDate, item.getValue()))
                            .toList();
                    return ConditionGroup.newBuilder()
                            .setEffect(conditionGroup.getEffect())
                            .addAllConditions(list)
                            .build();
                })
                .toList();
    }

    private static boolean isDateInRange(LocalDate serviceDate, GenericValue value) {
        return switch (value.getValueCase()) {
            case DATE_RANGE -> isDateInRange(serviceDate, value.getDateRange());
            case REPEAT_DATES -> isDateInRange(
                    serviceDate, value.getRepeatDates().getDateRange());
            default -> false;
        };
    }

    private static boolean isDateInRange(LocalDate serviceDate, StringDateRange dateRange) {
        if (!StringUtils.hasText(dateRange.getStartDate()) || !StringUtils.hasText(dateRange.getEndDate())) {
            return false;
        }
        LocalDate startDate = LocalDate.parse(dateRange.getStartDate());
        LocalDate endDate = LocalDate.parse(dateRange.getEndDate());
        return !serviceDate.isBefore(startDate) && !serviceDate.isAfter(endDate);
    }

    private static PricingRuleApplyLogModel buildUpdatedLog(
            PricingRuleApplyLogModel log, List<ConditionGroup> validPeakDates) {

        return log.toBuilder()
                .setPricingRule(buildUpdatedPricingRule(log.getPricingRule(), validPeakDates))
                .build();
    }

    private static PricingRule buildUpdatedPricingRule(PricingRule rule, List<ConditionGroup> validPeakDates) {

        return rule.toBuilder()
                .setRuleConfiguration(buildUpdatedConfiguration(validPeakDates))
                .build();
    }

    private static PricingRuleConfiguration buildUpdatedConfiguration(List<ConditionGroup> validPeakDates) {

        return PricingRuleConfiguration.newBuilder()
                .addAllConditionGroups(validPeakDates)
                .build();
    }
}
