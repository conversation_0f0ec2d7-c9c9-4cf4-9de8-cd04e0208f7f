package com.moego.api.v3.appointment.converter;

import com.moego.idl.api.appointment.v1.ListDayCardsResult;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface PetVaccineConverter {

    PetVaccineConverter INSTANCE = Mappers.getMapper(PetVaccineConverter.class);

    List<ListDayCardsResult.CalendarCardVaccineAlertInfo> toView(List<VaccineBindingRecordDto> dtos);

    List<ListDayCardsResult.CalendarCardVaccineInfo> toInfoView(List<VaccineBindingRecordDto> dtos);

    ListDayCardsResult.CalendarCardVaccineAlertInfo toView(VaccineBindingRecordDto dto);
}
