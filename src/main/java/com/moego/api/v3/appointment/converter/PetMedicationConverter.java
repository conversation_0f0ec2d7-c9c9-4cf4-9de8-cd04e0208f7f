package com.moego.api.v3.appointment.converter;

import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.business_customer.v1.BusinessPetMedicationScheduleDef;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/1/26
 */
@Mapper()
public interface PetMedicationConverter {

    PetMedicationConverter INSTANCE = Mappers.getMapper(PetMedicationConverter.class);

    default BusinessPetMedicationScheduleDef toBusinessPetMedicationScheduleDef(
            Long petId, AppointmentPetMedicationScheduleDef def) {
        return BusinessPetMedicationScheduleDef.newBuilder()
                .setPetId(petId)
                .setMedicationAmount(def.getMedicationAmount())
                .setMedicationUnit(def.getMedicationUnit())
                .setMedicationName(def.getMedicationName())
                .setMedicationNote(def.getMedicationNote())
                .addAllMedicationTimes(def.getMedicationTimesList())
                .build();
    }
}
