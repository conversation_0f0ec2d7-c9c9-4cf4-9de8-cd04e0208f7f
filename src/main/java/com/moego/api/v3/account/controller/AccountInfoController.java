package com.moego.api.v3.account.controller;

import com.moego.api.v3.account.config.SessionConfig;
import com.moego.api.v3.account.convertor.ProtoConvertor;
import com.moego.api.v3.account.helper.AccountHelper;
import com.moego.api.v3.account.helper.GrpcCookieHelper;
import com.moego.api.v3.account.helper.RiskControlHelper;
import com.moego.api.v3.account.helper.SessionHelper;
import com.moego.idl.api.account.v1.AccountInfoServiceGrpc;
import com.moego.idl.api.account.v1.AddPhoneNumberRequest;
import com.moego.idl.api.account.v1.AddPhoneNumberResponse;
import com.moego.idl.api.account.v1.CheckIdentifierAvailableRequest;
import com.moego.idl.api.account.v1.GetAccountInfoRequest;
import com.moego.idl.api.account.v1.GetAccountInfoResponse;
import com.moego.idl.api.account.v1.GetRelevantAccountsRequest;
import com.moego.idl.api.account.v1.GetRelevantAccountsResponse;
import com.moego.idl.api.account.v1.UpdatePasswordRequest;
import com.moego.idl.api.account.v1.UpdatePasswordResponse;
import com.moego.idl.api.account.v1.UpdateProfileRequest;
import com.moego.idl.api.account.v1.UpdateProfileResponse;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.account.v1.UpdateAccountRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.business.client.IBusinessAccountClient;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class AccountInfoController extends AccountInfoServiceGrpc.AccountInfoServiceImplBase {

    private final GrpcCookieHelper cookieHelper;
    private final SessionHelper sessionHelper;
    private final AccountHelper accountHelper;
    private final SessionConfig sessionConfig;
    private final RiskControlHelper riskControlHelper;
    private final IBusinessAccountClient iBusinessAccountClient;
    private final ProtoConvertor protoConvertor;

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void getAccountInfo(GetAccountInfoRequest request, StreamObserver<GetAccountInfoResponse> responseObserver) {
        // 会话中有 account id, 则不是匿名访问
        var accountId = AuthContext.get().accountId();

        // get account by id
        AccountModel account = null;
        if (accountId != null) {
            account = accountHelper.getAccountById(accountId);
        }

        // build response
        var builder = GetAccountInfoResponse.newBuilder();
        if (account != null) {
            builder.setAccount(account);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void updateProfile(UpdateProfileRequest request, StreamObserver<UpdateProfileResponse> responseObserver) {
        long accountId = AuthContext.get().accountId();

        var account = accountHelper.updateProfile(accountId, request);

        var response = UpdateProfileResponse.newBuilder().setAccount(account).build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void updatePassword(UpdatePasswordRequest request, StreamObserver<UpdatePasswordResponse> responseObserver) {
        long accountId = AuthContext.get().accountId();

        // 1. validate old password
        boolean correct = accountHelper.validatePassword(accountId, request.getOldPassword());
        if (!correct) {
            throw ExceptionUtil.bizException(Code.CODE_OLD_PASSWORD_ERROR);
        }

        // 2. update new password
        accountHelper.updatePassword(accountId, request.getNewPassword());

        // 3. delete all sessions
        sessionHelper.deleteSessionByAccountId(accountId);

        // 4. create new session
        var sessionContext = sessionConfig.getSessionContext();
        String sessionToken = sessionHelper.createSession(sessionContext, accountId);

        // 5. set cookie
        cookieHelper.setCookie(sessionContext, sessionToken);
        cookieHelper.removeLegacyCookies(sessionContext);

        // 6. build response
        var response = UpdatePasswordResponse.newBuilder().build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void addPhoneNumber(AddPhoneNumberRequest request, StreamObserver<AddPhoneNumberResponse> responseObserver) {
        // 0. check if phone number is used
        CheckIdentifierAvailableRequest identifierAvailableRequest = CheckIdentifierAvailableRequest.newBuilder()
                .setPhoneNumber(request.getPhoneNumber())
                .build();
        boolean isUsed = accountHelper.checkIdentifier(identifierAvailableRequest);
        if (isUsed) {
            throw ExceptionUtil.bizException(Code.CODE_PHONE_NUMBER_CONFLICT);
        }
        // 1. verify verification code
        riskControlHelper.verifyVerificationCode(request.getVerification());
        // 2. update account
        UpdateAccountRequest updateProfileRequest = UpdateAccountRequest.newBuilder()
                .setId(AuthContext.get().accountId())
                .setPhoneNumber(request.getPhoneNumber())
                .build();
        accountHelper.updateAccount(updateProfileRequest);

        responseObserver.onNext(AddPhoneNumberResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getRelevantAccounts(
            GetRelevantAccountsRequest request, StreamObserver<GetRelevantAccountsResponse> responseObserver) {
        long accountId = AuthContext.get().accountId();
        List<Integer> relevantAccountIds = iBusinessAccountClient.getRelevantAccountIds((int) accountId);
        // todo: relevant account list一般很小，如果大了需要改成批量获取
        var resBuilder = GetRelevantAccountsResponse.newBuilder();
        relevantAccountIds.forEach(relevantAccountId -> {
            AccountModel accountModel = accountHelper.getAccountById(relevantAccountId);
            resBuilder.addAccounts(protoConvertor.toAccountModelRelevantView(accountModel));
        });
        responseObserver.onNext(resBuilder.build());
        responseObserver.onCompleted();
    }
}
