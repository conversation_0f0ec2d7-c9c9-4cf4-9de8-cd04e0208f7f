package com.moego.api.v3.account.helper;

import static org.apache.tomcat.util.descriptor.web.Constants.COOKIE_SAME_SITE_ATTR;

import com.moego.api.v3.account.config.SessionConfig;
import com.moego.lib.common.grpc.server.GrpcResponseUtil;
import jakarta.servlet.http.Cookie;
import org.apache.tomcat.util.http.Rfc6265CookieProcessor;
import org.springframework.stereotype.Component;

@Component
public class GrpcCookieHelper {

    private final Rfc6265CookieProcessor cookieProcessor = new Rfc6265CookieProcessor();

    private String getCookieString(Cookie cookie) {
        return cookieProcessor.generateHeader(cookie, null);
    }

    public void setCookie(Cookie cookie) {
        GrpcResponseUtil.putMetadata("Set-Cookie", getCookieString(cookie));
    }

    public void setCookie(SessionConfig.SessionContext sessionContext, String value) {
        var targetDomain = sessionContext.targetDomain();
        var cookieName = sessionContext.source().cookieName();
        var maxAge = sessionContext.source().maxAge();

        Cookie cookie = new Cookie(cookieName, value);
        cookie.setDomain(targetDomain);
        cookie.setPath("/");
        cookie.setMaxAge(maxAge);
        cookie.setHttpOnly(true);
        cookie.setSecure(true);

        if (sessionContext.source().name().equals("business")) {
            cookie.setAttribute(COOKIE_SAME_SITE_ATTR, "None");
        }

        setCookie(cookie);
    }

    public void removeCookie(SessionConfig.SessionContext sessionContext) {
        var source = sessionContext.source();
        var targetDomain = sessionContext.targetDomain();

        // remove current cookie
        removeCookie(source.cookieName(), targetDomain, true);
    }

    public void removeLegacyCookies(SessionConfig.SessionContext sessionContext) {
        var source = sessionContext.source();
        var targetDomain = sessionContext.targetDomain();

        // remove legacy cookies
        switch (source.name()) {
                // customer legacy cookies, MGSID / MGSID-T2
                // TODO: 2023-06-30 之后可以删除这个 case
            case "customer" -> {
                for (var legacyCookieName : source.legacyCookieNames()) {
                    removeCookie(legacyCookieName, "moego.pet", true);
                }
            }
                // business legacy cookies
            case "business" -> {
                removeCookie("Account-Token", targetDomain, false);
                removeCookie("Staff-Token", targetDomain, false);
            }
            default -> {
                // do nothing
            }
        }
    }

    private void removeCookie(String cookieName, String targetDomain, boolean httpOnly) {
        Cookie cookie = new Cookie(cookieName, "");
        cookie.setDomain(targetDomain);
        cookie.setPath("/");
        cookie.setMaxAge(0);
        cookie.setHttpOnly(httpOnly);
        cookie.setSecure(true);
        setCookie(cookie);
    }
}
