package com.moego.api.v3.customer.controller;

import com.google.protobuf.Empty;
import com.moego.idl.api.customer.v1.GetPetMetadataListResponse;
import com.moego.idl.api.customer.v1.PetMetadataServiceGrpc.PetMetadataServiceImplBase;
import com.moego.idl.models.customer.v1.PetMetadataModel;
import com.moego.idl.service.customer.v1.GetPetMetadataListOutput;
import com.moego.idl.service.customer.v1.PetMetadataServiceGrpc.PetMetadataServiceBlockingStub;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@AllArgsConstructor
public class PetMetadataController extends PetMetadataServiceImplBase {

    private final PetMetadataServiceBlockingStub petMetadataServiceBlockingStub;

    @Auth(AuthType.ACCOUNT)
    @Override
    public void getPetMetadataList(Empty request, StreamObserver<GetPetMetadataListResponse> responseObserver) {
        GetPetMetadataListOutput output = petMetadataServiceBlockingStub.getPetMetadataList(
                Empty.newBuilder().build());
        List<PetMetadataModel> metadataList = output.getPetMetadataListList();
        responseObserver.onNext(GetPetMetadataListResponse.newBuilder()
                .addAllPetMetadataList(metadataList)
                .build());
        responseObserver.onCompleted();
    }
}
