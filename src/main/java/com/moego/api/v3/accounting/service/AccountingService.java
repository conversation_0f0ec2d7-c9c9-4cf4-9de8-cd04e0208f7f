package com.moego.api.v3.accounting.service;

import com.moego.idl.api.accounting.v1.AccountingBusinessView;
import com.moego.idl.api.accounting.v1.AddBusinessesParams;
import com.moego.idl.api.accounting.v1.AddBusinessesResult;
import com.moego.idl.api.accounting.v1.GetAuthTokenParams;
import com.moego.idl.api.accounting.v1.GetAuthTokenResult;
import com.moego.idl.api.accounting.v1.GetBusinessesParams;
import com.moego.idl.api.accounting.v1.GetBusinessesResult;
import com.moego.idl.api.accounting.v1.GetOnboardingStatusParams;
import com.moego.idl.api.accounting.v1.GetOnboardingStatusResult;
import com.moego.idl.api.accounting.v1.GetVisibilityResult;
import com.moego.idl.api.accounting.v1.RemoveBusinessesParams;
import com.moego.idl.api.accounting.v1.RemoveBusinessesResult;
import com.moego.idl.api.accounting.v1.SetBusinessesParams;
import com.moego.idl.api.accounting.v1.SetBusinessesResult;
import com.moego.idl.models.accounting.v1.Setting;
import com.moego.idl.models.organization.v1.LocationModel;
import com.moego.idl.service.accounting.v1.AccountingServiceGrpc;
import com.moego.idl.service.accounting.v1.AddUnselectedBusinessesRequest;
import com.moego.idl.service.accounting.v1.GetAuthTokenRequest;
import com.moego.idl.service.accounting.v1.GetAuthTokenResponse;
import com.moego.idl.service.accounting.v1.GetOnboardingStatusRequest;
import com.moego.idl.service.accounting.v1.GetUnselectedBusinessesRequest;
import com.moego.idl.service.accounting.v1.GetVisibilityRequest;
import com.moego.idl.service.accounting.v1.ListSettingsRequest;
import com.moego.idl.service.accounting.v1.RemoveUnselectedBusinessesRequest;
import com.moego.idl.service.accounting.v1.SetUnselectedBusinessesRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.ListLocationsRequest;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AccountingService {
    @Autowired
    private AccountingServiceGrpc.AccountingServiceBlockingStub accountingServiceBlockingStub;

    @Autowired
    private BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    public GetVisibilityResult getVisibility(Long companyId) {
        var response = accountingServiceBlockingStub.getVisibility(
                GetVisibilityRequest.newBuilder().setCompanyId(companyId).build());
        return GetVisibilityResult.newBuilder()
                .setVisible(response.getVisible())
                .setVisibilityClass(response.getVisibilityClass())
                .build();
    }

    public GetOnboardingStatusResult getOnboardingStatus(Long companyId, GetOnboardingStatusParams request) {
        return GetOnboardingStatusResult.newBuilder()
                .setOnboardingStatus(accountingServiceBlockingStub
                        .getOnboardingStatus(GetOnboardingStatusRequest.newBuilder()
                                .setChannelType(request.getChannelType())
                                .setCompanyId(companyId)
                                .build())
                        .getOnboardingStatus())
                .build();
    }

    public GetBusinessesResult getBusinesses(Long companyId, GetBusinessesParams request) {
        var getUnselectedBusinessesResponse =
                accountingServiceBlockingStub.getUnselectedBusinesses(GetUnselectedBusinessesRequest.newBuilder()
                        .setChannelType(request.getChannelType())
                        .setCompanyId(companyId)
                        .build());
        if (!getUnselectedBusinessesResponse.getHasCompletedSelection()) {
            return GetBusinessesResult.newBuilder()
                    .setHasCompletedSelection(false)
                    .build();
        }
        Set<Long> unselectedBusinessIdSet =
                new HashSet<>(getUnselectedBusinessesResponse.getUnselectedBusinessIdsList());

        Map<Long, Setting> settingMap = new HashMap<>();
        try {
            var listSettingsResponse = accountingServiceBlockingStub.listSettings(ListSettingsRequest.newBuilder()
                    .setFilter(ListSettingsRequest.Filter.newBuilder()
                            .addChannelTypes(request.getChannelType())
                            .addCompanyIds(companyId)
                            .build())
                    .build());

            settingMap = listSettingsResponse.getSettingsList().stream()
                    .collect(Collectors.toMap(Setting::getBusinessId, item -> item));
        } catch (Throwable e) {
            log.error("Failed to get settings", e);
        }

        var businessResponse = businessServiceBlockingStub.listLocations(ListLocationsRequest.newBuilder()
                .setFilter(ListLocationsRequest.Filter.newBuilder()
                        .addCompanyIds(companyId)
                        .build())
                .build());

        Map<Long, Setting> finalSettingMap = settingMap;
        return GetBusinessesResult.newBuilder()
                .setHasCompletedSelection(true)
                .addAllBusinesses(businessResponse.getLocationsList().stream()
                        .map(item -> AccountingBusinessView.newBuilder()
                                .setId(item.getId())
                                .setSyncStatus(
                                        unselectedBusinessIdSet.contains(item.getId())
                                                ? AccountingBusinessView.SyncStatus.NOT_SYNCED
                                                : AccountingBusinessView.SyncStatus.SYNCED)
                                .setName(item.getName())
                                .setAvatar(item.getAvatarPath())
                                .setSyncTime(finalSettingMap
                                        .getOrDefault(item.getId(), Setting.getDefaultInstance())
                                        .getStartSyncTime())
                                .build())
                        .toList())
                .build();
    }

    public SetBusinessesResult setBusinesses(Long companyId, SetBusinessesParams request) {
        Set<Long> selectedBusinessIdSet = new HashSet<>(request.getBusinessIdsList());

        var businessResponse = businessServiceBlockingStub.listLocations(ListLocationsRequest.newBuilder()
                .setFilter(ListLocationsRequest.Filter.newBuilder()
                        .addCompanyIds(companyId)
                        .build())
                .build());

        accountingServiceBlockingStub.setUnselectedBusinesses(SetUnselectedBusinessesRequest.newBuilder()
                .setChannelType(request.getChannelType())
                .setCompanyId(companyId)
                .addAllUnselectedBusinessIds(businessResponse.getLocationsList().stream()
                        .map(LocationModel::getId)
                        .filter(id -> !selectedBusinessIdSet.contains(id))
                        .collect(Collectors.toList()))
                .setLegalName(request.getLegalName())
                .setState(request.getState())
                .setEntityType(request.getEntityType())
                .build());

        return SetBusinessesResult.newBuilder().build();
    }

    public AddBusinessesResult addBusinesses(Long companyId, AddBusinessesParams request) {
        accountingServiceBlockingStub.removeUnselectedBusinesses(RemoveUnselectedBusinessesRequest.newBuilder()
                .setChannelType(request.getChannelType())
                .setCompanyId(companyId)
                .addAllUnselectedBusinessIds(request.getBusinessIdsList())
                .build());

        return AddBusinessesResult.newBuilder().build();
    }

    public RemoveBusinessesResult removeBusinesses(Long companyId, RemoveBusinessesParams request) {
        accountingServiceBlockingStub.addUnselectedBusinesses(AddUnselectedBusinessesRequest.newBuilder()
                .setChannelType(request.getChannelType())
                .setCompanyId(companyId)
                .addAllUnselectedBusinessIds(request.getBusinessIdsList())
                .build());

        return RemoveBusinessesResult.newBuilder().build();
    }

    public GetAuthTokenResult getAuthToken(Long companyId, Long businessId, GetAuthTokenParams request) {
        GetAuthTokenResponse response = accountingServiceBlockingStub.getAuthToken(GetAuthTokenRequest.newBuilder()
                .setChannelType(request.getChannelType())
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .build());
        return GetAuthTokenResult.newBuilder()
                .setToken(response.getToken())
                .setChannelBusinessId(response.getChannelBusinessId())
                .setExpirationTime(response.getExpirationTime())
                .build();
    }
}
