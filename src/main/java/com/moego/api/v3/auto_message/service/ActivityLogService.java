package com.moego.api.v3.auto_message.service;

import com.moego.idl.models.activity_log.v1.Resource;
import com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc;
import com.moego.idl.service.activity_log.v1.CreateActivityLogRequest;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityLogService {

    private final ActivityLogServiceGrpc.ActivityLogServiceBlockingStub activityLogService;

    static final String ACTIVITY_LOG_ACTION_UPDATE = "Update";

    public void addAutoMsgUpdateLog(Long companyId, Long businessId, Long tokenStaffId, Long id, Object details) {
        CreateActivityLogRequest.Builder builder = CreateActivityLogRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setOperatorId("" + tokenStaffId)
                .setIsRoot(true)
                .setAction(ACTIVITY_LOG_ACTION_UPDATE)
                .setResourceType(Resource.Type.AUTO_MESSAGE_SETTING)
                .setResourceId("" + id)
                .setDetails(JsonUtil.toJson(details));
        if (details != null) {
            builder.setClassName(details.getClass().getName());
        }
        addActivityLog(builder.build());
    }

    public void addReminderUpdateLog(Long companyId, Long businessId, Long tokenStaffId, Long id, Object details) {
        CreateActivityLogRequest.Builder builder = CreateActivityLogRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setOperatorId("" + tokenStaffId)
                .setIsRoot(true)
                .setAction(ACTIVITY_LOG_ACTION_UPDATE)
                .setResourceType(Resource.Type.REMINDER_SETTING)
                .setResourceId("" + id)
                .setDetails(JsonUtil.toJson(details));
        if (details != null) {
            builder.setClassName(details.getClass().getName());
        }
        addActivityLog(builder.build());
    }

    private void addActivityLog(CreateActivityLogRequest request) {
        ThreadPool.submit(() -> {
            activityLogService.createActivityLog(request);
        });
    }
}
