package com.moego.api.v3.promotion.controller;

import com.moego.api.v3.promotion.service.packages.PackageQueryService;
import com.moego.idl.api.promotion.v1.ListPackagesRequest;
import com.moego.idl.api.promotion.v1.ListPackagesResponse;
import com.moego.idl.api.promotion.v1.PackageServiceGrpc;
import com.moego.idl.models.pkg.v1.PackageModel;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class PromotionPackageController extends PackageServiceGrpc.PackageServiceImplBase {

    private final PackageQueryService packageQueryService;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPackages(ListPackagesRequest request, StreamObserver<ListPackagesResponse> responseObserver) {
        AuthContext authContext = AuthContext.get();
        List<PackageModel> packages =
                packageQueryService.query(authContext.getCompanyId().longValue(), request.getBusinessIdsList());
        responseObserver.onNext(
                ListPackagesResponse.newBuilder().addAllPackages(packages).build());
        responseObserver.onCompleted();
    }
}
