package com.moego.api.v3.finance_tools.controller;

import static com.moego.lib.common.auth.AuthType.BUSINESS;

import com.google.type.Interval;
import com.moego.api.v3.finance_tools.service.CashDrawerService;
import com.moego.idl.api.finance_tools.v1.CashDrawerServiceGrpc;
import com.moego.idl.api.finance_tools.v1.CreateCashAdjustmentRequest;
import com.moego.idl.api.finance_tools.v1.CreateCashAdjustmentResponse;
import com.moego.idl.api.finance_tools.v1.CreateReportRequest;
import com.moego.idl.api.finance_tools.v1.CreateReportResponse;
import com.moego.idl.api.finance_tools.v1.GetLastReportRequest;
import com.moego.idl.api.finance_tools.v1.GetLastReportResponse;
import com.moego.idl.api.finance_tools.v1.GetReportedCashTotalRequest;
import com.moego.idl.api.finance_tools.v1.GetReportedCashTotalResponse;
import com.moego.idl.api.finance_tools.v1.ListCashAdjustmentsRequest;
import com.moego.idl.api.finance_tools.v1.ListCashAdjustmentsResponse;
import com.moego.idl.api.finance_tools.v1.ListReportsRequest;
import com.moego.idl.api.finance_tools.v1.ListReportsResponse;
import com.moego.idl.api.finance_tools.v1.UpdateReportRequest;
import com.moego.idl.api.finance_tools.v1.UpdateReportResponse;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import java.time.Duration;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@GrpcService
@Slf4j
@AllArgsConstructor
public class CashDrawerController extends CashDrawerServiceGrpc.CashDrawerServiceImplBase {
    private static final Duration MAX_TIME_RANGE = Duration.ofDays(30);

    private final CashDrawerService cashDrawerService;

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CASH_DRAWER})
    public void listReports(ListReportsRequest request, StreamObserver<ListReportsResponse> responseObserver) {
        var response = cashDrawerService.listReports(AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CASH_DRAWER})
    public void getLastReport(GetLastReportRequest request, StreamObserver<GetLastReportResponse> responseObserver) {
        var response = cashDrawerService.getLastReport(AuthContext.get().businessId());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CASH_DRAWER})
    public void getReportedCashTotal(
            GetReportedCashTotalRequest request, StreamObserver<GetReportedCashTotalResponse> responseObserver) {
        validateTimeRange(request.getRange());
        var response = cashDrawerService.getReportedCashTotal(AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CASH_DRAWER})
    public void listCashAdjustments(
            ListCashAdjustmentsRequest request, StreamObserver<ListCashAdjustmentsResponse> responseObserver) {
        if (request.hasRange()) {
            validateTimeRange(request.getRange());
        }
        var response = cashDrawerService.listCashAdjustments(AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CASH_DRAWER})
    public void createCashAdjustment(
            CreateCashAdjustmentRequest request, StreamObserver<CreateCashAdjustmentResponse> responseObserver) {
        var response = cashDrawerService.createCashAdjustment(
                AuthContext.get().businessId(), AuthContext.get().staffId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CASH_DRAWER})
    public void createReport(CreateReportRequest request, StreamObserver<CreateReportResponse> responseObserver) {
        validateTimeRange(request.getReport().getRange());
        var response = cashDrawerService.createReport(
                AuthContext.get().companyId(),
                AuthContext.get().businessId(),
                AuthContext.get().staffId(),
                request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CASH_DRAWER})
    public void updateReport(UpdateReportRequest request, StreamObserver<UpdateReportResponse> responseObserver) {
        var response = cashDrawerService.updateReport(AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    private void validateTimeRange(Interval range) {
        var start = Instant.ofEpochSecond(
                range.getStartTime().getSeconds(), range.getStartTime().getNanos());
        var end = Instant.ofEpochSecond(
                range.getEndTime().getSeconds(), range.getEndTime().getNanos());
        if (start.isAfter(end)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_FINANCE_TOOLS_INVALID_TIME_RANGE,
                    "The time range start must not be after the time range end");
        }
        if (Duration.between(end, start).compareTo(MAX_TIME_RANGE) > 0) {
            throw ExceptionUtil.bizException(
                    Code.CODE_FINANCE_TOOLS_INVALID_TIME_RANGE, "The time range has a maximum of 30 days");
        }
    }
}
