package com.moego.api.v3.reporting.v2.converter;

import com.moego.idl.api.reporting.v2.ExportReportDataRequest;
import com.moego.idl.api.reporting.v2.ExportReportDataResponse;
import com.moego.idl.api.reporting.v2.FetchReportDataRequest;
import com.moego.idl.api.reporting.v2.FetchReportDataResponse;
import com.moego.idl.api.reporting.v2.MarkReportFavoriteRequest;
import com.moego.idl.api.reporting.v2.QueryReportMetasRequest;
import com.moego.idl.api.reporting.v2.QueryReportPagesRequest;
import com.moego.idl.api.reporting.v2.QueryReportPagesResponse;
import com.moego.idl.api.reporting.v2.QueryReportsMetasResponse;
import com.moego.idl.api.reporting.v2.SaveReportCustomizeConfigRequest;
import com.moego.idl.models.reporting.v2.ExportDataParams;
import com.moego.idl.models.reporting.v2.ExportDataResult;
import com.moego.idl.models.reporting.v2.FetchDataParams;
import com.moego.idl.models.reporting.v2.FetchDataResult;
import com.moego.idl.models.reporting.v2.QueryMetasParams;
import com.moego.idl.models.reporting.v2.QueryMetasResult;
import com.moego.idl.models.reporting.v2.QueryPageMetaParams;
import com.moego.idl.models.reporting.v2.QueryPageMetaResult;
import com.moego.idl.models.reporting.v2.ReportingScene;
import com.moego.idl.models.reporting.v2.TokenInfo;
import com.moego.idl.service.reporting.v2.ExportDataRequest;
import com.moego.idl.service.reporting.v2.ExportDataResponse;
import com.moego.idl.service.reporting.v2.ExportReportDataParams;
import com.moego.idl.service.reporting.v2.ExportReportDataResult;
import com.moego.idl.service.reporting.v2.FetchDataRequest;
import com.moego.idl.service.reporting.v2.FetchDataResponse;
import com.moego.idl.service.reporting.v2.FetchReportDataParams;
import com.moego.idl.service.reporting.v2.FetchReportDataResult;
import com.moego.idl.service.reporting.v2.MarkReportFavoriteParams;
import com.moego.idl.service.reporting.v2.QueryMetasRequest;
import com.moego.idl.service.reporting.v2.QueryMetasResponse;
import com.moego.idl.service.reporting.v2.QueryPageMetaRequest;
import com.moego.idl.service.reporting.v2.QueryPageMetaResponse;
import com.moego.idl.service.reporting.v2.QueryReportMetasParams;
import com.moego.idl.service.reporting.v2.QueryReportPagesParams;
import com.moego.idl.service.reporting.v2.QueryReportPagesResult;
import com.moego.idl.service.reporting.v2.QueryReportsMetasResult;
import com.moego.idl.service.reporting.v2.SaveReportCustomizeConfigParams;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface ReportsConverter {

    ReportsConverter INSTANCE = Mappers.getMapper(ReportsConverter.class);

    QueryReportPagesParams toQueryPagesParams(QueryReportPagesRequest request, TokenInfo tokenInfo);

    QueryReportPagesResponse toQueryPagesResponse(QueryReportPagesResult result);

    @Mapping(
            target = "action",
            expression = "java(MarkReportFavoriteParams.Action.forNumber(request.getAction().getNumber()))")
    MarkReportFavoriteParams toMarkFavoriteParams(MarkReportFavoriteRequest request, TokenInfo tokenInfo);

    SaveReportCustomizeConfigParams toSaveCustomizeConfigParams(
            SaveReportCustomizeConfigRequest request, TokenInfo tokenInfo);

    QueryReportMetasParams toQueryMetasParams(QueryReportMetasRequest request, TokenInfo tokenInfo);

    QueryReportsMetasResponse toQueryMetasResponse(QueryReportsMetasResult result);

    FetchReportDataParams toFetchDataParams(FetchReportDataRequest request, TokenInfo tokenInfo);

    FetchReportDataResponse toFetchDataResponse(FetchReportDataResult result);

    ExportReportDataParams toExportDataParams(ExportReportDataRequest request, TokenInfo tokenInfo);

    ExportReportDataResponse toExportDataResponse(ExportReportDataResult result);

    QueryPageMetaRequest toQueryPageMetaRequest(QueryPageMetaParams params, ReportingScene scene);

    QueryPageMetaResult toQueryPageMetaResult(QueryPageMetaResponse response);

    QueryMetasRequest toQueryMetasRequest(QueryMetasParams params, ReportingScene scene);

    QueryMetasResult toQueryMetasResult(QueryMetasResponse response);

    FetchDataRequest toFetchDataRequest(FetchDataParams params, ReportingScene scene);

    FetchDataResult toFetchDataResult(FetchDataResponse response);

    ExportDataRequest toExportDataRequest(ExportDataParams params, ReportingScene scene);

    ExportDataResult toExportDataResult(ExportDataResponse response);
}
