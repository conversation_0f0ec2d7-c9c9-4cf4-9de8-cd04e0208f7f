package com.moego.api.v3.reporting.v2.controller;

import com.moego.api.v3.reporting.v2.converter.CustomReportConverter;
import com.moego.idl.api.reporting.v2.CustomReportServiceGrpc;
import com.moego.idl.models.reporting.v2.DeleteCustomReportParams;
import com.moego.idl.models.reporting.v2.DeleteCustomReportResult;
import com.moego.idl.models.reporting.v2.DuplicateCustomReportParams;
import com.moego.idl.models.reporting.v2.DuplicateCustomReportResult;
import com.moego.idl.models.reporting.v2.ModifyCustomDiagramParams;
import com.moego.idl.models.reporting.v2.ModifyCustomDiagramResult;
import com.moego.idl.models.reporting.v2.ReportingScene;
import com.moego.idl.models.reporting.v2.SaveCustomReportParams;
import com.moego.idl.models.reporting.v2.SaveCustomReportResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class CustomReportController extends CustomReportServiceGrpc.CustomReportServiceImplBase {

    private final com.moego.idl.service.reporting.v2.CustomReportServiceGrpc.CustomReportServiceBlockingStub
            customReportService;
    private final CustomReportConverter converter = CustomReportConverter.INSTANCE;

    @Override
    @Auth(AuthType.COMPANY)
    public void saveCustomReport(
            SaveCustomReportParams request, StreamObserver<SaveCustomReportResult> responseObserver) {
        var res = customReportService.saveCustomReport(
                converter.toSaveCustomReportRequest(request, ReportingScene.COMMON));
        responseObserver.onNext(converter.toSaveCustomReportResult(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void modifyCustomDiagram(
            ModifyCustomDiagramParams request, StreamObserver<ModifyCustomDiagramResult> responseObserver) {
        var res = customReportService.modifyCustomDiagram(
                converter.toModifyCustomDiagramRequest(request, ReportingScene.COMMON));
        responseObserver.onNext(converter.toModifyCustomDiagramResult(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void duplicateCustomReport(
            DuplicateCustomReportParams request, StreamObserver<DuplicateCustomReportResult> responseObserver) {
        var res = customReportService.duplicateCustomReport(
                converter.toDuplicateCustomReportParams(request, ReportingScene.COMMON));
        responseObserver.onNext(converter.toDuplicateCustomReportResult(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deleteCustomReport(
            DeleteCustomReportParams request, StreamObserver<DeleteCustomReportResult> responseObserver) {
        var res = customReportService.deleteCustomReport(
                converter.toDeleteCustomReportRequest(request, ReportingScene.COMMON));
        responseObserver.onNext(converter.toDeleteCustomReportResult(res));
        responseObserver.onCompleted();
    }
}
