package com.moego.api.v3.message.converter;

import com.moego.common.constant.CommonConstant;
import com.moego.idl.models.business_customer.v1.BusinessCustomerContactPublicView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModelNameView;
import com.moego.server.customer.dto.CustomerContactDto;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import java.util.List;
import java.util.Objects;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @since 2023/11/19
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        imports = {CommonConstant.class, Objects.class})
public interface BusinessCustomerConverter {

    BusinessCustomerModelNameView dtoToNameView(MoeBusinessCustomerDTO dto);

    List<BusinessCustomerModelNameView> dtoToNameView(List<MoeBusinessCustomerDTO> dtoList);

    @Mapping(target = "id", source = "contactId")
    @Mapping(target = "isPrimary", expression = "java(Objects.equals(dto.getIsPrimary(), CommonConstant.YES))")
    BusinessCustomerContactPublicView dtoToView(CustomerContactDto dto);
}
