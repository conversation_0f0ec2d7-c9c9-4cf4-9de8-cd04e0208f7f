package com.moego.api.v3.message.converter;

import com.moego.common.constant.CommonConstant;
import com.moego.idl.models.message.v1.AutoMessageTemplatePublicView;
import com.moego.idl.models.message.v1.AutoMessageType;
import com.moego.server.message.dto.AutoMessageTemplateDTO;
import java.util.List;
import java.util.Objects;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @since 2023/11/19
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        imports = {AutoMessageType.class, CommonConstant.class, Objects.class})
public interface AutoMessageConverter {

    @Mapping(target = "type", expression = "java(AutoMessageType.forNumber(dto.getType()))")
    @Mapping(
            target = "isEnabled",
            expression = "java(Objects.equals(dto.getStatus().byteValue(), CommonConstant.ENABLE))")
    AutoMessageTemplatePublicView dtoToView(AutoMessageTemplateDTO dto);

    List<AutoMessageTemplatePublicView> dtoToView(List<AutoMessageTemplateDTO> dtoList);
}
