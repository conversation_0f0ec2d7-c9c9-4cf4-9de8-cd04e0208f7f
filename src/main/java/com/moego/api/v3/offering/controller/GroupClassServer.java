package com.moego.api.v3.offering.controller;

import com.moego.api.v3.offering.service.GroupClassService;
import com.moego.idl.api.offering.v1.CountInstancesGroupByClassParams;
import com.moego.idl.api.offering.v1.CountInstancesGroupByClassResult;
import com.moego.idl.api.offering.v1.CountInstancesGroupByStatusParams;
import com.moego.idl.api.offering.v1.CountInstancesGroupByStatusResult;
import com.moego.idl.api.offering.v1.CreateInstanceParams;
import com.moego.idl.api.offering.v1.CreateInstanceResult;
import com.moego.idl.api.offering.v1.DeleteInstanceParams;
import com.moego.idl.api.offering.v1.DeleteInstanceResult;
import com.moego.idl.api.offering.v1.GetInstanceParams;
import com.moego.idl.api.offering.v1.GetInstanceResult;
import com.moego.idl.api.offering.v1.GroupClassServiceGrpc;
import com.moego.idl.api.offering.v1.ListInstancesParams;
import com.moego.idl.api.offering.v1.ListInstancesResult;
import com.moego.idl.api.offering.v1.ListSessionsParams;
import com.moego.idl.api.offering.v1.ListSessionsResult;
import com.moego.idl.api.offering.v1.UpdateInstanceParams;
import com.moego.idl.api.offering.v1.UpdateInstanceResult;
import com.moego.idl.api.offering.v1.UpdateSessionParams;
import com.moego.idl.api.offering.v1.UpdateSessionResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@GrpcService
@RequiredArgsConstructor
@Slf4j
public class GroupClassServer extends GroupClassServiceGrpc.GroupClassServiceImplBase {

    @Autowired
    private GroupClassService groupClassService;

    @Override
    @Auth(AuthType.COMPANY)
    public void createInstance(CreateInstanceParams params, StreamObserver<CreateInstanceResult> responseObserver) {
        var result =
                groupClassService.createGroupClassInstance(AuthContext.get().companyId(), params);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void countInstancesGroupByStatus(
            CountInstancesGroupByStatusParams params,
            StreamObserver<CountInstancesGroupByStatusResult> resultStreamObserver) {
        var result = groupClassService.countGroupClassInstanceStatus(
                AuthContext.get().companyId(), params);
        resultStreamObserver.onNext(result);
        resultStreamObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void countInstancesGroupByClass(
            CountInstancesGroupByClassParams params,
            StreamObserver<CountInstancesGroupByClassResult> resultStreamObserver) {
        var result = groupClassService.countGroupClassInstancesByStatus(
                AuthContext.get().companyId(), params);
        resultStreamObserver.onNext(result);
        resultStreamObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getInstance(GetInstanceParams params, StreamObserver<GetInstanceResult> resultStreamObserver) {
        var result = groupClassService.getInstance(AuthContext.get().companyId(), params);
        resultStreamObserver.onNext(result);
        resultStreamObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listInstances(ListInstancesParams params, StreamObserver<ListInstancesResult> resultStreamObserver) {
        var result = groupClassService.listGroupClassInstances(AuthContext.get().companyId(), params);
        resultStreamObserver.onNext(result);
        resultStreamObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateInstance(UpdateInstanceParams params, StreamObserver<UpdateInstanceResult> resultStreamObserver) {
        var result =
                groupClassService.updateGroupClassInstance(AuthContext.get().companyId(), params);
        resultStreamObserver.onNext(result);
        resultStreamObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deleteInstance(DeleteInstanceParams params, StreamObserver<DeleteInstanceResult> resultStreamObserver) {
        var result =
                groupClassService.deleteGroupClassInstance(AuthContext.get().companyId(), params);
        resultStreamObserver.onNext(result);
        resultStreamObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateSession(UpdateSessionParams params, StreamObserver<UpdateSessionResult> resultStreamObserver) {
        var result =
                groupClassService.updateGroupClassSessions(AuthContext.get().companyId(), params);
        resultStreamObserver.onNext(result);
        resultStreamObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listSessions(ListSessionsParams params, StreamObserver<ListSessionsResult> resultStreamObserver) {
        var result = groupClassService.listGroupClassSessions(AuthContext.get().companyId(), params);
        resultStreamObserver.onNext(result);
        resultStreamObserver.onCompleted();
    }
}
