package com.moego.api.v3.offering.service;

import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.membership.v1.PetFilter;
import com.moego.idl.models.offering.v1.ServiceFilterByPet;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetCoatTypeServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetCodeServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetSizeServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListBindingPetCodeRequest;
import com.moego.idl.service.business_customer.v1.ListPetCoatTypeRequest;
import com.moego.idl.service.business_customer.v1.ListPetSizeRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.server.customer.client.IPetSearchClient;
import com.moego.server.customer.dto.SearchPetResult;
import com.moego.server.customer.params.SearchPetParams;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class PetService {
    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub
            businessCustomerPetServiceClient;
    private final BusinessPetSizeServiceGrpc.BusinessPetSizeServiceBlockingStub businessPetSizeServiceClient;
    private final BusinessPetCoatTypeServiceGrpc.BusinessPetCoatTypeServiceBlockingStub
            businessPetCoatTypeServiceClient;
    private final IPetSearchClient petSearchClient;
    private final BusinessPetCodeServiceGrpc.BusinessPetCodeServiceBlockingStub businessPetCodeServiceClient;

    public ServiceFilterByPet getPetFilter(Long companyId, Long petId) {
        var pet = businessCustomerPetServiceClient.getPet(
                com.moego.idl.service.business_customer.v1.GetPetRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setId(petId)
                        .build());
        var petSizeList = businessPetSizeServiceClient.listPetSize(
                com.moego.idl.service.business_customer.v1.ListPetSizeRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build());

        var builder = ServiceFilterByPet.newBuilder().setPetType(pet.getPet().getPetType());

        if (Strings.isNotEmpty(pet.getPet().getBreed())) {
            builder.setPetBreed(pet.getPet().getBreed());
        }

        if (Strings.isNotEmpty(pet.getPet().getCoatType())) {
            var petCoatList = businessPetCoatTypeServiceClient
                    .listPetCoatType(com.moego.idl.service.business_customer.v1.ListPetCoatTypeRequest.newBuilder()
                            .setCompanyId(companyId)
                            .build())
                    .getCoatTypesList();
            petCoatList.stream()
                    .filter(coatType -> coatType.getName().equals(pet.getPet().getCoatType()))
                    .findFirst()
                    .ifPresent(coatType -> builder.setPetCoatTypeId(coatType.getId()));
        }

        if (Strings.isNotEmpty(pet.getPet().getWeight())) {
            var weight = new BigDecimal(pet.getPet().getWeight());
            builder.setPetWeight(weight.doubleValue());

            var roundedWeight = weight.setScale(0, RoundingMode.HALF_UP).intValue();
            petSizeList.getSizesList().stream()
                    .filter(size -> size.getWeightLow() <= roundedWeight && size.getWeightHigh() >= roundedWeight)
                    .findFirst()
                    .ifPresentOrElse(
                            petSizeModel -> builder.setPetSizeId(petSizeModel.getId()),
                            () -> builder.setPetSizeId(-1)); // 如果未命中任何一个 Pet size，则传 -1，一定不会满足 Pet size filter 的条件
        }

        final var petCodeResp = businessPetCodeServiceClient.listBindingPetCode(ListBindingPetCodeRequest.newBuilder()
                .setCompanyId(companyId)
                .setPetId(petId)
                .build());
        if (petCodeResp.getPetCodesCount() > 0) {
            builder.addAllPetCodeIds(petCodeResp.getPetCodesList().stream()
                    .filter(code -> !code.getDeleted())
                    .map(BusinessPetCodeModel::getId)
                    .toList());
        }

        return builder.build();
    }

    public SearchPetResult searchApplicablePetByService(
            Long companyId,
            Long staffId,
            String keyword,
            PaginationRequest paginationRequest,
            ServiceModel serviceDetail) {
        SearchPetParams params = new SearchPetParams();
        params.setCompanyId(companyId);
        params.setStaffId(staffId);
        params.setTerm(keyword);
        params.setPageNum(paginationRequest.getPageNum());
        params.setPageSize(paginationRequest.getPageSize());

        if (serviceDetail.getBreedFilter()) {
            List<SearchPetParams.PetTypeAndBreedFilter> petTypeAndBreedFilters = new ArrayList<>();
            serviceDetail.getCustomizedBreedList().forEach(breed -> {
                SearchPetParams.PetTypeAndBreedFilter filter = new SearchPetParams.PetTypeAndBreedFilter();
                filter.setPetTypeId(Math.toIntExact(breed.getPetTypeId()));
                filter.setBreedNames(breed.getBreedsList());
                petTypeAndBreedFilters.add(filter);
            });
            params.setPetTypeAndBreedFilters(petTypeAndBreedFilters);
        }

        if (serviceDetail.getPetSizeFilter()) {
            params.setPetWeightFilters(serviceDetail.getCustomizedPetSizesList().stream()
                    .map(petSizeId -> {
                        SearchPetParams.PetWeightFilter petWeightFilter = new SearchPetParams.PetWeightFilter();
                        petWeightFilter.setPetSizeId(petSizeId);
                        return petWeightFilter;
                    })
                    .toList());
        } else if (serviceDetail.getWeightFilter()) {
            SearchPetParams.PetWeightFilter petWeightFilter = new SearchPetParams.PetWeightFilter();
            petWeightFilter.setWeightLow(serviceDetail.getWeightRange(0));
            petWeightFilter.setWeightHigh(serviceDetail.getWeightRange(1));
            params.setPetWeightFilters(List.of(petWeightFilter));
        }

        if (serviceDetail.getCoatFilter()) {
            params.setCoatTypeFilters(serviceDetail.getCustomizedCoatList().stream()
                    .map(coatTypeId -> {
                        SearchPetParams.PetCoatTypeFilter coatTypeFilter = new SearchPetParams.PetCoatTypeFilter();
                        coatTypeFilter.setCoatTypeId(coatTypeId);
                        return coatTypeFilter;
                    })
                    .toList());
        }
        return petSearchClient.searchPet(params);
    }

    public Map<Long, PetFilter> batchGetPetFilter(Long companyId, List<Long> petIds) {
        if (petIds == null || petIds.isEmpty()) {
            log.warn("batchGetPetFilter received empty petIds, returning empty result.");
            return Collections.emptyMap();
        }

        // 1. 批量获取宠物基本信息
        var batchGetPetInfoResponse = businessCustomerPetServiceClient.batchGetPetInfo(
                BatchGetPetInfoRequest.newBuilder().addAllIds(petIds).build());

        // 2. 获取宠物体型信息
        var petSizeList = businessPetSizeServiceClient
                .listPetSize(
                        ListPetSizeRequest.newBuilder().setCompanyId(companyId).build())
                .getSizesList();

        // 3. 获取宠物毛发类型信息
        var petCoatList = businessPetCoatTypeServiceClient
                .listPetCoatType(ListPetCoatTypeRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getCoatTypesList();

        // 4. 处理批量宠物数据
        Map<Long, PetFilter> result = new HashMap<>();
        for (var petInfo : batchGetPetInfoResponse.getPetsList()) {
            var builder = PetFilter.newBuilder().setPetType(petInfo.getPetType());

            // 设置品种
            if (Strings.isNotEmpty(petInfo.getBreed())) {
                builder.setPetBreed(petInfo.getBreed());
            }

            // 处理毛发类型
            if (Strings.isNotEmpty(petInfo.getCoatType())) {
                petCoatList.stream()
                        .filter(coatType -> Objects.equals(coatType.getName(), petInfo.getCoatType()))
                        .findFirst()
                        .ifPresent(coatType -> builder.setPetCoatId(coatType.getId()));
            }

            // 处理宠物体重对应的 Size
            if (Strings.isNotEmpty(petInfo.getWeight())) {
                var weight = new BigDecimal(petInfo.getWeight());
                var roundedWeight = weight.setScale(0, RoundingMode.HALF_UP).intValue();

                petSizeList.stream()
                        .filter(size -> size.getWeightLow() <= roundedWeight && size.getWeightHigh() >= roundedWeight)
                        .findFirst()
                        .ifPresentOrElse(
                                petSizeModel -> builder.setPetSizeId(petSizeModel.getId()),
                                () -> builder.setPetSizeId(-1));
            }

            result.put(petInfo.getId(), builder.build());
        }

        return result;
    }
}
