package com.moego.api.v3.offering.controller;

import com.google.common.collect.Sets;
import com.google.protobuf.Timestamp;
import com.google.type.Date;
import com.moego.api.v3.appointment.service.CompanySettingService;
import com.moego.api.v3.business_customer.service.BusinessCustomerService;
import com.moego.api.v3.business_customer.service.BusinessPetService;
import com.moego.api.v3.fulfillment.consts.FulfillmentStatusConst;
import com.moego.api.v3.membership.service.MembershipService;
import com.moego.api.v3.offering.convertor.CustomerConverter;
import com.moego.api.v3.offering.convertor.PetConverter;
import com.moego.api.v3.offering.convertor.ServiceConvertor;
import com.moego.api.v3.offering.service.AppointmentService;
import com.moego.api.v3.offering.service.PetService;
import com.moego.api.v3.offering.service.ServiceService;
import com.moego.idl.api.offering.v1.PetServiceGrpc;
import com.moego.idl.api.offering.v1.SearchAssociationInformationParams;
import com.moego.idl.api.offering.v1.SearchAssociationInformationResult;
import com.moego.idl.api.offering.v1.SearchEnrollmentInfoParams;
import com.moego.idl.api.offering.v1.SearchEnrollmentInfoResult;
import com.moego.idl.api.offering.v1.SearchPetForGroupClassCheckInParams;
import com.moego.idl.api.offering.v1.SearchPetForGroupClassCheckInResult;
import com.moego.idl.api.offering.v1.SearchPetForQuickCheckInParams;
import com.moego.idl.api.offering.v1.SearchPetForQuickCheckInResult;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModelNameView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetCalendarView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.fulfillment.v1.FulfillmentModel;
import com.moego.idl.models.fulfillment.v1.GroupClassDetailModel;
import com.moego.idl.models.membership.v1.MembershipSubscriptionListModel;
import com.moego.idl.models.offering.v1.GroupClassSession;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.service.fulfillment.v1.FulfillmentServiceGrpc;
import com.moego.idl.service.fulfillment.v1.GroupClassAttendanceServiceGrpc;
import com.moego.idl.service.fulfillment.v1.GroupClassDetailServiceGrpc;
import com.moego.idl.service.fulfillment.v1.ListFulfillmentsRequest;
import com.moego.idl.service.fulfillment.v1.ListGroupClassDetailsRequest;
import com.moego.idl.service.fulfillment.v1.ListUncheckInPetsRequest;
import com.moego.idl.service.fulfillment.v1.ListUncheckInPetsResponse;
import com.moego.idl.service.offering.v1.GetInstanceRequest;
import com.moego.idl.service.offering.v1.GetServiceDetailRequest;
import com.moego.idl.service.offering.v1.GroupClassServiceGrpc;
import com.moego.idl.service.offering.v1.ListSessionsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.utils.StringUtils;
import com.moego.lib.utils.model.Pair;
import com.moego.server.customer.dto.SearchPetResult;
import io.grpc.stub.StreamObserver;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class PetServer extends PetServiceGrpc.PetServiceImplBase {
    private final ServiceManagementServiceBlockingStub serviceManagementClient;
    private final AppointmentService appointmentService;
    private final PetService petService;
    private final MembershipService membershipService;
    private final GroupClassServiceGrpc.GroupClassServiceBlockingStub groupClassStub;
    private final FulfillmentServiceGrpc.FulfillmentServiceBlockingStub fulfillmentStub;
    private final GroupClassDetailServiceGrpc.GroupClassDetailServiceBlockingStub groupClassDetailStub;
    private final GroupClassAttendanceServiceGrpc.GroupClassAttendanceServiceBlockingStub groupClassAttendanceStub;
    private final BusinessPetService businessPetService;
    private final BusinessCustomerService businessCustomerService;
    private final ServiceService serviceService;
    private final CompanySettingService companySettingService;

    @Override
    @Auth(AuthType.COMPANY)
    public void searchPetForQuickCheckIn(
            SearchPetForQuickCheckInParams request, StreamObserver<SearchPetForQuickCheckInResult> responseObserver) {
        // get service detail
        var serviceDetail = serviceManagementClient
                .getServiceDetail(GetServiceDetailRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setServiceId(request.getServiceId())
                        .build())
                .getService();

        // get pet list
        var petSearchResult = petService.searchApplicablePetByService(
                AuthContext.get().companyId(),
                AuthContext.get().staffId(),
                request.getKeyword(),
                request.getPagination(),
                serviceDetail);
        // remove duplicate pets
        var petList = petSearchResult.pets().stream()
                .collect(Collectors.toMap(
                        SearchPetResult.PetWithCustomerDTO::getPetId, Function.identity(), (a, b) -> a))
                .values()
                .stream()
                .sorted((a, b) -> Double.compare(
                        Optional.ofNullable(b.getScore()).orElse(0d),
                        Optional.ofNullable(a.getScore()).orElse(0d)))
                .toList();
        if (petList.isEmpty()) {
            responseObserver.onNext(SearchPetForQuickCheckInResult.newBuilder()
                    .setPagination(PaginationResponse.newBuilder()
                            .setTotal(petSearchResult.pagination().total())
                            .setPageNum(request.getPagination().getPageNum())
                            .setPageSize(request.getPagination().getPageSize())
                            .build())
                    .build());
            responseObserver.onCompleted();
            return;
        }

        List<Long> customerIds = petList.stream()
                .map(SearchPetResult.PetWithCustomerDTO::getCustomerId)
                .map(Long::valueOf)
                .distinct()
                .toList();
        var clientMemberships = membershipService.getAllSubscriptions(customerIds);

        // query appointment for every pets
        var petAppointmentWithSpecificServiceAndDate = appointmentService.batchCheckPetWithServiceAndDate(
                AuthContext.get().companyId(),
                request.hasBusinessId() ? request.getBusinessId() : null,
                request.getServiceId(),
                petList.stream()
                        .map(SearchPetResult.PetWithCustomerDTO::getPetId)
                        .map(Integer::longValue)
                        .toList(),
                request.getDate());

        responseObserver.onNext(SearchPetForQuickCheckInResult.newBuilder()
                .addAllPets(petList.stream()
                        .map(pet -> {
                            SearchPetForQuickCheckInResult.PetViewForQuickCheckIn.Builder builder =
                                    SearchPetForQuickCheckInResult.PetViewForQuickCheckIn.newBuilder();
                            builder.setPet(BusinessCustomerPetCalendarView.newBuilder()
                                            .setId(pet.getPetId())
                                            .setPetName(pet.getPetName())
                                            .setAvatarPath(pet.getPetAvatarPath())
                                            .setPetType(PetType.forNumber(pet.getPetTypeId()))
                                            .setBreed(pet.getPetBreed())
                                            .setWeight(BigDecimal.valueOf(Optional.ofNullable(pet.getPetWeight())
                                                            .orElse(0.0))
                                                    .toString())
                                            .setCoatType(pet.getPetCoatType()))
                                    .setClient(BusinessCustomerModelNameView.newBuilder()
                                            .setId(pet.getCustomerId())
                                            .setFirstName(pet.getCustomerFirstName())
                                            .setLastName(pet.getCustomerLastName())
                                            .build())
                                    .setHasAppointmentToday(petAppointmentWithSpecificServiceAndDate.getOrDefault(
                                            pet.getPetId().longValue(), false))
                                    .setMembershipSubscriptions(clientMemberships.getOrDefault(
                                            pet.getCustomerId().longValue(),
                                            MembershipSubscriptionListModel.getDefaultInstance()));
                            return builder.build();
                        })
                        .toList())
                .setPagination(PaginationResponse.newBuilder()
                        .setTotal(petSearchResult.pagination().total())
                        .setPageNum(request.getPagination().getPageNum())
                        .setPageSize(request.getPagination().getPageSize())
                        .build())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void searchAssociationInfo(
            SearchAssociationInformationParams request,
            StreamObserver<SearchAssociationInformationResult> responseObserver) {
        List<Long> customerIds = request.getCustomerIdsList();
        List<Long> petIds = request.getPetIdsList();
        var clientMemberships = membershipService.getAllSubscriptions(customerIds);

        // query appointment for every pets
        var petAppointmentWithSpecificServiceAndDate = appointmentService.batchCheckPetWithServiceAndDate(
                AuthContext.get().companyId(), null, request.getServiceId(), petIds, request.getDate());

        // pet appointment
        var pa = petIds.stream()
                .map(id -> SearchAssociationInformationResult.HasAppointment.newBuilder()
                        .setPetId(id)
                        .setHasAppointment(petAppointmentWithSpecificServiceAndDate.getOrDefault(id, false))
                        .build())
                .toList();

        // customer memberships subscriptions
        var cms = customerIds.stream()
                .map(id -> SearchAssociationInformationResult.ClientMemberShipSubscription.newBuilder()
                        .setCustomerId(id)
                        .setMembershipSubscriptions(clientMemberships.getOrDefault(
                                id, MembershipSubscriptionListModel.getDefaultInstance()))
                        .build())
                .toList();
        // response
        responseObserver.onNext(SearchAssociationInformationResult.newBuilder()
                .addAllPetAppointments(pa)
                .addAllClientMembershipSubscriptions(cms)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void searchEnrollmentInfo(
            SearchEnrollmentInfoParams request, StreamObserver<SearchEnrollmentInfoResult> responseObserver) {
        var groupClassInstance = groupClassStub
                .getInstance(GetInstanceRequest.newBuilder()
                        .setId(request.getGroupClassInstanceId())
                        .build())
                .getGroupClassInstance();
        var service = serviceManagementClient
                .getServiceDetail(GetServiceDetailRequest.newBuilder()
                        .setServiceId(groupClassInstance.getGroupClassId())
                        .build())
                .getService();

        var petsAndPagination =
                switch (request.getEnrollmentTypeCase()) {
                    case BY_PET_IDS -> handlePetIdsEnrollment(AuthContext.get().companyId(), request, service);
                    case BY_KEYWORD -> handleKeywordEnrollment(
                            AuthContext.get().companyId(), AuthContext.get().staffId(), request, service);
                    default -> Pair.of(
                            new ArrayList<SearchEnrollmentInfoResult.EnrollmentPetView>(),
                            PaginationResponse.getDefaultInstance());
                };

        responseObserver.onNext(SearchEnrollmentInfoResult.newBuilder()
                .addAllPets(petsAndPagination.key())
                .setPagination(petsAndPagination.value())
                .build());
        responseObserver.onCompleted();
    }

    private Pair<List<SearchEnrollmentInfoResult.EnrollmentPetView>, PaginationResponse> handlePetIdsEnrollment(
            long companyId, SearchEnrollmentInfoParams request, ServiceModel service) {
        var petIds = request.getByPetIds().getPetIdsList();
        var petIdToInfo = businessPetService.listPetsInfo(petIds);
        var customerIds = petIdToInfo.values().stream()
                .map(BusinessCustomerPetInfoModel::getCustomerId)
                .distinct()
                .toList();
        var customerIdToInfo = businessCustomerService.listBusinessCustomerInfos(customerIds).stream()
                .collect(Collectors.toMap(BusinessCustomerInfoModel::getId, Function.identity()));

        var petIdToCompletedClassIds =
                getPetCompletedGroupClassIds(companyId, request.getBusinessId(), petIds, service);

        var serviceIds = Stream.concat(
                        petIdToCompletedClassIds.values().stream()
                                .flatMap(Collection::stream)
                                .distinct(),
                        service.getPrerequisiteClassIdsList().stream())
                .toList();
        var serviceIdToInfo = serviceService.getServiceMap(companyId, serviceIds);

        var pets = petIds.stream()
                .map(petId -> {
                    var pet = petIdToInfo.get(petId);
                    if (pet == null) {
                        throw ExceptionUtil.bizException(Code.CODE_PET_NOT_FOUND);
                    }
                    var customer = customerIdToInfo.get(pet.getCustomerId());
                    if (customer == null) {
                        throw ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND);
                    }
                    var notCompletedPrerequisites = Sets.difference(
                                    new HashSet<>(service.getPrerequisiteClassIdsList()),
                                    petIdToCompletedClassIds.getOrDefault(petId, Set.of()))
                            .stream()
                            .map(serviceIdToInfo::get)
                            .map(ServiceConvertor.INSTANCE::toGroupClassModel)
                            .toList();
                    return SearchEnrollmentInfoResult.EnrollmentPetView.newBuilder()
                            .setPet(PetConverter.INSTANCE.toView(pet))
                            .setClient(CustomerConverter.INSTANCE.toView(customer))
                            .addAllNotCompletedPrerequisites(notCompletedPrerequisites)
                            .build();
                })
                .toList();
        return Pair.of(
                pets,
                PaginationResponse.newBuilder()
                        .setPageNum(1)
                        .setPageSize(pets.size())
                        .setTotal(pets.size())
                        .build());
    }

    private Pair<List<SearchEnrollmentInfoResult.EnrollmentPetView>, PaginationResponse> handleKeywordEnrollment(
            long companyId, long staffId, SearchEnrollmentInfoParams request, ServiceModel service) {
        var petSearchResult = petService.searchApplicablePetByService(
                companyId,
                staffId,
                request.getByKeyword().getKeyword(),
                request.getByKeyword().getPagination(),
                service);
        var petIds = petSearchResult.pets().stream()
                .map(SearchPetResult.PetWithCustomerDTO::getPetId)
                .map(Integer::longValue)
                .toList();

        var petIdToCompletedClassIds =
                getPetCompletedGroupClassIds(companyId, request.getBusinessId(), petIds, service);

        var serviceIds = Stream.concat(
                        petIdToCompletedClassIds.values().stream()
                                .flatMap(Collection::stream)
                                .distinct(),
                        service.getPrerequisiteClassIdsList().stream())
                .toList();
        var serviceIdToInfo = serviceService.getServiceMap(companyId, serviceIds);

        var pets = petSearchResult.pets().stream()
                .map(petWithCustomer -> {
                    var notCompletedPrerequisites = Sets.difference(
                                    new HashSet<>(service.getPrerequisiteClassIdsList()),
                                    petIdToCompletedClassIds.getOrDefault(
                                            petWithCustomer.getPetId().longValue(), Set.of()))
                            .stream()
                            .map(serviceIdToInfo::get)
                            .map(ServiceConvertor.INSTANCE::toGroupClassModel)
                            .toList();
                    return SearchEnrollmentInfoResult.EnrollmentPetView.newBuilder()
                            .setPet(PetConverter.INSTANCE.toView(petWithCustomer))
                            .setClient(CustomerConverter.INSTANCE.toView(petWithCustomer))
                            .addAllNotCompletedPrerequisites(notCompletedPrerequisites)
                            .build();
                })
                .toList();
        return Pair.of(
                pets,
                PaginationResponse.newBuilder()
                        .setPageNum(petSearchResult.pagination().pageNum())
                        .setPageSize(petSearchResult.pagination().pageSize())
                        .setTotal(petSearchResult.pagination().total())
                        .build());
    }

    private Map<Long, Set<Long>> getPetCompletedGroupClassIds(
            long companyId, long businessId, List<Long> petIds, ServiceModel service) {
        if (!service.getIsRequirePrerequisiteClass() && service.getPrerequisiteClassIdsCount() == 0) {
            return Map.of();
        }
        var fulfillments = fulfillmentStub
                .listFulfillments(ListFulfillmentsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addBusinessIds(businessId)
                        .setFilter(ListFulfillmentsRequest.Filter.newBuilder()
                                .addAllPetIds(petIds)
                                .addAllServiceIds(service.getPrerequisiteClassIdsList())
                                .addAllStatuses(FulfillmentStatusConst.PRE_PAYMENT_MODE_ACTIVE_STATUSES)
                                .build())
                        .build())
                .getFulfillmentsList();
        if (fulfillments.isEmpty()) {
            return Map.of();
        }
        var fulfillmentIds = fulfillments.stream().map(FulfillmentModel::getId).toList();
        var groupClassDetailsList = groupClassDetailStub
                .listGroupClassDetails(ListGroupClassDetailsRequest.newBuilder()
                        .setFilter(
                                ListGroupClassDetailsRequest.Filter.newBuilder().addAllFulfillmentIds(fulfillmentIds))
                        .build())
                .getGroupClassDetailsList();
        return groupClassDetailsList.stream()
                .filter(i -> Objects.equals(i.getStatus(), GroupClassDetailModel.Status.COMPLETED))
                .collect(Collectors.groupingBy(
                        GroupClassDetailModel::getPetId,
                        Collectors.mapping(GroupClassDetailModel::getGroupClassId, Collectors.toSet())));
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void searchPetForGroupClassCheckIn(
            SearchPetForGroupClassCheckInParams request,
            StreamObserver<SearchPetForGroupClassCheckInResult> responseObserver) {
        var timeZoneName =
                companySettingService.mustGetTimeZoneName(AuthContext.get().companyId());
        var now = ZonedDateTime.now(ZoneId.of(timeZoneName)).toLocalDate();
        var builder = ListUncheckInPetsRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .setDate(Date.newBuilder()
                        .setYear(now.getYear())
                        .setMonth(now.getMonthValue())
                        .setDay(now.getDayOfMonth()));
        if (request.hasGroupClassSessionId()) {
            builder.setGroupClassSessionId(request.getGroupClassSessionId());
        }

        var response = groupClassDetailStub.listUncheckInPets(builder.build());
        if (response.getPetSessionsCount() == 0) {
            responseObserver.onNext(SearchPetForGroupClassCheckInResult.newBuilder()
                    .setPagination(PaginationResponse.newBuilder()
                            .setPageNum(request.getPagination().getPageNum())
                            .setPageSize(request.getPagination().getPageSize()))
                    .build());
            responseObserver.onCompleted();
            return;
        }

        var petIds = response.getPetSessionsList().stream()
                .map(ListUncheckInPetsResponse.PetSession::getPetId)
                .distinct()
                .toList();
        var petIdToInfo = businessPetService.listPetsInfo(petIds);
        var customerIds = petIdToInfo.values().stream()
                .map(BusinessCustomerPetInfoModel::getCustomerId)
                .distinct()
                .toList();
        var customerInfos = businessCustomerService.listBusinessCustomerInfos(customerIds);

        var customerIdToInfo =
                customerInfos.stream().collect(Collectors.toMap(BusinessCustomerInfoModel::getId, Function.identity()));

        var filteredPets = filterPetsByKeyword(petIdToInfo, customerIdToInfo, request.getKeyword());

        var sortedPets = sortPetsByName(filteredPets);

        var pagedResult = applyPagination(sortedPets, request.getPagination());

        responseObserver.onNext(SearchPetForGroupClassCheckInResult.newBuilder()
                .addAllPets(buildCheckInPets(pagedResult.key(), customerIdToInfo))
                .setPagination(pagedResult.value())
                .build());
        responseObserver.onCompleted();
    }

    private List<SearchPetForGroupClassCheckInResult.CheckInPetView> buildCheckInPets(
            List<BusinessCustomerPetInfoModel> pets, Map<Long, BusinessCustomerInfoModel> customerIdToInfo) {
        List<SearchPetForGroupClassCheckInResult.CheckInPetView> checkInPetViews = new ArrayList<>();

        for (BusinessCustomerPetInfoModel petInfo : pets) {
            var customerInfo = customerIdToInfo.getOrDefault(petInfo.getCustomerId(), null);
            if (customerInfo == null) {
                continue;
            }
            checkInPetViews.add(SearchPetForGroupClassCheckInResult.CheckInPetView.newBuilder()
                    .setPet(PetConverter.INSTANCE.toView(petInfo))
                    .setClient(CustomerConverter.INSTANCE.toView(customerInfo))
                    //                            .setGroupClassInstance(groupClassInstanceView) TODO 待实现
                    .build());
        }

        return checkInPetViews;
    }

    private List<BusinessCustomerPetInfoModel> filterPetsByKeyword(
            Map<Long, BusinessCustomerPetInfoModel> petIdToInfo,
            Map<Long, BusinessCustomerInfoModel> customerIdToInfo,
            String keyword) {

        List<BusinessCustomerPetInfoModel> filteredPets = new ArrayList<>();
        if (keyword == null || StringUtils.isEmpty(keyword.trim())) {
            return new ArrayList<>(petIdToInfo.values());
        }

        String normalizedKeyword = keyword.toLowerCase().trim();

        for (Map.Entry<Long, BusinessCustomerPetInfoModel> entry : petIdToInfo.entrySet()) {
            var petInfo = entry.getValue();
            var customerInfo = customerIdToInfo.get(petInfo.getCustomerId());

            if (matchesKeyword(petInfo, customerInfo, normalizedKeyword)) {
                filteredPets.add(petInfo);
            }
        }

        return filteredPets;
    }

    private boolean matchesKeyword(
            BusinessCustomerPetInfoModel petInfo, BusinessCustomerInfoModel customerInfo, String keyword) {
        String petName = petInfo.getPetName().toLowerCase();
        String customerFirstName =
                customerInfo != null ? customerInfo.getFirstName().toLowerCase() : "";
        String customerLastName =
                customerInfo != null ? customerInfo.getLastName().toLowerCase() : "";
        String customerFullName = customerFirstName + " " + customerLastName;

        return petName.contains(keyword)
                || customerFullName.contains(keyword)
                || customerFirstName.contains(keyword)
                || customerLastName.contains(keyword);
    }

    private List<BusinessCustomerPetInfoModel> sortPetsByName(List<BusinessCustomerPetInfoModel> pets) {
        return pets.stream()
                .sorted(Comparator.comparing(BusinessCustomerPetInfoModel::getPetName))
                .toList();
    }

    private Pair<List<BusinessCustomerPetInfoModel>, PaginationResponse> applyPagination(
            List<BusinessCustomerPetInfoModel> pets, PaginationRequest pagination) {
        int totalCount = pets.size();
        int totalPages = (int) Math.ceil((double) totalCount / pagination.getPageSize());

        // 验证并调整页码
        var pageNum = pagination.getPageNum();
        var pageSize = pagination.getPageSize();
        if (pageNum > totalPages && totalPages > 0) {
            pageNum = totalPages;
        }

        // 计算分页的索引
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalCount);

        // 获取当前页的数据
        List<BusinessCustomerPetInfoModel> pagedPets;
        if (startIndex < totalCount) {
            pagedPets = pets.subList(startIndex, endIndex);
        } else {
            pagedPets = new ArrayList<>();
        }

        return Pair.of(
                pagedPets,
                PaginationResponse.newBuilder()
                        .setPageNum(pageNum)
                        .setPageSize(pageSize)
                        .setTotal(totalCount)
                        .build());
    }

    private List<Long> getNeedCheckInSession(long companyId, SearchPetForGroupClassCheckInParams request) {
        if (request.hasGroupClassSessionId()) {
            return List.of(request.getGroupClassSessionId());
        }
        // 未指定场次，则默认查询今天所有的场次
        var timeZoneName = companySettingService.mustGetTimeZoneName(companyId);
        var listTodaySessionsRequest = ListSessionsRequest.newBuilder()
                .setCompanyId(companyId)
                .setStartTimeMin(Timestamp.newBuilder()
                        .setSeconds(LocalDate.now()
                                .atStartOfDay()
                                .atZone(ZoneId.of(timeZoneName))
                                .toEpochSecond()))
                .setStartTimeMax(Timestamp.newBuilder()
                        .setSeconds(LocalDate.now()
                                .plusDays(1)
                                .atStartOfDay()
                                .atZone(ZoneId.of(timeZoneName))
                                .toEpochSecond()))
                .build();
        return groupClassStub.listSessions(listTodaySessionsRequest).getSessionsList().stream()
                .map(GroupClassSession::getId)
                .toList();
    }
}
