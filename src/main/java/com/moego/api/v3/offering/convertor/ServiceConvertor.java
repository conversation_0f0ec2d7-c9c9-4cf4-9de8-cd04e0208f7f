package com.moego.api.v3.offering.convertor;

import com.moego.idl.api.offering.v1.CommonServiceView;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.GroupClassModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceModel;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        unmappedTargetPolicy = ReportingPolicy.WARN,
        collectionMappingStrategy = CollectionMappingStrategy.TARGET_IMMUTABLE)
public interface ServiceConvertor {

    ServiceConvertor INSTANCE = Mappers.getMapper(ServiceConvertor.class);

    @Mapping(target = "id", source = "serviceId")
    ServiceBriefView toBriefView(ServiceModel serviceModel);

    @Mapping(target = "petSpecificPrices", ignore = true)
    @Mapping(target = "petSpecificDurations", ignore = true)
    CommonServiceView toCommonView(CustomizedServiceView view);

    GroupClassModel toGroupClassModel(ServiceBriefView view);
}
