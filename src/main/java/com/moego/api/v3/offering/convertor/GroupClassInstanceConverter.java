package com.moego.api.v3.offering.convertor;

import com.google.type.Date;
import com.moego.idl.api.offering.v1.GroupClassInstanceView;
import com.moego.idl.api.offering.v1.GroupClassSessionView;
import com.moego.idl.models.offering.v1.GroupClassInstance;
import com.moego.idl.models.offering.v1.GroupClassSession;
import java.util.List;

public class GroupClassInstanceConverter {
    public static GroupClassInstanceView toGroupClassInstanceView(
            GroupClassInstance groupClassInstance, List<GroupClassSession> groupClassSessions) {

        return GroupClassInstanceView.newBuilder()
                .setId(groupClassInstance.getId())
                .setCompanyId(groupClassInstance.getCompanyId())
                .setTrainingGroupClassId(groupClassInstance.getGroupClassId())
                .setName(groupClassInstance.getName())
                .setStartDate(Date.newBuilder()
                        .setYear(groupClassInstance.getStartTime().getYear())
                        .setMonth(groupClassInstance.getStartTime().getMonth())
                        .setDay(groupClassInstance.getStartTime().getDay())
                        .build())
                .setStartTimeOfDayMinutes(groupClassInstance.getStartTime().getHours() * 60L
                        + groupClassInstance.getStartTime().getMinutes())
                .setTimeZone(groupClassInstance.getStartTime().getTimeZone())
                .setCapacity(groupClassInstance.getCapacity())
                .setOccurrence(groupClassInstance.getOccurrence())
                .setStatus(groupClassInstance.getStatus())
                .setPrice(groupClassInstance.getPrice())
                .addAllSessions(groupClassSessions.stream()
                        .map(GroupClassInstanceConverter::toGroupClassSessionView)
                        .toList())
                .build();
    }

    public static GroupClassSessionView toGroupClassSessionView(GroupClassSession groupClassSession) {

        return GroupClassSessionView.newBuilder()
                .setId(groupClassSession.getId())
                .setCompanyId(groupClassSession.getCompanyId())
                .setTrainingGroupClassInstanceId(groupClassSession.getGroupClassInstanceId())
                .setInterval(groupClassSession.getInterval())
                .setDurationSessionMinutes(
                        (int) (groupClassSession.getDuration().getSeconds() / 60L))
                .setIsModified(groupClassSession.getIsModified())
                .setStatus(groupClassSession.getStatus())
                .build();
    }
}
