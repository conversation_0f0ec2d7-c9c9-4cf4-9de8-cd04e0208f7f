package com.moego.api.v3.order.convertor;

import com.moego.idl.api.order.v1.AddCompanyServiceChargeParams;
import com.moego.idl.api.order.v1.AddServiceChargeRequest;
import com.moego.idl.api.order.v1.DeleteServiceChargeRequest;
import com.moego.idl.api.order.v1.GetServiceChargeListRequest;
import com.moego.idl.api.order.v1.SortServiceChargeRequest;
import com.moego.idl.api.order.v1.UpdateCompanyServiceChargeParams;
import com.moego.idl.api.order.v1.UpdateServiceChargeRequest;
import com.moego.idl.service.order.v1.AddCompanyServiceChargeRequest;
import com.moego.idl.service.order.v1.AddServiceChargeInput;
import com.moego.idl.service.order.v1.DeleteServiceChargeInput;
import com.moego.idl.service.order.v1.GetServiceChargeListInput;
import com.moego.idl.service.order.v1.SortServiceChargeInput;
import com.moego.idl.service.order.v1.UpdateCompanyServiceChargeRequest;
import com.moego.idl.service.order.v1.UpdateServiceChargeInput;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ServiceChargeConvertor {

    ServiceChargeConvertor INSTANCE = Mappers.getMapper(ServiceChargeConvertor.class);

    GetServiceChargeListInput toGetServiceChargeListInput(GetServiceChargeListRequest request);

    AddServiceChargeInput toAddServiceChargeInput(AddServiceChargeRequest request);

    UpdateServiceChargeInput toUpdateServiceChargeInput(UpdateServiceChargeRequest request);

    AddCompanyServiceChargeRequest toAddCompanyServiceChargeInput(AddCompanyServiceChargeParams request);

    UpdateCompanyServiceChargeRequest toUpdateCompanyServiceChargeCompanyInput(
            UpdateCompanyServiceChargeParams request);

    default SortServiceChargeInput toSortServiceChargeInput(SortServiceChargeRequest request) {
        return SortServiceChargeInput.newBuilder()
                .addAllSortedId(request.getSortedIdList())
                .build();
    }

    DeleteServiceChargeInput toDeleteServiceChargeInput(DeleteServiceChargeRequest request);
}
