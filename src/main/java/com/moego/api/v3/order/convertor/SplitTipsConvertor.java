package com.moego.api.v3.order.convertor;

import com.moego.idl.api.order.v1.ClearTipsSplitChangedStatusParams;
import com.moego.idl.api.order.v1.CustomizedTipConfigRequest;
import com.moego.idl.api.order.v1.GetTipsSplitChangedStatusParams;
import com.moego.idl.service.order.v1.CustomizedTipConfigInput;
import com.moego.idl.service.order.v2.ClearTipsSplitChangedStatusRequest;
import com.moego.idl.service.order.v2.GetTipsSplitChangedStatusRequest;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SplitTipsConvertor {
    SplitTipsConvertor INSTANCE = Mappers.getMapper(SplitTipsConvertor.class);

    List<CustomizedTipConfigInput> toInputList(List<CustomizedTipConfigRequest> requestList);

    GetTipsSplitChangedStatusRequest toRequest(GetTipsSplitChangedStatusParams request);

    ClearTipsSplitChangedStatusRequest toRequest(ClearTipsSplitChangedStatusParams request);
}
