package com.moego.api.v3.google_partner.controller;

import static com.moego.idl.service.google_partner.v1.GoogleReserveIntegrationServiceGrpc.GoogleReserveIntegrationServiceBlockingStub;
import static com.moego.lib.common.auth.AuthType.BUSINESS;

import com.moego.idl.api.google_partner.v1.GetOrInsertGoogleReserveIntegrationParam;
import com.moego.idl.api.google_partner.v1.GoogleReserveIntegrationServiceGrpc;
import com.moego.idl.api.google_partner.v1.UpdateGoogleReserveIntegrationParam;
import com.moego.idl.models.google_partner.v1.GoogleReserveIntegrationModel;
import com.moego.idl.models.google_partner.v1.GoogleReserveIntegrationStatus;
import com.moego.idl.service.google_partner.v1.GetGoogleReserveIntegrationRequest;
import com.moego.idl.service.google_partner.v1.InsertGoogleReserveIntegrationRequest;
import com.moego.idl.service.google_partner.v1.UpdateGoogleReserveIntegrationRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@GrpcService
@RequiredArgsConstructor
public class GoogleReserveIntegrationController
        extends GoogleReserveIntegrationServiceGrpc.GoogleReserveIntegrationServiceImplBase {

    private final GoogleReserveIntegrationServiceBlockingStub stub;

    @Override
    @Auth(BUSINESS)
    public void getOrInsertGoogleReserveIntegration(
            GetOrInsertGoogleReserveIntegrationParam request,
            StreamObserver<GoogleReserveIntegrationModel> responseObserver) {
        int businessId = Optional.ofNullable(AuthContext.get().getBusinessId()).orElseThrow();

        GoogleReserveIntegrationModel model;
        try {
            model = stub.getGoogleReserveIntegration(GetGoogleReserveIntegrationRequest.newBuilder()
                    .setBusinessId(businessId)
                    .build());
        } catch (StatusRuntimeException e) {
            if (e.getStatus().getCode() != Status.Code.NOT_FOUND) {
                throw e;
            }
            model = stub.insertGoogleReserveIntegration(InsertGoogleReserveIntegrationRequest.newBuilder()
                    .setBusinessId(businessId)
                    .build());
        }

        responseObserver.onNext(model);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void updateGoogleReserveIntegration(
            UpdateGoogleReserveIntegrationParam request,
            StreamObserver<GoogleReserveIntegrationModel> responseObserver) {
        AuthContext ac = AuthContext.get();
        UpdateGoogleReserveIntegrationRequest.Builder builder = UpdateGoogleReserveIntegrationRequest.newBuilder();
        builder.setBusinessId(Optional.ofNullable(ac.getBusinessId()).orElseThrow());
        if (request.hasEnabled()) {
            builder.setEnabled(request.getEnabled());
            // Reset Google reserve status when disable integration,
            // see https://moego.atlassian.net/jira/software/c/projects/ERP/issues/ERP-7509
            if (!request.getEnabled()) {
                builder.setStatus(GoogleReserveIntegrationStatus.GOOGLE_RESERVE_INTEGRATION_STATUS_UNSPECIFIED);
            }
        }

        responseObserver.onNext(stub.updateGoogleReserveIntegration(builder.build()));
        responseObserver.onCompleted();
    }
}
