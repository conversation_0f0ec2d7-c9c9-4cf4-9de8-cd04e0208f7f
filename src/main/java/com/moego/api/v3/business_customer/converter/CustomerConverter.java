package com.moego.api.v3.business_customer.converter;

import com.moego.common.utils.PermissionUtil;
import com.moego.idl.api.business_customer.v1.ListDuplicateCustomerGroupsResult;
import com.moego.idl.api.business_customer.v1.PreviewCustomerMergeResult;
import com.moego.idl.models.agreement.v1.AgreementWithRecentRecordsView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerContactModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.CustomerDuplicationCheckView;
import com.moego.idl.models.business_customer.v1.PetDuplicationCheckView;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        imports = {PermissionUtil.class})
public interface CustomerConverter {

    CustomerConverter INSTANCE = Mappers.getMapper(CustomerConverter.class);

    /**
     * DO NOT DELETE THIS METHOD
     * used by {@link CustomerConverter#toCustomerView(CustomerDuplicationCheckView view, int totalAppointmentCount, boolean mask)}
     */
    ListDuplicateCustomerGroupsResult.PetView toPetView(PetDuplicationCheckView view);

    @Mapping(
            target = "phoneNumber",
            expression =
                    "java(mask ? PermissionUtil.phoneNumberConfusion(view.getPhoneNumber()) : view.getPhoneNumber())")
    @Mapping(
            target = "email",
            expression = "java(mask ? PermissionUtil.emailConfusion(view.getEmail()) : view.getEmail())")
    ListDuplicateCustomerGroupsResult.CustomerView toCustomerView(
            CustomerDuplicationCheckView view, int totalAppointmentCount, boolean mask);

    @Mapping(target = "id", source = "agreementId")
    @Mapping(target = "title", source = "agreementTitle")
    PreviewCustomerMergeResult.AgreementView toAgreementView(AgreementWithRecentRecordsView view);

    @Mapping(
            target = "phoneNumber",
            expression =
                    "java(mask ? PermissionUtil.phoneNumberConfusion(model.getPhoneNumber()) : model.getPhoneNumber())")
    @Mapping(
            target = "email",
            expression = "java(mask ? PermissionUtil.emailConfusion(model.getEmail()) : model.getEmail())")
    PreviewCustomerMergeResult.ContactView toContactView(BusinessCustomerContactModel model, boolean mask);

    PreviewCustomerMergeResult.PetView toPetView(BusinessCustomerPetInfoModel model, boolean duplicate);

    @Mapping(
            target = "phoneNumber",
            expression =
                    "java(mask ? PermissionUtil.phoneNumberConfusion(view.getPhoneNumber()) : view.getPhoneNumber())")
    @Mapping(
            target = "email",
            expression = "java(mask ? PermissionUtil.emailConfusion(view.getEmail()) : view.getEmail())")
    PreviewCustomerMergeResult.CustomerView toCustomerView(
            BusinessCustomerInfoModel view, boolean hasMembership, boolean mask);
}
