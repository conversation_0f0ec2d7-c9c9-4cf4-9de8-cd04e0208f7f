package com.moego.api.v3.business_customer.service;

import com.moego.idl.api.business_customer.v1.CreatePetColorResult;
import com.moego.idl.api.business_customer.v1.ListPetBindingResult;
import com.moego.idl.api.business_customer.v1.ListPetColorResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.business_customer.v1.BindingColorRequest;
import com.moego.idl.service.business_customer.v1.BusinessPetColorServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListColorBindingRequest;
import com.moego.idl.service.business_customer.v1.ListColorBindingResponse;
import com.moego.idl.service.business_customer.v1.ListColorRequest;
import com.moego.idl.service.business_customer.v1.ListColorResponse;
import com.moego.idl.service.business_customer.v1.UpdateColorRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BusinessColorService {
    private final BusinessPetColorServiceGrpc.BusinessPetColorServiceBlockingStub petColorServiceBlockingStub;

    public ListPetColorResult listPetColor(Long companyId) {
        var colors = petColorServiceBlockingStub.listColor(
                ListColorRequest.newBuilder().setCompanyId(companyId).build());
        var list = colors.getColorsList().stream()
                .map(color -> ListPetColorResult.Color.newBuilder()
                        .setColorId(color.getId())
                        .setColorName(color.getName())
                        .setStatus(color.getStatus())
                        .setCreateTime(color.getCreateTime())
                        .setUpdateTime(color.getUpdateTime())
                        .build())
                .collect(Collectors.toList());
        return ListPetColorResult.newBuilder().addAllColors(list).build();
    }

    public CreatePetColorResult createColor(Long companyId, String name) {
        var resp = petColorServiceBlockingStub.updateColor(UpdateColorRequest.newBuilder()
                .setColorName(name)
                .setCompanyId(companyId)
                .setStatus(UpdateColorRequest.Status.NORMAL)
                .build());

        var colors = petColorServiceBlockingStub.listColor(ListColorRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllColorIds(List.of(resp.getId()))
                .build());

        var color = colors.getColorsList().stream()
                .map(c -> CreatePetColorResult.Color.newBuilder()
                        .setColorId(c.getId())
                        .setColorName(c.getName())
                        .setStatus(c.getStatus())
                        .setCreateTime(c.getCreateTime())
                        .setUpdateTime(c.getUpdateTime())
                        .build())
                .findFirst()
                .orElse(CreatePetColorResult.Color.getDefaultInstance());

        return CreatePetColorResult.newBuilder().setResult(true).setColor(color).build();
    }

    public Boolean deleteColor(Long companyId, Long colorId) {
        var resp = petColorServiceBlockingStub.updateColor(UpdateColorRequest.newBuilder()
                .setColorId(colorId)
                .setCompanyId(companyId)
                .setStatus(UpdateColorRequest.Status.DELETED)
                .build());
        if (Objects.isNull(resp)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "update failed");
        }
        return true;
    }

    public void bindingColor(Long petId, Long colorId) {
        var resp = petColorServiceBlockingStub.bindingColor(BindingColorRequest.newBuilder()
                .setColorId(colorId)
                .setPetId(petId)
                .setStatus(BindingColorRequest.Status.NORMAL)
                .build());
        if (!resp.getResult()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "binding failed");
        }
    }

    public ListPetBindingResult listPetBindingColor(Long colorId, Long petId) {
        Long companyId = AuthContext.get().companyId();
        var builder = ListColorBindingRequest.newBuilder();
        Optional.ofNullable(petId).ifPresent(builder::setPetId);
        Optional.ofNullable(colorId).ifPresent(builder::setColorId);
        var resp = petColorServiceBlockingStub.listColorBinding(builder.build());
        var bindings = resp.getBindingsList();
        var colorIds = bindings.stream()
                .map(ListColorBindingResponse.ColorBinding::getColorId)
                .toList();

        var colorResponse = petColorServiceBlockingStub.listColor(ListColorRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllColorIds(colorIds)
                .build());
        var colors = colorResponse.getColorsList().stream()
                .collect(Collectors.toMap(ListColorResponse.Color::getId, c -> c));
        return ListPetBindingResult.newBuilder()
                .addAllPetColorsBindings(bindings.stream()
                        .map(b -> {
                            var build = ListPetBindingResult.PetColorBinding.newBuilder()
                                    .setColorId(b.getColorId())
                                    .setPetId(b.getPetId())
                                    .setStatus(b.getStatus());
                            Optional.ofNullable(colors.get(b.getColorId())).ifPresent(c -> {
                                build.setColorName(c.getName());
                            });
                            return build.build();
                        })
                        .toList())
                .build();
    }
}
