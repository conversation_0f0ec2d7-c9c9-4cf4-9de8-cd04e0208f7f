package com.moego.api.v3.business_customer.service;

import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Service
@RequiredArgsConstructor
public class BusinessCustomerAddressService {

    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub addressStub;

    public List<BusinessCustomerAddressModel> listPrimaryAddresses(List<Long> customerIds) {
        if (ObjectUtils.isEmpty(customerIds)) {
            return List.of();
        }
        return addressStub
                .batchGetCustomerPrimaryAddress(BatchGetCustomerPrimaryAddressRequest.newBuilder()
                        .addAllCustomerIds(customerIds)
                        .build())
                .getAddressesMap()
                .values()
                .stream()
                .toList();
    }
}
