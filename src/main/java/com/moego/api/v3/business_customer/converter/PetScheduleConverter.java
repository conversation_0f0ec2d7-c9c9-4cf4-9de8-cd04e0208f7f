package com.moego.api.v3.business_customer.converter;

import com.moego.idl.models.business_customer.v1.BusinessPetFeedingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetFeedingScheduleView;
import com.moego.idl.models.business_customer.v1.BusinessPetMedicationModel;
import com.moego.idl.models.business_customer.v1.BusinessPetMedicationScheduleView;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @since 2024/1/18
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface PetScheduleConverter {

    @Mapping(target = ".", source = "model")
    BusinessPetFeedingScheduleView modelToView(BusinessPetFeedingModel model);

    @Mapping(target = ".", source = "model")
    BusinessPetMedicationScheduleView modelToView(BusinessPetMedicationModel model);
}
