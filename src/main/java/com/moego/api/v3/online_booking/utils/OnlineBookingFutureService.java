package com.moego.api.v3.online_booking.utils;

import static com.moego.common.utils.CommonUtil.isNormal;

import com.moego.api.v3.appointment.converter.PrePayConverter;
import com.moego.api.v3.online_booking.converter.AutoAssignConverter;
import com.moego.api.v3.online_booking.converter.ClientConverter;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.api.online_booking.v1.AddressDetail;
import com.moego.idl.api.online_booking.v1.GroomingAutoAssignDetail;
import com.moego.idl.api.online_booking.v1.OrderBookingRequestView;
import com.moego.idl.api.online_booking.v1.PayBookingRequestView;
import com.moego.idl.models.appointment.v1.AppointmentNoteModel;
import com.moego.idl.models.appointment.v1.AppointmentNoteType;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.business_customer.v1.BusinessCustomerContactModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeBindingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineDiscountModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.models.order.v1.OrderStatus;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.appointment.v1.AppointmentNoteServiceGrpc;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetCustomerLastAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetCustomerLastAppointmentResponse;
import com.moego.idl.service.appointment.v1.GetCustomerLastNoteRequest;
import com.moego.idl.service.appointment.v1.GetCustomerLastNoteResponse;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerResponse;
import com.moego.idl.service.business_customer.v1.BatchListBindingPetCodeRequest;
import com.moego.idl.service.business_customer.v1.BatchListBindingPetCodeResponse;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerContactServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetCodeServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.ListCustomerContactRequest;
import com.moego.idl.service.marketing.v1.DiscountCodeServiceGrpc;
import com.moego.idl.service.marketing.v1.GetDiscountCodeInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeOutput;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.idl.service.offering.v1.MGetLodgingUnitRequest;
import com.moego.idl.service.offering.v1.MGetLodgingUnitResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.order.v1.GetOrderListRequest;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.customer.api.IProfileRequestAddressService;
import com.moego.server.customer.dto.ProfileRequestAddressDTO;
import com.moego.server.grooming.api.IAutoAssignService;
import com.moego.server.grooming.client.IBookOnlineDepositClient;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.dto.AutoAssignDTO;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.ob.ServiceAreaResultDTO;
import com.moego.server.grooming.params.ob.ServiceAreaParams;
import jakarta.annotation.Nullable;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class OnlineBookingFutureService {

    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentServiceBlockingStub;
    private final AppointmentNoteServiceGrpc.AppointmentNoteServiceBlockingStub appointmentNoteService;
    private final LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitService;
    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerService;
    private final BusinessCustomerContactServiceGrpc.BusinessCustomerContactServiceBlockingStub
            businessCustomerContactService;
    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub
            businessCustomerAddressService;
    private final OrderServiceGrpc.OrderServiceBlockingStub orderService;
    private final IBookOnlineDepositClient bookOnlineDepositClient;
    private final IAutoAssignService autoAssignService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementService;
    private final BusinessPetCodeServiceGrpc.BusinessPetCodeServiceBlockingStub businessPetCodeService;
    private final DiscountCodeServiceGrpc.DiscountCodeServiceBlockingStub discountCodeClient;
    private final IProfileRequestAddressService profileRequestAddressApi;
    private final IGroomingOnlineBookingClient groomingOnlineBookingClient;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffService;

    public CompletableFuture<Map<Long, StaffModel>> getStaffMap(
            List<BookingRequestModel> bookingRequests,
            CompletableFuture<Map<Long, GroomingAutoAssignDetail>> groomingAutoAssignsFuture) {
        return groomingAutoAssignsFuture.thenApplyAsync(
                groomingAutoAssignMap -> {
                    List<Long> serviceStaffIds = bookingRequests.stream()
                            .flatMap(bookingRequest -> bookingRequest.getServicesList().stream())
                            .filter(service -> service.hasGrooming() || service.hasGroupClass())
                            .flatMap(service -> {
                                List<Long> list = new ArrayList<>();
                                if (service.hasGrooming()) {
                                    list.add(service.getGrooming().getService().getStaffId());
                                    list.addAll(service.getGrooming().getAddonsList().stream()
                                            .map(GroomingAddOnDetailModel::getStaffId)
                                            .toList());
                                }
                                // group class
                                if (service.hasGroupClass()) {
                                    list.add(
                                            service.getGroupClass().getService().getStaffId());
                                }
                                return list.stream();
                            })
                            .filter(id -> Objects.nonNull(id) && id > 0)
                            .toList();

                    List<Long> autoAssignStaffIds = groomingAutoAssignMap.values().stream()
                            .map(GroomingAutoAssignDetail::getStaffId)
                            .map(Long::valueOf)
                            .toList();

                    List<Long> staffIds = Stream.of(serviceStaffIds, autoAssignStaffIds)
                            .flatMap(List::stream)
                            .filter(id -> Objects.nonNull(id) && id > 0)
                            .distinct()
                            .toList();

                    if (CollectionUtils.isEmpty(staffIds)) {
                        return Map.of();
                    }
                    return staffService
                            .queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                                    .addAllStaffIds(staffIds)
                                    .build())
                            .getStaffsList()
                            .stream()
                            .collect(Collectors.toMap(StaffModel::getId, Function.identity()));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, StaffModel>> getStaffMap(
            BookingRequestModel bookingRequest, CompletableFuture<GroomingAutoAssignDetail> groomingAutoAssignFuture) {
        return groomingAutoAssignFuture.thenApplyAsync(
                autoAssignDetail -> {
                    Set<Long> staffIds = bookingRequest.getServicesList().stream()
                            .filter(service -> service.hasGrooming() || service.hasGroupClass())
                            .flatMap(service -> {
                                List<Long> list = new ArrayList<>();
                                if (service.hasGrooming()) {
                                    list.add(service.getGrooming().getService().getStaffId());
                                    list.addAll(service.getGrooming().getAddonsList().stream()
                                            .map(GroomingAddOnDetailModel::getStaffId)
                                            .toList());
                                }
                                // group class
                                if (service.hasGroupClass()) {
                                    list.add(
                                            service.getGroupClass().getService().getStaffId());
                                }
                                return list.stream();
                            })
                            .filter(id -> Objects.nonNull(id) && id > 0)
                            .collect(Collectors.toSet());
                    if (Objects.nonNull(autoAssignDetail) && autoAssignDetail.getStaffId() > 0) {
                        staffIds.add((long) autoAssignDetail.getStaffId());
                    }

                    if (CollectionUtils.isEmpty(staffIds)) {
                        return Map.of();
                    }
                    return staffService
                            .queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                                    .addAllStaffIds(staffIds)
                                    .build())
                            .getStaffsList()
                            .stream()
                            .collect(Collectors.toMap(StaffModel::getId, Function.identity()));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, ServiceBriefView>> getServiceMap(long companyId, Collection<Long> serviceIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(serviceIds)) {
                        return Map.of();
                    }
                    List<ServiceBriefView> serviceList = serviceManagementService
                            .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .addAllServiceIds(serviceIds)
                                    .build())
                            .getServicesList()
                            .stream()
                            .toList();
                    return serviceList.stream().collect(Collectors.toMap(ServiceBriefView::getId, Function.identity()));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<BusinessPetCodeModel>>> getPetCodeMap(long companyId, List<Long> petIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(petIds)) {
                        return Map.of();
                    }
                    BatchListBindingPetCodeResponse batchListBindingPetCodeResponse =
                            businessPetCodeService.batchListBindingPetCode(BatchListBindingPetCodeRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .addAllPetIds(petIds)
                                    .build());
                    Map<Long, BusinessPetCodeBindingModel> petCodeBindingModelMap =
                            batchListBindingPetCodeResponse.getBindingsList().stream()
                                    .collect(Collectors.toMap(
                                            BusinessPetCodeBindingModel::getPetId, Function.identity()));
                    Map<Long, BusinessPetCodeModel> petCodeModelMap =
                            batchListBindingPetCodeResponse.getPetCodesList().stream()
                                    .collect(Collectors.toMap(BusinessPetCodeModel::getId, Function.identity()));
                    return petIds.stream().collect(Collectors.toMap(Function.identity(), petId -> {
                        BusinessPetCodeBindingModel businessPetCodeBindingModel = petCodeBindingModelMap.get(petId);
                        if (Objects.isNull(businessPetCodeBindingModel)) {
                            return List.of();
                        }
                        return businessPetCodeBindingModel.getPetCodeIdsList().stream()
                                .map(petCodeModelMap::get)
                                .toList();
                    }));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<AppointmentNoteModel> getCustomerLastAlertNoteFuture(long companyId, long customerId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    GetCustomerLastNoteResponse response =
                            appointmentNoteService.getCustomerLastNote(GetCustomerLastNoteRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .setCustomerId(customerId)
                                    .setType(AppointmentNoteType.ALERT_NOTES)
                                    .build());
                    return response.getNote();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, AppointmentNoteModel>> getCustomersLastAlertNoteFuture(
            long companyId, Collection<Long> customerIds) {
        return CompletableFuture.supplyAsync(
                () -> customerIds.stream()
                        .map(customerId -> {
                            GetCustomerLastNoteResponse response =
                                    appointmentNoteService.getCustomerLastNote(GetCustomerLastNoteRequest.newBuilder()
                                            .setCompanyId(companyId)
                                            .setCustomerId(customerId)
                                            .setType(AppointmentNoteType.ALERT_NOTES)
                                            .build());
                            return response.getNote();
                        })
                        .collect(Collectors.toMap(
                                AppointmentNoteModel::getCustomerId, Function.identity(), (a, b) -> b)),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BusinessCustomerModel>> getBusinessCustomerMap(
            long companyId, Collection<Long> customerIdList) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(customerIdList)) {
                        return Map.of();
                    }
                    BatchGetCustomerResponse customerResponse =
                            businessCustomerService.batchGetCustomer(BatchGetCustomerRequest.newBuilder()
                                    .setTenant(Tenant.newBuilder()
                                            .setCompanyId(companyId)
                                            .build())
                                    .addAllIds(customerIdList)
                                    .build());
                    return customerResponse.getCustomersList().stream()
                            .collect(Collectors.toMap(BusinessCustomerModel::getId, Function.identity()));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<BusinessCustomerContactModel>> getCustomerContact(long companyId, long customerId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    var customerResponse =
                            businessCustomerContactService.listCustomerContact(ListCustomerContactRequest.newBuilder()
                                    .setTenant(Tenant.newBuilder()
                                            .setCompanyId(companyId)
                                            .build())
                                    .setCustomerId(customerId)
                                    .build());
                    return customerResponse.getContactsList();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>>>
            getLodgingMap(List<Long> lodgingIdList) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(lodgingIdList)) {
                        return new AbstractMap.SimpleEntry<>(Map.of(), Map.of());
                    }
                    MGetLodgingUnitResponse lodgingUnitResponse =
                            lodgingUnitService.mGetLodgingUnit(MGetLodgingUnitRequest.newBuilder()
                                    .addAllIdList(lodgingIdList)
                                    .build());
                    Map<Long, LodgingUnitModel> lodgingUnitMap = lodgingUnitResponse.getLodgingUnitListList().stream()
                            .collect(Collectors.toMap(LodgingUnitModel::getId, Function.identity()));
                    Map<Long, LodgingTypeModel> lodgingTypeMap = lodgingUnitResponse.getLodgingTypeListList().stream()
                            .collect(Collectors.toMap(LodgingTypeModel::getId, Function.identity()));
                    return new AbstractMap.SimpleEntry<>(lodgingUnitMap, lodgingTypeMap);
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Boolean> getIsNewCustomer(long companyId, long customerId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    GetCustomerLastAppointmentResponse customerLastAppointmentResponse =
                            appointmentServiceBlockingStub.getCustomerLastAppointment(
                                    GetCustomerLastAppointmentRequest.newBuilder()
                                            .setCompanyId(companyId)
                                            .addCustomerId(customerId)
                                            .setStatus(AppointmentStatus.FINISHED)
                                            .build());
                    return CollectionUtils.isEmpty(customerLastAppointmentResponse.getCustomerLastAppointmentMap())
                            || Objects.isNull(customerLastAppointmentResponse
                                    .getCustomerLastAppointmentMap()
                                    .get(customerId));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<Long>> getNewCustomerIdList(long companyId, Collection<Long> customerIdList) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(customerIdList)) {
                        return List.of();
                    }
                    GetCustomerLastAppointmentResponse customerLastAppointmentResponse =
                            appointmentServiceBlockingStub.getCustomerLastAppointment(
                                    GetCustomerLastAppointmentRequest.newBuilder()
                                            .setCompanyId(companyId)
                                            .addAllCustomerId(customerIdList)
                                            .setStatus(AppointmentStatus.FINISHED)
                                            .build());
                    return customerIdList.stream()
                            .filter(customerId -> CollectionUtils.isEmpty(
                                            customerLastAppointmentResponse.getCustomerLastAppointmentMap())
                                    || Objects.isNull(customerLastAppointmentResponse
                                            .getCustomerLastAppointmentMap()
                                            .get(customerId)))
                            .toList();
                },
                ThreadPool.getSubmitExecutor());
    }

    public PayBookingRequestView getPrePayCalendarView(OrderModel order, @Nullable BookOnlineDepositDTO deposit) {
        PayBookingRequestView.Builder builder = PayBookingRequestView.newBuilder();
        builder.setPaidAmount(order.getPaidAmount()).setRefundAmount(order.getRefundedAmount());

        if (Objects.nonNull(deposit)) {
            if (DepositPaymentTypeEnum.PrePay.equals(deposit.getDepositType())) {
                builder.setPrePayAmount(deposit.getAmount().doubleValue())
                        .setPrePayStatus(deposit.getStatus())
                        .setPreAuthEnable(false);
                // Deposit order 时算出来的总是 100%，并且 deposit rules 是 by item 算的，这里展示比例的意义不大。
                if (!OrderModel.OrderType.DEPOSIT.equals(order.getOrderType())) {
                    builder.setPrePayRate(PrePayConverter.INSTANCE.getPrePayRate(deposit, order.getTotalAmount()));
                }
            } else {
                builder.setPreAuthEnable(true);
            }
        }
        return builder.build();
    }

    public CompletableFuture<Map<Long, PayBookingRequestView>> getPrePayCalendarViews(
            Long businessId, Collection<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> appointmentIds.stream()
                        .filter(CommonUtil::isNormal)
                        .collect(Collectors.toMap(Function.identity(), appointmentId -> {
                            // invoice
                            OrderModel order = getOrderForOBAppointment(appointmentId);
                            if (!isNormal(order.getId())) {
                                return PayBookingRequestView.getDefaultInstance();
                            }

                            // deposit
                            BookOnlineDepositDTO obDepositByGroomingId =
                                    bookOnlineDepositClient.getOBDepositByGroomingId(
                                            Math.toIntExact(businessId), Math.toIntExact(appointmentId));

                            // pre pay
                            return getPrePayCalendarView(order, obDepositByGroomingId);
                        })),
                ThreadPool.getSubmitExecutor());
    }

    /**
     * Get the order by OB's appointment id.
     * For new order flow, the order can only be a DEPOSIT order.
     * For old order flow, the order should be an ORIGIN order.
     */
    private OrderModel getOrderForOBAppointment(long appointmentId) {
        var orders = orderService
                .getOrderList(GetOrderListRequest.newBuilder()
                        .setSourceType(OrderSourceType.APPOINTMENT.name().toLowerCase())
                        .addSourceIds(appointmentId)
                        // Include all types of orders (extra, deposit, etc.).
                        .setIncludeExtraOrder(true)
                        .build())
                .getOrderListList();
        return orders.stream()
                .filter(it -> it.getOrder().getStatus() != OrderStatus.REMOVED_VALUE)
                .min(Comparator.comparing(a -> a.getOrder().getId()))
                .map(OrderDetailModel::getOrder)
                .orElse(OrderModel.getDefaultInstance());
    }

    /**
     * Get prepay calendar views for BD booking requests.
     *
     * @param businessId businessId
     * @param bookingRequestIds bookingRequestIds
     * @return key: bookingRequestId
     */
    public CompletableFuture<Map<Long, PayBookingRequestView>> getBDPrePayCalendarViews(
            Long businessId, Collection<Long> bookingRequestIds) {
        return CompletableFuture.supplyAsync(
                () -> bookingRequestIds.stream()
                        .filter(CommonUtil::isNormal)
                        .collect(Collectors.toMap(Function.identity(), bookingRequestId -> {
                            // invoice
                            OrderModel order = getOrderForBookingRequest(bookingRequestId);
                            if (!isNormal(order.getId())) {
                                return PayBookingRequestView.getDefaultInstance();
                            }

                            // deposit
                            BookOnlineDepositDTO obDepositByGroomingId =
                                    bookOnlineDepositClient.getOBDepositByBookingRequestId(
                                            Math.toIntExact(businessId), bookingRequestId);

                            // pre pay
                            return getPrePayCalendarView(order, obDepositByGroomingId);
                        })),
                ThreadPool.getSubmitExecutor());
    }

    /**
     * Get the order by booking request id.
     * For new order flow, the order can only be a DEPOSIT order.
     * For old order flow, the order should be an ORIGIN order.
     */
    private OrderModel getOrderForBookingRequest(long bookingRequestId) {
        var orders = orderService
                .getOrderList(GetOrderListRequest.newBuilder()
                        .setSourceType(OrderSourceType.BOOKING_REQUEST.name().toLowerCase())
                        .addSourceIds(bookingRequestId)
                        // Include all types of orders (extra, deposit, etc.).
                        .setIncludeExtraOrder(true)
                        .build())
                .getOrderListList();
        return orders.stream()
                .filter(it -> it.getOrder().getStatus() != OrderStatus.REMOVED_VALUE)
                .min(Comparator.comparing(a -> a.getOrder().getId()))
                .map(OrderDetailModel::getOrder)
                .orElse(OrderModel.getDefaultInstance());
    }

    public CompletableFuture<OrderBookingRequestView> getOrderCalendarView(
            Long businessId, Long companyId, Long appointmentId, Long bookingRequestId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    OrderDetailModel orderDetail = orderService.getOrderDetail(GetOrderRequest.newBuilder()
                            .setBusinessId(businessId)
                            .setSourceId(appointmentId)
                            .setSourceType(OrderSourceType.APPOINTMENT.name().toLowerCase())
                            .build());
                    if (!orderDetail.hasOrder()) {
                        orderDetail = orderService.getOrderDetail(GetOrderRequest.newBuilder()
                                .setBusinessId(businessId)
                                .setSourceId(bookingRequestId)
                                .setSourceType(
                                        OrderSourceType.BOOKING_REQUEST.name().toLowerCase())
                                .build());
                    }
                    if (!orderDetail.hasOrder()) {
                        // V4 开始，不一定有 Order.
                        return OrderBookingRequestView.getDefaultInstance();
                    }

                    if (CollectionUtils.isEmpty(orderDetail.getLineDiscountsList())) {
                        return OrderBookingRequestView.getDefaultInstance();
                    }

                    List<Long> discountCodeIdList = orderDetail.getLineDiscountsList().stream()
                            .map(OrderLineDiscountModel::getDiscountCodeId)
                            .filter(id -> id > 0)
                            .distinct()
                            .toList();
                    if (CollectionUtils.isEmpty(discountCodeIdList) || discountCodeIdList.size() > 1) {
                        return OrderBookingRequestView.getDefaultInstance();
                    }

                    GetDiscountCodeOutput discountCodeOutput;
                    try {
                        discountCodeOutput = discountCodeClient.getDiscountCode(GetDiscountCodeInput.newBuilder()
                                .setCompanyId(companyId)
                                .setId(discountCodeIdList.get(0))
                                .build());
                    } catch (Exception e) {
                        log.error("get discount code error", e);
                        return OrderBookingRequestView.getDefaultInstance();
                    }

                    return OrderBookingRequestView.newBuilder()
                            .setDiscountCodeName(
                                    discountCodeOutput.getDiscountCodeModel().getDiscountCode())
                            .build();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<ServiceItemType>> getServiceTypeFuture(BookingRequestModel bookingRequestModel) {
        return CompletableFuture.supplyAsync(
                () -> bookingRequestModel.getServicesList().stream()
                        .map(service -> switch (service.getServiceCase()) {
                            case BOARDING -> ServiceItemType.BOARDING;
                            case DAYCARE -> ServiceItemType.DAYCARE;
                            case GROOMING -> ServiceItemType.GROOMING;
                            case EVALUATION -> ServiceItemType.EVALUATION;
                            case DOG_WALKING -> ServiceItemType.DOG_WALKING;
                            case GROUP_CLASS -> ServiceItemType.GROUP_CLASS;
                            default -> null;
                        })
                        .filter(Objects::nonNull)
                        .distinct()
                        .toList(),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<GroomingAutoAssignDetail> getGroomingAutoAssign(Long appointmentId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (!isNormal(appointmentId)) {
                        return GroomingAutoAssignDetail.getDefaultInstance();
                    }
                    AutoAssignDTO autoAssign = autoAssignService.getAutoAssign(Math.toIntExact(appointmentId));
                    if (autoAssign == null
                            || (Objects.isNull(autoAssign.getStaffId())
                                    && Objects.isNull(autoAssign.getAppointmentTime()))) {
                        return GroomingAutoAssignDetail.getDefaultInstance();
                    }
                    return AutoAssignConverter.INSTANCE.toGroomingAutoAssignDetail(autoAssign);
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, GroomingAutoAssignDetail>> getGroomingAutoAssigns(List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> autoAssignService
                        .listAutoAssign(
                                appointmentIds.stream().map(Math::toIntExact).toList())
                        .stream()
                        .filter(autoAssign -> Objects.nonNull(autoAssign.getStaffId())
                                || Objects.nonNull(autoAssign.getAppointmentTime()))
                        .collect(Collectors.toMap(
                                obj -> obj.getAppointmentId().longValue(),
                                AutoAssignConverter.INSTANCE::toGroomingAutoAssignDetail)),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<AddressDetail> getAddressDetail(Long customerId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    ProfileRequestAddressDTO cPrimaryAddress = profileRequestAddressApi
                            .listByCustomerIds(List.of(Math.toIntExact(customerId)))
                            .getOrDefault(Math.toIntExact(customerId), List.of())
                            .stream()
                            .filter(ProfileRequestAddressDTO::getIsPrimary)
                            .findFirst()
                            .orElse(null);
                    if (cPrimaryAddress != null) {
                        return ClientConverter.INSTANCE.toAddressDetail(cPrimaryAddress);
                    }
                    var request = GetCustomerPrimaryAddressRequest.newBuilder()
                            .setCustomerId(customerId)
                            .build();
                    var response = businessCustomerAddressService.getCustomerPrimaryAddress(request);
                    if (response.hasAddress()) {
                        return ClientConverter.INSTANCE.toAddressDetail(response.getAddress());
                    }
                    return null;
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, Boolean>> getOutOfServiceArea(Long businessId, List<Long> customerIds) {
        return CompletableFuture.supplyAsync(
                () -> customerIds.stream().distinct().collect(Collectors.toMap(Function.identity(), customerId -> {
                    AddressDetail addressDetail = getAddressDetail(customerId).join();
                    if (Objects.isNull(addressDetail)) {
                        return false;
                    }
                    return isOutOfArea(
                                    businessId,
                                    addressDetail.getLat(),
                                    addressDetail.getLng(),
                                    addressDetail.getZipcode())
                            .join();
                })));
    }

    private CompletableFuture<Boolean> isOutOfArea(Long businessId, String lat, String lng, String zipcode) {
        return CompletableFuture.supplyAsync(
                () -> {
                    ServiceAreaParams.ClientAddressParams clientAddressParams =
                            new ServiceAreaParams.ClientAddressParams();
                    clientAddressParams.setLat(lat);
                    clientAddressParams.setLng(lng);
                    clientAddressParams.setZipcode(zipcode);
                    clientAddressParams.setAddressId(0);
                    ServiceAreaParams serviceAreaParams = new ServiceAreaParams();
                    serviceAreaParams.setBusinessId(Math.toIntExact(businessId));
                    serviceAreaParams.setAddressParamsList(Collections.singletonList(clientAddressParams));
                    List<ServiceAreaResultDTO> resultList =
                            groomingOnlineBookingClient.getServiceAreaResultList(serviceAreaParams);
                    return resultList.stream()
                            .findFirst()
                            .orElse(new ServiceAreaResultDTO())
                            .getOutOfArea();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<ServiceItemType>>> getServiceTypeFuture(
            List<BookingRequestModel> bookingRequestModels) {
        return CompletableFuture.supplyAsync(
                () -> bookingRequestModels.stream()
                        .collect(Collectors.toMap(
                                BookingRequestModel::getId,
                                bookingRequestModel -> bookingRequestModel.getServicesList().stream()
                                        .map(service -> switch (service.getServiceCase()) {
                                            case BOARDING -> ServiceItemType.BOARDING;
                                            case DAYCARE -> ServiceItemType.DAYCARE;
                                            case GROOMING -> ServiceItemType.GROOMING;
                                            case EVALUATION -> ServiceItemType.EVALUATION;
                                            case DOG_WALKING -> ServiceItemType.DOG_WALKING;
                                            case GROUP_CLASS -> ServiceItemType.GROUP_CLASS;
                                            default -> null;
                                        })
                                        .filter(Objects::nonNull)
                                        .distinct()
                                        .toList())),
                ThreadPool.getSubmitExecutor());
    }
}
