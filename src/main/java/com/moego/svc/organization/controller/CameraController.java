package com.moego.svc.organization.controller;

import com.moego.idl.service.organization.v1.CameraServiceGrpc;
import com.moego.idl.service.organization.v1.GetCameraListRequest;
import com.moego.idl.service.organization.v1.GetCameraListResponse;
import com.moego.idl.service.organization.v1.UpdateCameraRequest;
import com.moego.idl.service.organization.v1.UpdateCameraResponse;
import com.moego.svc.organization.converter.CameraConvert;
import com.moego.svc.organization.entity.Camera;
import com.moego.svc.organization.service.CameraService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;

@Controller
@RequiredArgsConstructor
public class CameraController extends CameraServiceGrpc.CameraServiceImplBase {

    private final CameraService cameraService;
    private final CameraConvert cameraConvert;

    @Override
    public void getCameraList(GetCameraListRequest request, StreamObserver<GetCameraListResponse> responseObserver) {
        var filter = cameraConvert.toFilter(request);
        List<Camera> cameraList;
        if (filter.getCameraFilter() != null) {
            cameraList =
                    cameraService.getCameraListByFilter(request.getTenant().getCompanyId(), filter.getCameraFilter());
        } else {
            cameraList = cameraService.getCameraListByFilter(request.getTenant().getCompanyId(), filter);
        }
        responseObserver.onNext(GetCameraListResponse.newBuilder()
                .addAllCameras(cameraConvert.toModels(cameraList))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateCamera(UpdateCameraRequest request, StreamObserver<UpdateCameraResponse> responseObserver) {
        cameraService.updateCamera(
                request.getTenant().getCompanyId(),
                Camera.builder()
                        .id(request.getCameraId())
                        .isActive(request.hasIsActive() ? request.getIsActive() : null)
                        .visibilityType(
                                request.hasVisibilityType()
                                        ? request.getVisibilityType().getNumber()
                                        : null)
                        .build());
        responseObserver.onNext(UpdateCameraResponse.newBuilder().build());
        responseObserver.onCompleted();
    }
}
