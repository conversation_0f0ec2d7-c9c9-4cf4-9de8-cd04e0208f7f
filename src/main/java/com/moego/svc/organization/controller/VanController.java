package com.moego.svc.organization.controller;

import com.moego.idl.models.organization.v1.VanListModel;
import com.moego.idl.models.organization.v1.VanModel;
import com.moego.idl.service.organization.v1.ForceAssignStaffToVanRequest;
import com.moego.idl.service.organization.v1.ForceAssignStaffToVanResponse;
import com.moego.idl.service.organization.v1.GetAssignedVanForStaffIdRequest;
import com.moego.idl.service.organization.v1.GetAssignedVanForStaffIdResponse;
import com.moego.idl.service.organization.v1.GetVanListByMultiCompanyIdRequest;
import com.moego.idl.service.organization.v1.GetVanListByMultiCompanyIdResponse;
import com.moego.idl.service.organization.v1.GetVanListByStaffIdsRequest;
import com.moego.idl.service.organization.v1.GetVanListByStaffIdsResponse;
import com.moego.idl.service.organization.v1.VanServiceGrpc;
import com.moego.svc.organization.converter.VanConvert;
import com.moego.svc.organization.service.VanService;
import io.grpc.stub.StreamObserver;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
@RequiredArgsConstructor
public class VanController extends VanServiceGrpc.VanServiceImplBase {
    @Autowired
    private VanService vanService;

    @Override
    public void getVanList(
            com.moego.idl.service.organization.v1.GetVanListRequest request,
            io.grpc.stub.StreamObserver<com.moego.idl.service.organization.v1.GetVanListResponse> responseObserver) {}

    @Override
    public void getVanListByStaffIds(
            GetVanListByStaffIdsRequest request, StreamObserver<GetVanListByStaffIdsResponse> responseObserver) {
        var vanMap = vanService.getVanMapByStaffIds(
                request.getCompanyId(),
                request.getStaffIdsList().stream().map(Long::intValue).collect(Collectors.toList()));
        Map<Long, VanModel> staffVanMap = new HashMap<>();
        vanMap.forEach((s, v) -> {
            staffVanMap.put(s.longValue(), VanConvert.INSTANCE.toVanModel(v));
        });
        var response = GetVanListByStaffIdsResponse.newBuilder()
                .putAllStaffVanMap(staffVanMap)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void getVanListByMultiCompanyId(
            GetVanListByMultiCompanyIdRequest request,
            StreamObserver<GetVanListByMultiCompanyIdResponse> responseObserver) {
        var vanListMapByCid = vanService.getVanListByMultiCompanyId(request.getCompanyIdsList());
        var builder = GetVanListByMultiCompanyIdResponse.newBuilder();
        vanListMapByCid.forEach((key, value) -> builder.putCompanyVanListMap(
                key,
                VanListModel.newBuilder()
                        .addAllVans(value.stream()
                                .map(VanConvert.INSTANCE::toVanModel)
                                .collect(Collectors.toList()))
                        .build()));
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAssignedVanForStaffId(
            GetAssignedVanForStaffIdRequest request,
            StreamObserver<GetAssignedVanForStaffIdResponse> responseObserver) {
        var staffVanMap = vanService.getAssignedVanForStaffId(request.getStaffIdsList());
        responseObserver.onNext(GetAssignedVanForStaffIdResponse.newBuilder()
                .putAllStaffVanMap(staffVanMap)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void forceAssignStaffToVan(
            ForceAssignStaffToVanRequest request, StreamObserver<ForceAssignStaffToVanResponse> responseObserver) {
        vanService.forceAssignStaffToVan(request.getStaffId(), request.getVanId());
        responseObserver.onNext(ForceAssignStaffToVanResponse.newBuilder().build());
        responseObserver.onCompleted();
    }
}
