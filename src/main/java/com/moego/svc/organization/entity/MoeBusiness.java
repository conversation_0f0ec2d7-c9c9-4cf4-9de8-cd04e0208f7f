package com.moego.svc.organization.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeBusiness {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.company_id
     *
     * @mbg.generated
     */
    private Integer companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.business_name
     *
     * @mbg.generated
     */
    private String businessName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.phone_number
     *
     * @mbg.generated
     */
    private String phoneNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.avatar_path
     *
     * @mbg.generated
     */
    private String avatarPath;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.website
     *
     * @mbg.generated
     */
    private String website;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.address
     *
     * @mbg.generated
     */
    private String address;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.address1
     *
     * @mbg.generated
     */
    private String address1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.address2
     *
     * @mbg.generated
     */
    private String address2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.address_city
     *
     * @mbg.generated
     */
    private String addressCity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.address_state
     *
     * @mbg.generated
     */
    private String addressState;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.address_zipcode
     *
     * @mbg.generated
     */
    private String addressZipcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.address_country
     *
     * @mbg.generated
     */
    private String addressCountry;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.address_lat
     *
     * @mbg.generated
     */
    private String addressLat;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.address_lng
     *
     * @mbg.generated
     */
    private String addressLng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.country
     *
     * @mbg.generated
     */
    private String country;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.country_alpha2_code
     *
     * @mbg.generated
     */
    private String countryAlpha2Code;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.country_code
     *
     * @mbg.generated
     */
    private String countryCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.currency_symbol
     *
     * @mbg.generated
     */
    private String currencySymbol;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.currency_code
     *
     * @mbg.generated
     */
    private String currencyCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.time_format_type
     *
     * @mbg.generated
     */
    private Byte timeFormatType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.unit_of_weight_type
     *
     * @mbg.generated
     */
    private Byte unitOfWeightType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.unit_of_distance_type
     *
     * @mbg.generated
     */
    private Byte unitOfDistanceType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.timezone_name
     *
     * @mbg.generated
     */
    private String timezoneName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.timezone_seconds
     *
     * @mbg.generated
     */
    private Integer timezoneSeconds;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.date_format_type
     *
     * @mbg.generated
     */
    private Byte dateFormatType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.calendar_format_type
     *
     * @mbg.generated
     */
    private Byte calendarFormatType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.number_format_type
     *
     * @mbg.generated
     */
    private Byte numberFormatType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.book_online_name
     *
     * @mbg.generated
     */
    private String bookOnlineName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.app_type
     *
     * @mbg.generated
     */
    private Byte appType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.primary_pay_type
     *
     * @mbg.generated
     */
    private Byte primaryPayType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.source
     *
     * @mbg.generated
     */
    private Byte source;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.clock_in_out_enable
     *
     * @mbg.generated
     */
    private Byte clockInOutEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.clock_in_out_notify
     *
     * @mbg.generated
     */
    private Byte clockInOutNotify;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.is_enable_access_code
     *
     * @mbg.generated
     */
    private Byte isEnableAccessCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.smart_schedule_max_dist
     *
     * @mbg.generated
     */
    private Integer smartScheduleMaxDist;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.smart_schedule_max_time
     *
     * @mbg.generated
     */
    private Integer smartScheduleMaxTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.service_area_enable
     *
     * @mbg.generated
     */
    private Byte serviceAreaEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.facebook
     *
     * @mbg.generated
     */
    private String facebook;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.instagram
     *
     * @mbg.generated
     */
    private String instagram;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.google
     *
     * @mbg.generated
     */
    private String google;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.yelp
     *
     * @mbg.generated
     */
    private String yelp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.smart_schedule_start_lat
     *
     * @mbg.generated
     */
    private String smartScheduleStartLat;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.smart_schedule_start_lng
     *
     * @mbg.generated
     */
    private String smartScheduleStartLng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.smart_schedule_end_lat
     *
     * @mbg.generated
     */
    private String smartScheduleEndLat;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.smart_schedule_end_lng
     *
     * @mbg.generated
     */
    private String smartScheduleEndLng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.service_range_max_dist
     *
     * @mbg.generated
     */
    private Integer serviceRangeMaxDist;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.service_range_max_time
     *
     * @mbg.generated
     */
    private Integer serviceRangeMaxTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.smart_schedule_service_range
     *
     * @mbg.generated
     */
    private Integer smartScheduleServiceRange;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.smart_schedule_start_addr
     *
     * @mbg.generated
     */
    private String smartScheduleStartAddr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.smart_schedule_end_addr
     *
     * @mbg.generated
     */
    private String smartScheduleEndAddr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.service_range_center_addr
     *
     * @mbg.generated
     */
    private String serviceRangeCenterAddr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.service_range_center_lat
     *
     * @mbg.generated
     */
    private String serviceRangeCenterLat;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.service_range_center_lng
     *
     * @mbg.generated
     */
    private String serviceRangeCenterLng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.source_from
     *
     * @mbg.generated
     */
    private Byte sourceFrom;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.message_send_by
     *
     * @mbg.generated
     */
    private Byte messageSendBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.send_daily
     *
     * @mbg.generated
     */
    private Byte sendDaily;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.business_mode
     *
     * @mbg.generated
     */
    private Byte businessMode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.know_about_us
     *
     * @mbg.generated
     */
    private String knowAboutUs;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.appt_per_week
     *
     * @mbg.generated
     */
    private Byte apptPerWeek;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.business_years
     *
     * @mbg.generated
     */
    private Byte businessYears;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.move_from
     *
     * @mbg.generated
     */
    private Byte moveFrom;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.retail_enable
     *
     * @mbg.generated
     */
    private Byte retailEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.notification_sound_enable
     *
     * @mbg.generated
     */
    private Byte notificationSoundEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.contact_email
     *
     * @mbg.generated
     */
    private String contactEmail;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.invitation_code
     *
     * @mbg.generated
     */
    private String invitationCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.tiktok
     *
     * @mbg.generated
     */
    private String tiktok;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business.staff_availability_type
     *
     * @mbg.generated
     */
    private Integer staffAvailabilityType;
}
