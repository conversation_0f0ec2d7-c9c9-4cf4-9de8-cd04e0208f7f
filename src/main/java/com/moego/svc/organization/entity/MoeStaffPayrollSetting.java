package com.moego.svc.organization.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeStaffPayrollSetting {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.service_commission_enable
     *
     * @mbg.generated
     */
    private Boolean serviceCommissionEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.service_commission_type
     *
     * @mbg.generated
     */
    private Byte serviceCommissionType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.tier_type
     *
     * @mbg.generated
     */
    private Byte tierType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.service_pay_rate
     *
     * @mbg.generated
     */
    private BigDecimal servicePayRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.addon_pay_rate
     *
     * @mbg.generated
     */
    private BigDecimal addonPayRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.hourly_commission_enable
     *
     * @mbg.generated
     */
    private Boolean hourlyCommissionEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.hourly_pay
     *
     * @mbg.generated
     */
    private BigDecimal hourlyPay;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.tips_commission_enable
     *
     * @mbg.generated
     */
    private Boolean tipsCommissionEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.tips_pay_rate
     *
     * @mbg.generated
     */
    private BigDecimal tipsPayRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.create_time
     *
     * @mbg.generated
     */
    private LocalDateTime createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_payroll_setting.service_tier_rate_config
     *
     * @mbg.generated
     */
    private String serviceTierRateConfig;
}
