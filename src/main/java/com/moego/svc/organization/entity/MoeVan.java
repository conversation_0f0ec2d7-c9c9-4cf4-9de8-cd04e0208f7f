package com.moego.svc.organization.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeVan {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.nick_name
     *
     * @mbg.generated
     */
    private String nickName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.avatar_path
     *
     * @mbg.generated
     */
    private String avatarPath;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.date_in_service
     *
     * @mbg.generated
     */
    private String dateInService;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.license_plate
     *
     * @mbg.generated
     */
    private String licensePlate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.model
     *
     * @mbg.generated
     */
    private String model;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.make
     *
     * @mbg.generated
     */
    private String make;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.is_deleted
     *
     * @mbg.generated
     */
    private Byte isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van.note
     *
     * @mbg.generated
     */
    private String note;
}
