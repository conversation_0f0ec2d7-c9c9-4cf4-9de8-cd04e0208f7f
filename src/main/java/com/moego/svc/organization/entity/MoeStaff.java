package com.moego.svc.organization.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeStaff {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.account_id
     *
     * @mbg.generated
     */
    private Integer accountId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.role_id
     *
     * @mbg.generated
     */
    private Integer roleId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.avatar_path
     *
     * @mbg.generated
     */
    private String avatarPath;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.first_name
     *
     * @mbg.generated
     */
    private String firstName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.last_name
     *
     * @mbg.generated
     */
    private String lastName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.employee_category
     *
     * @mbg.generated
     */
    private Byte employeeCategory;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.phone_number
     *
     * @mbg.generated
     */
    private String phoneNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.hire_date
     *
     * @mbg.generated
     */
    private Long hireDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.fire_date
     *
     * @mbg.generated
     */
    private Long fireDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.allow_login
     *
     * @mbg.generated
     */
    private Byte allowLogin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.group_leader_id
     *
     * @mbg.generated
     */
    private Integer groupLeaderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.note
     *
     * @mbg.generated
     */
    private String note;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.inactive
     *
     * @mbg.generated
     */
    private Byte inactive;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.create_by_id
     *
     * @mbg.generated
     */
    private Integer createById;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.book_online_available
     *
     * @mbg.generated
     */
    private Byte bookOnlineAvailable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.show_on_calendar
     *
     * @mbg.generated
     */
    private Byte showOnCalendar;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.show_calendar_staff_all
     *
     * @mbg.generated
     */
    private Byte showCalendarStaffAll;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.access_code
     *
     * @mbg.generated
     */
    private String accessCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.token
     *
     * @mbg.generated
     */
    private String token;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.invite_code
     *
     * @mbg.generated
     */
    private String inviteCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.account_last_visited_at
     *
     * @mbg.generated
     */
    private Long accountLastVisitedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.account_sort
     *
     * @mbg.generated
     */
    private Integer accountSort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.company_id
     *
     * @mbg.generated
     */
    private Integer companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.enterprise_id
     *
     * @mbg.generated
     */
    private Integer enterpriseId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.working_in_all_locations
     *
     * @mbg.generated
     */
    private Boolean workingInAllLocations;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.color_code
     *
     * @mbg.generated
     */
    private String colorCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.last_visit_business_id
     *
     * @mbg.generated
     */
    private Integer lastVisitBusinessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.access_all_working_locations_staff
     *
     * @mbg.generated
     */
    private Byte accessAllWorkingLocationsStaff;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.profile_email
     *
     * @mbg.generated
     */
    private String profileEmail;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.require_access_code
     *
     * @mbg.generated
     */
    private Boolean requireAccessCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.is_shown_on_all_calendar
     *
     * @mbg.generated
     */
    private Boolean isShownOnAllCalendar;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff.source
     *
     * @mbg.generated
     */
    private Integer source;
}
