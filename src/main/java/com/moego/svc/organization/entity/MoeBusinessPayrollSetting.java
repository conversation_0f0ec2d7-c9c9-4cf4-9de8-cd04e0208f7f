package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeBusinessPayrollSetting {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.split_tips_method
     *
     * @mbg.generated
     */
    private Byte splitTipsMethod;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.new_payroll_enable
     *
     * @mbg.generated
     */
    private Boolean newPayrollEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.service_commission_based
     *
     * @mbg.generated
     */
    private Byte serviceCommissionBased;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.create_time
     *
     * @mbg.generated
     */
    private LocalDateTime createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.company_id
     *
     * @mbg.generated
     */
    private Long companyId;
}
