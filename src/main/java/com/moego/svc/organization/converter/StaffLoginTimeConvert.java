package com.moego.svc.organization.converter;

import com.google.type.TimeOfDay;
import com.moego.idl.models.organization.v1.StaffLoginLimitType;
import com.moego.idl.models.organization.v1.StaffLoginTimeDef;
import com.moego.idl.models.organization.v1.StaffLoginTimeModel;
import com.moego.idl.utils.v1.TimeOfDayInterval;
import com.moego.svc.organization.dto.staff.MoeStaffLoginTimeDTO;
import com.moego.svc.organization.entity.MoeStaffLoginTime;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {TimeConverter.class, StructConverter.class})
public interface StaffLoginTimeConvert {
    StaffLoginTimeConvert INSTANCE = Mappers.getMapper(StaffLoginTimeConvert.class);

    @Mapping(target = "timeRange", expression = "java(toTimeOfDayInterval(in.getStartTime(),in.getEndTime()))")
    StaffLoginTimeModel toStaffLoginTimeModel(MoeStaffLoginTimeDTO in);

    @Mapping(target = "startTime", expression = "java(fromTimeOfDay(in.getTimeRange().getStart()))")
    @Mapping(target = "endTime", expression = "java(fromTimeOfDay(in.getTimeRange().getEnd()))")
    MoeStaffLoginTime toMoeStaffLoginTime(StaffLoginTimeDef in);

    @Mapping(target = "timeRange", expression = "java(toTimeOfDayInterval(in.getStartTime(),in.getEndTime()))")
    StaffLoginTimeDef toStaffLoginTimeDef(MoeStaffLoginTimeDTO in);

    MoeStaffLoginTimeDTO toDTO(MoeStaffLoginTime in);

    default StaffLoginLimitType toStaffLoginLimitType(Byte in) {
        return StaffLoginLimitType.forNumber(in);
    }

    default Byte fromStaffLoginLimitType(StaffLoginLimitType in) {
        return (byte) in.getNumber();
    }

    default TimeOfDay toTimeOfDay(Long minute) {
        return TimeOfDay.newBuilder()
                .setHours((int) (minute / 60))
                .setMinutes((int) (minute % 60))
                .build();
    }

    default TimeOfDayInterval toTimeOfDayInterval(Long startMinute, Long endMinute) {
        return TimeOfDayInterval.newBuilder()
                .setStart(toTimeOfDay(startMinute))
                .setEnd(toTimeOfDay(endMinute))
                .build();
    }

    default Long fromTimeOfDay(TimeOfDay t) {
        return t.getHours() * 60L + t.getMinutes();
    }
}
