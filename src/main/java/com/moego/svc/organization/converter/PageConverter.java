/*
 * @since 2023-05-29 15:23:19
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.organization.converter;

import com.moego.common.utils.Pagination;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.svc.organization.utils.PageInfo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface PageConverter {
    default PageInfo toPageInfo(PaginationRequest request) {
        var pageSize = request.hasPageSize() ? request.getPageSize() : 20;
        var pageNum = request.hasPageNum() ? request.getPageNum() : 1;
        return new PageInfo(pageNum, pageSize, 0);
    }

    default PageInfo toPageInfo(PaginationResponse response) {
        var pageSize = response.getPageSize();
        var pageNum = response.getPageNum();
        return new PageInfo(pageNum < 1 ? 1 : 0, pageSize, 0);
    }

    PaginationRequest toRequest(PageInfo pageInfo);

    PaginationResponse toResponse(PageInfo pageInfo);

    PaginationResponse toResponse(Pagination pagination);

    Pagination toPagination(PaginationRequest request);
}
