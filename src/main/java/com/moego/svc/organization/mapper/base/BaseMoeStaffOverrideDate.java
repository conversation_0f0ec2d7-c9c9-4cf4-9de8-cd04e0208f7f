package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeStaffOverrideDate;
import com.moego.svc.organization.entity.MoeStaffOverrideDateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeStaffOverrideDate {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    long countByExample(MoeStaffOverrideDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int deleteByExample(MoeStaffOverrideDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int insert(MoeStaffOverrideDate row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int insertSelective(MoeStaffOverrideDate row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    List<MoeStaffOverrideDate> selectByExampleWithBLOBs(MoeStaffOverrideDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    List<MoeStaffOverrideDate> selectByExample(MoeStaffOverrideDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    MoeStaffOverrideDate selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeStaffOverrideDate row, @Param("example") MoeStaffOverrideDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("row") MoeStaffOverrideDate row, @Param("example") MoeStaffOverrideDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoeStaffOverrideDate row, @Param("example") MoeStaffOverrideDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeStaffOverrideDate row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeStaffOverrideDate row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeStaffOverrideDate row);
}
