package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeStaffAccess;
import com.moego.svc.organization.entity.MoeStaffAccessExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeStaffAccessMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    long countByExample(MoeStaffAccessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int deleteByExample(MoeStaffAccessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int insert(MoeStaffAccess record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int insertSelective(MoeStaffAccess record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    List<MoeStaffAccess> selectByExampleWithBLOBs(MoeStaffAccessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    List<MoeStaffAccess> selectByExample(MoeStaffAccessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    MoeStaffAccess selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeStaffAccess record, @Param("example") MoeStaffAccessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("record") MoeStaffAccess record, @Param("example") MoeStaffAccessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MoeStaffAccess record, @Param("example") MoeStaffAccessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeStaffAccess record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeStaffAccess record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeStaffAccess record);
}
