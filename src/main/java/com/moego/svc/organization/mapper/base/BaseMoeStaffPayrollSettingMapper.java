package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeStaffPayrollSetting;
import com.moego.svc.organization.entity.MoeStaffPayrollSettingExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeStaffPayrollSettingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    long countByExample(MoeStaffPayrollSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    int deleteByExample(MoeStaffPayrollSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    int insert(MoeStaffPayrollSetting row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    int insertSelective(MoeStaffPayrollSetting row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    List<MoeStaffPayrollSetting> selectByExampleWithBLOBs(MoeStaffPayrollSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    List<MoeStaffPayrollSetting> selectByExample(MoeStaffPayrollSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    MoeStaffPayrollSetting selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeStaffPayrollSetting row, @Param("example") MoeStaffPayrollSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("row") MoeStaffPayrollSetting row, @Param("example") MoeStaffPayrollSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("row") MoeStaffPayrollSetting row, @Param("example") MoeStaffPayrollSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeStaffPayrollSetting row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeStaffPayrollSetting row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeStaffPayrollSetting row);
}
