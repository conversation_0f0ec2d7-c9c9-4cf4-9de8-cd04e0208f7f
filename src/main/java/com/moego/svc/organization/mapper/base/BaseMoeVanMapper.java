package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeVan;
import com.moego.svc.organization.entity.MoeVanExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeVanMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    long countByExample(MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int deleteByExample(MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int insert(<PERSON><PERSON><PERSON> row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int insertSelective(<PERSON><PERSON><PERSON> row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    List<MoeVan> selectByExampleWithBLOBs(MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    List<MoeVan> selectByExample(MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    MoeVan selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") MoeVan row, @Param("example") MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("row") MoeVan row, @Param("example") MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoeVan row, @Param("example") MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeVan row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeVan row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeVan row);
}
