package com.moego.svc.organization.service;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.PaymentMethodStatus;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.organization.entity.MoeBusinessPaymentMethod;
import com.moego.svc.organization.entity.MoeBusinessPaymentMethodExample;
import com.moego.svc.organization.mapper.MoeBusinessPaymentMethodMapper;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PaymentMethodService {

    private final MoeBusinessPaymentMethodMapper businessPaymentMethodMapper;

    public Map<Long, List<MoeBusinessPaymentMethod>> getPaymentMethodsByBusinessIds(
            Long companyId, List<Long> businessIds) {
        if (companyId == null && businessIds.isEmpty()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id and business ids is empty");
        }
        MoeBusinessPaymentMethodExample example = new MoeBusinessPaymentMethodExample();
        var criteria = example.createCriteria()
                .andStatusEqualTo((byte) PaymentMethodStatus.PAYMENT_METHOD_STATUS_NORMAL.getNumber());
        if (companyId != null) {
            criteria.andCompanyIdEqualTo(companyId);
        }
        if (!businessIds.isEmpty()) {
            criteria.andBusinessIdIn(businessIds.stream().map(Long::intValue).toList());
        }

        return businessPaymentMethodMapper.selectByExample(example).stream()
                .collect(Collectors.groupingBy(
                        paymentMethod -> paymentMethod.getBusinessId().longValue()));
    }
}
