package com.moego.svc.organization.service;

import com.google.common.collect.ImmutableMap;
import com.moego.common.constant.CommonConstant;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.WeekUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.dto.BusinessWorkingHourDayDetailDTO;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.params.MoeBookOnlineStaffTimeParams;
import com.moego.svc.organization.entity.MoeBusiness;
import com.moego.svc.organization.entity.MoeStaffWorkingHour;
import com.moego.svc.organization.entity.MoeStaffWorkingHourExample;
import com.moego.svc.organization.mapper.MoeStaffWorkingHourMapper;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class StaffWorkingHourService {

    private final MoeStaffWorkingHourMapper staffWorkingHourMapper;
    private final BusinessService businessService;
    private final IGroomingOnlineBookingClient groomingOnlineBookingClient;
    private final StaffAvailabilityService staffAvailabilityService;

    // todo 待移到常量类
    public static final byte NORMAL_SCHEDULE_TYPE = 1;

    public static final byte WEEK_PERIOD = 7;

    public static final String EMPTY_JSON = "{}";

    public static final String ENDLESS_DATE = "9999-12-31";

    public List<MoeStaffWorkingHour> getStaffWorkingHourListByCompanyId(Long companyId) {
        MoeStaffWorkingHourExample example = new MoeStaffWorkingHourExample();
        example.createCriteria().andCompanyIdEqualTo(companyId);
        return staffWorkingHourMapper.selectByExampleWithBLOBs(example);
    }

    public void initStaffWorkingHour(Long companyId, List<Long> businessIds, Long staffId) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return;
        }
        businessIds.forEach(businessId -> initStaffWorkingHourForOneLocation(companyId, businessId, staffId));
    }

    private void initStaffWorkingHourForOneLocation(Long companyId, Long businessId, Long staffId) {
        MoeStaffWorkingHourExample example = new MoeStaffWorkingHourExample();
        example.createCriteria().andBusinessIdEqualTo(businessId.intValue()).andStaffIdEqualTo(staffId.intValue());
        List<MoeStaffWorkingHour> staffWorkingHours = staffWorkingHourMapper.selectByExampleWithBLOBs(example);
        // 如果已经初始化过，则不再初始化
        if (!CollectionUtils.isEmpty(staffWorkingHours)) {
            return;
        }

        // 获取商家的 working hour
        String businessWorkingHour = businessService.getWorkingHours(businessId);
        MoeBusiness business = businessService.getLocationDetail(businessId);

        // 构造初始化数据
        MoeStaffWorkingHour workingHour = MoeStaffWorkingHour.builder()
                .companyId(companyId)
                .businessId(businessId.intValue())
                .staffId(staffId.intValue())
                .scheduleType(NORMAL_SCHEDULE_TYPE)
                // business 的 create time 作为 startDate
                .startDate(DateUtil.getStringDate(business.getCreateTime()))
                .endDate(ENDLESS_DATE)
                .firstWeek(businessWorkingHour)
                .secondWeek(businessWorkingHour)
                .thirdWeek(businessWorkingHour)
                .forthWeek(businessWorkingHour)
                .build();

        staffWorkingHourMapper.insertSelective(workingHour);

        staffAvailabilityService.initTimeStaffAvailabilityByTimeAndBySlot(companyId, businessId, List.of(staffId));

        // 保存OB Staff Time
        ThreadPool.execute(() -> {
            MoeBookOnlineStaffTimeParams obStaffTime = new MoeBookOnlineStaffTimeParams();
            obStaffTime.setStaffId(staffId.intValue());
            // 根据每天的配置时间，设置staffTime
            BusinessWorkingHourDayDetailDTO timeData =
                    JsonUtil.toBean(businessWorkingHour, BusinessWorkingHourDayDetailDTO.class);
            Map<String, StaffTime> resultStaffTime = getStaffTimeMap(timeData);
            obStaffTime.setStaffTimes(JsonUtil.toJson(resultStaffTime));
            groomingOnlineBookingClient.saveAvailableStaffTime(businessId.intValue(), companyId, obStaffTime);

            MoeStaffDto staffDto = new MoeStaffDto();
            staffDto.setBusinessId(businessId.intValue());
            staffDto.setCompanyId(companyId.intValue());
            staffDto.setId(staffId.intValue());
            staffDto.setBookOnlineAvailable(CommonConstant.ENABLE);
            groomingOnlineBookingClient.initializeAvailableStaffV2(
                    businessId.intValue(), Collections.singletonList(staffDto));
        });
    }

    private static Map<String, StaffTime> getStaffTimeMap(BusinessWorkingHourDayDetailDTO timeData) {
        Map<String, Function<BusinessWorkingHourDayDetailDTO, List<TimeRangeDto>>> staffTimeMapForGet = ImmutableMap.of(
                WeekUtil.KEY_OF_MONDAY,
                BusinessWorkingHourDayDetailDTO::getMonday,
                WeekUtil.KEY_OF_TUESDAY,
                BusinessWorkingHourDayDetailDTO::getTuesday,
                WeekUtil.KEY_OF_WEDNESDAY,
                BusinessWorkingHourDayDetailDTO::getWednesday,
                WeekUtil.KEY_OF_THURSDAY,
                BusinessWorkingHourDayDetailDTO::getThursday,
                WeekUtil.KEY_OF_FRIDAY,
                BusinessWorkingHourDayDetailDTO::getFriday,
                WeekUtil.KEY_OF_SATURDAY,
                BusinessWorkingHourDayDetailDTO::getSaturday,
                WeekUtil.KEY_OF_SUNDAY,
                BusinessWorkingHourDayDetailDTO::getSunday);

        return staffTimeMapForGet.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> {
            List<TimeRangeDto> timeRangeDtoList = e.getValue().apply(timeData);
            StaffTime tmpStaffTime = new StaffTime();
            if (CollectionUtils.isEmpty(timeRangeDtoList)) {
                tmpStaffTime.setIsSelected(java.lang.Boolean.FALSE);
            } else {
                tmpStaffTime.setTimeRange(timeRangeDtoList);
            }
            return tmpStaffTime;
        }));
    }
}
