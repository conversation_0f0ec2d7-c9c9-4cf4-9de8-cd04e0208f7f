package com.moego.svc.organization.service;

import com.microtripit.mandrillapp.lutung.view.MandrillMessage;
import com.moego.common.constant.Dictionary;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.BusinessConst;
import com.moego.common.enums.BusinessSourceEnum;
import com.moego.common.enums.BusinessSourceFromEnum;
import com.moego.common.enums.StaffEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCoatTypeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CreateServiceDef;
import com.moego.idl.models.offering.v1.CustomizedBreed;
import com.moego.idl.models.offering.v1.ServiceCategoryModel;
import com.moego.idl.models.offering.v1.ServiceFilter;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.idl.models.organization.v1.MigrateStatus;
import com.moego.idl.models.organization.v1.PaymentMethodStatus;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.organization.v1.WeightUnit;
import com.moego.idl.service.business_customer.v1.BusinessCustomerInitializationServiceGrpc.BusinessCustomerInitializationServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.InitDemoProfileDef;
import com.moego.idl.service.business_customer.v1.InitSettingDef;
import com.moego.idl.service.business_customer.v1.InitializeBusinessCustomerModuleRequest;
import com.moego.idl.service.business_customer.v1.InitializeBusinessCustomerModuleResponse;
import com.moego.idl.service.offering.v1.CreateServiceRequest;
import com.moego.idl.service.order.v1.AddCompanyServiceChargeRequest;
import com.moego.idl.service.organization.v1.CreateCompanyFromEnterpriseHubRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.grooming.dto.InitDataGroomingResultDto;
import com.moego.server.grooming.dto.ServiceCategoryListDto;
import com.moego.server.grooming.params.ServiceCategoryUpdateDto;
import com.moego.server.message.client.IEmailClient;
import com.moego.server.message.client.ISendClient;
import com.moego.server.message.enums.MessageDetailEnum;
import com.moego.server.message.enums.MessageTargetTypeEnums;
import com.moego.server.message.params.MailSendParams;
import com.moego.server.message.params.SendMessagesParams;
import com.moego.server.payment.dto.AdminEnterpriseCustomizedPaymentSettingView;
import com.moego.svc.organization.client.AccountClient;
import com.moego.svc.organization.client.AgreementClient;
import com.moego.svc.organization.client.AutoMsgClient;
import com.moego.svc.organization.client.BusinessCustomerClient;
import com.moego.svc.organization.client.GroomingCategoryServiceClient;
import com.moego.svc.organization.client.GroomingClient;
import com.moego.svc.organization.client.MessageClient;
import com.moego.svc.organization.client.MetadataClient;
import com.moego.svc.organization.client.OfferClient;
import com.moego.svc.organization.client.OrderClient;
import com.moego.svc.organization.client.PaymentClient;
import com.moego.svc.organization.client.PermissionClient;
import com.moego.svc.organization.client.RetailClient;
import com.moego.svc.organization.client.SmsClient;
import com.moego.svc.organization.dto.init.InitCompanyDTO;
import com.moego.svc.organization.dto.init.InitCompanyFromEnterpiseHubDTO;
import com.moego.svc.organization.dto.init.InitCustomerDTO;
import com.moego.svc.organization.dto.init.InitGroomingDTO;
import com.moego.svc.organization.entity.MoeBusiness;
import com.moego.svc.organization.entity.MoeBusinessExample;
import com.moego.svc.organization.entity.MoeBusinessPaymentMethod;
import com.moego.svc.organization.entity.MoeBusinessPaymentMethodExample;
import com.moego.svc.organization.entity.MoeBusinessTax;
import com.moego.svc.organization.entity.MoeBusinessWorkingHour;
import com.moego.svc.organization.entity.MoeCompany;
import com.moego.svc.organization.entity.MoeCompanyExample;
import com.moego.svc.organization.entity.MoeStaff;
import com.moego.svc.organization.entity.MoeStaffNotification;
import com.moego.svc.organization.mapper.MoeBusinessMapper;
import com.moego.svc.organization.mapper.MoeBusinessPaymentMethodMapper;
import com.moego.svc.organization.mapper.MoeBusinessTaxMapper;
import com.moego.svc.organization.mapper.MoeBusinessWorkingHourMapper;
import com.moego.svc.organization.mapper.MoeCompanyMapper;
import com.moego.svc.organization.mapper.MoeStaffMapper;
import com.moego.svc.organization.mapper.MoeStaffNotificationMapper;
import com.moego.svc.organization.mapper.MoeStaffWorkingLocationMapper;
import com.moego.svc.organization.vo.CreateCompanyVO;
import freemarker.template.Template;
import io.micrometer.common.util.StringUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

@Service
@RequiredArgsConstructor
@Slf4j
public class InitService {
    private final MoeBusinessMapper moeBusinessMapper;
    private final MoeBusinessPaymentMethodMapper paymentMethodMapper;
    private final MoeBusinessTaxMapper moeBusinessTaxMapper;
    private final MoeBusinessWorkingHourMapper moeBusinessWorkingHourMapper;
    private final MoeCompanyMapper companyMapper;
    private final MoeStaffMapper staffMapper;
    private final MoeStaffNotificationMapper staffNotificationMapper;
    private final MoeStaffWorkingLocationMapper staffWorkingLocationMapper;
    private final CompanyQuestionService companyQuestionService;

    private final AccountClient accountClient;
    private final GroomingClient groomingClient;
    private final SmsClient smsClient;
    private final RetailClient retailClient;
    private final IEmailClient iEmailClient;
    private final ISendClient iSendClient;
    private final PermissionClient permissionClient;
    private final AgreementClient agreementClient;
    private final AutoMsgClient autoMsgClient;
    private final MessageClient messageClient;
    private final GroomingCategoryServiceClient groomingCategoryServiceClient;
    private final BusinessCustomerClient businessCustomerClient;
    private final OrderClient orderClient;
    private final PaymentClient paymentClient;

    private final BusinessCustomerInitializationServiceBlockingStub businessCustomerInitializationServiceBlockingStub;

    private final EnterpriseService enterpriseService;
    private final TaxRuleService taxRuleService;

    private final FreeMarkerConfigurer configurer;

    private final StaffAvailabilityService staffAvailabilityService;

    private static final String DEFAULT_STAFF_FIRST_NAME = "Default";
    private static final String DEFAULT_STAFF_LAST_NAME = "Staff";
    private static final String DEFAULT_TIME =
            "{\"monday\":[{\"startTime\":540,\"endTime\":1140}],\"tuesday\":[{\"startTime\":540,\"endTime\":1140}],\"wednesday\":[{\"startTime\":540,\"endTime\":1140}],\"thursday\":[{\"startTime\":540,\"endTime\":1140}],\"friday\":[{\"startTime\":540,\"endTime\":1140}],\"saturday\":[{\"startTime\":540,\"endTime\":1140}],\"sunday\":[{\"startTime\":540,\"endTime\":1140}]}";
    private final CompanyService companyService;
    private final OfferClient offerClient;

    @Value("${business.register.inform.email}")
    private String registerInformEmail;

    private final MetadataClient metadataClient;

    public InitCompanyDTO initCompany(CreateCompanyVO createCompanyVO) {
        AccountModel accountModel = accountClient.getAccountById(createCompanyVO.accountId());

        // init basic company info
        InitCompanyDTO initCompanyDTO = initCompanyBasicInfo(createCompanyVO, accountModel);
        // mark company is migrate
        metadataClient.setCompanyMigrateStatus(initCompanyDTO.getCompanyId(), MigrateStatus.MIGRATED);

        // init other module
        ThreadPool.execute(() -> initOtherModule(initCompanyDTO));

        // send email notification
        ThreadPool.execute(() -> sendEmailNotification(accountModel, createCompanyVO));
        return initCompanyDTO;
    }

    /**
     * 初始化 company 基本信息
     */
    @Transactional
    public InitCompanyDTO initCompanyBasicInfo(CreateCompanyVO createCompanyVO, AccountModel accountModel) {
        InitCompanyDTO initCompanyDTO = new InitCompanyDTO();
        if (!checkCompanyLimit(createCompanyVO.accountId())) {
            throw ExceptionUtil.bizException(Code.CODE_COMPANY_REACH_MAX_LIMIT);
        }

        // 先写入 company
        MoeCompany company = MoeCompany.builder().build();
        company.setAccountId(createCompanyVO.accountId().intValue());
        company.setName(createCompanyVO.business().getBusinessName());
        if (BusinessConst.APP_TYPE_SALON.equals(createCompanyVO.business().getAppType())) {
            company.setVansNum(0);
            company.setLocationNum(1);
        } else {
            company.setVansNum(1);
            company.setLocationNum(0);
        }
        company.setCountry(createCompanyVO.country().name());
        company.setCountryAlpha2Code(createCompanyVO.country().code());
        company.setTimezoneName(createCompanyVO.timeZone().name());
        company.setTimezoneSeconds(createCompanyVO.timeZone().seconds());
        company.setSource(createCompanyVO.source().getNumber());
        company.setKnowAboutUs(createCompanyVO.knowAboutUs());
        company.setCreateTime(DateUtil.get10Timestamp());
        company.setUpdateTime(company.getCreateTime());
        company.setCurrencyCode(createCompanyVO.currencyCode());
        company.setCurrencySymbol(createCompanyVO.currencySymbol());
        company.setCompanyType(createCompanyVO.companyType());
        companyMapper.insertSelective(company);
        Long companyId = company.getId().longValue();
        initCompanyDTO.setCompanyId(companyId);

        // 写入 business
        MoeBusiness business = createCompanyVO.business();
        business.setCountry(createCompanyVO.country().name());
        business.setCountryAlpha2Code(createCompanyVO.country().code());
        business.setPhoneNumber(createCompanyVO.phoneNumber());
        business.setCurrencyCode(createCompanyVO.currencyCode());
        business.setCurrencySymbol(createCompanyVO.currencySymbol());
        business.setTimezoneName(createCompanyVO.timeZone().name());
        business.setTimezoneSeconds(createCompanyVO.timeZone().seconds());
        Long businessId = initBusiness(companyId, business);
        initCompanyDTO.setBusinessId(businessId);
        initCompanyDTO.setBusinessName(business.getBusinessName());

        // 初始化 owner staff
        Integer ownerStaffId = initOwnerStaff(companyId, businessId, accountModel);
        initCompanyDTO.setOwnerStaffId(ownerStaffId.longValue());

        // 初始化 staff availability
        initStaffAvailability(businessId, companyId, ownerStaffId.longValue());

        // 初始化
        companyQuestionService.initCompantQuestion(companyId);
        return initCompanyDTO;
    }

    private void initStaffAvailability(final Long businessId, final Long companyId, final Long staffId) {
        ThreadPool.execute(() -> staffAvailabilityService.initTimeStaffAvailabilityByTimeAndBySlot(
                companyId, businessId, List.of(staffId)));
    }

    /**
     * 初始化 company 其他模块，可异步执行
     */
    private void initOtherModule(InitCompanyDTO initCompanyDTO) {
        // init agreement
        agreementClient.initBusinessAgreement(
                initCompanyDTO.getCompanyId(),
                initCompanyDTO.getBusinessId().intValue(),
                initCompanyDTO.getBusinessName());

        // 初始化 sms setting
        smsClient.initSmsSetting(initCompanyDTO.getCompanyId());

        // 初始化 tax 配置
        Long taxId = initTax(initCompanyDTO.getCompanyId());
        initCompanyDTO.setTaxId(taxId);

        // 初始化 Role & Permission
        initRole(initCompanyDTO.getCompanyId());

        initNotification(initCompanyDTO.getOwnerStaffId(), initCompanyDTO.getCompanyId());

        InitCustomerDTO initCustomerDTO = initCustomerModule(initCompanyDTO);
        InitGroomingDTO initGroomingDTO = initGroomingModule(initCompanyDTO, initCustomerDTO);

        try {
            initMessageModule(initCompanyDTO, initCustomerDTO);
        } catch (Exception e) {
            log.error("init message module error", e);
        }

        try {
            initRetailModule(initCompanyDTO, initGroomingDTO);
        } catch (Exception e) {
            log.error("init retail module error", e);
        }

        // 初始化 message
        autoMsgClient.initBusinessAutoMsg(initCompanyDTO.getCompanyId(), initCompanyDTO.getBusinessId());
        messageClient.initBusinessTemplate(initCompanyDTO.getCompanyId(), initCompanyDTO.getBusinessId());
    }

    public InitCompanyFromEnterpiseHubDTO initCompanyCreatedFromEnterpriseHub(
            CreateCompanyFromEnterpriseHubRequest request) {
        // 1、初始化company &business&owner staff 配置
        InitCompanyFromEnterpiseHubDTO initDTO = initCompanyAndBusinessSetting(request);

        // mark company is mxigrate
        metadataClient.setCompanyMigrateStatus(initDTO.getNewCompanyId(), MigrateStatus.MIGRATED);

        // 2、初始化数据前置依赖
        initData(initDTO);

        try {
            // 3、copy service
            copyServiceSetting(initDTO);

        } catch (Exception e) {
            log.error("init company error copyServiceSetting", e);
        }
        return initDTO;
    }

    /**
     * 初始化company和business设置
     * @param request
     */
    public InitCompanyFromEnterpiseHubDTO initCompanyAndBusinessSetting(CreateCompanyFromEnterpriseHubRequest request) {
        /**
         * 1、company setting
         */
        InitCompanyFromEnterpiseHubDTO initDTO = new InitCompanyFromEnterpiseHubDTO();
        MoeCompany newCompany = MoeCompany.builder().build();
        newCompany.setAccountId((int) request.getAccountId());
        MoeCompany templateCompany = companyService.getCompanyById(request.getTemplateCompanyId());
        Byte companyType = 0;
        newCompany.setName(request.getName());
        newCompany.setCountry(templateCompany.getCountry());
        newCompany.setCreateTime(DateUtil.get10Timestamp());
        newCompany.setUpdateTime(DateUtil.get10Timestamp());
        if (request.hasVanNum() && request.getVanNum() != 0) {
            newCompany.setVansNum(request.getVanNum());
            companyType = 0;
        }
        if (request.hasLocationNum() && request.getLocationNum() != 0) {
            newCompany.setLocationNum(request.getLocationNum());
            companyType = 1;
        }
        if (request.hasLocationNum()
                && request.hasVanNum()
                && request.getLocationNum() != 0
                && request.getVanNum() != 0) {
            companyType = 2;
        }
        newCompany.setEnterpriseId((int) request.getEnterpriseId());
        /** 待定
         * company.setLevel(1);
         * company.setIsNewPricing(0);
         * company.setEnableSquare((byte)0);
         */
        newCompany.setEnableStripeReader(templateCompany.getEnableStripeReader());
        newCompany.setCurrencyCode(templateCompany.getCurrencyCode());
        newCompany.setCurrencySymbol(templateCompany.getCurrencySymbol());
        newCompany.setDateFormatType(templateCompany.getDateFormatType());
        newCompany.setTimeFormatType(templateCompany.getTimeFormatType());
        newCompany.setUnitOfWeightType(templateCompany.getUnitOfWeightType());
        newCompany.setUnitOfDistanceType(templateCompany.getUnitOfDistanceType());
        newCompany.setNotificationSoundEnable(templateCompany.getNotificationSoundEnable());
        newCompany.setCountryAlpha2Code(templateCompany.getCountryAlpha2Code());
        newCompany.setTimezoneName(templateCompany.getTimezoneName());
        newCompany.setTimezoneSeconds(templateCompany.getTimezoneSeconds());
        newCompany.setCompanyType(companyType);
        // 插入数据
        int rowsAffected = companyMapper.insertSelective(newCompany);
        if (rowsAffected == 0) {
            throw ExceptionUtil.bizException(Code.CODE_COMPANY_NOT_FOUND);
        }
        initDTO.setTemplateCompanyId(request.getTemplateCompanyId());
        initDTO.setNewCompanyId(newCompany.getId().longValue());
        /**
         * 2、business setting
         */
        List<Integer> businessIds = new ArrayList<>();

        var example = new MoeBusinessExample();
        example.createCriteria().andCompanyIdEqualTo(templateCompany.getId());
        List<MoeBusiness> templateBusinesses = moeBusinessMapper.selectByExample(example);
        Map<Integer, Integer> businessIdMap = new HashMap<>();

        /*
         因为现在 enterprise 只能配置一个模版，不能同时适配 only salon/only mobile / hybrid 这三种创建场景
         暂时先只拷贝一个business
        */
        // 如果模版的business 只有一个，则命名用tenant name，否则，用tenant name + type
        //        if (templateBusinesses.size() == 1) {
        MoeBusiness templateBusiness = templateBusinesses.get(0);
        MoeBusiness business = MoeBusiness.builder()
                .country(templateBusiness.getCountry())
                .businessName(request.getName())
                .businessMode(templateBusiness.getAppType())
                .appType(templateBusiness.getAppType())
                .businessYears(templateBusiness.getBusinessYears())
                .countryAlpha2Code(templateBusiness.getCountryAlpha2Code())
                .currencyCode(templateBusiness.getCurrencyCode())
                .currencySymbol(templateBusiness.getCurrencySymbol())
                .timezoneName(templateBusiness.getTimezoneName())
                .timezoneSeconds(templateBusiness.getTimezoneSeconds())
                .build();

        Long businessId = initBusiness(newCompany.getId().longValue(), business);
        businessIds.add(businessId.intValue());
        businessIdMap.put(templateBusiness.getId(), businessId.intValue());
        //        } else {
        //            for (MoeBusiness templateBusiness : templateBusinesses) {
        //                MoeBusiness business = MoeBusiness.builder()
        //                        .country(templateBusiness.getCountry())
        //                        .businessName(request.getName() + " " +
        // BusinessUtil.getAppType(templateBusiness.getAppType()))
        //                        .businessMode(templateBusiness.getAppType())
        //                        .appType(templateBusiness.getAppType())
        //                        .businessYears(templateBusiness.getBusinessYears())
        //                        .countryAlpha2Code(templateBusiness.getCountryAlpha2Code())
        //                        .currencyCode(templateBusiness.getCurrencyCode())
        //                        .currencySymbol(templateBusiness.getCurrencySymbol())
        //                        .timezoneName(templateBusiness.getTimezoneName())
        //                        .timezoneSeconds(templateBusiness.getTimezoneSeconds())
        //                        .build();
        //
        //                Long businessId = initBusiness(newCompany.getId().longValue(), business);
        //                businessIds.add(businessId.intValue());
        //                businessIdMap.put(templateBusiness.getId(), businessId.intValue());
        //            }
        //        }

        if (businessIdMap.isEmpty()) {
            throw ExceptionUtil.bizException(Code.CODE_BUSINESS_IS_EMPTY);
        }
        initDTO.setBusinessIdMap(businessIdMap);
        initDTO.setBusinessIds(businessIds);

        // 设置map

        /**
         * 3、owner staff 只需要一个
         */
        AccountModel accountModel = accountClient.getAccountById(request.getAccountId());
        Integer ownerStaffId = initAssignOwnerStaff(
                newCompany.getId().longValue(), businessIds.get(0).longValue(), accountModel, request);
        initDTO.setOwnerStaffId(ownerStaffId.longValue());

        // 初始化
        companyQuestionService.initCompantQuestion(newCompany.getId().longValue());

        // 初始化自定义费率
        handleEnterpriseCusPaySetting(request.getEnterpriseId(), initDTO.getNewCompanyId());

        return initDTO;
    }

    /**
     * 初始化数据，包括agreement、sms setting、tax配置、role & permission、notification
     */
    private void initData(InitCompanyFromEnterpiseHubDTO initDTO) {
        // copy tax
        taxRuleService.copyTax(initDTO);

        // company 纬度的init
        // init  CopyCustomerSetting & grooming
        InitCustomerDTO initCustomerDTO = copyCustomerSetting(initDTO);

        // init grooming
        // 默认不要问卷
        companyQuestionService.initCompanyFromEnterpriseQuestion(initDTO.getNewCompanyId());

        // 初始化 sms setting
        smsClient.initSmsSetting(initDTO.getNewCompanyId());

        // 初始化 Role & Permission
        initRole(initDTO.getNewCompanyId());

        // todo 待定是否需要
        initNotification(initDTO.getOwnerStaffId(), initDTO.getNewCompanyId());

        // 构造 InitCompanyDTO 复用一些初始化方法
        InitCompanyDTO initCompanyDTO = new InitCompanyDTO();
        initCompanyDTO.setCompanyId(initDTO.getNewCompanyId());
        initCompanyDTO.setBusinessId(initDTO.getBusinessIds().get(0).longValue());
        initCompanyDTO.setTaxId(initDTO.getTaxId());
        initCompanyDTO.setOwnerStaffId(initDTO.getOwnerStaffId());

        // initGroomingModule 会初始化一些service 导致后续copy service 会出现问题
        //        InitGroomingDTO initGroomingDTO = initGroomingModule(initCompanyDTO, initCustomerDTO);

        try {
            initMessageModule(initCompanyDTO, initCustomerDTO);
        } catch (Exception e) {
            log.error("init message module error", e);
        }

        //        try {
        //            initRetailModule(initCompanyDTO, initGroomingDTO);
        //        } catch (Exception e) {
        //            log.error("init retail module error", e);
        //        }

        // business 纬度的 init
        initDTO.getBusinessIdMap().forEach((templateBusinessId, newBusinessId) -> {
            // init agreement
            agreementClient.copyAgreement(
                    initDTO.getTemplateCompanyId().intValue(),
                    initDTO.getNewCompanyId().intValue(),
                    newBusinessId,
                    templateBusinessId);
            // 初始化 message
            autoMsgClient.initBusinessAutoMsg(
                    initDTO.getNewCompanyId(), initDTO.getBusinessIds().get(0).longValue());
            messageClient.initBusinessTemplate(
                    initDTO.getNewCompanyId(), initDTO.getBusinessIds().get(0).longValue());
        });
    }

    public MoeStaffNotification initNotification(Long staffId, Long companyId) {
        MoeStaffNotification moeStaffNotification = MoeStaffNotification.builder()
                .staffId(staffId.intValue())
                .companyId(companyId)
                .bookingCreated(StaffEnum.NOTIFICATION_TYPE_ACROSS_WORKING_LOCATIONS)
                .bookingCancelled(StaffEnum.NOTIFICATION_TYPE_ACROSS_WORKING_LOCATIONS)
                .bookingRescheduled(StaffEnum.NOTIFICATION_TYPE_ACROSS_WORKING_LOCATIONS)
                .newBooking(StaffEnum.NOTIFICATION_TYPE_ACROSS_WORKING_LOCATIONS)
                .newIntakeForm(StaffEnum.NOTIFICATION_TYPE_ACROSS_WORKING_LOCATIONS)
                .agreementSigned(StaffEnum.NOTIFICATION_TYPE_ACROSS_WORKING_LOCATIONS)
                .invoicePaid(StaffEnum.NOTIFICATION_TYPE_ACROSS_WORKING_LOCATIONS)
                .reviewSubmitted(StaffEnum.NOTIFICATION_TYPE_ACROSS_WORKING_LOCATIONS)
                .newAbandonedBookings(StaffEnum.NOTIFICATION_TYPE_DO_NOT_NOTIFY)
                .assignedTask(StaffEnum.NOTIFICATION_TYPE_RELATED_TO_THEM)
                .createTime(DateUtil.get10Timestamp())
                .updateTime(DateUtil.get10Timestamp())
                .build();
        staffNotificationMapper.insertSelective(moeStaffNotification);
        return moeStaffNotification;
    }

    public MoeStaffNotification initNotificationForMigrate(Long staffId, Long companyId) {
        MoeStaffNotification moeStaffNotification = MoeStaffNotification.builder()
                .staffId(staffId.intValue())
                .companyId(companyId)
                .bookingCreated(StaffEnum.NOTIFICATION_TYPE_RELATED_TO_THEM)
                .bookingCancelled(StaffEnum.NOTIFICATION_TYPE_RELATED_TO_THEM)
                .bookingRescheduled(StaffEnum.NOTIFICATION_TYPE_RELATED_TO_THEM)
                .newBooking(StaffEnum.NOTIFICATION_CLOSE)
                .newIntakeForm(StaffEnum.NOTIFICATION_CLOSE)
                .agreementSigned(StaffEnum.NOTIFICATION_TYPE_RELATED_TO_THEM)
                .invoicePaid(StaffEnum.NOTIFICATION_CLOSE)
                .reviewSubmitted(StaffEnum.NOTIFICATION_CLOSE)
                .newAbandonedBookings(StaffEnum.NOTIFICATION_CLOSE)
                .assignedTask(StaffEnum.NOTIFICATION_TYPE_RELATED_TO_THEM)
                .createTime(DateUtil.get10Timestamp())
                .updateTime(DateUtil.get10Timestamp())
                .build();
        staffNotificationMapper.insertSelective(moeStaffNotification);
        return moeStaffNotification;
    }

    private InitCustomerDTO initCustomerModule(InitCompanyDTO initCompanyDTO) {
        var request = InitializeBusinessCustomerModuleRequest.newBuilder()
                .setCompanyId(initCompanyDTO.getCompanyId())
                .setBusinessId(initCompanyDTO.getBusinessId())
                .setStaffId(initCompanyDTO.getOwnerStaffId())
                .setWeightUnit(WeightUnit.POUND)
                .build();
        var response = businessCustomerInitializationServiceBlockingStub.initializeBusinessCustomerModule(request);
        return InitCustomerDTO.builder()
                .customerId(response.getCustomerId())
                .miniPetId(response.getMiniPetId())
                .maxPetId(response.getMaxPetId())
                .build();
    }

    private InitCustomerDTO copyCustomerSetting(InitCompanyFromEnterpiseHubDTO initDTO) {
        // customer setting 只需要初始化一个
        var request = InitializeBusinessCustomerModuleRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(initDTO.getNewCompanyId()))
                .setInitSetting(InitSettingDef.newBuilder()
                        .setCopyFromTenant(Tenant.newBuilder().setCompanyId(initDTO.getTemplateCompanyId()))
                        .setWeightUnitValue(WeightUnit.POUND_VALUE))
                .setInitDemoProfile(InitDemoProfileDef.newBuilder()
                        .setStaffId(initDTO.getOwnerStaffId())
                        .setPreferredBusinessId(initDTO.getBusinessIds().get(0)))
                .build();
        InitializeBusinessCustomerModuleResponse response =
                businessCustomerInitializationServiceBlockingStub.initializeBusinessCustomerModule(request);
        if (response.getCustomerId() == 0) {
            return InitCustomerDTO.builder().build();
        }
        return InitCustomerDTO.builder()
                .customerId(response.getCustomerId())
                .miniPetId(response.getMiniPetId())
                .maxPetId(response.getMaxPetId())
                .build();
    }

    private void copyServiceSetting(InitCompanyFromEnterpiseHubDTO initDTO) {
        // 1、初始化 service category
        initServiceCategory(initDTO, ServiceItemType.GROOMING);
        initServiceCategory(initDTO, ServiceItemType.BOARDING);
        initServiceCategory(initDTO, ServiceItemType.DAYCARE);
        // 初始化 add on
        // 1、获取template company 需要的信息 service type
        List<ServiceCategoryListDto> addOnCategoryListDtos =
                groomingCategoryServiceClient.listServiceCategory(initDTO.getTemplateCompanyId(), (byte) 2, (byte) 1);
        if (!addOnCategoryListDtos.isEmpty()) {
            // 2. add service category
            List<ServiceCategoryUpdateDto> updateAddOnDto = new ArrayList<>();
            addOnCategoryListDtos.forEach(addOnCategoryListDto -> {
                ServiceCategoryUpdateDto dto = new ServiceCategoryUpdateDto();

                dto.setName(addOnCategoryListDto.getName());
                dto.setType(addOnCategoryListDto.getType().byteValue());
                dto.setServiceItemType(1);
                //            addServiceParam.setServiceCategoryList(serviceCategoryListDto.getServiceCategoryList());
                updateAddOnDto.add(dto);
            });
            groomingCategoryServiceClient.addServiceCategory(
                    initDTO.getNewCompanyId(), (byte) 2, (byte) 1, updateAddOnDto);
        }
        // service charge
        List<ServiceCharge> companyServiceChargeList =
                orderClient.getCompanyServiceChargeList(initDTO.getTemplateCompanyId());

        // 2、构建映射关系
        Map<Long, Long> petPetCoatIdMap = new HashMap<>();
        Map<Long, Long> petSizeIdMap = new HashMap<>();
        Map<Long, Long> taxIdMap = new HashMap<>();
        Map<Long, Long> categoryIdMap = new HashMap<>();
        Map<Long, Long> ServiceIdMap = new HashMap<>();

        // 拉新旧company code pet 数据
        List<BusinessPetCoatTypeModel> templatePetCoatTypes =
                businessCustomerClient.listPetCoatType(initDTO.getTemplateCompanyId());
        List<BusinessPetCoatTypeModel> newCompanyPetCoatTypes =
                businessCustomerClient.listPetCoatType(initDTO.getNewCompanyId());

        List<BusinessPetSizeModel> templatePetSizes =
                businessCustomerClient.listPetSize(initDTO.getTemplateCompanyId());
        List<BusinessPetSizeModel> newCompanyPetSizes = businessCustomerClient.listPetSize(initDTO.getNewCompanyId());

        // 拉新旧company tax 数据
        List<MoeBusinessTax> templateTaxRuleList = taxRuleService.getTaxRuleList(initDTO.getTemplateCompanyId());
        List<MoeBusinessTax> newCompanyTaxRuleList = taxRuleService.getTaxRuleList(initDTO.getNewCompanyId());

        // 拉template id 每个service
        List<ServiceCategoryModel> templateServiceList =
                offerClient.getServiceList(initDTO.getTemplateCompanyId(), ServiceType.SERVICE);
        List<ServiceCategoryModel> newCompanyServiceList =
                offerClient.getServiceList(initDTO.getNewCompanyId(), ServiceType.SERVICE);

        // add on list
        List<ServiceCategoryModel> templateAddOnList =
                offerClient.getServiceList(initDTO.getTemplateCompanyId(), ServiceType.ADDON);
        List<ServiceCategoryModel> newCompanyAddOnList =
                offerClient.getServiceList(initDTO.getNewCompanyId(), ServiceType.ADDON);

        /**
         * 拆分 service 和 add on 分别copy，因为add on 对service有依赖关系
         */

        // 构建各类id 映射
        // service 分类映射
        Map<String, Long> templateServiceCategoryMap = new HashMap<>();
        for (ServiceCategoryModel category : templateServiceList) {
            templateServiceCategoryMap.put(category.getName(), category.getCategoryId());
        }
        for (ServiceCategoryModel category : newCompanyServiceList) {
            Long templateCategoryId = templateServiceCategoryMap.get(category.getName());
            if (templateCategoryId != null) {
                categoryIdMap.put(templateCategoryId, category.getCategoryId());
            }
        }

        // add on 分类映射
        Map<String, Long> templateAddOnCategoryMap = new HashMap<>();
        for (ServiceCategoryModel category : templateAddOnList) {
            templateAddOnCategoryMap.put(category.getName(), category.getCategoryId());
        }
        for (ServiceCategoryModel category : newCompanyAddOnList) {
            Long templateCategoryId = templateAddOnCategoryMap.get(category.getName());
            if (templateCategoryId != null) {
                categoryIdMap.put(templateCategoryId, category.getCategoryId());
            }
        }

        // pet coat type 映射
        Map<String, Long> templatePetCoatTypeMap = new HashMap<>();
        for (BusinessPetCoatTypeModel templatePetCoatType : templatePetCoatTypes) {
            templatePetCoatTypeMap.put(templatePetCoatType.getName(), templatePetCoatType.getId());
        }

        for (BusinessPetCoatTypeModel newCompanyPetCoatType : newCompanyPetCoatTypes) {
            Long templatePetCoatTypeId = templatePetCoatTypeMap.get(newCompanyPetCoatType.getName());
            if (templatePetCoatTypeId != null) {
                petPetCoatIdMap.put(templatePetCoatTypeId, newCompanyPetCoatType.getId());
            }
        }

        // Pet 尺寸映射
        Map<String, Long> templatePetSizeMap = new HashMap<>();
        for (BusinessPetSizeModel templatePetSize : templatePetSizes) {
            templatePetSizeMap.put(templatePetSize.getName(), templatePetSize.getId());
        }

        for (BusinessPetSizeModel newCompanyPetSize : newCompanyPetSizes) {
            Long templatePetSizeId = templatePetSizeMap.get(newCompanyPetSize.getName());
            if (templatePetSizeId != null) {
                petSizeIdMap.put(templatePetSizeId, newCompanyPetSize.getId());
            }
        }

        // 税收规则映射
        Map<String, Long> templateTaxRuleMap = new HashMap<>();
        for (MoeBusinessTax templateTaxRule : templateTaxRuleList) {
            templateTaxRuleMap.put(
                    templateTaxRule.getTaxName(), templateTaxRule.getId().longValue());
        }

        for (MoeBusinessTax newCompanyTaxRule : newCompanyTaxRuleList) {
            Long templateTaxRuleId = templateTaxRuleMap.get(newCompanyTaxRule.getTaxName());
            if (templateTaxRuleId != null) {
                taxIdMap.put(templateTaxRuleId, newCompanyTaxRule.getId().longValue());
            }
        }

        // 3、写入service
        Set<Long> skipServiceSet = new HashSet<>();
        templateServiceList.forEach(templateServiceCategory -> {
            if (categoryIdMap.containsKey(templateServiceCategory.getCategoryId())) {
                // pack service
                templateServiceCategory.getServicesList().forEach(templateService -> {

                    // 1、create def
                    CreateServiceDef.Builder createServicebuilder = CreateServiceDef.newBuilder()
                            .setCategoryId(categoryIdMap.get(templateServiceCategory.getCategoryId()))
                            .setServiceItemType(templateService.getServiceItemType())
                            .setPriceUnit(templateService.getPriceUnit())
                            .setRequireDedicatedStaff(templateService.getRequireDedicatedStaff())
                            .setName(templateService.getName())
                            .setInactive(templateService.getInactive())
                            .setColorCode(templateService.getColorCode())
                            .setIsAllLocation(true)
                            .setBreedFilter(templateService.getBreedFilter())
                            .setDescription(templateService.getDescription())
                            .setCoatFilter(templateService.getCoatFilter())
                            .setType(templateService.getType())
                            .setServiceFilter(templateService.getServiceFilter())
                            .setRequireDedicatedLodging(true)
                            .setPetSizeFilter(templateService.getPetSizeFilter())
                            .setLodgingFilter(templateService.getLodgingFilter())
                            .setCanTip(templateService.getCanTip())
                            .setAddToCommissionBase(templateService.getAddToCommissionBase())
                            .setDescription(templateService.getDescription())
                            .setPrice(templateService.getPrice())
                            .setDuration(templateService.getDuration());

                    // tax
                    if (!taxIdMap.containsKey(templateService.getTaxId())) {
                        // 原 service 对应的 tax 已经被删除,不复制这个 service
                        skipServiceSet.add(templateService.getServiceId());
                        return;
                    }
                    createServicebuilder.setTaxId(taxIdMap.get(templateService.getTaxId()));

                    // pet breed
                    if (templateService.getCustomizedBreedCount() > 0) {
                        templateService.getCustomizedBreedList().forEach(templateBreed -> {
                            CustomizedBreed.Builder cusBreedBuilder = CustomizedBreed.newBuilder()
                                    .setIsAll(templateBreed.getIsAll())
                                    .addAllBreeds(templateBreed.getBreedsList());
                            cusBreedBuilder.setPetTypeId(templateBreed.getPetTypeId());
                            createServicebuilder.addCustomizedBreed(cusBreedBuilder.build());
                        });
                    }

                    if (templateService.getCustomizedCoatCount() > 0) {
                        List<Long> customizedCoats = new ArrayList<>();
                        templateService.getCustomizedCoatList().forEach(templateCoatId -> {
                            if (!petPetCoatIdMap.isEmpty() && petPetCoatIdMap.containsKey(templateCoatId)) {
                                customizedCoats.add(petPetCoatIdMap.get(templateCoatId));
                            }
                        });
                        createServicebuilder.addAllCustomizedCoat(customizedCoats);
                    }

                    if (templateService.getCustomizedPetSizesCount() > 0) {
                        List<Long> customizedPetSizes = new ArrayList<>();
                        templateService.getCustomizedPetSizesList().forEach(templatePetSizeId -> {
                            if (!petSizeIdMap.isEmpty() && petSizeIdMap.containsKey(templatePetSizeId)) {
                                customizedPetSizes.add(petSizeIdMap.get(templatePetSizeId));
                            }
                        });
                        createServicebuilder.addAllCustomizedPetSizes(customizedPetSizes);
                    }

                    // 1、add service
                    CreateServiceRequest request = CreateServiceRequest.newBuilder()
                            .setCreateServiceDef(createServicebuilder.build())
                            .setTokenCompanyId(initDTO.getNewCompanyId())
                            .setInternalOperator("enterprise hub")
                            .build();
                    offerClient.addService(request);
                });
            }
        });

        // 要重新拉一次service list，因为这个copy template 的company 已经创建了，要重新拉一次记录映射关系
        List<ServiceCategoryModel> newCompanyServiceListCopy =
                offerClient.getServiceList(initDTO.getNewCompanyId(), ServiceType.SERVICE);

        // 4、copy add on

        // service id 映射，用于add on 中指定service
        Map<String, Long> templateServiceMap = new HashMap<>();
        for (ServiceCategoryModel templateServiceCategory : templateServiceList) {
            for (ServiceModel templateService : templateServiceCategory.getServicesList()) {
                String key = templateServiceCategory.getName() + ":" + templateService.getName();
                templateServiceMap.put(key, templateService.getServiceId());
            }
        }

        for (ServiceCategoryModel newCompanyServiceCategory : newCompanyServiceListCopy) {
            for (ServiceModel newCompanyService : newCompanyServiceCategory.getServicesList()) {
                String key = newCompanyServiceCategory.getName() + ":" + newCompanyService.getName();
                Long templateServiceId = templateServiceMap.get(key);
                if (templateServiceId != null) {
                    ServiceIdMap.put(templateServiceId, newCompanyService.getServiceId());
                }
            }
        }

        templateAddOnList.forEach(templateAddOnCategory -> {
            if (categoryIdMap.containsKey(templateAddOnCategory.getCategoryId())) {
                // pack service
                templateAddOnCategory.getServicesList().forEach(templateAddOn -> {

                    // 1、create def
                    CreateServiceDef.Builder createServicebuilder = CreateServiceDef.newBuilder()
                            .setCategoryId(categoryIdMap.get(templateAddOnCategory.getCategoryId()))
                            .setServiceItemType(templateAddOn.getServiceItemType())
                            .setColorCode(templateAddOn.getColorCode())
                            .setPriceUnit(templateAddOn.getPriceUnit())
                            .setRequireDedicatedStaff(templateAddOn.getRequireDedicatedStaff())
                            .setName(templateAddOn.getName())
                            .setInactive(templateAddOn.getInactive())
                            .setIsAllLocation(true)
                            .setBreedFilter(templateAddOn.getBreedFilter())
                            .setDescription(templateAddOn.getDescription())
                            .setCoatFilter(templateAddOn.getCoatFilter())
                            .setType(templateAddOn.getType())
                            .setServiceFilter(templateAddOn.getServiceFilter())
                            .setRequireDedicatedLodging(true)
                            .setPetSizeFilter(templateAddOn.getPetSizeFilter())
                            .setLodgingFilter(templateAddOn.getLodgingFilter())
                            .setCanTip(templateAddOn.getCanTip())
                            .setAddToCommissionBase(templateAddOn.getAddToCommissionBase())
                            .setDescription(templateAddOn.getDescription())
                            .setPrice(templateAddOn.getPrice())
                            .setDuration(templateAddOn.getDuration());

                    // tax
                    if (!taxIdMap.containsKey(templateAddOn.getTaxId())) {
                        // 原 add on 对应的 tax 已经被删除,不复制这个 add on
                        return;
                    }
                    createServicebuilder.setTaxId(taxIdMap.get(templateAddOn.getTaxId()));
                    // pet breed
                    if (templateAddOn.getCustomizedBreedCount() > 0) {
                        templateAddOn.getCustomizedBreedList().forEach(templateBreed -> {
                            CustomizedBreed.Builder cusBreedBuilder = CustomizedBreed.newBuilder()
                                    .setIsAll(templateBreed.getIsAll())
                                    .addAllBreeds(templateBreed.getBreedsList());
                            cusBreedBuilder.setPetTypeId(templateBreed.getPetTypeId());
                            createServicebuilder.addCustomizedBreed(cusBreedBuilder.build());
                        });
                    }

                    if (templateAddOn.getCustomizedCoatCount() > 0) {
                        List<Long> customizedCoats = new ArrayList<>();
                        templateAddOn.getCustomizedCoatList().forEach(templateCoatId -> {
                            if (!petPetCoatIdMap.isEmpty() && petPetCoatIdMap.containsKey(templateCoatId)) {
                                customizedCoats.add(petPetCoatIdMap.get(templateCoatId));
                            }
                        });
                        createServicebuilder.addAllCustomizedCoat(customizedCoats);
                    }

                    if (templateAddOn.getCustomizedPetSizesCount() > 0) {
                        List<Long> customizedPetSizes = new ArrayList<>();
                        templateAddOn.getCustomizedPetSizesList().forEach(templatePetSizeId -> {
                            if (!petSizeIdMap.isEmpty() && petSizeIdMap.containsKey(templatePetSizeId)) {
                                customizedPetSizes.add(petSizeIdMap.get(templatePetSizeId));
                            }
                        });
                        createServicebuilder.addAllCustomizedPetSizes(customizedPetSizes);
                    }

                    // add on Applicable services
                    if (templateAddOn.getServiceFilterListCount() > 0) {
                        templateAddOn.getServiceFilterListList().forEach(templateServiceFilter -> {
                            ServiceFilter.Builder newCompanyServiceFilterBuilder = ServiceFilter.newBuilder();
                            newCompanyServiceFilterBuilder.setServiceItemType(
                                    templateServiceFilter.getServiceItemType());
                            newCompanyServiceFilterBuilder.setAvailableForAllServices(
                                    templateServiceFilter.getAvailableForAllServices());
                            if (templateServiceFilter.getAvailableServiceIdListCount() > 0) {
                                List<Long> newCompanyAvailableServiceIdList = new ArrayList<>();
                                templateServiceFilter
                                        .getAvailableServiceIdListList()
                                        .forEach(templateServiceId -> {
                                            if (ServiceIdMap.containsKey(templateServiceId)
                                                    && !skipServiceSet.contains(templateServiceId)) {
                                                newCompanyAvailableServiceIdList.add(
                                                        ServiceIdMap.get(templateServiceId));
                                            }
                                        });
                                newCompanyServiceFilterBuilder.addAllAvailableServiceIdList(
                                        newCompanyAvailableServiceIdList);
                            }
                            newCompanyServiceFilterBuilder.build();
                            createServicebuilder.addServiceFilterList(newCompanyServiceFilterBuilder.build());
                        });
                    }

                    // 1、copy add on
                    CreateServiceRequest request = CreateServiceRequest.newBuilder()
                            .setCreateServiceDef(createServicebuilder.build())
                            .setTokenCompanyId(initDTO.getNewCompanyId())
                            .setInternalOperator("enterprise hub")
                            .build();
                    offerClient.addService(request);
                });
            }
        });

        // 5、写入service charge
        companyServiceChargeList.forEach(serviceCharge -> {
            AddCompanyServiceChargeRequest.Builder builder = AddCompanyServiceChargeRequest.newBuilder();
            builder.setCompanyId(initDTO.getNewCompanyId());
            builder.setName(serviceCharge.getName());
            builder.setPrice(serviceCharge.getPrice());
            builder.setIsActive(serviceCharge.getIsActive());
            builder.setIsAllLocation(true);
            builder.setApplyUpcomingAppt(false);
            builder.setOperatorId(0);
            builder.setIsMandatory(serviceCharge.getIsMandatory());
            builder.setDescription(serviceCharge.getDescription());
            long templateTaxId = (long) serviceCharge.getTaxId();
            if (templateTaxId == 0 || !taxIdMap.containsKey(templateTaxId)) {
                return;
            }
            builder.setTaxId(Math.toIntExact(taxIdMap.get(templateTaxId)));

            orderClient.addCompanyServiceCharge(builder.build());
        });
    }

    public void initServiceCategory(InitCompanyFromEnterpiseHubDTO initDTO, ServiceItemType serviceItemType) {
        // 1、获取template company 需要的信息 service type
        List<ServiceCategoryListDto> serviceCategoryListDtos = groomingCategoryServiceClient.listServiceCategory(
                initDTO.getTemplateCompanyId(), (byte) 1, (byte) serviceItemType.getNumber());
        if (!serviceCategoryListDtos.isEmpty()) {
            // 2. add service category
            List<ServiceCategoryUpdateDto> updateServiceDto = new ArrayList<>();
            serviceCategoryListDtos.forEach(serviceCategoryListDto -> {
                ServiceCategoryUpdateDto dto = new ServiceCategoryUpdateDto();

                dto.setName(serviceCategoryListDto.getName());
                dto.setType(serviceCategoryListDto.getType().byteValue());
                dto.setServiceItemType(serviceItemType.getNumber());
                updateServiceDto.add(dto);
            });
            groomingCategoryServiceClient.addServiceCategory(
                    initDTO.getNewCompanyId(), (byte) 1, (byte) serviceItemType.getNumber(), updateServiceDto);
        }
    }

    private void initMessageModule(InitCompanyDTO initCompanyDTO, InitCustomerDTO customerResultDto) {
        Long companyId = initCompanyDTO.getCompanyId();
        Integer businessId = Math.toIntExact(initCompanyDTO.getBusinessId());
        Integer ownerStaffId = Math.toIntExact(initCompanyDTO.getOwnerStaffId());
        Integer customerId = Math.toIntExact(customerResultDto.getCustomerId());

        // 发送一条消息给 demo customer（需要打开message thread）
        SendMessagesParams sendMessagesParams = new SendMessagesParams();
        sendMessagesParams.setStaffId(ownerStaffId);
        sendMessagesParams.setMessageBody("Hello Demo Profile!");
        sendMessagesParams.setCompanyId(companyId);
        sendMessagesParams.setBusinessId(businessId);
        sendMessagesParams.setTargetType(MessageTargetTypeEnums.TARGET_TYPE_THREAD.getValue());
        sendMessagesParams.getCustomer().setCustomerId(customerId);
        sendMessagesParams.setMethod(MessageDetailEnum.MESSAGE_METHOD_MSG.getValue());
        iSendClient.sendMessageToCustomerAndSaveMessage(businessId, ownerStaffId, sendMessagesParams);
    }

    private InitGroomingDTO initGroomingModule(InitCompanyDTO initCompanyDTO, InitCustomerDTO customerResultDto) {
        if (Objects.isNull(customerResultDto)) {
            return null;
        }
        InitDataGroomingResultDto initDataGroomingResultDto =
                groomingClient.initGroomingModule(initCompanyDTO, customerResultDto);
        if (Objects.isNull(initDataGroomingResultDto)) {
            return null;
        }
        return InitGroomingDTO.builder()
                .fullLargeServiceId(
                        initDataGroomingResultDto.getFullLargeServiceId().longValue())
                .build();
    }

    private void initRetailModule(InitCompanyDTO initCompanyDTO, InitGroomingDTO initGroomingDTO) {
        if (Objects.isNull(initGroomingDTO)) {
            return;
        }
        retailClient.initRetailModule(initCompanyDTO, initGroomingDTO);
    }

    private void sendEmailNotification(AccountModel accountModel, CreateCompanyVO createCompanyVO) {
        MoeBusiness moeBusiness = createCompanyVO.business();
        Template template;
        String html;
        Map<String, Object> module = new HashMap<>();
        module.put("firstName", accountModel.getFirstName());
        module.put("lastName", accountModel.getLastName());
        module.put("businessName", moeBusiness.getBusinessName() == null ? "" : moeBusiness.getBusinessName());
        accountModel.getEmail();
        module.put("email", accountModel.getEmail());
        module.put("phoneNumber", moeBusiness.getPhoneNumber() == null ? "" : moeBusiness.getPhoneNumber());
        module.put(
                "address",
                CommonUtil.getFullAddress(
                        moeBusiness.getAddress1(),
                        moeBusiness.getAddress2(),
                        moeBusiness.getAddressCity(),
                        moeBusiness.getAddressState(),
                        moeBusiness.getAddressCountry(),
                        moeBusiness.getAddressZipcode()));
        module.put("website", moeBusiness.getWebsite() == null ? "" : moeBusiness.getWebsite());
        module.put(
                "submitTime", DateUtil.dateToBusinessFormat(DateUtil.get10Timestamp(), Dictionary.DATE_FORMAT_DEFAULT));
        module.put("sourceFrom", BusinessSourceFromEnum.getDescByType(moeBusiness.getSourceFrom()));

        int source = createCompanyVO.source().getNumber();
        String note = BusinessSourceEnum.getDescByType(source);
        if (source == BusinessSourceEnum.OTHER.getType() && StringUtils.isNotEmpty(moeBusiness.getKnowAboutUs())) {
            // 如果source 是other ，note需要加上用户填入的know about us内容
            note += ": " + moeBusiness.getKnowAboutUs();
        }
        module.put("note", note);
        try {
            template = configurer.getConfiguration().getTemplate("register.html");
            html = FreeMarkerTemplateUtils.processTemplateIntoString(template, module);
        } catch (Exception e) {
            log.error("parse [register.html] error", e);
            return;
        }

        // 发送邮件
        MailSendParams mailSendParams = new MailSendParams();
        mailSendParams.setSubject("New Business Register");
        mailSendParams.setHtml(html);

        // 设置发送人
        List<MandrillMessage.Recipient> to = new ArrayList<>();
        MandrillMessage.Recipient recipient = new MandrillMessage.Recipient();

        recipient.setEmail(registerInformEmail);
        recipient.setName("MoeGo");
        recipient.setType(MandrillMessage.Recipient.Type.TO);

        to.add(recipient);
        mailSendParams.setTo(to);
        iEmailClient.sendEmail(mailSendParams);
    }

    private boolean checkCompanyLimit(Long accountId) {
        MoeCompanyExample moeCompanyExample = new MoeCompanyExample();
        moeCompanyExample.createCriteria().andAccountIdEqualTo(accountId.intValue());
        long companyNum = companyMapper.countByExample(moeCompanyExample);
        if (companyNum == 0) {
            return true;
        }

        // 只有 Enterprise account 才允许创建多个 company
        return enterpriseService.checkEnterpriseAccount(accountId);
    }

    @Transactional
    public Long initBusiness(Long companyId, MoeBusiness moeBusiness) {
        long initTime = DateUtil.get10Timestamp();
        moeBusiness.setCompanyId(companyId.intValue());
        if (moeBusiness.getBusinessMode() == null) {
            moeBusiness.setBusinessMode(moeBusiness.getAppType());
        }
        moeBusinessMapper.insertSelective(moeBusiness);
        Long businessId = moeBusiness.getId().longValue();

        initPaymentMethod(companyId, businessId, initTime);

        initBusinessWorkingHour(companyId, businessId.intValue());

        return businessId;
    }

    private void initPaymentMethod(Long companyId, Long businessId, long initTime) {
        MoeBusinessPaymentMethodExample example = new MoeBusinessPaymentMethodExample();
        example.createCriteria().andBusinessIdEqualTo(0).andStatusEqualTo((byte)
                PaymentMethodStatus.PAYMENT_METHOD_STATUS_NORMAL.getNumber());
        List<MoeBusinessPaymentMethod> paymentMethods = paymentMethodMapper.selectByExample(example);

        paymentMethods.forEach(paymentMethod -> {
            paymentMethod.setBusinessId(businessId.intValue());
            paymentMethod.setCompanyId(companyId);
            paymentMethod.setCreateTime(initTime);
            paymentMethod.setUpdateTime(initTime);
            paymentMethodMapper.insertSelective(paymentMethod);
        });
    }

    private Long initTax(Long companyId) {
        MoeBusinessTax tax = MoeBusinessTax.builder()
                .companyId(companyId)
                .createTime(DateUtil.get10Timestamp())
                .updateTime(DateUtil.get10Timestamp())
                .taxRate(0.0)
                .taxName("None")
                .build();
        moeBusinessTaxMapper.insertSelective(tax);
        return tax.getId().longValue();
    }

    private Long initRole(Long companyId) {
        return permissionClient.initRoleAndPermission(companyId);
    }

    private Integer initOwnerStaff(Long companyId, Long businessId, AccountModel accountModel) {
        MoeStaff staff = MoeStaff.builder()
                .accountId((int) accountModel.getId())
                .companyId(companyId.intValue())
                .firstName(accountModel.getFirstName())
                .lastName(accountModel.getLastName())
                .avatarPath(accountModel.getAvatarPath())
                .allowLogin(StaffEnum.ALLOW_LOGIN_TRUE)
                .showOnCalendar(StaffEnum.SHOW_ON_CALENDAR_TRUE)
                .showCalendarStaffAll(StaffEnum.SHOW_CALENDAR_STAFF_ALL_TRUE)
                .accessAllWorkingLocationsStaff(BooleanEnum.VALUE_TRUE)
                .sort(1) // 历史逻辑：默认 staff 的 sort 都是 0，给 owner 赋值大于 0 时，owner 就会在最前
                .employeeCategory(StaffEnum.EMPLOYEE_CATEGORY_OWNER)
                .workingInAllLocations(true)
                .hireDate(DateUtil.get10Timestamp())
                .createTime(DateUtil.get10Timestamp())
                .updateTime(DateUtil.get10Timestamp())
                .lastVisitBusinessId(businessId.intValue())
                .inviteCode(buildStaffInviteCode())
                .build();
        staffMapper.insertSelective(staff);
        return staff.getId();
    }

    /**
     * 初始化 owner staff，会初始化两个staff，
     * 一个是accountModel对应的staff，
     * 另一个是临时的staff 供 enterprise hub staff绑定
     * @param companyId
     * @param businessId
     * @param accountModel
     * @return
     */
    private Integer initAssignOwnerStaff(
            Long companyId, Long businessId, AccountModel accountModel, CreateCompanyFromEnterpriseHubRequest request) {
        MoeStaff activeStaff = MoeStaff.builder()
                .accountId((int) accountModel.getId())
                .companyId(companyId.intValue())
                .firstName(accountModel.getFirstName())
                .lastName(accountModel.getLastName())
                .profileEmail(accountModel.getEmail())
                .avatarPath(accountModel.getAvatarPath())
                .allowLogin(StaffEnum.ALLOW_LOGIN_TRUE)
                .showOnCalendar(StaffEnum.SHOW_ON_CALENDAR_TRUE)
                .showCalendarStaffAll(StaffEnum.SHOW_CALENDAR_STAFF_ALL_TRUE)
                .sort(1) // 历史逻辑：默认 staff 的 sort 都是 0，给 owner 赋值大于 0 时，owner 就会在最前
                .employeeCategory(StaffEnum.EMPLOYEE_CATEGORY_OWNER)
                .workingInAllLocations(true)
                .hireDate(DateUtil.get10Timestamp())
                .createTime(DateUtil.get10Timestamp())
                .updateTime(DateUtil.get10Timestamp())
                .lastVisitBusinessId(businessId.intValue())
                .inviteCode(buildStaffInviteCode())
                .build();
        staffMapper.insertSelective(activeStaff);
        if (request.hasFirstName()) {
            MoeStaff templateStaff = MoeStaff.builder()
                    .accountId(0)
                    .companyId(companyId.intValue())
                    .firstName(request.getFirstName())
                    .lastName(request.getLastName())
                    .profileEmail(request.getEmail())
                    .status(StaffEnum.STATUS_TEMPORARY) // 设置临时状态的owner
                    .allowLogin(StaffEnum.ALLOW_LOGIN_TRUE)
                    .showOnCalendar(StaffEnum.SHOW_ON_CALENDAR_TRUE)
                    .showCalendarStaffAll(StaffEnum.SHOW_CALENDAR_STAFF_ALL_TRUE)
                    .sort(1) // 历史逻辑：默认 staff 的 sort 都是 0，给 owner 赋值大于 0 时，owner 就会在最前
                    .employeeCategory(StaffEnum.EMPLOYEE_CATEGORY_OWNER)
                    .workingInAllLocations(true)
                    .hireDate(DateUtil.get10Timestamp())
                    .createTime(DateUtil.get10Timestamp())
                    .updateTime(DateUtil.get10Timestamp())
                    .lastVisitBusinessId(businessId.intValue())
                    .inviteCode(buildStaffInviteCode())
                    .build();
            staffMapper.insertSelective(templateStaff);
        }

        return activeStaff.getId();
    }

    private void handleEnterpriseCusPaySetting(long enterpriseId, long companyId) {
        AdminEnterpriseCustomizedPaymentSettingView enterpriseExistCusPaySetting =
                paymentClient.getEnterpriseExistCusPaySetting(enterpriseId);
        if (enterpriseExistCusPaySetting.getId() == null || enterpriseExistCusPaySetting.getId() == 0) {
            return;
        }
        paymentClient.createCompanyCusPaymentSetting(companyId, enterpriseExistCusPaySetting);
    }

    private void initBusinessWorkingHour(Long companyId, Integer businessId) {
        MoeBusinessWorkingHour moeBusinessWorkingHour = MoeBusinessWorkingHour.builder()
                .companyId(companyId)
                .businessId(businessId)
                .timeData(DEFAULT_TIME)
                .build();
        moeBusinessWorkingHourMapper.insertSelective(moeBusinessWorkingHour);
    }

    public String buildStaffInviteCode() {
        MoeStaff existStaff;
        String code;
        do {
            code = CommonUtil.getRandomString(32);
            existStaff = staffMapper.queryByInviteCode(code);
        } while (existStaff != null);
        return code;
    }
}
