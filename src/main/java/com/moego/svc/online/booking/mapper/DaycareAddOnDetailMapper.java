package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.DaycareAddOnDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.DaycareAddOnDetail;
import com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface DaycareAddOnDetailMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<DaycareAddOnDetailMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, bookingRequestId, serviceDetailId, petId, addOnId, specificDates, isEveryday, servicePrice, taxId, duration, createdAt, updatedAt, deletedAt, quantityPerDay);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<DaycareAddOnDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<DaycareAddOnDetail> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="DaycareAddOnDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_detail_id", property="serviceDetailId", jdbcType=JdbcType.BIGINT),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.BIGINT),
        @Result(column="add_on_id", property="addOnId", jdbcType=JdbcType.BIGINT),
        @Result(column="specific_dates", property="specificDates", typeHandler=StringToJsonbTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="is_everyday", property="isEveryday", jdbcType=JdbcType.BIT),
        @Result(column="service_price", property="servicePrice", jdbcType=JdbcType.NUMERIC),
        @Result(column="tax_id", property="taxId", jdbcType=JdbcType.BIGINT),
        @Result(column="duration", property="duration", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="quantity_per_day", property="quantityPerDay", jdbcType=JdbcType.INTEGER)
    })
    List<DaycareAddOnDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("DaycareAddOnDetailResult")
    Optional<DaycareAddOnDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, daycareAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, daycareAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    default int insertMultiple(Collection<DaycareAddOnDetail> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, daycareAddOnDetail, c ->
            c.map(bookingRequestId).toProperty("bookingRequestId")
            .map(serviceDetailId).toProperty("serviceDetailId")
            .map(petId).toProperty("petId")
            .map(addOnId).toProperty("addOnId")
            .map(specificDates).toProperty("specificDates")
            .map(isEveryday).toProperty("isEveryday")
            .map(servicePrice).toProperty("servicePrice")
            .map(taxId).toProperty("taxId")
            .map(duration).toProperty("duration")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
            .map(quantityPerDay).toProperty("quantityPerDay")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    default int insertSelective(DaycareAddOnDetail row) {
        return MyBatis3Utils.insert(this::insert, row, daycareAddOnDetail, c ->
            c.map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(serviceDetailId).toPropertyWhenPresent("serviceDetailId", row::getServiceDetailId)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(addOnId).toPropertyWhenPresent("addOnId", row::getAddOnId)
            .map(specificDates).toPropertyWhenPresent("specificDates", row::getSpecificDates)
            .map(isEveryday).toPropertyWhenPresent("isEveryday", row::getIsEveryday)
            .map(servicePrice).toPropertyWhenPresent("servicePrice", row::getServicePrice)
            .map(taxId).toPropertyWhenPresent("taxId", row::getTaxId)
            .map(duration).toPropertyWhenPresent("duration", row::getDuration)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
            .map(quantityPerDay).toPropertyWhenPresent("quantityPerDay", row::getQuantityPerDay)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    default Optional<DaycareAddOnDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, daycareAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    default List<DaycareAddOnDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, daycareAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    default List<DaycareAddOnDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, daycareAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    default Optional<DaycareAddOnDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, daycareAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(DaycareAddOnDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(serviceDetailId).equalTo(row::getServiceDetailId)
                .set(petId).equalTo(row::getPetId)
                .set(addOnId).equalTo(row::getAddOnId)
                .set(specificDates).equalTo(row::getSpecificDates)
                .set(isEveryday).equalTo(row::getIsEveryday)
                .set(servicePrice).equalTo(row::getServicePrice)
                .set(taxId).equalTo(row::getTaxId)
                .set(duration).equalTo(row::getDuration)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt)
                .set(quantityPerDay).equalTo(row::getQuantityPerDay);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(DaycareAddOnDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(serviceDetailId).equalToWhenPresent(row::getServiceDetailId)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(addOnId).equalToWhenPresent(row::getAddOnId)
                .set(specificDates).equalToWhenPresent(row::getSpecificDates)
                .set(isEveryday).equalToWhenPresent(row::getIsEveryday)
                .set(servicePrice).equalToWhenPresent(row::getServicePrice)
                .set(taxId).equalToWhenPresent(row::getTaxId)
                .set(duration).equalToWhenPresent(row::getDuration)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
                .set(quantityPerDay).equalToWhenPresent(row::getQuantityPerDay);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    default int updateByPrimaryKeySelective(DaycareAddOnDetail row) {
        return update(c ->
            c.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(serviceDetailId).equalToWhenPresent(row::getServiceDetailId)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(addOnId).equalToWhenPresent(row::getAddOnId)
            .set(specificDates).equalToWhenPresent(row::getSpecificDates)
            .set(isEveryday).equalToWhenPresent(row::getIsEveryday)
            .set(servicePrice).equalToWhenPresent(row::getServicePrice)
            .set(taxId).equalToWhenPresent(row::getTaxId)
            .set(duration).equalToWhenPresent(row::getDuration)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .set(quantityPerDay).equalToWhenPresent(row::getQuantityPerDay)
            .where(id, isEqualTo(row::getId))
        );
    }
}