package com.moego.svc.online.booking.utils;

import static java.util.stream.Collectors.groupingBy;

import com.moego.idl.models.appointment.v1.LodgingOccupiedStatus;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.util.CollectionUtils;

public class LodgingUtils {
    // 按满载程度由低到高排序
    public static final Map<LodgingOccupiedStatus, Integer> LODGING_STATUS_ORDER_MAP = Map.of(
            LodgingOccupiedStatus.VACANT,
            1,
            LodgingOccupiedStatus.PARTIALLY_OCCUPIED,
            2,
            LodgingOccupiedStatus.FULLY_OCCUPIED,
            3);

    /**
     * 计算一段时间内，每个房间的占用状态。由于每天的占用状态不同，去最大负载的占用状态
     * @param lodgingTypeList 房型列表
     * @param lodgingUnitList 房间列表
     * @param petCntPerLodgingPerDay 房间在一段时间内，每一天的宠物数量
     * @return <key: lodgingUnitId, value: LodgingOccupiedStatus>  lodging 使用状态 1: 未使用 2: 使用中，未满 3: 使用中，已满
     */
    public static Map<Long, LodgingOccupiedStatus> calLodgingStatus(
            List<LodgingTypeModel> lodgingTypeList,
            List<LodgingUnitModel> lodgingUnitList,
            Map<Long, Map<String, Integer>> petCntPerLodgingPerDay) {
        Map<Long, LodgingOccupiedStatus> result = new HashMap<>();
        if (CollectionUtils.isEmpty(lodgingUnitList)) {
            return result;
        }
        Map<Long, List<LodgingUnitModel>> lodgingType2Unit =
                lodgingUnitList.stream().collect(groupingBy(LodgingUnitModel::getLodgingTypeId));
        for (LodgingTypeModel lodgingType : lodgingTypeList) {
            Map<Long, LodgingOccupiedStatus> status =
                    calLodgingStatus(lodgingType, lodgingType2Unit.get(lodgingType.getId()), petCntPerLodgingPerDay);
            result.putAll(status);
        }
        return result;
    }

    static Map<Long, LodgingOccupiedStatus> calLodgingStatus(
            LodgingTypeModel lodgingType,
            List<LodgingUnitModel> lodgingUnitList,
            Map<Long, Map<String, Integer>> petCntPerLodgingPerDay) {
        Map<Long, LodgingOccupiedStatus> result = new HashMap<>();
        if (CollectionUtils.isEmpty(lodgingUnitList)) {
            return result;
        }
        // 初始化所有 lodging 为未使用状态
        for (LodgingUnitModel lodgingUnit : lodgingUnitList) {
            result.put(lodgingUnit.getId(), LodgingOccupiedStatus.VACANT);
        }

        // 计算每一个 lodging 每一天占用状态
        petCntPerLodgingPerDay.forEach((lodgingId, petCntPerDay) -> {
            if (!result.containsKey(lodgingId)) {
                return;
            }
            petCntPerDay.forEach((date, petCnt) -> {
                LodgingOccupiedStatus status = calLodgingStatusPerDay(lodgingType, petCnt);
                if (LODGING_STATUS_ORDER_MAP.get(status) > LODGING_STATUS_ORDER_MAP.get(result.get(lodgingId))) {
                    result.put(lodgingId, status);
                }
            });
        });
        return result;
    }

    /**
     * 计算房型下寄养了 petCnt 只宠物时，房间的占用状态
     * @param lodgingType 房型
     * @param petCnt 宠物数量
     * @return 房间的占用状态
     */
    static LodgingOccupiedStatus calLodgingStatusPerDay(LodgingTypeModel lodgingType, Integer petCnt) {
        if (petCnt <= 0) {
            return LodgingOccupiedStatus.VACANT;
        }
        if (petCnt < lodgingType.getMaxPetNum()) {
            return LodgingOccupiedStatus.PARTIALLY_OCCUPIED;
        }
        return LodgingOccupiedStatus.FULLY_OCCUPIED;
    }
}
