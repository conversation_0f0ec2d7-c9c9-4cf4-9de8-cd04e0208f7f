package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class StaffAvailabilityTimeDayDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_time_day")
    public static final StaffAvailabilityTimeDay staffAvailabilityTimeDay = new StaffAvailabilityTimeDay();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.id")
    public static final SqlColumn<Long> id = staffAvailabilityTimeDay.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.company_id")
    public static final SqlColumn<Long> companyId = staffAvailabilityTimeDay.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.business_id")
    public static final SqlColumn<Long> businessId = staffAvailabilityTimeDay.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.staff_id")
    public static final SqlColumn<Long> staffId = staffAvailabilityTimeDay.staffId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.day_of_week")
    public static final SqlColumn<Integer> dayOfWeek = staffAvailabilityTimeDay.dayOfWeek;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.is_available")
    public static final SqlColumn<Boolean> isAvailable = staffAvailabilityTimeDay.isAvailable;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.limit_ids")
    public static final SqlColumn<List<Long>> limitIds = staffAvailabilityTimeDay.limitIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.created_at")
    public static final SqlColumn<Date> createdAt = staffAvailabilityTimeDay.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.updated_at")
    public static final SqlColumn<Date> updatedAt = staffAvailabilityTimeDay.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_time_day")
    public static final class StaffAvailabilityTimeDay extends AliasableSqlTable<StaffAvailabilityTimeDay> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> staffId = column("staff_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> dayOfWeek = column("day_of_week", JDBCType.INTEGER);

        public final SqlColumn<Boolean> isAvailable = column("is_available", JDBCType.BIT);

        public final SqlColumn<List<Long>> limitIds = column("limit_ids", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler");

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public StaffAvailabilityTimeDay() {
            super("staff_availability_time_day", StaffAvailabilityTimeDay::new);
        }
    }
}