package com.moego.svc.online.booking.helper;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.LocationDateTimeDef;
import com.moego.idl.service.organization.v1.BatchGetCompanyIdRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetWorkingLocationDateTimeRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class BusinessHelper {

    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessStub;

    /**
     * Get company id by business id, throw BizException if not found
     *
     * @param businessId business id
     * @return company id
     */
    public long mustGetCompanyId(long businessId) {
        var companyId = businessStub
                .batchGetCompanyId(BatchGetCompanyIdRequest.newBuilder()
                        .addBusinessIds(businessId)
                        .build())
                .getBusinessCompanyIdMapMap()
                .get(businessId);
        if (companyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company not found for businessId: " + businessId);
        }
        return companyId;
    }

    /**
     * Get date time by business ids
     *
     * @param businessIds business id list
     * @return Map<businessId, current date>
     */
    public Map<Long, String> batchGetBusinessDateTimeMap(List<Long> businessIds) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return Map.of();
        }
        var getWorkingLocationDateTimeResponse =
                businessStub.getWorkingLocationDateTime(GetWorkingLocationDateTimeRequest.newBuilder()
                        .addAllBusinessIds(businessIds)
                        .build());

        return getWorkingLocationDateTimeResponse.getLocationDateTimeList().stream()
                .collect(Collectors.toMap(LocationDateTimeDef::getBusinessId, LocationDateTimeDef::getCurrentDate));
    }

    /**
     * Get current date by business id
     *
     * @param businessId business id
     * @return LocationDateTimeDef, if not found return default current date
     */
    public String getBusinessCurrentDate(Long businessId) {
        Map<Long, String> locationDateTimeMap = batchGetBusinessDateTimeMap(List.of(businessId));
        if (locationDateTimeMap.containsKey(businessId)) {
            return locationDateTimeMap.get(businessId);
        }
        throw ExceptionUtil.bizException(
                Code.CODE_BUSINESS_NOT_FOUND, "current date not found for businessId: " + businessId);
    }
}
