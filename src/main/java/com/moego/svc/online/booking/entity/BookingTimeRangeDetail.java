package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table booking_time_range_detail
 */
public class BookingTimeRangeDetail {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.setting_id")
    private Long settingId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.time_range_type")
    private Integer timeRangeType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.first_week")
    private String firstWeek;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.second_week")
    private String secondWeek;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.third_week")
    private String thirdWeek;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.forth_week")
    private String forthWeek;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.setting_id")
    public Long getSettingId() {
        return settingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.setting_id")
    public void setSettingId(Long settingId) {
        this.settingId = settingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.time_range_type")
    public Integer getTimeRangeType() {
        return timeRangeType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.time_range_type")
    public void setTimeRangeType(Integer timeRangeType) {
        this.timeRangeType = timeRangeType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.first_week")
    public String getFirstWeek() {
        return firstWeek;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.first_week")
    public void setFirstWeek(String firstWeek) {
        this.firstWeek = firstWeek;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.second_week")
    public String getSecondWeek() {
        return secondWeek;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.second_week")
    public void setSecondWeek(String secondWeek) {
        this.secondWeek = secondWeek;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.third_week")
    public String getThirdWeek() {
        return thirdWeek;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.third_week")
    public void setThirdWeek(String thirdWeek) {
        this.thirdWeek = thirdWeek;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.forth_week")
    public String getForthWeek() {
        return forthWeek;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.forth_week")
    public void setForthWeek(String forthWeek) {
        this.forthWeek = forthWeek;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", settingId=").append(settingId);
        sb.append(", timeRangeType=").append(timeRangeType);
        sb.append(", firstWeek=").append(firstWeek);
        sb.append(", secondWeek=").append(secondWeek);
        sb.append(", thirdWeek=").append(thirdWeek);
        sb.append(", forthWeek=").append(forthWeek);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BookingTimeRangeDetail other = (BookingTimeRangeDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSettingId() == null ? other.getSettingId() == null : this.getSettingId().equals(other.getSettingId()))
            && (this.getTimeRangeType() == null ? other.getTimeRangeType() == null : this.getTimeRangeType().equals(other.getTimeRangeType()))
            && (this.getFirstWeek() == null ? other.getFirstWeek() == null : this.getFirstWeek().equals(other.getFirstWeek()))
            && (this.getSecondWeek() == null ? other.getSecondWeek() == null : this.getSecondWeek().equals(other.getSecondWeek()))
            && (this.getThirdWeek() == null ? other.getThirdWeek() == null : this.getThirdWeek().equals(other.getThirdWeek()))
            && (this.getForthWeek() == null ? other.getForthWeek() == null : this.getForthWeek().equals(other.getForthWeek()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSettingId() == null) ? 0 : getSettingId().hashCode());
        result = prime * result + ((getTimeRangeType() == null) ? 0 : getTimeRangeType().hashCode());
        result = prime * result + ((getFirstWeek() == null) ? 0 : getFirstWeek().hashCode());
        result = prime * result + ((getSecondWeek() == null) ? 0 : getSecondWeek().hashCode());
        result = prime * result + ((getThirdWeek() == null) ? 0 : getThirdWeek().hashCode());
        result = prime * result + ((getForthWeek() == null) ? 0 : getForthWeek().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        return result;
    }
}