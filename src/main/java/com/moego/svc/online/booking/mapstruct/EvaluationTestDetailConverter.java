package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.service.online_booking.v1.CreateEvaluationTestDetailRequest;
import com.moego.svc.online.booking.entity.EvaluationTestDetail;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EvaluationTestDetailConverter {
    EvaluationTestDetailConverter INSTANCE = Mappers.getMapper(EvaluationTestDetailConverter.class);

    EvaluationTestDetailModel entityToModel(EvaluationTestDetail entity);

    EvaluationTestDetail createRequestToEntity(CreateEvaluationTestDetailRequest createRequest);

    /*
     * Do NOT use any of the methods below,
     * their purpose is to perform mutual conversions between Protobuf and Java value types.
     */

    default com.google.protobuf.Timestamp dateToPBTimestamp(java.util.Date date) {
        return com.google.protobuf.util.Timestamps.fromDate(date);
    }

    default java.util.Date pbTimestampToDate(com.google.protobuf.Timestamp timestamp) {
        return new java.util.Date(com.google.protobuf.util.Timestamps.toMillis(timestamp));
    }

    default int pbEnumToInt(com.google.protobuf.ProtocolMessageEnum enumValue) {
        return enumValue.getNumber();
    }

    @AfterMapping
    default void setEndDateTime(CreateEvaluationTestDetailRequest source, @MappingTarget EvaluationTestDetail target) {
        LocalDateTime startDateTime = LocalDateTime.of(
                LocalDate.parse(source.getStartDate()), LocalTime.ofSecondOfDay(source.getStartTime() * 60L));
        LocalDateTime endDateTime = startDateTime.plusMinutes(source.getDuration());
        target.setEndDate(endDateTime.toLocalDate().toString());
        target.setEndTime(endDateTime.toLocalTime().toSecondOfDay() / 60);
    }
}
