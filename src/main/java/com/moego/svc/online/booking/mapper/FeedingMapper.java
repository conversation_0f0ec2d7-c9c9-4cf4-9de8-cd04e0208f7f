package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.FeedingDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.Feeding;
import com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface FeedingMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<FeedingMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    BasicColumn[] selectList = BasicColumn.columnList(id, bookingRequestId, serviceDetailId, serviceDetailType, time, amount, unit, foodType, foodSource, instruction, createdAt, updatedAt, deletedAt, note, amountStr);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<Feeding> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<Feeding> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="FeedingResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_detail_id", property="serviceDetailId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_detail_type", property="serviceDetailType", jdbcType=JdbcType.INTEGER),
        @Result(column="time", property="time", typeHandler=StringToJsonbTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="amount", property="amount", jdbcType=JdbcType.NUMERIC),
        @Result(column="unit", property="unit", jdbcType=JdbcType.VARCHAR),
        @Result(column="food_type", property="foodType", jdbcType=JdbcType.VARCHAR),
        @Result(column="food_source", property="foodSource", jdbcType=JdbcType.VARCHAR),
        @Result(column="instruction", property="instruction", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="note", property="note", jdbcType=JdbcType.VARCHAR),
        @Result(column="amount_str", property="amountStr", jdbcType=JdbcType.VARCHAR)
    })
    List<Feeding> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("FeedingResult")
    Optional<Feeding> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, feeding, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, feeding, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    default int insertMultiple(Collection<Feeding> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, feeding, c ->
            c.map(bookingRequestId).toProperty("bookingRequestId")
            .map(serviceDetailId).toProperty("serviceDetailId")
            .map(serviceDetailType).toProperty("serviceDetailType")
            .map(time).toProperty("time")
            .map(amount).toProperty("amount")
            .map(unit).toProperty("unit")
            .map(foodType).toProperty("foodType")
            .map(foodSource).toProperty("foodSource")
            .map(instruction).toProperty("instruction")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
            .map(note).toProperty("note")
            .map(amountStr).toProperty("amountStr")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    default int insertSelective(Feeding row) {
        return MyBatis3Utils.insert(this::insert, row, feeding, c ->
            c.map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(serviceDetailId).toPropertyWhenPresent("serviceDetailId", row::getServiceDetailId)
            .map(serviceDetailType).toPropertyWhenPresent("serviceDetailType", row::getServiceDetailType)
            .map(time).toPropertyWhenPresent("time", row::getTime)
            .map(amount).toPropertyWhenPresent("amount", row::getAmount)
            .map(unit).toPropertyWhenPresent("unit", row::getUnit)
            .map(foodType).toPropertyWhenPresent("foodType", row::getFoodType)
            .map(foodSource).toPropertyWhenPresent("foodSource", row::getFoodSource)
            .map(instruction).toPropertyWhenPresent("instruction", row::getInstruction)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
            .map(note).toPropertyWhenPresent("note", row::getNote)
            .map(amountStr).toPropertyWhenPresent("amountStr", row::getAmountStr)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    default Optional<Feeding> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, feeding, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    default List<Feeding> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, feeding, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    default List<Feeding> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, feeding, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    default Optional<Feeding> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, feeding, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    static UpdateDSL<UpdateModel> updateAllColumns(Feeding row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(serviceDetailId).equalTo(row::getServiceDetailId)
                .set(serviceDetailType).equalTo(row::getServiceDetailType)
                .set(time).equalTo(row::getTime)
                .set(amount).equalTo(row::getAmount)
                .set(unit).equalTo(row::getUnit)
                .set(foodType).equalTo(row::getFoodType)
                .set(foodSource).equalTo(row::getFoodSource)
                .set(instruction).equalTo(row::getInstruction)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt)
                .set(note).equalTo(row::getNote)
                .set(amountStr).equalTo(row::getAmountStr);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(Feeding row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(serviceDetailId).equalToWhenPresent(row::getServiceDetailId)
                .set(serviceDetailType).equalToWhenPresent(row::getServiceDetailType)
                .set(time).equalToWhenPresent(row::getTime)
                .set(amount).equalToWhenPresent(row::getAmount)
                .set(unit).equalToWhenPresent(row::getUnit)
                .set(foodType).equalToWhenPresent(row::getFoodType)
                .set(foodSource).equalToWhenPresent(row::getFoodSource)
                .set(instruction).equalToWhenPresent(row::getInstruction)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
                .set(note).equalToWhenPresent(row::getNote)
                .set(amountStr).equalToWhenPresent(row::getAmountStr);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    default int updateByPrimaryKeySelective(Feeding row) {
        return update(c ->
            c.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(serviceDetailId).equalToWhenPresent(row::getServiceDetailId)
            .set(serviceDetailType).equalToWhenPresent(row::getServiceDetailType)
            .set(time).equalToWhenPresent(row::getTime)
            .set(amount).equalToWhenPresent(row::getAmount)
            .set(unit).equalToWhenPresent(row::getUnit)
            .set(foodType).equalToWhenPresent(row::getFoodType)
            .set(foodSource).equalToWhenPresent(row::getFoodSource)
            .set(instruction).equalToWhenPresent(row::getInstruction)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .set(note).equalToWhenPresent(row::getNote)
            .set(amountStr).equalToWhenPresent(row::getAmountStr)
            .where(id, isEqualTo(row::getId))
        );
    }
}