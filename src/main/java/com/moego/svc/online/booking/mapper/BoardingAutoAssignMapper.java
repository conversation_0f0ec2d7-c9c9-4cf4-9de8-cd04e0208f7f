package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BoardingAutoAssignDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.BoardingAutoAssign;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface BoardingAutoAssignMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<BoardingAutoAssignMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    BasicColumn[] selectList = BasicColumn.columnList(id, bookingRequestId, boardingServiceDetailId, lodgingId, createdAt, updatedAt, deletedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<BoardingAutoAssign> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<BoardingAutoAssign> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="BoardingAutoAssignResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="boarding_service_detail_id", property="boardingServiceDetailId", jdbcType=JdbcType.BIGINT),
        @Result(column="lodging_id", property="lodgingId", jdbcType=JdbcType.BIGINT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<BoardingAutoAssign> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("BoardingAutoAssignResult")
    Optional<BoardingAutoAssign> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, boardingAutoAssign, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, boardingAutoAssign, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    default int insertMultiple(Collection<BoardingAutoAssign> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, boardingAutoAssign, c ->
            c.map(bookingRequestId).toProperty("bookingRequestId")
            .map(boardingServiceDetailId).toProperty("boardingServiceDetailId")
            .map(lodgingId).toProperty("lodgingId")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    default int insertSelective(BoardingAutoAssign row) {
        return MyBatis3Utils.insert(this::insert, row, boardingAutoAssign, c ->
            c.map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(boardingServiceDetailId).toPropertyWhenPresent("boardingServiceDetailId", row::getBoardingServiceDetailId)
            .map(lodgingId).toPropertyWhenPresent("lodgingId", row::getLodgingId)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    default Optional<BoardingAutoAssign> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, boardingAutoAssign, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    default List<BoardingAutoAssign> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, boardingAutoAssign, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    default List<BoardingAutoAssign> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, boardingAutoAssign, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    default Optional<BoardingAutoAssign> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, boardingAutoAssign, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    static UpdateDSL<UpdateModel> updateAllColumns(BoardingAutoAssign row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(boardingServiceDetailId).equalTo(row::getBoardingServiceDetailId)
                .set(lodgingId).equalTo(row::getLodgingId)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(BoardingAutoAssign row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(boardingServiceDetailId).equalToWhenPresent(row::getBoardingServiceDetailId)
                .set(lodgingId).equalToWhenPresent(row::getLodgingId)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    default int updateByPrimaryKeySelective(BoardingAutoAssign row) {
        return update(c ->
            c.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(boardingServiceDetailId).equalToWhenPresent(row::getBoardingServiceDetailId)
            .set(lodgingId).equalToWhenPresent(row::getLodgingId)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}