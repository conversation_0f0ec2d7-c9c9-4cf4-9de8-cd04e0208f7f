package com.moego.svc.online.booking.helper;

import static com.moego.common.enums.ServiceItemEnum.getMainServiceItemType;
import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.order.LineApplyType;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.appointment.v2.PricingRuleApplySourceType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.grooming.v1.AppointmentSource;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v2.PetDetailCalculateDef;
import com.moego.idl.models.offering.v2.PetDetailCalculateResultDef;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import com.moego.idl.models.order.v1.OrderLineTaxModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.models.order.v1.OrderStatus;
import com.moego.idl.models.organization.v1.TaxRuleModel;
import com.moego.idl.service.appointment.v2.ApplyPricingRuleRequest;
import com.moego.idl.service.appointment.v2.PricingRuleApplyServiceGrpc;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetBookingRequestRequest;
import com.moego.idl.service.order.v1.CreateOrderRequest;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.order.v1.UpdateOrderIncrRequest;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.server.grooming.api.IInvoiceApplyPackageService;
import com.moego.svc.online.booking.service.BookingRequestService;
import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/11/27
 */
@Component
@RequiredArgsConstructor
public class OrderHelper {

    private final OrderServiceGrpc.OrderServiceBlockingStub orderStub;
    private final BookingRequestService bookingRequestService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;
    private final ServiceHelper serviceHelper;
    private final TaxHelper taxHelper;
    private final PricingRuleApplyServiceGrpc.PricingRuleApplyServiceBlockingStub pricingRuleApplyStub;
    private final CustomerHelper customerHelper;
    private final IInvoiceApplyPackageService invoiceApplyPackageApi;
    private final FeatureFlagApi featureFlagApi;

    /**
     * Get order detail by source type and source id.
     *
     * @param sourceType source type
     * @param sourceId   source id
     * @return order detail
     */
    @Nullable
    public OrderDetailModel getBySource(OrderSourceType sourceType, long sourceId, boolean latest) {
        var orderDetail = orderStub.getOrderDetail(GetOrderRequest.newBuilder()
                .setSourceId(sourceId)
                .setSourceType(sourceType.name().toLowerCase())
                .setLatest(latest)
                .build());
        return isNormal(orderDetail.getOrder().getId()) ? orderDetail : null;
    }

    /**
     * Update order from booking request, refresh order using latest booking request data.
     *
     * @param bookingRequestId booking request  id
     */
    public void updateOrderForBookingRequest(long bookingRequestId) {
        var bookingRequestModel = bookingRequestService.mustGetBookingRequestModel(GetBookingRequestRequest.newBuilder()
                .setId(bookingRequestId)
                .addAllAssociatedModels(
                        List.of(BookingRequestAssociatedModel.SERVICE, BookingRequestAssociatedModel.ADD_ON))
                .build());

        if (isNewOrderFlow(bookingRequestModel.getCompanyId())) {
            // In the new order flow, the order is immutable.
            return;
        }

        var order = getOrder(bookingRequestModel);
        if (order == null) {
            return;
        }

        doUpdateOrderForBookingRequest(bookingRequestModel, order);
    }

    private boolean isNewOrderFlow(long companyId) {
        return featureFlagApi.isOn(
                FeatureFlags.NEW_ORDER_FLOW,
                FeatureFlagContext.builder().company(companyId).build());
    }

    private void doUpdateOrderForBookingRequest(BookingRequestModel bookingRequestModel, OrderDetailModel order) {

        var serviceIdToService = serviceHelper.listService(extractServiceIdsForBookingRequest(bookingRequestModel));
        var evaluationIdToEvaluation = serviceHelper.getEvaluationByIds(
                extractEvaluationIds(bookingRequestModel), bookingRequestModel.getBusinessId());

        var lineItems =
                buildOrderLineItemsForBookingRequest(bookingRequestModel, serviceIdToService, evaluationIdToEvaluation);

        var updateRequest = UpdateOrderIncrRequest.newBuilder()
                .setOrderId(order.getOrder().getId())
                .addAllLineItems(mergeWithExistingLineItems(lineItems, order.getLineItemsList()))
                .build();

        orderStub.updateOrderIncremental(updateRequest);

        // 更新 package 的占用情况
        refreshPackageUsage(order.getOrder().getId());
    }

    private void refreshPackageUsage(long orderId) {
        invoiceApplyPackageApi.updateAppliedPackageForOrder(IInvoiceApplyPackageService.UpdateForOrderParam.builder()
                .orderId(orderId)
                .build());
    }

    private static List<OrderLineItemModel> mergeWithExistingLineItems(
            List<OrderLineItemModel> newLineItems, List<OrderLineItemModel> existingLineItems) {
        // 逻辑参考：com.moego.svc.appointment.service.remote.OrderRemoteService.buildCombinedItemModels
        var deletedItems = existingLineItems.stream()
                .filter(item -> Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_SERVICE.getType())
                        || Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_EVALUATION_SERVICE.getType()))
                .map(item -> item.toBuilder().setIsDeleted(true).build())
                .toList();
        return Stream.concat(newLineItems.stream(), deletedItems.stream()).toList();
    }

    /**
     * Create order from booking request.
     *
     * <p> Do nothing if order already exists.
     *
     * @param bookingRequestId booking request id
     * @return order id
     */
    public long createOrderForBookingRequest(long bookingRequestId) {
        var bookingRequestModel = bookingRequestService.mustGetBookingRequestModel(GetBookingRequestRequest.newBuilder()
                .setId(bookingRequestId)
                .addAllAssociatedModels(
                        List.of(BookingRequestAssociatedModel.SERVICE, BookingRequestAssociatedModel.ADD_ON))
                .build());

        var order = getOrder(bookingRequestModel);
        if (order != null) {
            return order.getOrder().getId();
        }

        return doCreateOrderByBookingRequestId(bookingRequestModel);
    }

    private long doCreateOrderByBookingRequestId(BookingRequestModel bookingRequestModel) {

        var serviceIdToService = serviceHelper.listService(extractServiceIdsForBookingRequest(bookingRequestModel));
        var evaluationIdToEvaluation = serviceHelper.getEvaluationByIds(
                extractEvaluationIds(bookingRequestModel), bookingRequestModel.getBusinessId());

        var order = buildOrderFromBookingRequest(bookingRequestModel, serviceIdToService, evaluationIdToEvaluation);
        var orderLineItems =
                buildOrderLineItemsForBookingRequest(bookingRequestModel, serviceIdToService, evaluationIdToEvaluation);

        var resp = orderStub.createOrder(CreateOrderRequest.newBuilder()
                .setOrder(order)
                .addAllLineItems(orderLineItems)
                .build());

        return resp.getId();
    }

    private static String buildTitle(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, EvaluationBriefView> evaluationIdToEvaluation) {
        return bookingRequest.getServicesList().stream()
                .map(service -> switch (service.getServiceCase()) {
                    case GROOMING -> {
                        var serviceId = service.getGrooming().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceBriefView::getName)
                                .orElse(null);
                    }
                    case BOARDING -> {
                        var serviceId = service.getBoarding().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceBriefView::getName)
                                .orElse(null);
                    }
                    case DAYCARE -> {
                        var serviceId = service.getDaycare().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceBriefView::getName)
                                .orElse(null);
                    }
                    case EVALUATION -> {
                        var evaluationId = service.getEvaluation().getService().getEvaluationId();
                        yield Optional.ofNullable(evaluationIdToEvaluation.get(evaluationId))
                                .map(EvaluationBriefView::getName)
                                .orElse(null);
                    }
                    case DOG_WALKING -> {
                        var serviceId = service.getDogWalking().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceBriefView::getName)
                                .orElse(null);
                    }
                    case GROUP_CLASS -> {
                        var serviceId = service.getGroupClass().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceBriefView::getName)
                                .orElse(null);
                    }
                    default -> null;
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.joining(", "));
    }

    private OrderModel buildOrderFromBookingRequest(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, EvaluationBriefView> evaluationIdToEvaluation) {

        var customer = customerHelper.mustGetCustomer(bookingRequest.getCustomerId());

        var desc = String.format("%s %s", customer.getFirstName(), customer.getLastName());
        var title = buildTitle(bookingRequest, serviceIdToService, evaluationIdToEvaluation);

        return OrderModel.newBuilder()
                .setCompanyId(bookingRequest.getCompanyId())
                .setBusinessId(bookingRequest.getBusinessId())
                .setCustomerId(bookingRequest.getCustomerId())
                .setSourceType(OrderSourceType.BOOKING_REQUEST.name().toLowerCase())
                .setStatus(OrderStatus.CREATED.getNumber())
                .setSource(AppointmentSource.APPOINTMENT_SOURCE_OB)
                .setSourceId(bookingRequest.getId())
                .setDescription(desc)
                .setTitle(title)
                .setCreateBy(0)
                .setUpdateBy(0)
                .build();
    }

    @Nullable
    private OrderDetailModel getOrder(BookingRequestModel bookingRequest) {
        var order = getBySource(OrderSourceType.BOOKING_REQUEST, bookingRequest.getId(), true);
        if (order != null) {
            return order;
        }

        if (getMainServiceItemType(bookingRequest.getServiceTypeInclude()) == ServiceItemEnum.GROOMING
                && isNormal(bookingRequest.getAppointmentId())) {
            return getBySource(OrderSourceType.APPOINTMENT, bookingRequest.getAppointmentId(), true);
        }

        return null;
    }

    private List<OrderLineItemModel> buildOrderLineItemsForBookingRequest(
            BookingRequestModel bookingRequestModel,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, EvaluationBriefView> evaluationIdToEvaluation) {
        var taxIdToTax =
                taxHelper.listTax(extractTaxIds(serviceIdToService.values(), evaluationIdToEvaluation.values()));
        var pricingRuleResults = calculatePricingRuleResults(
                bookingRequestModel, serviceIdToService, listCustomizedService(bookingRequestModel));

        return buildLineItemsForServiceDetails(
                bookingRequestModel, serviceIdToService, evaluationIdToEvaluation, taxIdToTax, pricingRuleResults);
    }

    private static List<OrderLineItemModel> buildLineItemsForServiceDetails(
            BookingRequestModel bookingRequestModel,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, EvaluationBriefView> evaluationIdToEvaluation,
            Map<Long, TaxRuleModel> taxIdToTax,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var petDetails = bookingRequestModel.getServicesList();
        if (petDetails.isEmpty()) {
            return List.of();
        }

        var mainServiceItemType = getMainServiceItemType(bookingRequestModel.getServiceTypeInclude());

        var result =
                switch (mainServiceItemType) {
                    case BOARDING -> buildLineItemsForBoarding(
                            bookingRequestModel, serviceIdToService, taxIdToTax, pricingRuleResults);
                    case DAYCARE -> buildLineItemsForDaycare(
                            bookingRequestModel, serviceIdToService, taxIdToTax, pricingRuleResults);
                    case GROOMING -> buildLineItemsForGrooming(bookingRequestModel, serviceIdToService, taxIdToTax);
                    case EVALUATION -> buildLineItemsForEvaluation(bookingRequestModel, evaluationIdToEvaluation);
                    case DOG_WALKING -> buildLineItemsForDogWalking(
                            bookingRequestModel, serviceIdToService, taxIdToTax);
                    case GROUP_CLASS -> buildLineItemsForGroupClass(
                            bookingRequestModel, serviceIdToService, taxIdToTax);
                    default -> throw bizException(
                            Code.CODE_PARAMS_ERROR, "Unsupported service type: " + mainServiceItemType);
                };

        return mergeOrderLineItems(result);
    }

    private static List<OrderLineItemModel> buildLineItemsForDogWalking(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var dogWalkingService : getDogWalkingServices(bookingRequest)) {
            result.addAll(
                    buildLineItemForDogWalking(bookingRequest, serviceIdToService, taxIdToTax, dogWalkingService));
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemForDogWalking(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            BookingRequestModel.DogWalkingService dogWalking) {

        var petDetail = dogWalking.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        var builder = OrderLineItemModel.newBuilder();
        builder.setBusinessId(bookingRequest.getBusinessId());
        builder.setObjectId(petDetail.getServiceId());
        builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        builder.setName(service.getName());
        builder.setDescription(service.getDescription());
        builder.setUnitPrice(petDetail.getServicePrice());
        builder.setQuantity(1);

        var taxId = service.getTaxId();
        if (taxId > 0) {
            var tax = Optional.ofNullable(taxIdToTax.get(taxId))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
            builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
        }

        return List.of(builder.build());
    }

    private static OrderLineTaxModel buildOrderLineTax(BookingRequestModel bookingRequest, TaxRuleModel tax) {
        return OrderLineTaxModel.newBuilder()
                .setBusinessId(bookingRequest.getBusinessId())
                .setApplyType(LineApplyType.TYPE_ITEM.getType())
                .setTaxId(tax.getId())
                .setTaxRate(tax.getRate())
                .build();
    }

    private static List<OrderLineItemModel> buildLineItemsForGrooming(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var groomingService : getGroomingServices(bookingRequest)) {
            result.addAll(buildLineItemForGrooming(bookingRequest, serviceIdToService, taxIdToTax, groomingService));
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemsForEvaluation(
            BookingRequestModel bookingRequest, Map<Long, EvaluationBriefView> evaluationIdToEvaluation) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var evaluationService : getEvaluationServices(bookingRequest)) {
            result.add(buildLineItemForEvaluation(bookingRequest, evaluationIdToEvaluation, evaluationService));
        }

        return result;
    }

    private static OrderLineItemModel buildLineItemForEvaluation(
            BookingRequestModel bookingRequest,
            Map<Long, EvaluationBriefView> evaluationIdToEvaluation,
            BookingRequestModel.EvaluationService evaluation) {

        var petDetail = evaluation.getService();
        var evaluationModel = Optional.ofNullable(evaluationIdToEvaluation.get(petDetail.getEvaluationId()))
                .orElseThrow(() ->
                        bizException(Code.CODE_PARAMS_ERROR, "Evaluation not found: " + petDetail.getEvaluationId()));

        var builder = OrderLineItemModel.newBuilder();
        builder.setPetId(petDetail.getPetId());
        builder.setBusinessId(bookingRequest.getBusinessId());
        builder.setObjectId(petDetail.getEvaluationId());
        builder.setType(OrderItemType.ITEM_TYPE_EVALUATION_SERVICE.getType());
        builder.setName(evaluationModel.getName());
        builder.setUnitPrice(petDetail.getServicePrice());
        builder.setQuantity(1);

        return builder.build();
    }

    private static List<OrderLineItemModel> buildLineItemsForBoarding(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var boardingService : getBoardingServices(bookingRequest)) {
            result.addAll(buildLineItemForBoarding(
                    bookingRequest, serviceIdToService, taxIdToTax, boardingService, pricingRuleResults));
        }

        for (var groomingService : getGroomingServices(bookingRequest)) {
            result.addAll(buildLineItemForGrooming(bookingRequest, serviceIdToService, taxIdToTax, groomingService));
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemsForDaycare(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var result = new ArrayList<OrderLineItemModel>();

        // 特别注意：daycare 只收第一天的钱

        String firstDate = getFirstDateForDaycare(bookingRequest);

        var daycareServices = getDaycareServices(bookingRequest).stream()
                .filter(e -> e.getService().getSpecificDatesList().contains(firstDate))
                .toList();

        for (var daycareService : daycareServices) {
            result.addAll(buildLineItemForDaycare(
                    bookingRequest, serviceIdToService, taxIdToTax, daycareService, pricingRuleResults));
        }

        var groomingServicesInFirstDate = getGroomingServices(bookingRequest).stream()
                .filter(e -> Objects.equals(e.getService().getStartDate(), firstDate))
                .toList();

        for (var groomingService : groomingServicesInFirstDate) {
            result.addAll(buildLineItemForGrooming(bookingRequest, serviceIdToService, taxIdToTax, groomingService));
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemForDaycare(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            BookingRequestModel.DaycareService daycare,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var petDetail = daycare.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        // NOTE: 这里有个特殊逻辑：daycare 为多天时，只计算第一天的价格
        // See https://moego.atlassian.net/browse/MER-1091
        var date =
                petDetail.getSpecificDatesList().stream().sorted().findFirst().orElse(null);

        if (date == null) {
            return List.of();
        }

        var result = new ArrayList<OrderLineItemModel>();

        // service
        var serviceBuilder = OrderLineItemModel.newBuilder();
        serviceBuilder.setBusinessId(bookingRequest.getBusinessId());
        serviceBuilder.setPetId(petDetail.getPetId());
        serviceBuilder.setObjectId(petDetail.getServiceId());
        serviceBuilder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        serviceBuilder.setName(service.getName());
        serviceBuilder.setDescription(service.getDescription());
        serviceBuilder.setUnitPrice(getPrice(pricingRuleResults, petDetail, date));
        serviceBuilder.setQuantity(1);

        var serviceTaxId = service.getTaxId();
        if (serviceTaxId > 0) {
            var tax = Optional.ofNullable(taxIdToTax.get(serviceTaxId))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + serviceTaxId));
            serviceBuilder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
        }

        result.add(serviceBuilder.build());

        // addons
        for (var addon : daycare.getAddonsList()) {
            if (!addon.getIsEveryday() && !addon.getSpecificDatesList().contains(date)) { // 只算第一天的 addon
                continue;
            }

            var addonModel = Optional.ofNullable(serviceIdToService.get(addon.getAddOnId()))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Addon not found: " + addon.getAddOnId()));

            var builder = OrderLineItemModel.newBuilder();
            builder.setBusinessId(bookingRequest.getBusinessId());
            builder.setPetId(addon.getPetId());
            builder.setObjectId(addon.getAddOnId());
            builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
            builder.setName(addonModel.getName());
            builder.setDescription(addonModel.getDescription());
            builder.setUnitPrice(addon.getServicePrice());
            builder.setQuantity(addon.getQuantityPerDay());

            var taxId = addonModel.getTaxId();
            if (taxId > 0) {
                var tax = Optional.ofNullable(taxIdToTax.get(taxId))
                        .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
                builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
            }

            result.add(builder.build());
        }

        return result;
    }

    private static Double getPrice(
            List<PetDetailCalculateResultDef> pricingRuleResults, DaycareServiceDetailModel petDetail, String date) {
        return pricingRuleResults.stream()
                .filter(e -> e.getPetId() == petDetail.getPetId()
                        && e.getServiceId() == petDetail.getServiceId()
                        && Objects.equals(e.getServiceDate(), date))
                .findFirst()
                .map(PetDetailCalculateResultDef::getAdjustedPrice)
                .orElseGet(petDetail::getServicePrice);
    }

    private static String getFirstDateForDaycare(BookingRequestModel bookingRequest) {
        return getDaycareServices(bookingRequest).stream()
                .flatMap(e -> e.getService().getSpecificDatesList().stream())
                .sorted()
                .findFirst()
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "There is no specific date for daycare service"));
    }

    private static List<OrderLineItemModel> buildLineItemsForGroupClass(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var groupClassService : getGroupClassServices(bookingRequest)) {
            result.addAll(
                    buildLineItemForGroupClass(bookingRequest, serviceIdToService, taxIdToTax, groupClassService));
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemForGroupClass(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            BookingRequestModel.GroupClassService groupClass) {

        var petDetail = groupClass.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        var builder = OrderLineItemModel.newBuilder();
        builder.setBusinessId(bookingRequest.getBusinessId());
        builder.setObjectId(petDetail.getServiceId());
        builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        builder.setName(service.getName());
        builder.setDescription(service.getDescription());
        builder.setUnitPrice(petDetail.getServicePrice());
        builder.setQuantity(1);

        var taxId = service.getTaxId();
        if (taxId > 0) {
            var tax = Optional.ofNullable(taxIdToTax.get(taxId))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
            builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
        }

        return List.of(builder.build());
    }

    private static List<OrderLineItemModel> buildLineItemForBoarding(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            BookingRequestModel.BoardingService boarding,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var petDetail = boarding.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        var result = new ArrayList<OrderLineItemModel>();

        var start = LocalDate.parse(petDetail.getStartDate());
        var end = isCalculateByDay(service)
                ? LocalDate.parse(petDetail.getEndDate()).plusDays(1)
                : LocalDate.parse(petDetail.getEndDate());

        var dates = start.datesUntil(end).toList();

        for (var date : dates) {
            var price = pricingRuleResults.stream()
                    .filter(e -> e.getPetId() == petDetail.getPetId()
                            && e.getServiceId() == petDetail.getServiceId()
                            && Objects.equals(e.getServiceDate(), date.toString()))
                    .findFirst()
                    .map(PetDetailCalculateResultDef::getAdjustedPrice)
                    .orElseGet(petDetail::getServicePrice);

            var builder = OrderLineItemModel.newBuilder();
            builder.setBusinessId(bookingRequest.getBusinessId());
            builder.setPetId(petDetail.getPetId());
            builder.setObjectId(petDetail.getServiceId());
            builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
            builder.setName(service.getName());
            builder.setDescription(service.getDescription());
            builder.setUnitPrice(price);
            builder.setQuantity(1);

            var taxId = service.getTaxId();
            if (taxId > 0) {
                var tax = Optional.ofNullable(taxIdToTax.get(taxId))
                        .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
                builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
            }

            result.add(builder.build());
        }

        for (var addon : boarding.getAddonsList()) {

            var nightCount = LocalDate.parse(petDetail.getStartDate())
                    .datesUntil(LocalDate.parse(petDetail.getEndDate()))
                    .count();

            long count =
                    switch (addon.getDateType()) {
                        case PET_DETAIL_DATE_EVERYDAY, PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> nightCount;
                        case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> nightCount + 1;
                        case PET_DETAIL_DATE_SPECIFIC_DATE -> addon.getSpecificDatesCount();
                        case PET_DETAIL_DATE_DATE_POINT, PET_DETAIL_DATE_LAST_DAY, PET_DETAIL_DATE_FIRST_DAY -> 1;
                        default -> throw bizException(
                                Code.CODE_PARAMS_ERROR, "Invalid date type: " + addon.getDateType());
                    };

            var addonModel = Optional.ofNullable(serviceIdToService.get(addon.getAddOnId()))
                    .orElseGet(ServiceBriefView::getDefaultInstance);

            for (int i = 0; i < count; i++) {
                var builder = OrderLineItemModel.newBuilder();
                builder.setBusinessId(bookingRequest.getBusinessId());
                builder.setPetId(addon.getPetId());
                builder.setObjectId(addon.getAddOnId());
                builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
                builder.setName(addonModel.getName());
                builder.setDescription(addonModel.getDescription());
                builder.setUnitPrice(addon.getServicePrice());
                builder.setQuantity(addon.getQuantityPerDay());

                var taxId = addonModel.getTaxId();
                if (taxId > 0) {
                    var tax = Optional.ofNullable(taxIdToTax.get(taxId))
                            .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
                    builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
                }

                result.add(builder.build());
            }
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemForGrooming(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            BookingRequestModel.GroomingService grooming) {
        var result = new ArrayList<OrderLineItemModel>();

        result.add(buildLineItemForGroomingService(bookingRequest, serviceIdToService, taxIdToTax, grooming));

        for (var addon : grooming.getAddonsList()) {
            result.add(buildLintItemForGroomingAddon(bookingRequest, serviceIdToService, taxIdToTax, addon));
        }

        return result;
    }

    private static OrderLineItemModel buildLintItemForGroomingAddon(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            GroomingAddOnDetailModel addon) {
        var addonModel = Optional.ofNullable(serviceIdToService.get(addon.getAddOnId()))
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Addon not found: " + addon.getAddOnId()));

        var addonBuilder = OrderLineItemModel.newBuilder();
        addonBuilder.setBusinessId(bookingRequest.getBusinessId());
        addonBuilder.setPetId(addon.getPetId());
        addonBuilder.setObjectId(addon.getAddOnId());
        addonBuilder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        addonBuilder.setName(addonModel.getName());
        addonBuilder.setDescription(addonModel.getDescription());
        addonBuilder.setUnitPrice(addon.getServicePrice());
        addonBuilder.setQuantity(1);

        var addonTaxId = addonModel.getTaxId();
        if (addonTaxId > 0) {
            var tax = Optional.ofNullable(taxIdToTax.get(addonTaxId))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + addonTaxId));
            addonBuilder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
        }

        return addonBuilder.build();
    }

    private static OrderLineItemModel buildLineItemForGroomingService(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            BookingRequestModel.GroomingService grooming) {
        var petDetail = grooming.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        var builder = OrderLineItemModel.newBuilder();
        builder.setBusinessId(bookingRequest.getBusinessId());
        builder.setPetId(petDetail.getPetId());
        builder.setObjectId(petDetail.getServiceId());
        builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        builder.setName(service.getName());
        builder.setDescription(service.getDescription());
        builder.setUnitPrice(petDetail.getServicePrice());
        builder.setQuantity(1);

        var taxId = service.getTaxId();
        if (taxId > 0) {
            var tax = Optional.ofNullable(taxIdToTax.get(taxId))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
            builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
        }

        return builder.build();
    }

    private List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> listCustomizedService(
            BookingRequestModel bookingRequestModel) {
        var queryConditions = buildQueryConditions(bookingRequestModel);
        if (queryConditions.isEmpty()) {
            return List.of();
        }

        var builder = BatchGetCustomizedServiceRequest.newBuilder();
        builder.setCompanyId(bookingRequestModel.getCompanyId());
        builder.addAllQueryConditionList(queryConditions);

        return serviceStub.batchGetCustomizedService(builder.build()).getCustomizedServiceListList();
    }

    private static List<BookingRequestModel.BoardingService> getBoardingServices(BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasBoarding)
                .map(BookingRequestModel.Service::getBoarding)
                .toList();
    }

    private static List<BookingRequestModel.DaycareService> getDaycareServices(BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasDaycare)
                .map(BookingRequestModel.Service::getDaycare)
                .toList();
    }

    private static List<BookingRequestModel.GroomingService> getGroomingServices(BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .map(BookingRequestModel.Service::getGrooming)
                .toList();
    }

    private static List<BookingRequestModel.EvaluationService> getEvaluationServices(
            BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .map(BookingRequestModel.Service::getEvaluation)
                .toList();
    }

    private static List<BookingRequestModel.DogWalkingService> getDogWalkingServices(
            BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasDogWalking)
                .map(BookingRequestModel.Service::getDogWalking)
                .toList();
    }

    private static List<BookingRequestModel.GroupClassService> getGroupClassServices(
            BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasGroupClass)
                .map(BookingRequestModel.Service::getGroupClass)
                .toList();
    }

    private static List<OrderLineItemModel> mergeOrderLineItems(List<OrderLineItemModel> lineItems) {
        var uniqKeyToLineItem = lineItems.stream()
                .collect(Collectors.groupingBy(
                        e -> e.getPetId() + ":" + e.getObjectId() + ":" + e.getUnitPrice(),
                        Collectors.collectingAndThen(Collectors.toList(), list -> {
                            var builder = list.get(0).toBuilder();
                            builder.setQuantity(list.stream()
                                    .mapToInt(OrderLineItemModel::getQuantity)
                                    .sum());
                            return builder.build();
                        })));
        return List.copyOf(uniqKeyToLineItem.values());
    }

    private static List<CustomizedServiceQueryCondition> buildQueryConditions(BookingRequestModel bookingRequestModel) {
        var queryConditions = new ArrayList<CustomizedServiceQueryCondition>();
        for (var petIdAndServiceDetails :
                buildPetIdToServiceDetails(bookingRequestModel).entrySet()) {
            var petId = petIdAndServiceDetails.getKey();
            var serviceDetails = petIdAndServiceDetails.getValue();
            for (var serviceDetail : serviceDetails) {
                for (var serviceId : extractServiceIdsForServiceDetail(serviceDetail)) {
                    var condBuilder = CustomizedServiceQueryCondition.newBuilder()
                            .setServiceId(serviceId)
                            .setBusinessId(bookingRequestModel.getBusinessId());
                    var staffId = extractStaffId(serviceDetail);
                    if (isNormal(staffId)) {
                        condBuilder.setStaffId(staffId);
                    }
                    if (isNormal(petId)) {
                        condBuilder.setPetId(petId);
                    }
                    queryConditions.add(condBuilder.build());
                }
            }
        }
        return queryConditions;
    }

    private static Map<Long, List<BookingRequestModel.Service>> buildPetIdToServiceDetails(
            BookingRequestModel bookingRequestModel) {
        return bookingRequestModel.getServicesList().stream().collect(Collectors.groupingBy(OrderHelper::extractPetId));
    }

    private static Set<Long> extractEvaluationIds(BookingRequestModel bookingRequestModel) {
        return bookingRequestModel.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .map(service -> service.getEvaluation().getService().getEvaluationId())
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());
    }

    private static Set<Long> extractServiceIdsForBookingRequest(BookingRequestModel bookingRequestModel) {
        return bookingRequestModel.getServicesList().stream()
                .map(OrderHelper::extractServiceIdsForServiceDetail)
                .flatMap(Collection::stream)
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());
    }

    private static Set<Long> extractServiceIdsForServiceDetail(BookingRequestModel.Service service) {
        return switch (service.getServiceCase()) {
            case GROOMING -> {
                var serviceIds = new HashSet<Long>();
                var serviceId = service.getGrooming().getService().getServiceId();
                serviceIds.add(serviceId);
                for (var addon : service.getGrooming().getAddonsList()) {
                    serviceIds.add(addon.getAddOnId());
                }
                yield serviceIds;
            }
            case BOARDING -> {
                var serviceIds = new HashSet<Long>();
                var serviceId = service.getBoarding().getService().getServiceId();
                serviceIds.add(serviceId);
                for (var addon : service.getBoarding().getAddonsList()) {
                    serviceIds.add(addon.getAddOnId());
                }
                yield serviceIds;
            }
            case DAYCARE -> {
                var serviceIds = new HashSet<Long>();
                var serviceId = service.getDaycare().getService().getServiceId();
                serviceIds.add(serviceId);
                for (var addon : service.getDaycare().getAddonsList()) {
                    serviceIds.add(addon.getAddOnId());
                }
                yield serviceIds;
            }
            case DOG_WALKING -> {
                var serviceIds = new HashSet<Long>();
                var serviceId = service.getDogWalking().getService().getServiceId();
                serviceIds.add(serviceId);
                yield serviceIds;
            }
            case GROUP_CLASS -> {
                var serviceIds = new HashSet<Long>();
                var serviceId = service.getGroupClass().getService().getServiceId();
                serviceIds.add(serviceId);
                yield serviceIds;
            }
            case EVALUATION, SERVICE_NOT_SET -> Set.of();
                // default -> List.of(); // 别加 default，这里依赖 enum 特性，在编译时发现问题
        };
    }

    @Nullable
    private static Long extractStaffId(BookingRequestModel.Service service) {
        return switch (service.getServiceCase()) {
            case GROOMING -> service.getGrooming().getService().getStaffId();
            case DOG_WALKING -> service.getDogWalking().getService().getStaffId();
            case GROUP_CLASS -> service.getGroupClass().getService().getStaffId();
            default -> null;
        };
    }

    private static long extractPetId(BookingRequestModel.Service service) {
        return switch (service.getServiceCase()) {
            case GROOMING -> service.getGrooming().getService().getPetId();
            case BOARDING -> service.getBoarding().getService().getPetId();
            case DAYCARE -> service.getDaycare().getService().getPetId();
            case EVALUATION -> service.getEvaluation().getService().getPetId();
            case DOG_WALKING -> service.getDogWalking().getService().getPetId();
            case GROUP_CLASS -> service.getGroupClass().getService().getPetId();
            default -> throw bizException(
                    Code.CODE_PARAMS_ERROR, "Unsupported service type: " + service.getServiceCase());
        };
    }

    private static Set<Long> extractTaxIds(
            Collection<ServiceBriefView> services, Collection<EvaluationBriefView> evaluations) {
        var result = new HashSet<Long>();
        for (var service : services) {
            if (isNormal(service.getTaxId())) {
                result.add(service.getTaxId());
            }
        }
        for (var evaluation : evaluations) {
            if (isNormal(evaluation.getTaxId())) {
                result.add(evaluation.getTaxId());
            }
        }
        return result;
    }

    private List<PetDetailCalculateResultDef> calculatePricingRuleResults(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceIdToService,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList) {

        var petDetailCalculateDefs = new ArrayList<PetDetailCalculateDef>();
        for (var bookingRequestItem : bookingRequest.getServicesList()) {
            var defs =
                    switch (bookingRequestItem.getServiceCase()) {
                        case BOARDING -> {
                            BoardingServiceDetailModel boarding =
                                    bookingRequestItem.getBoarding().getService();
                            CustomizedServiceView customizedService = ServiceHelper.findCustomizedService(
                                    customizedServiceList, boarding.getServiceId(), boarding.getPetId(), null);
                            if (Objects.nonNull(customizedService)
                                    && Objects.equals(
                                            ServiceOverrideType.CLIENT, customizedService.getPriceOverrideType())) {
                                yield List.<PetDetailCalculateDef>of();
                            }
                            yield buildPetDetailCalculateDefForBoarding(
                                    bookingRequestItem.getBoarding(), serviceIdToService);
                        }
                        case DAYCARE -> {
                            DaycareServiceDetailModel daycare =
                                    bookingRequestItem.getDaycare().getService();
                            CustomizedServiceView customizedService = ServiceHelper.findCustomizedService(
                                    customizedServiceList, daycare.getServiceId(), daycare.getPetId(), null);
                            if (Objects.nonNull(customizedService)
                                    && Objects.equals(
                                            ServiceOverrideType.CLIENT, customizedService.getPriceOverrideType())) {
                                yield List.<PetDetailCalculateDef>of();
                            }
                            yield buildPetDetailCalculateDefForDaycare(bookingRequestItem.getDaycare());
                        }
                        default -> List.<PetDetailCalculateDef>of();
                    };
            petDetailCalculateDefs.addAll(defs);
        }

        if (petDetailCalculateDefs.isEmpty()) {
            return List.of();
        }

        return pricingRuleApplyStub
                .applyPricingRule(ApplyPricingRuleRequest.newBuilder()
                        .setCompanyId(bookingRequest.getCompanyId())
                        .setBusinessId(bookingRequest.getBusinessId())
                        .setSourceId(bookingRequest.getId())
                        .setSourceType(PricingRuleApplySourceType.SOURCE_TYPE_BOOKING_REQUEST)
                        .addAllPetDetails(petDetailCalculateDefs)
                        .build())
                .getPetDetailsList();
    }

    private static boolean isCalculateByDay(ServiceBriefView service) {
        return service.getServiceItemType() == ServiceItemType.DAYCARE
                || (service.getServiceItemType() == ServiceItemType.BOARDING
                        && service.getPriceUnit() == ServicePriceUnit.PER_DAY);
    }

    private static List<PetDetailCalculateDef> buildPetDetailCalculateDefForBoarding(
            BookingRequestModel.BoardingService boarding, Map<Long, ServiceBriefView> serviceIdToService) {

        var petDetail = boarding.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        Stream<LocalDate> dates = isCalculateByDay(service)
                ? LocalDate.parse(petDetail.getStartDate())
                        .datesUntil(LocalDate.parse(petDetail.getEndDate()).plusDays(1))
                : LocalDate.parse(petDetail.getStartDate()).datesUntil(LocalDate.parse(petDetail.getEndDate()));

        return dates.map(date -> {
                    var builder = PetDetailCalculateDef.newBuilder();
                    builder.setPetId(petDetail.getPetId());
                    builder.setServiceId(petDetail.getServiceId());
                    builder.setServicePrice(petDetail.getServicePrice());
                    builder.setServiceDate(date.toString());
                    return builder.build();
                })
                .toList();
    }

    private static List<PetDetailCalculateDef> buildPetDetailCalculateDefForDaycare(
            BookingRequestModel.DaycareService daycare) {
        var result = new ArrayList<PetDetailCalculateDef>();

        var service = daycare.getService();

        for (var date : service.getSpecificDatesList()) {
            var builder = PetDetailCalculateDef.newBuilder();
            builder.setPetId(service.getPetId());
            builder.setServiceId(service.getServiceId());
            builder.setServicePrice(service.getServicePrice());
            builder.setServiceDate(date);
            result.add(builder.build());
        }

        return result;
    }
}
