package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.EvaluationTestDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.EvaluationTestDetail;
import com.moego.svc.online.booking.typehandler.StringToDateTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface EvaluationTestDetailMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<EvaluationTestDetailMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, bookingRequestId, petId, evaluationId, servicePrice, duration, startDate, startTime, endDate, endTime, createdAt, updatedAt, deletedAt, serviceId);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<EvaluationTestDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<EvaluationTestDetail> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="EvaluationTestDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.BIGINT),
        @Result(column="evaluation_id", property="evaluationId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_price", property="servicePrice", jdbcType=JdbcType.NUMERIC),
        @Result(column="duration", property="duration", jdbcType=JdbcType.INTEGER),
        @Result(column="start_date", property="startDate", typeHandler=StringToDateTypeHandler.class, jdbcType=JdbcType.DATE),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.INTEGER),
        @Result(column="end_date", property="endDate", typeHandler=StringToDateTypeHandler.class, jdbcType=JdbcType.DATE),
        @Result(column="end_time", property="endTime", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="service_id", property="serviceId", jdbcType=JdbcType.BIGINT)
    })
    List<EvaluationTestDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("EvaluationTestDetailResult")
    Optional<EvaluationTestDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, evaluationTestDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, evaluationTestDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    default int insertMultiple(Collection<EvaluationTestDetail> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, evaluationTestDetail, c ->
            c.map(bookingRequestId).toProperty("bookingRequestId")
            .map(petId).toProperty("petId")
            .map(evaluationId).toProperty("evaluationId")
            .map(servicePrice).toProperty("servicePrice")
            .map(duration).toProperty("duration")
            .map(startDate).toProperty("startDate")
            .map(startTime).toProperty("startTime")
            .map(endDate).toProperty("endDate")
            .map(endTime).toProperty("endTime")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
            .map(serviceId).toProperty("serviceId")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    default int insertSelective(EvaluationTestDetail row) {
        return MyBatis3Utils.insert(this::insert, row, evaluationTestDetail, c ->
            c.map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(evaluationId).toPropertyWhenPresent("evaluationId", row::getEvaluationId)
            .map(servicePrice).toPropertyWhenPresent("servicePrice", row::getServicePrice)
            .map(duration).toPropertyWhenPresent("duration", row::getDuration)
            .map(startDate).toPropertyWhenPresent("startDate", row::getStartDate)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(endDate).toPropertyWhenPresent("endDate", row::getEndDate)
            .map(endTime).toPropertyWhenPresent("endTime", row::getEndTime)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
            .map(serviceId).toPropertyWhenPresent("serviceId", row::getServiceId)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    default Optional<EvaluationTestDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, evaluationTestDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    default List<EvaluationTestDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, evaluationTestDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    default List<EvaluationTestDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, evaluationTestDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    default Optional<EvaluationTestDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, evaluationTestDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(EvaluationTestDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(petId).equalTo(row::getPetId)
                .set(evaluationId).equalTo(row::getEvaluationId)
                .set(servicePrice).equalTo(row::getServicePrice)
                .set(duration).equalTo(row::getDuration)
                .set(startDate).equalTo(row::getStartDate)
                .set(startTime).equalTo(row::getStartTime)
                .set(endDate).equalTo(row::getEndDate)
                .set(endTime).equalTo(row::getEndTime)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt)
                .set(serviceId).equalTo(row::getServiceId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(EvaluationTestDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(evaluationId).equalToWhenPresent(row::getEvaluationId)
                .set(servicePrice).equalToWhenPresent(row::getServicePrice)
                .set(duration).equalToWhenPresent(row::getDuration)
                .set(startDate).equalToWhenPresent(row::getStartDate)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(endDate).equalToWhenPresent(row::getEndDate)
                .set(endTime).equalToWhenPresent(row::getEndTime)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
                .set(serviceId).equalToWhenPresent(row::getServiceId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    default int updateByPrimaryKeySelective(EvaluationTestDetail row) {
        return update(c ->
            c.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(evaluationId).equalToWhenPresent(row::getEvaluationId)
            .set(servicePrice).equalToWhenPresent(row::getServicePrice)
            .set(duration).equalToWhenPresent(row::getDuration)
            .set(startDate).equalToWhenPresent(row::getStartDate)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(endDate).equalToWhenPresent(row::getEndDate)
            .set(endTime).equalToWhenPresent(row::getEndTime)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .set(serviceId).equalToWhenPresent(row::getServiceId)
            .where(id, isEqualTo(row::getId))
        );
    }
}