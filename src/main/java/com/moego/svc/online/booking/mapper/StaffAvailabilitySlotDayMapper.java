package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.StaffAvailabilitySlotDayDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.StaffAvailabilitySlotDay;
import com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface StaffAvailabilitySlotDayMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<StaffAvailabilitySlotDayMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    BasicColumn[] selectList = BasicColumn.columnList(id, companyId, businessId, staffId, dayOfWeek, isAvailable, startTime, endTime, capacity, limitIds, createdAt, updatedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<StaffAvailabilitySlotDay> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<StaffAvailabilitySlotDay> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="StaffAvailabilitySlotDayResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.BIGINT),
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT),
        @Result(column="day_of_week", property="dayOfWeek", jdbcType=JdbcType.INTEGER),
        @Result(column="is_available", property="isAvailable", jdbcType=JdbcType.BIT),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.INTEGER),
        @Result(column="end_time", property="endTime", jdbcType=JdbcType.INTEGER),
        @Result(column="capacity", property="capacity", jdbcType=JdbcType.INTEGER),
        @Result(column="limit_ids", property="limitIds", typeHandler=JsonArrayTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<StaffAvailabilitySlotDay> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("StaffAvailabilitySlotDayResult")
    Optional<StaffAvailabilitySlotDay> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, staffAvailabilitySlotDay, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, staffAvailabilitySlotDay, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    default int insertMultiple(Collection<StaffAvailabilitySlotDay> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, staffAvailabilitySlotDay, c ->
            c.map(companyId).toProperty("companyId")
            .map(businessId).toProperty("businessId")
            .map(staffId).toProperty("staffId")
            .map(dayOfWeek).toProperty("dayOfWeek")
            .map(isAvailable).toProperty("isAvailable")
            .map(startTime).toProperty("startTime")
            .map(endTime).toProperty("endTime")
            .map(capacity).toProperty("capacity")
            .map(limitIds).toProperty("limitIds")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    default int insertSelective(StaffAvailabilitySlotDay row) {
        return MyBatis3Utils.insert(this::insert, row, staffAvailabilitySlotDay, c ->
            c.map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(staffId).toPropertyWhenPresent("staffId", row::getStaffId)
            .map(dayOfWeek).toPropertyWhenPresent("dayOfWeek", row::getDayOfWeek)
            .map(isAvailable).toPropertyWhenPresent("isAvailable", row::getIsAvailable)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(endTime).toPropertyWhenPresent("endTime", row::getEndTime)
            .map(capacity).toPropertyWhenPresent("capacity", row::getCapacity)
            .map(limitIds).toPropertyWhenPresent("limitIds", row::getLimitIds)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    default Optional<StaffAvailabilitySlotDay> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, staffAvailabilitySlotDay, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    default List<StaffAvailabilitySlotDay> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, staffAvailabilitySlotDay, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    default List<StaffAvailabilitySlotDay> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, staffAvailabilitySlotDay, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    default Optional<StaffAvailabilitySlotDay> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, staffAvailabilitySlotDay, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    static UpdateDSL<UpdateModel> updateAllColumns(StaffAvailabilitySlotDay row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalTo(row::getCompanyId)
                .set(businessId).equalTo(row::getBusinessId)
                .set(staffId).equalTo(row::getStaffId)
                .set(dayOfWeek).equalTo(row::getDayOfWeek)
                .set(isAvailable).equalTo(row::getIsAvailable)
                .set(startTime).equalTo(row::getStartTime)
                .set(endTime).equalTo(row::getEndTime)
                .set(capacity).equalTo(row::getCapacity)
                .set(limitIds).equalTo(row::getLimitIds)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(StaffAvailabilitySlotDay row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(staffId).equalToWhenPresent(row::getStaffId)
                .set(dayOfWeek).equalToWhenPresent(row::getDayOfWeek)
                .set(isAvailable).equalToWhenPresent(row::getIsAvailable)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(endTime).equalToWhenPresent(row::getEndTime)
                .set(capacity).equalToWhenPresent(row::getCapacity)
                .set(limitIds).equalToWhenPresent(row::getLimitIds)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    default int updateByPrimaryKeySelective(StaffAvailabilitySlotDay row) {
        return update(c ->
            c.set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(staffId).equalToWhenPresent(row::getStaffId)
            .set(dayOfWeek).equalToWhenPresent(row::getDayOfWeek)
            .set(isAvailable).equalToWhenPresent(row::getIsAvailable)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(endTime).equalToWhenPresent(row::getEndTime)
            .set(capacity).equalToWhenPresent(row::getCapacity)
            .set(limitIds).equalToWhenPresent(row::getLimitIds)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}