package com.moego.svc.online.booking.service;

import com.google.type.DayOfWeek;
import com.moego.common.distributed.LockManager;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.online_booking.v1.AvailabilityType;
import com.moego.idl.models.organization.v1.StaffBasicView;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.idl.service.organization.v1.GetCompanyIdResponse;
import com.moego.idl.service.organization.v1.GetStaffsByWorkingLocationRequest;
import com.moego.idl.service.organization.v1.GetStaffsByWorkingLocationResponse;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.svc.online.booking.entity.StaffAvailability;
import com.moego.svc.online.booking.entity.StaffAvailabilityDayHour;
import com.moego.svc.online.booking.entity.StaffAvailabilityTimeDay;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class StaffAvailabilityInitService {

    private final LockManager lockManager;
    private final StaffAvailabilityService staffAvailabilityService;
    private final StaffAvailabilityDayHourService staffAvailabilityDayHourService;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceStub;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffServiceStub;

    private static final Integer DEFAULT_START_TIME = 9 * 60;
    private static final Integer DEFAULT_END_TIME = 18 * 60;
    private static final Integer DEFAULT_STAFF_CAPACITY = 10;
    private static final Boolean DEFAULT_IS_AVAILABLE = true;
    private static final List<Long> DEFAULT_LIMIT_IDS = List.of();

    private static final StaffAvailabilityTimeDay defaultStaffAvailabilityTimeDay;

    private static final StaffAvailabilityDayHour defaultStaffAvailabilityTimeDayHour;

    static {
        defaultStaffAvailabilityTimeDay = new StaffAvailabilityTimeDay();
        defaultStaffAvailabilityTimeDay.setIsAvailable(DEFAULT_IS_AVAILABLE);
        defaultStaffAvailabilityTimeDay.setLimitIds(DEFAULT_LIMIT_IDS);

        defaultStaffAvailabilityTimeDayHour = new StaffAvailabilityDayHour();

        defaultStaffAvailabilityTimeDayHour.setDayType(AvailabilityType.AVAILABILITY_TYPE_BY_WORKING_HOURS_VALUE);
        defaultStaffAvailabilityTimeDayHour.setStartTime(DEFAULT_START_TIME);
        defaultStaffAvailabilityTimeDayHour.setEndTime(DEFAULT_END_TIME);
    }

    // 初始化 staff availability & staff availability time day & staff availability day hour
    public void checkStaffAvailabilityWithInit(Long companyId, Long businessId, List<Long> staffIds) {
        if (businessId == null) {
            throw new IllegalArgumentException("businessId is invalid");
        }

        // staffIds为空 获取business下全部staffIds 防止新建Staff不存在
        if (CollectionUtils.isEmpty(staffIds)) {
            GetStaffsByWorkingLocationResponse staffsByWorkingLocation =
                    staffServiceStub.getStaffsByWorkingLocation(GetStaffsByWorkingLocationRequest.newBuilder()
                            .setCompanyId(companyId)
                            .setWorkingLocationId(businessId)
                            .build());
            staffIds = staffsByWorkingLocation.getStaffsList().stream()
                    .map(StaffBasicView::getId)
                    .collect(Collectors.toList());
        }

        List<StaffAvailability> staffAvailabilityList =
                staffAvailabilityService.getStaffAvailabilities(businessId, staffIds);

        // 获取未初始化的 staffId
        Set<Long> existingStaffIds = staffAvailabilityList.stream()
                .map(StaffAvailability::getStaffId)
                .collect(Collectors.toSet());
        List<Long> missingStaffIds = staffIds.stream()
                .filter(staffId -> !existingStaffIds.contains(staffId))
                .toList();

        if (!missingStaffIds.isEmpty()) {
            GetCompanyIdResponse resp = businessServiceStub.getCompanyId(
                    GetCompanyIdRequest.newBuilder().setBusinessId(businessId).build());
            initStaffAvailabilities(resp.getCompanyId(), businessId, missingStaffIds);
        }
    }

    public void initStaffAvailabilities(Long companyId, Long businessId, List<Long> staffIds) {
        List<StaffAvailability> staffAvailabilities = new ArrayList<>();
        // 加锁避免重复初始化
        staffIds.forEach(staffId -> {
            String resourceKey = lockManager.getResourceKey(LockManager.NEW_OB_STAFF_TIME, staffId);
            String value = CommonUtil.getUuid();
            if (!lockManager.lock(resourceKey, value)) {
                return;
            }

            try {
                StaffAvailability staffAvailability = initStaffAvailability(companyId, businessId, staffId);
                if (Objects.nonNull(staffAvailability)) {
                    staffAvailabilities.add(staffAvailability);
                }
                initStaffAvailabilityTimeDays(companyId, businessId, staffId);
                // by slot不需要初始化
            } finally {
                lockManager.unlock(resourceKey, value);
            }
        });
    }

    private StaffAvailability initStaffAvailability(Long companyId, Long businessId, Long staffId) {
        if (Objects.nonNull(staffAvailabilityService.getStaffAvailability(businessId, staffId))) {
            // already initialized
            return null;
        }
        StaffAvailability staffAvailability = new StaffAvailability();
        staffAvailability.setCompanyId(companyId);
        staffAvailability.setBusinessId(businessId);
        staffAvailability.setStaffId(staffId);
        staffAvailability.setIsAvailable(DEFAULT_IS_AVAILABLE);
        staffAvailability.setCreatedAt(new Date());
        staffAvailability.setUpdatedAt(new Date());
        staffAvailabilityService.insert(staffAvailability);

        return staffAvailability;
    }

    private void initStaffAvailabilityTimeDays(Long companyId, Long businessId, Long staffId) {
        List<StaffAvailabilityTimeDay> availabilityTimeDays =
                staffAvailabilityDayHourService.getStaffAvailabilityTimeDays(businessId, List.of(staffId));
        if (!CollectionUtils.isEmpty(availabilityTimeDays)) {
            // already initialized
            return;
        }

        List<StaffAvailabilityTimeDay> staffAvailabilityTimeDays = new ArrayList<>();
        for (DayOfWeek value : DayOfWeek.values()) {
            if (DayOfWeek.DAY_OF_WEEK_UNSPECIFIED.equals(value) || DayOfWeek.UNRECOGNIZED.equals(value)) {
                continue;
            }
            StaffAvailabilityTimeDay staffAvailabilityTimeDay = new StaffAvailabilityTimeDay();
            BeanUtils.copyProperties(defaultStaffAvailabilityTimeDay, staffAvailabilityTimeDay);
            staffAvailabilityTimeDay.setCompanyId(companyId);
            staffAvailabilityTimeDay.setBusinessId(businessId);
            staffAvailabilityTimeDay.setStaffId(staffId);
            staffAvailabilityTimeDay.setDayOfWeek(value.getNumber());
            staffAvailabilityTimeDay.setCreatedAt(new Date());
            staffAvailabilityTimeDay.setUpdatedAt(new Date());
            staffAvailabilityTimeDays.add(staffAvailabilityTimeDay);
        }

        staffAvailabilityDayHourService.batchCreateStaffAvailabilityTimeDay(staffAvailabilityTimeDays);

        List<StaffAvailabilityDayHour> staffAvailabilityTimeDayHours = new ArrayList<>();
        staffAvailabilityTimeDays.forEach(staffAvailabilityTimeDay -> {
            StaffAvailabilityDayHour staffAvailabilityTimeDayHour = new StaffAvailabilityDayHour();
            BeanUtils.copyProperties(defaultStaffAvailabilityTimeDayHour, staffAvailabilityTimeDayHour);
            staffAvailabilityTimeDayHour.setDayId(staffAvailabilityTimeDay.getId());
            staffAvailabilityTimeDayHour.setCreatedAt(new Date());
            staffAvailabilityTimeDayHour.setUpdatedAt(new Date());
            staffAvailabilityTimeDayHours.add(staffAvailabilityTimeDayHour);
        });

        staffAvailabilityDayHourService.batchCreateStaffAvailabilityDayHour(staffAvailabilityTimeDayHours);
    }
}
