package com.moego.svc.appointment.controller;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.appointment.v1.ListPetPlaygroupRequest;
import com.moego.idl.service.appointment.v1.ListPetPlaygroupResponse;
import com.moego.idl.service.appointment.v1.PetPlaygroupServiceGrpc;
import com.moego.idl.service.appointment.v1.ReschedulePetPlaygroupRequest;
import com.moego.idl.service.appointment.v1.ReschedulePetPlaygroupResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.appointment.converter.DateConverter;
import com.moego.svc.appointment.converter.PetPlaygroupConverter;
import com.moego.svc.appointment.converter.TimeConverter;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.PetPlaygroup;
import com.moego.svc.appointment.service.AppointmentRescheduleService;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import com.moego.svc.appointment.service.PetPlaygroupService;
import com.moego.svc.appointment.service.params.UpdatePetPlaygroupParams;
import com.moego.svc.appointment.service.remote.PetRemoteService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;

@GrpcService
@RequiredArgsConstructor
public class PetPlaygroupController extends PetPlaygroupServiceGrpc.PetPlaygroupServiceImplBase {
    private final PetPlaygroupService petPlaygroupService;
    private final AppointmentServiceProxy appointmentService;
    private final AppointmentRescheduleService appointmentRescheduleService;
    private final PetDetailServiceProxy petDetailService;
    private final PetRemoteService petRemoteService;

    @Override
    public void listPetPlaygroup(
            ListPetPlaygroupRequest request, StreamObserver<ListPetPlaygroupResponse> responseObserver) {
        List<PetPlaygroup> petPlaygroups = petPlaygroupService.listPlaygroupByDateRange(
                request.getCompanyId(),
                request.getBusinessId(),
                TimeConverter.INSTANCE.toLocalDate(request.getStartDate()).toString(),
                TimeConverter.INSTANCE.toLocalDate(request.getEndDate()).toString(),
                request.getPlaygroupIdsList());
        responseObserver.onNext(ListPetPlaygroupResponse.newBuilder()
                .addAllPetPlaygroups(PetPlaygroupConverter.INSTANCE.entityToModel(petPlaygroups))
                .build());

        responseObserver.onCompleted();
    }

    @Override
    public void reschedulePetPlaygroup(
            ReschedulePetPlaygroupRequest request, StreamObserver<ReschedulePetPlaygroupResponse> responseObserver) {

        PetPlaygroup petPlaygroup = petPlaygroupService.get(request.getCompanyId(), request.getPetPlaygroupId());
        if (petPlaygroup == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Pet playgroup not found");
        }

        var appointment = appointmentService.mustGet(petPlaygroup.getAppointmentId());

        var targetDateStr =
                DateConverter.INSTANCE.fromGoogleDate(request.getDate()).toString();

        var isDateChanged = !petPlaygroup.getDate().equals(targetDateStr);
        if (isDateChanged) {
            // boarding 相关 appointment 不支持在 Playgroup 里 reschedule 日期
            if (ServiceItemEnum.convertBitValueList(appointment.getServiceTypeInclude())
                    .contains(ServiceItemEnum.BOARDING)) {
                throw bizException(Code.CODE_PARAMS_ERROR, "Boarding appointment cannot be rescheduled in playgroup");
            }
            var beforePetDetails =
                    petDetailService.getPetDetailList(appointment.getId().longValue());
            var updatedPetDetails = beforePetDetails.stream()
                    .map(petDetail -> {
                        MoeGroomingPetDetail updatedPetDetail = new MoeGroomingPetDetail();
                        BeanUtils.copyProperties(petDetail, updatedPetDetail);
                        updatedPetDetail.setStartDate(targetDateStr);
                        updatedPetDetail.setEndDate(targetDateStr);
                        return updatedPetDetail;
                    })
                    .toList();

            // reschedule pet details & update pet playgroup date and sort in last index
            appointmentRescheduleService.reschedulePetDetails(appointment, beforePetDetails, updatedPetDetails);
        }

        var updatedPetPlaygroup = new UpdatePetPlaygroupParams();
        updatedPetPlaygroup.setAppointmentId(petPlaygroup.getAppointmentId());
        updatedPetPlaygroup.setPetId(petPlaygroup.getPetId());
        updatedPetPlaygroup.setDate(targetDateStr);
        updatedPetPlaygroup.setIndex(request.getIndex());
        updatedPetPlaygroup.setPlaygroupId(request.getPlaygroupId());

        petPlaygroupService.updatePetPlaygroup(request.getCompanyId(), updatedPetPlaygroup);

        responseObserver.onNext(ReschedulePetPlaygroupResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
