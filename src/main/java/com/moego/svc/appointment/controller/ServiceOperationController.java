package com.moego.svc.appointment.controller;

import com.moego.idl.service.appointment.v1.GetServiceOperationListRequest;
import com.moego.idl.service.appointment.v1.GetServiceOperationListResponse;
import com.moego.idl.service.appointment.v1.ServiceOperationServiceGrpc;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.appointment.converter.GroomingServiceOperationConverter;
import com.moego.svc.appointment.domain.MoeGroomingServiceOperation;
import com.moego.svc.appointment.service.ServiceOperationService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class ServiceOperationController extends ServiceOperationServiceGrpc.ServiceOperationServiceImplBase {

    private final ServiceOperationService serviceOperationService;

    @Override
    public void getServiceOperationList(
            GetServiceOperationListRequest request, StreamObserver<GetServiceOperationListResponse> responseObserver) {
        List<MoeGroomingServiceOperation> serviceOperationList =
                serviceOperationService.getServiceOperationList(request.getAppointmentIdsList());

        responseObserver.onNext(GetServiceOperationListResponse.newBuilder()
                .addAllServiceOperations(GroomingServiceOperationConverter.INSTANCE.toModel(serviceOperationList))
                .build());
        responseObserver.onCompleted();
    }
}
