package com.moego.svc.appointment.controller;

import com.moego.idl.models.appointment.v1.WaitListCalendarView;
import com.moego.idl.service.appointment.v1.GetWaitListByAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetWaitListByAppointmentResponse;
import com.moego.idl.service.appointment.v1.WaitListServiceGrpc;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.appointment.converter.WaitListConverter;
import com.moego.svc.appointment.domain.MoeWaitList;
import com.moego.svc.appointment.service.WaitListService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class WaitListController extends WaitListServiceGrpc.WaitListServiceImplBase {

    private final WaitListService waitListService;

    @Override
    public void getWaitListByAppointment(
            GetWaitListByAppointmentRequest request,
            StreamObserver<GetWaitListByAppointmentResponse> responseObserver) {
        List<MoeWaitList> waitListByAppointmentIdList =
                waitListService.getWaitListByAppointmentIdList(request.getCompanyId(), request.getAppointmentIdsList());

        Map<Long, WaitListCalendarView> calendarViewMap = waitListByAppointmentIdList.stream()
                .collect(Collectors.toMap(
                        MoeWaitList::getAppointmentId, WaitListConverter.INSTANCE::toCalendarView, (a, b) -> a));

        responseObserver.onNext(GetWaitListByAppointmentResponse.newBuilder()
                .putAllWaitList(calendarViewMap)
                .build());
        responseObserver.onCompleted();
    }
}
