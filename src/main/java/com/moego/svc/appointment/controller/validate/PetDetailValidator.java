package com.moego.svc.appointment.controller.validate;

import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.svc.appointment.service.remote.OfferingRemoteService;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2025/3/14
 */
@Component
@RequiredArgsConstructor
public class PetDetailValidator {

    private final OfferingRemoteService offeringRemoteService;
    private final FeatureFlagApi featureFlagApi;

    public void validatePetDetails(List<PetDetailDef> petDetailDefs) {
        if (!featureFlagApi.isOn(
                FeatureFlags.VALIDATE_PET_DETAILS, FeatureFlagContext.builder().build())) {
            return;
        }

        var serviceIds = petDetailDefs.stream()
                .flatMap(def -> Stream.concat(
                        def.getServicesList().stream().map(SelectedServiceDef::getServiceId),
                        def.getAddOnsList().stream().map(SelectedAddOnDef::getAddOnId)))
                .distinct()
                .toList();
        var idToService = offeringRemoteService.getServiceModels(null, serviceIds).stream()
                .collect(Collectors.toMap(ServiceBriefView::getId, Function.identity()));

        petDetailDefs.forEach(def -> validatePetDetail(def, idToService));
    }

    private static void validatePetDetail(PetDetailDef petDetailDef, Map<Long, ServiceBriefView> idToService) {
        petDetailDef.getServicesList().forEach(serviceDef -> {
            switch (serviceDef.getDateType()) {
                    // 仅 daycare service 支持 everyday
                case PET_DETAIL_DATE_EVERYDAY,
                        PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
                        PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> validateEverydayDay(serviceDef, idToService);
                    // 目前仅 daycare service 存在该情况
                case PET_DETAIL_DATE_SPECIFIC_DATE -> validateSpecificDates(serviceDef);
                case PET_DETAIL_DATE_DATE_POINT -> validateDatePoint(serviceDef, idToService);
                case PET_DETAIL_DATE_LAST_DAY, PET_DETAIL_DATE_FIRST_DAY -> validateSingleDay(serviceDef);
                default -> {} // No-op
            }
        });

        petDetailDef.getAddOnsList().forEach(addOnDef -> {
            switch (addOnDef.getAddonDateType()) {
                    // 1. add another care type 情况下单独添加 add-on 不会传递 associatedServiceId，因此不做校验
                    // 2. edit pet and services 情况下切换 service 到 add-on 也不会传递
                case PET_DETAIL_DATE_EVERYDAY, PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> {} // No-op
                case PET_DETAIL_DATE_SPECIFIC_DATE -> validateSpecificDates(addOnDef);
                case PET_DETAIL_DATE_DATE_POINT -> validateDatePoint(addOnDef);
                case PET_DETAIL_DATE_LAST_DAY, PET_DETAIL_DATE_FIRST_DAY -> validateSingleDay(addOnDef);
                default -> {} // No-op
            }
        });
    }

    private static void validateDatePoint(SelectedServiceDef serviceDef, Map<Long, ServiceBriefView> idToService) {
        var isLegal =
                switch (idToService.get(serviceDef.getServiceId()).getServiceItemType()) {
                    case BOARDING -> StringUtils.hasText(serviceDef.getStartDate())
                            && serviceDef.hasStartTime()
                            && serviceDef.hasEndDate()
                            && serviceDef.hasEndTime();
                    case DAYCARE -> StringUtils.hasText(serviceDef.getStartDate())
                            && serviceDef.hasStartTime()
                            && serviceDef.hasEndTime();
                    case GROOMING, DOG_WALKING -> StringUtils.hasText(serviceDef.getStartDate())
                            && serviceDef.hasStartTime()
                            && serviceDef.hasStaffId();
                    default -> true; // 其他 case 均不做校验
                };
        if (isLegal) {
            return;
        }
        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid date point params");
    }

    private static void validateEverydayDay(SelectedServiceDef serviceDef, Map<Long, ServiceBriefView> idToService) {
        var isLegal = Optional.ofNullable(idToService.get(serviceDef.getServiceId()))
                .map(service -> service.getServiceItemType() == ServiceItemType.DAYCARE)
                .orElse(false);
        if (isLegal) {
            return;
        }
        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid everyday day params");
    }

    private static void validateSpecificDates(SelectedServiceDef serviceDef) {
        var isLegal = serviceDef.getSpecificDatesCount() != 0;
        if (isLegal) {
            return;
        }
        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid specific dates params");
    }

    private static void validateSingleDay(SelectedServiceDef serviceDef) {
        if (!serviceDef.hasStaffId()) {
            return;
        }
        if (!serviceDef.hasStartTime()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid single day params");
        }
    }
    //    private static void validateEverydayDay(SelectedAddOnDef addOnDef, Set<Long> serviceIds) {
    //        var isLegal = addOnDef.hasAssociatedServiceId() && serviceIds.contains(addOnDef.getAssociatedServiceId());
    //        if (isLegal) {
    //            return;
    //        }
    //        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid everyday day params");
    //    }

    private static void validateDatePoint(SelectedAddOnDef addOnDef) {
        var isLegal = addOnDef.hasStartDate() && addOnDef.hasStartTime() && addOnDef.hasStaffId();
        if (isLegal) {
            return;
        }
        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid date point params");
    }

    private static void validateSpecificDates(SelectedAddOnDef addOnDef) {
        var isLegal = addOnDef.getSpecificDatesCount() != 0;
        if (isLegal) {
            return;
        }
        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid specific dates params");
    }

    private static void validateSingleDay(SelectedAddOnDef serviceDef) {
        if (!serviceDef.hasStaffId()) {
            // 没有 staff id 的情况下，不校验
            return;
        }
        // 有 staff id 的情况下，校验 start_time 和 end_time
        if (!serviceDef.hasStartTime()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid single day params");
        }
    }
}
