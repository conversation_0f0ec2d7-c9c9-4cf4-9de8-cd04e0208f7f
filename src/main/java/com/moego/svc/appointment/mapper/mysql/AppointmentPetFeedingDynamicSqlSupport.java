package com.moego.svc.appointment.mapper.mysql;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class AppointmentPetFeedingDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_pet_feeding")
    public static final AppointmentPetFeeding appointmentPetFeeding = new AppointmentPetFeeding();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.id")
    public static final SqlColumn<Long> id = appointmentPetFeeding.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.company_id")
    public static final SqlColumn<Long> companyId = appointmentPetFeeding.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.appointment_id")
    public static final SqlColumn<Long> appointmentId = appointmentPetFeeding.appointmentId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.pet_detail_id")
    public static final SqlColumn<Long> petDetailId = appointmentPetFeeding.petDetailId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.pet_id")
    public static final SqlColumn<Long> petId = appointmentPetFeeding.petId;

    /**
     * Database Column Remarks:
     *   such as 1.2, 1/2, 1 etc.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.feeding_amount")
    public static final SqlColumn<String> feedingAmount = appointmentPetFeeding.feedingAmount;

    /**
     * Database Column Remarks:
     *   pet_metadata.metadata_value, metadata_name = 2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.feeding_unit")
    public static final SqlColumn<String> feedingUnit = appointmentPetFeeding.feedingUnit;

    /**
     * Database Column Remarks:
     *   pet_metadata.metadata_value, metadata_name = 3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.feeding_type")
    public static final SqlColumn<String> feedingType = appointmentPetFeeding.feedingType;

    /**
     * Database Column Remarks:
     *   pet_metadata.metadata_value, metadata_name = 4
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.feeding_source")
    public static final SqlColumn<String> feedingSource = appointmentPetFeeding.feedingSource;

    /**
     * Database Column Remarks:
     *   pet_metadata.metadata_value, metadata_name = 5
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.feeding_instruction")
    public static final SqlColumn<String> feedingInstruction = appointmentPetFeeding.feedingInstruction;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.created_at")
    public static final SqlColumn<Date> createdAt = appointmentPetFeeding.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.updated_at")
    public static final SqlColumn<Date> updatedAt = appointmentPetFeeding.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.deleted_at")
    public static final SqlColumn<Date> deletedAt = appointmentPetFeeding.deletedAt;

    /**
     * Database Column Remarks:
     *   feeding note, user input
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_feeding.feeding_note")
    public static final SqlColumn<String> feedingNote = appointmentPetFeeding.feedingNote;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_pet_feeding")
    public static final class AppointmentPetFeeding extends AliasableSqlTable<AppointmentPetFeeding> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> appointmentId = column("appointment_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petDetailId = column("pet_detail_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<String> feedingAmount = column("feeding_amount", JDBCType.VARCHAR);

        public final SqlColumn<String> feedingUnit = column("feeding_unit", JDBCType.VARCHAR);

        public final SqlColumn<String> feedingType = column("feeding_type", JDBCType.VARCHAR);

        public final SqlColumn<String> feedingSource = column("feeding_source", JDBCType.VARCHAR);

        public final SqlColumn<String> feedingInstruction = column("feeding_instruction", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> feedingNote = column("feeding_note", JDBCType.LONGVARCHAR);

        public AppointmentPetFeeding() {
            super("appointment_pet_feeding", AppointmentPetFeeding::new);
        }
    }
}