package com.moego.svc.appointment.mapper.pg;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class DaycareAutoRolloverRecordDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_auto_rollover_record")
    public static final DaycareAutoRolloverRecord daycareAutoRolloverRecord = new DaycareAutoRolloverRecord();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_auto_rollover_record.id")
    public static final SqlColumn<Long> id = daycareAutoRolloverRecord.id;

    /**
     * Database Column Remarks:
     *   对应 moe_grooming.moe_grooming_pet_detail.id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_auto_rollover_record.daycare_service_detail_id")
    public static final SqlColumn<Long> daycareServiceDetailId = daycareAutoRolloverRecord.daycareServiceDetailId;

    /**
     * Database Column Remarks:
     *   daycare service id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_auto_rollover_record.service_id")
    public static final SqlColumn<Long> serviceId = daycareAutoRolloverRecord.serviceId;

    /**
     * Database Column Remarks:
     *   1: pending, 2: processing, 3: success, 4: failed
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_auto_rollover_record.status")
    public static final SqlColumn<Integer> status = daycareAutoRolloverRecord.status;

    /**
     * Database Column Remarks:
     *   rollover 触发时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_auto_rollover_record.rollover_time")
    public static final SqlColumn<Date> rolloverTime = daycareAutoRolloverRecord.rolloverTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_auto_rollover_record.created_at")
    public static final SqlColumn<Date> createdAt = daycareAutoRolloverRecord.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_auto_rollover_record.updated_at")
    public static final SqlColumn<Date> updatedAt = daycareAutoRolloverRecord.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_auto_rollover_record")
    public static final class DaycareAutoRolloverRecord extends AliasableSqlTable<DaycareAutoRolloverRecord> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> daycareServiceDetailId = column("daycare_service_detail_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceId = column("service_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<Date> rolloverTime = column("rollover_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public DaycareAutoRolloverRecord() {
            super("daycare_auto_rollover_record", DaycareAutoRolloverRecord::new);
        }
    }
}