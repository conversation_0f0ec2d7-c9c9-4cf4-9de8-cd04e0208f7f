package com.moego.svc.appointment.mapper.mysql;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MoeGroomingServiceOperationDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    public static final MoeGroomingServiceOperation moeGroomingServiceOperation = new MoeGroomingServiceOperation();

    /**
     * Database Column Remarks:
     *   record id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.id")
    public static final SqlColumn<Long> id = moeGroomingServiceOperation.id;

    /**
     * Database Column Remarks:
     *   business id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.business_id")
    public static final SqlColumn<Integer> businessId = moeGroomingServiceOperation.businessId;

    /**
     * Database Column Remarks:
     *   grooming id/ticket id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.grooming_id")
    public static final SqlColumn<Integer> groomingId = moeGroomingServiceOperation.groomingId;

    /**
     * Database Column Remarks:
     *   grooming service id/pet detail id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.grooming_service_id")
    public static final SqlColumn<Integer> groomingServiceId = moeGroomingServiceOperation.groomingServiceId;

    /**
     * Database Column Remarks:
     *   pet id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.pet_id")
    public static final SqlColumn<Integer> petId = moeGroomingServiceOperation.petId;

    /**
     * Database Column Remarks:
     *   staff id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.staff_id")
    public static final SqlColumn<Integer> staffId = moeGroomingServiceOperation.staffId;

    /**
     * Database Column Remarks:
     *   operation name
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.operation_name")
    public static final SqlColumn<String> operationName = moeGroomingServiceOperation.operationName;

    /**
     * Database Column Remarks:
     *   start time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.start_time")
    public static final SqlColumn<Integer> startTime = moeGroomingServiceOperation.startTime;

    /**
     * Database Column Remarks:
     *   duration
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.duration")
    public static final SqlColumn<Integer> duration = moeGroomingServiceOperation.duration;

    /**
     * Database Column Remarks:
     *   comment
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.comment")
    public static final SqlColumn<String> comment = moeGroomingServiceOperation.comment;

    /**
     * Database Column Remarks:
     *   price
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.price")
    public static final SqlColumn<BigDecimal> price = moeGroomingServiceOperation.price;

    /**
     * Database Column Remarks:
     *   price ratio
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.price_ratio")
    public static final SqlColumn<BigDecimal> priceRatio = moeGroomingServiceOperation.priceRatio;

    /**
     * Database Column Remarks:
     *   status
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.status")
    public static final SqlColumn<Boolean> status = moeGroomingServiceOperation.status;

    /**
     * Database Column Remarks:
     *   create time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.create_time")
    public static final SqlColumn<Date> createTime = moeGroomingServiceOperation.createTime;

    /**
     * Database Column Remarks:
     *   update time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.update_time")
    public static final SqlColumn<Date> updateTime = moeGroomingServiceOperation.updateTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_service_operation.company_id")
    public static final SqlColumn<Long> companyId = moeGroomingServiceOperation.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    public static final class MoeGroomingServiceOperation extends AliasableSqlTable<MoeGroomingServiceOperation> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Integer> businessId = column("business_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> groomingId = column("grooming_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> groomingServiceId = column("grooming_service_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> petId = column("pet_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> staffId = column("staff_id", JDBCType.INTEGER);

        public final SqlColumn<String> operationName = column("operation_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<Integer> duration = column("duration", JDBCType.INTEGER);

        public final SqlColumn<String> comment = column("comment", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> price = column("price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> priceRatio = column("price_ratio", JDBCType.DECIMAL);

        public final SqlColumn<Boolean> status = column("status", JDBCType.BIT);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public MoeGroomingServiceOperation() {
            super("moe_grooming_service_operation", MoeGroomingServiceOperation::new);
        }
    }
}