package com.moego.svc.appointment.mapper.mysql;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class PetPlaygroupDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pet_playgroup")
    public static final PetPlaygroup petPlaygroup = new PetPlaygroup();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pet_playgroup.id")
    public static final SqlColumn<Long> id = petPlaygroup.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pet_playgroup.company_id")
    public static final SqlColumn<Long> companyId = petPlaygroup.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pet_playgroup.business_id")
    public static final SqlColumn<Long> businessId = petPlaygroup.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pet_playgroup.playgroup_id")
    public static final SqlColumn<Long> playgroupId = petPlaygroup.playgroupId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pet_playgroup.appointment_id")
    public static final SqlColumn<Long> appointmentId = petPlaygroup.appointmentId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pet_playgroup.pet_id")
    public static final SqlColumn<Long> petId = petPlaygroup.petId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pet_playgroup.date")
    public static final SqlColumn<String> date = petPlaygroup.date;

    /**
     * Database Column Remarks:
     *   pet playgroup list sort. start with 1 and put the smallest first
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pet_playgroup.sort")
    public static final SqlColumn<Integer> sort = petPlaygroup.sort;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pet_playgroup.created_at")
    public static final SqlColumn<Date> createdAt = petPlaygroup.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pet_playgroup.updated_at")
    public static final SqlColumn<Date> updatedAt = petPlaygroup.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pet_playgroup")
    public static final class PetPlaygroup extends AliasableSqlTable<PetPlaygroup> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> playgroupId = column("playgroup_id", JDBCType.BIGINT);

        public final SqlColumn<Long> appointmentId = column("appointment_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<String> date = column("date", JDBCType.VARCHAR);

        public final SqlColumn<Integer> sort = column("sort", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public PetPlaygroup() {
            super("pet_playgroup", PetPlaygroup::new);
        }
    }
}