package com.moego.svc.appointment.converter;

import com.google.protobuf.ProtocolMessageEnum;
import com.google.protobuf.Timestamp;
import com.moego.idl.models.appointment.v1.Address;
import com.moego.idl.models.appointment.v1.AppointmentTracking;
import com.moego.idl.models.appointment.v1.AppointmentTrackingView;
import com.moego.idl.models.appointment.v1.DelayedStatus;
import com.moego.idl.models.appointment.v1.StaffLocationStatus;
import com.moego.idl.models.appointment.v1.UpdateAppointmentTrackingDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.service.appointment.v1.BatchUpsertAppointmentTrackingRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTrackingRequest;
import com.moego.svc.appointment.service.params.ListAppointmentTrackingFilter;
import com.moego.svc.appointment.service.params.UpdateAppointmentTrackingFilter;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {StructConverter.class, TimeConverter.class})
public interface AppointmentTrackingConverter {

    AppointmentTrackingConverter INSTANCE = Mappers.getMapper(AppointmentTrackingConverter.class);

    ListAppointmentTrackingFilter toFilter(ListAppointmentTrackingRequest.Filter filter);

    UpdateAppointmentTrackingFilter toFilter(BatchUpsertAppointmentTrackingRequest.Filter filter);

    AppointmentTracking toModel(com.moego.svc.appointment.domain.AppointmentTracking in);

    AppointmentTrackingView toView(com.moego.svc.appointment.domain.AppointmentTracking in);

    List<AppointmentTracking> toModel(List<com.moego.svc.appointment.domain.AppointmentTracking> in);

    com.moego.svc.appointment.domain.AppointmentTracking toModel(Long appointmentId, UpdateAppointmentTrackingDef in);

    Address toAddress(BusinessCustomerAddressModel in);

    default Long timestampToLong(Timestamp timestamp) {
        return timestamp.getSeconds();
    }

    default int pbEnumToInt(ProtocolMessageEnum enumValue) {
        return enumValue.getNumber();
    }

    default StaffLocationStatus toStaffLocationStatus(Integer value) {
        return StaffLocationStatus.forNumber(value);
    }

    default DelayedStatus toDelayedStatus(Integer value) {
        return DelayedStatus.forNumber(value);
    }
}
