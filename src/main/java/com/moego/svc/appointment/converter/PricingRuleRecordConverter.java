package com.moego.svc.appointment.converter;

import com.moego.idl.models.appointment.v2.PricingRuleApplyLogModel;
import com.moego.svc.appointment.domain.PricingRuleApplyLog;
import com.moego.svc.appointment.domain.PricingRuleRecordApplyLog;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {StructConverter.class, TimeConverter.class})
public abstract class PricingRuleRecordConverter {

    public static final PricingRuleRecordConverter INSTANCE = Mappers.getMapper(PricingRuleRecordConverter.class);

    public abstract List<PricingRuleApplyLogModel> entityToModel(List<PricingRuleRecordApplyLog> model);

    @Mapping(
            target = "sourceType",
            expression =
                    "java(com.moego.idl.models.appointment.v2.PricingRuleApplySourceType.forNumber(model.getSourceType()))")
    public abstract PricingRuleApplyLogModel entityToModel(PricingRuleRecordApplyLog model);

    @Mapping(target = "pricingRule", ignore = true)
    @Mapping(target = "adjustedPrice", source = "servicePrice")
    public abstract PricingRuleRecordApplyLog toNewModel(PricingRuleApplyLog model);
}
