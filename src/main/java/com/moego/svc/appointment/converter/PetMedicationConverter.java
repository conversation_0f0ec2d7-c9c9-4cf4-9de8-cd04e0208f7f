package com.moego.svc.appointment.converter;

import static org.bouncycastle.jcajce.util.AnnotatedPrivateKey.LABEL;

import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleType;
import com.moego.idl.models.business_customer.v1.FeedingMedicationScheduleDateType;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.appointment.domain.AppointmentPetMedication;
import com.moego.svc.appointment.domain.AppointmentPetScheduleSetting;
import com.moego.svc.appointment.dto.PetDetailDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/1/26
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        uses = {StructConverter.class})
public interface PetMedicationConverter {

    List<PetDetailDTO.PetMedicationScheduleDTO> defToDTO(List<AppointmentPetMedicationScheduleDef> def);

    default PetDetailDTO.PetMedicationScheduleDTO defToDTO(AppointmentPetMedicationScheduleDef def) {
        AppointmentPetMedication medication = new AppointmentPetMedication();
        medication.setMedicationAmount(def.getMedicationAmount());
        medication.setMedicationUnit(def.getMedicationUnit());
        medication.setMedicationName(def.getMedicationName());
        medication.setMedicationNote(def.getMedicationNote());
        if (def.hasSelectedDate()) {
            medication.setDateType(def.getSelectedDate().getDateType());
            if (def.getSelectedDate().getDateType().equals(FeedingMedicationScheduleDateType.SPECIFIC_DATE)) {
                medication.setSpecificDates(
                        JsonUtil.toJson(def.getSelectedDate().getSpecificDatesList()));
            } else {
                medication.setSpecificDates("[]");
            }
        }
        return new PetDetailDTO.PetMedicationScheduleDTO()
                .setMedication(medication)
                .setScheduleSettings(def.getMedicationTimesList().stream()
                        .map(time -> {
                            AppointmentPetScheduleSetting scheduleSetting = new AppointmentPetScheduleSetting();
                            scheduleSetting.setScheduleTime(time.getScheduleTime());
                            scheduleSetting.setScheduleType(BusinessPetScheduleType.MEDICATION_VALUE);
                            scheduleSetting.setScheduleExtraJson(JsonUtil.toJson(time.getExtraJsonMap()));
                            return scheduleSetting;
                        })
                        .toList());
    }

    default AppointmentPetMedicationScheduleDef toDef(AppointmentPetMedication medication) {
        if (medication == null) {
            return null;
        }

        AppointmentPetMedicationScheduleDef.Builder appointmentPetMedicationScheduleDef =
                AppointmentPetMedicationScheduleDef.newBuilder();

        appointmentPetMedicationScheduleDef.setSelectedDate(appointmentPetMedicationToSelectedDateDef(medication));
        if (medication.getMedicationAmount() != null) {
            appointmentPetMedicationScheduleDef.setMedicationAmount(medication.getMedicationAmount());
        }
        if (medication.getMedicationUnit() != null) {
            appointmentPetMedicationScheduleDef.setMedicationUnit(medication.getMedicationUnit());
        }
        if (medication.getMedicationName() != null) {
            appointmentPetMedicationScheduleDef.setMedicationName(medication.getMedicationName());
        }
        if (medication.getMedicationNote() != null) {
            appointmentPetMedicationScheduleDef.setMedicationNote(medication.getMedicationNote());
        }

        return appointmentPetMedicationScheduleDef.build();
    }

    default AppointmentPetMedicationScheduleDef.SelectedDateDef appointmentPetMedicationToSelectedDateDef(
            AppointmentPetMedication appointmentPetMedication) {
        if (appointmentPetMedication == null) {
            return null;
        }

        AppointmentPetMedicationScheduleDef.SelectedDateDef.Builder selectedDateDef =
                AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder();

        if (appointmentPetMedication.getDateType() != null) {
            selectedDateDef.setDateType(appointmentPetMedication.getDateType());
        }
        if (appointmentPetMedication.getSpecificDates() != null) {
            selectedDateDef.addAllSpecificDates(
                    JsonUtil.toList(appointmentPetMedication.getSpecificDates(), String.class));
        }

        return selectedDateDef.build();
    }

    @Mapping(target = "dateType", source = "selectedDate.dateType")
    @Mapping(target = "specificDates", source = "selectedDate.specificDates")
    AppointmentPetMedication toDomain(AppointmentPetMedicationScheduleDef def);

    default List<AppointmentPetMedicationScheduleDef> toPetMedicationSchedules(
            List<AppointmentPetMedication> medications, List<AppointmentPetScheduleSetting> scheduleSettings) {
        Map<Long, List<AppointmentPetScheduleSetting>> scheduleMap = scheduleSettings.stream()
                .filter(setting -> setting.getScheduleType().equals(BusinessPetScheduleType.MEDICATION_VALUE))
                .collect(Collectors.groupingBy(AppointmentPetScheduleSetting::getScheduleId));
        return medications.stream()
                .map(medication -> {
                    AppointmentPetMedicationScheduleDef def = toDef(medication);
                    def = def.toBuilder()
                            .addAllMedicationTimes(scheduleMap.getOrDefault(medication.getId(), List.of()).stream()
                                    .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                            .setScheduleTime(time.getScheduleTime())
                                            .putAllExtraJson(
                                                    JsonUtil.toBean(time.getScheduleExtraJson(), new TypeRef<>() {}))
                                            .build())
                                    .toList())
                            .build();
                    return def;
                })
                .toList();
    }

    default List<AppointmentPetMedicationScheduleDef> buildPetMedicationSchedules(
            List<BookingRequestModel.Service> services) {
        if (CollectionUtils.isEmpty(services)) {
            return Collections.emptyList();
        }

        List<AppointmentPetMedicationScheduleDef> result = new ArrayList<>();

        for (BookingRequestModel.Service service : services) {
            if (service.hasBoarding()) {
                BookingRequestModel.BoardingService boardingService = service.getBoarding();

                if (!boardingService.getMedicationsList().isEmpty()) {
                    result.addAll(
                            medicationToAppointmentPetMedicationScheduleDef(boardingService.getMedicationsList()));
                }
            } else if (service.hasDaycare()) {
                BookingRequestModel.DaycareService daycareService = service.getDaycare();

                result.addAll(medicationToAppointmentPetMedicationScheduleDef(daycareService.getMedicationsList()));
            }
        }

        return result;
    }

    default List<AppointmentPetMedicationScheduleDef> medicationToAppointmentPetMedicationScheduleDef(
            List<MedicationModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return List.of();
        }
        return models.stream()
                .map(this::medicationToAppointmentPetMedicationScheduleDef)
                .filter(Objects::nonNull)
                .toList();
    }

    default AppointmentPetMedicationScheduleDef medicationToAppointmentPetMedicationScheduleDef(MedicationModel model) {
        if (Objects.isNull(model) || CollectionUtils.isEmpty(model.getTimeList())) {
            return null;
        }
        return AppointmentPetMedicationScheduleDef.newBuilder()
                .setMedicationAmount(getAmount(model))
                .setMedicationUnit(model.getUnit())
                .setMedicationName(model.getMedicationName())
                .setMedicationNote(model.getNotes())
                .addAllMedicationTimes(model.getTimeList().stream()
                        .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(time.getTime())
                                .putExtraJson(LABEL, time.getLabel())
                                .build())
                        .toList())
                .setSelectedDate(AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                        .setDateType(model.getSelectedDate().getDateType())
                        .addAllSpecificDates(model.getSelectedDate().getSpecificDatesList())
                        .build())
                .build();
    }

    default String getAmount(MedicationModel model) {
        return model.hasAmountStr() ? model.getAmountStr() : String.valueOf(model.getAmount());
    }
}
