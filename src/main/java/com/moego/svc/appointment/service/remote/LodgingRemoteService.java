package com.moego.svc.appointment.service.remote;

import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.service.offering.v1.GetLodgingTypeListRequest;
import com.moego.idl.service.offering.v1.GetLodgingUnitListRequest;
import com.moego.idl.service.offering.v1.LodgingTypeServiceGrpc;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.svc.appointment.domain.BoardingSplitLodging;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class LodgingRemoteService {

    private final LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitClient;
    private final LodgingTypeServiceGrpc.LodgingTypeServiceBlockingStub lodgingTypeClient;

    public List<LodgingUnitModel> getLodgingUnitByUnitIds(long companyId, long businessId, Collection<Long> unitIds) {
        GetLodgingUnitListRequest.Builder getLodgingUnitBuilder = GetLodgingUnitListRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .addAllUnitIds(unitIds);
        return lodgingUnitClient
                .getLodgingUnitList(getLodgingUnitBuilder.build())
                .getLodgingUnitListList();
    }

    public List<LodgingTypeModel> getLodgingType(long companyId) {
        return lodgingTypeClient
                .getLodgingTypeList(GetLodgingTypeListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getLodgingTypeListList();
    }

    public Map<Long, LodgingTypeModel> getLodgingTypeMap(long companyId) {
        return lodgingTypeClient
                .getLodgingTypeList(GetLodgingTypeListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getLodgingTypeListList()
                .stream()
                .collect(Collectors.toMap(LodgingTypeModel::getId, Function.identity()));
    }

    public Map<Long, LodgingUnitModel> getLodgingMap(
            Collection<MoeGroomingPetDetail> petDetailList,
            Collection<BoardingSplitLodging> splitLodgings,
            long companyId) {
        if (CollectionUtils.isEmpty(petDetailList) && CollectionUtils.isEmpty(splitLodgings)) {
            return Map.of();
        }
        var allLodgingUnitIds = Stream.concat(
                        petDetailList.stream().map(MoeGroomingPetDetail::getLodgingId),
                        splitLodgings.stream().map(BoardingSplitLodging::getLodgingId))
                .filter(k -> k > 0)
                .distinct()
                .toList();
        return lodgingUnitClient
                .getLodgingUnitList(GetLodgingUnitListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllUnitIds(allLodgingUnitIds)
                        .build())
                .getLodgingUnitListList()
                .stream()
                .collect(Collectors.toMap(LodgingUnitModel::getId, Function.identity()));
    }

    public Map<Long, LodgingUnitModel> getLodgingMapByEvaluationDetails(
            long companyId, Collection<EvaluationServiceDetail> evaluationDetails) {
        if (CollectionUtils.isEmpty(evaluationDetails)) {
            return Map.of();
        }
        var lodgingUnitIds = evaluationDetails.stream()
                .map(EvaluationServiceDetail::getLodgingId)
                .filter(id -> id > 0)
                .distinct()
                .toList();
        return getLodgingMap(companyId, lodgingUnitIds);
    }

    public Map<Long, LodgingUnitModel> getLodgingMap(long companyId, Collection<Long> lodgingUnitIds) {
        if (CollectionUtils.isEmpty(lodgingUnitIds)) {
            return Map.of();
        }
        return lodgingUnitClient
                .getLodgingUnitList(GetLodgingUnitListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllUnitIds(lodgingUnitIds)
                        .build())
                .getLodgingUnitListList()
                .stream()
                .collect(Collectors.toMap(LodgingUnitModel::getId, Function.identity()));
    }
}
