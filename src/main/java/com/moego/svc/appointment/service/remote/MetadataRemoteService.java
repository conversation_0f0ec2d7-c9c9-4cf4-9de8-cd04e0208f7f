package com.moego.svc.appointment.service.remote;

import com.moego.idl.models.metadata.v1.ValueModel;
import com.moego.idl.service.metadata.v1.DescribeValuesRequest;
import com.moego.idl.service.metadata.v1.GetKeyRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MetadataRemoteService {
    private static final String ALLOW_BOARDING_AND_DAYCARE = "allow_boarding_and_daycare";

    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;

    public boolean isAllowBoardingAndDaycare(long companyId) {
        return metadataServiceBlockingStub
                .describeValues(DescribeValuesRequest.newBuilder()
                        .setKeyId(metadataServiceBlockingStub
                                .getKey(GetKeyRequest.newBuilder()
                                        .setName(ALLOW_BOARDING_AND_DAYCARE)
                                        .build())
                                .getKey()
                                .getId())
                        .addOwnerIds(companyId)
                        .build())
                .getValuesList()
                .stream()
                .map(ValueModel::getValue)
                .anyMatch(value -> value.equalsIgnoreCase("true"));
    }
}
