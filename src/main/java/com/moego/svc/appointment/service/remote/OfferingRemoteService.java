package com.moego.svc.appointment.service.remote;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.EvaluationModel;
import com.moego.idl.models.offering.v1.PlaygroupModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceOverrideRule;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetEvaluationListRequest;
import com.moego.idl.service.offering.v1.GetEvaluationListWithEvaluationIdsRequest;
import com.moego.idl.service.offering.v1.GetServiceByPetAndServiceIdRequest;
import com.moego.idl.service.offering.v1.GetServiceByPetAndServiceIdResponse;
import com.moego.idl.service.offering.v1.GetServiceDetailRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ListPlaygroupRequest;
import com.moego.idl.service.offering.v1.OverrideServiceRequest;
import com.moego.idl.service.offering.v1.PlaygroupServiceGrpc;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.utils.v2.Int64List;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.svc.appointment.converter.ServiceOverrideConverter;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.dto.PetServiceOverrideDTO;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/1/24
 */
@Service
@RequiredArgsConstructor
public class OfferingRemoteService {

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceBlockingStub;
    private final EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationServiceBlockingStub;
    private final ServiceOverrideConverter serviceOverrideConverter;
    private final PlaygroupServiceGrpc.PlaygroupServiceBlockingStub playgroupServiceStub;

    public Map<Long, Map<Long, CustomizedServiceView>> listService(
            Long companyId, Long businessId, List<PetDetailDef> defs) {
        // 一只 pet 可能存在多个 defs
        var petServiceIdsMap = defs.stream()
                .collect(Collectors.groupingBy(
                        PetDetailDef::getPetId,
                        Collectors.flatMapping(
                                e -> {
                                    var serviceIdsStream =
                                            e.getServicesList().stream().map(SelectedServiceDef::getServiceId);
                                    var addOnIdsStream =
                                            e.getAddOnsList().stream().map(SelectedAddOnDef::getAddOnId);
                                    return Stream.concat(serviceIdsStream, addOnIdsStream)
                                            .distinct();
                                },
                                Collectors.toList())));

        return listService(companyId, businessId, petServiceIdsMap);
    }

    /**
     * List pet's available service list
     * Includes the override configuration
     * Pet override > business override > company override
     *
     * @param companyId        company id
     * @param businessId      business id
     * @param petServiceIdsMap pet id -> selected service ids
     * @return pet id -> available service id -> service model
     */
    public Map<Long, Map<Long, CustomizedServiceView>> listService(
            Long companyId, Long businessId, Map<Long, List<Long>> petServiceIdsMap) {
        if (CollectionUtils.isEmpty(petServiceIdsMap)) {
            return Map.of();
        }
        Map<Long, Int64List> petIdWithServiceIds = petServiceIdsMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> Int64List.newBuilder()
                        .addAllValues(entry.getValue())
                        .build()));
        GetServiceByPetAndServiceIdResponse response =
                serviceBlockingStub.getServiceByPetAndServiceId(GetServiceByPetAndServiceIdRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .putAllPetIdWithServiceIdList(petIdWithServiceIds)
                        .build());
        return response.getPetIdWithAvailableServiceListMap().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getServicesList().stream()
                        .collect(Collectors.toMap(CustomizedServiceView::getId, Function.identity(), (s1, s2) -> s1))));
    }

    public void overrideCustomServiceSetting(Long companyId, List<PetServiceOverrideDTO> overrideDTOList) {
        Map<Long, ServiceOverrideRule> overrideRulesByServiceId =
                overrideDTOList.stream()
                        .collect(Collectors.groupingBy(PetServiceOverrideDTO::getServiceId))
                        .entrySet()
                        .stream()
                        .collect(Collectors.toMap(
                                entry -> entry.getKey().longValue(), entry -> ServiceOverrideRule.newBuilder()
                                        .addAllPetOverrideList(entry.getValue().stream()
                                                .map(serviceOverrideConverter::toPetOverrideRule)
                                                .toList())
                                        .build()));
        serviceBlockingStub.overrideService(OverrideServiceRequest.newBuilder()
                .setCompanyId(companyId)
                .putAllOverrideRulesByServiceId(overrideRulesByServiceId)
                .build());
    }

    public List<ServiceBriefView> getServiceModels(Long companyId, List<Long> serviceIds) {
        var builder = GetServiceListByIdsRequest.newBuilder().addAllServiceIds(serviceIds);
        if (CommonUtil.isNormal(companyId)) {
            builder.setCompanyId(companyId);
        }
        return serviceBlockingStub.getServiceListByIds(builder.build()).getServicesList();
    }

    public Map<Long, EvaluationBriefView> getEvaluations(List<Long> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Map.of();
        }
        return evaluationServiceBlockingStub
                .getEvaluationListWithEvaluationIds(GetEvaluationListWithEvaluationIdsRequest.newBuilder()
                        .addAllEvaluationIds(serviceIds)
                        .build())
                .getEvaluationsList()
                .stream()
                .collect(Collectors.toMap(EvaluationBriefView::getId, Function.identity(), (k1, k2) -> k1));
    }

    public ServiceModel getServiceModel(Long serviceId) {
        var response = serviceBlockingStub.getServiceDetail(
                GetServiceDetailRequest.newBuilder().setServiceId(serviceId).build());
        return response.getService();
    }

    public List<PlaygroupModel> getPlaygroups(Long companyId) {
        return playgroupServiceStub
                .listPlaygroup(ListPlaygroupRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1000)
                                .build())
                        .build())
                .getPlaygroupsList();
    }

    public Map<Long, CustomizedServiceView> getCustomizedServiceMap(
            Collection<MoeGroomingPetDetail> petDetailList, long companyId, long businessId) {
        if (CollectionUtils.isEmpty(petDetailList)) {
            return Map.of();
        }
        var serviceIds = petDetailList.stream()
                .map(MoeGroomingPetDetail::getServiceId)
                .filter(k -> k > 0)
                .distinct()
                .toList();
        var queryConditions = serviceIds.stream()
                .map(serviceId -> CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(serviceId)
                        .setBusinessId(businessId)
                        .build())
                .toList();
        return serviceBlockingStub
                .batchGetCustomizedService(BatchGetCustomizedServiceRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllQueryConditionList(queryConditions)
                        .build())
                .getCustomizedServiceListList()
                .stream()
                .collect(Collectors.toMap(
                        // Key: service ID from query condition
                        serviceInfo -> serviceInfo.getQueryCondition().getServiceId(),
                        BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService,
                        (existing, replacement) -> existing));
    }

    public Map<Long, EvaluationModel> listEvaluations(long companyId) {
        return evaluationServiceBlockingStub
                .getEvaluationList(GetEvaluationListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getEvaluationsList()
                .stream()
                .collect(Collectors.toMap(EvaluationModel::getId, Function.identity(), (k1, k2) -> k1));
    }
}
