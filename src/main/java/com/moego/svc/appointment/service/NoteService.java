package com.moego.svc.appointment.service;

import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

import com.moego.idl.models.appointment.v1.AppointmentNoteType;
import com.moego.svc.appointment.domain.MoeGroomingNote;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingNoteDynamicSqlSupport;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingNoteMapper;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2021/1/25
 */
@Service
@RequiredArgsConstructor
public class NoteService {

    private final MoeGroomingNoteMapper groomingNoteMapper;

    @Transactional
    public int insertMultiple(List<MoeGroomingNote> notes) {
        if (CollectionUtils.isEmpty(notes)) {
            return 0;
        }

        int count = 0;
        for (var note : notes) {
            var id = insert(note);
            if (id > 0) {
                count++;
            }
        }

        return count;
    }

    public List<MoeGroomingNote> getNoteByAppointmentId(Collection<Long> appointmentIdList) {
        return groomingNoteMapper
                .select(c -> c.where(
                                MoeGroomingNoteDynamicSqlSupport.groomingId,
                                isIn(appointmentIdList.stream()
                                        .map(Long::intValue)
                                        .toList()))
                        .and(MoeGroomingNoteDynamicSqlSupport.isDeleted, isEqualTo(Boolean.FALSE)))
                .stream()
                .filter(e -> StringUtils.hasText(e.getNote()))
                .toList();
    }

    @Nullable
    public MoeGroomingNote getLastNoteByCustomerId(Long companyId, Long customerId, AppointmentNoteType type) {
        return groomingNoteMapper
                .selectOne(completer -> completer
                        .where(MoeGroomingNoteDynamicSqlSupport.companyId, isEqualTo(companyId))
                        .and(MoeGroomingNoteDynamicSqlSupport.customerId, isEqualTo(Math.toIntExact(customerId)))
                        .and(MoeGroomingNoteDynamicSqlSupport.type, isEqualTo((byte) type.getNumber()))
                        .and(MoeGroomingNoteDynamicSqlSupport.isDeleted, isEqualTo(Boolean.FALSE))
                        .orderBy(MoeGroomingNoteDynamicSqlSupport.updateTime.descending())
                        .limit(1))
                .filter(e -> StringUtils.hasText(e.getNote()))
                .orElse(null);
    }

    public long insert(MoeGroomingNote note) {
        if (!StringUtils.hasText(note.getNote())) {
            return 0;
        }
        groomingNoteMapper.insertSelective(note);
        return note.getId();
    }

    public MoeGroomingNote getNoteById(Integer id) {
        return groomingNoteMapper
                .selectByPrimaryKey(id)
                .filter(e -> StringUtils.hasText(e.getNote()))
                .orElse(null);
    }

    public int update(MoeGroomingNote note) {
        if (!StringUtils.hasText(note.getNote())) {
            return 0;
        }
        return groomingNoteMapper.updateByPrimaryKeySelective(note);
    }

    public List<MoeGroomingNote> listAppointmentNotes(
            Long companyId,
            List<Long> customerIds,
            List<Long> businessIds,
            List<AppointmentNoteType> types,
            List<Long> appointmentIdList) {
        return groomingNoteMapper
                .select(c -> {
                    var completer = c.where(MoeGroomingNoteDynamicSqlSupport.companyId, isEqualTo(companyId))
                            .and(MoeGroomingNoteDynamicSqlSupport.isDeleted, isEqualTo(Boolean.FALSE));

                    if (!CollectionUtils.isEmpty(customerIds)) {
                        completer.and(
                                MoeGroomingNoteDynamicSqlSupport.customerId,
                                isIn(customerIds.stream().map(Math::toIntExact).toList()));
                    }
                    if (!CollectionUtils.isEmpty(businessIds)) {
                        completer.and(
                                MoeGroomingNoteDynamicSqlSupport.businessId,
                                isIn(businessIds.stream().map(Math::toIntExact).toList()));
                    }
                    if (!CollectionUtils.isEmpty(types)) {
                        completer.and(
                                MoeGroomingNoteDynamicSqlSupport.type,
                                isIn(types.stream()
                                        .map(k -> (byte) k.getNumber())
                                        .toList()));
                    }

                    if (!CollectionUtils.isEmpty(appointmentIdList)) {
                        completer.and(
                                MoeGroomingNoteDynamicSqlSupport.groomingId,
                                isIn(appointmentIdList.stream()
                                        .map(Math::toIntExact)
                                        .toList()));
                    }
                    completer.orderBy(MoeGroomingNoteDynamicSqlSupport.updateTime.descending());

                    return completer;
                })
                .stream()
                .filter(e -> StringUtils.hasText(e.getNote()))
                .toList();
    }
}
