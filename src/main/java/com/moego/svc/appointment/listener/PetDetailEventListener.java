package com.moego.svc.appointment.listener;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.offering.v1.AutoRolloverRuleServiceGrpc.AutoRolloverRuleServiceBlockingStub;
import com.moego.idl.service.offering.v1.BatchGetAutoRolloverRuleRequest;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.svc.appointment.domain.DaycareAutoRolloverRecord;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.MoeGroomingServiceOperation;
import com.moego.svc.appointment.listener.event.PetDetailEvent;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.DaycareAutoRolloverRecordService;
import com.moego.svc.appointment.service.ServiceOperationService;
import com.moego.svc.appointment.service.params.UpdateExtraOrderByPetDetailParams;
import com.moego.svc.appointment.service.remote.OrderRemoteService;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PetDetailEventListener implements ApplicationListener<PetDetailEvent> {

    private final AppointmentServiceProxy appointmentService;
    private final DaycareAutoRolloverRecordService daycareAutoRolloverRecordService;
    private final AutoRolloverRuleServiceBlockingStub autoRolloverRuleStub;
    private final OrderRemoteService orderRemoteService;
    private final ServiceOperationService serviceOperationService;

    @Override
    public void onApplicationEvent(PetDetailEvent event) {
        if (event instanceof PetDetailEvent.Created createdEvent) {
            handleCreatedEvent(createdEvent);
            return;
        }
        if (event instanceof PetDetailEvent.Updated updatedEvent) {
            handleUpdatedEvent(updatedEvent);
            return;
        }
        if (event instanceof PetDetailEvent.Deleted deletedEvent) {
            handleDeletedEvent(deletedEvent);
        }
    }

    void handleCreatedEvent(PetDetailEvent.Created createdEvent) {
        var petDetail = createdEvent.getEntity();

        ThreadPool.execute(() -> tryCreateDaycareAutoRolloverRecord(petDetail));
    }

    void handleUpdatedEvent(PetDetailEvent.Updated updatedEvent) {

        var before = updatedEvent.getBefore();
        var after = updatedEvent.getAfter();

        // 更新 AutoRolloverRecord，更新 rollover 时间
        updateAutoRollover(before, after);

        // 更新 multi staff 信息
        updateServiceOperation(before, after);

        // 更新 order 价格
        if (updatedEvent.isShouldUpdateOrder()) {
            updateOrderPrice(before, after);
        }

        // 更新对应 appointment 的时间
        updateAppointmentDateTime(before, after);
    }

    private void updateServiceOperation(MoeGroomingPetDetail before, MoeGroomingPetDetail after) {
        var serviceOperationList = serviceOperationService
                .listByPetDetailIds(List.of(before.getId()))
                .getOrDefault(before.getId(), List.of());
        if (serviceOperationList.isEmpty()) {
            return;
        }

        // ServiceOperation 只关注 duration 和 price，都相等则不需要更新
        if (Objects.equals(before.getServiceTime(), after.getServiceTime())
                && before.getServicePrice().compareTo(after.getServicePrice()) == 0) {
            return;
        }

        serviceOperationService.reassignOperationTime(
                after.getServiceTime(), before.getServiceTime(), serviceOperationList);

        serviceOperationService.reassignOperationPrice(
                after.getServicePrice(), before.getServicePrice(), serviceOperationList);

        for (var serviceOperation : serviceOperationList) {
            var updateBean = new MoeGroomingServiceOperation();
            updateBean.setId(serviceOperation.getId());
            updateBean.setPrice(serviceOperation.getPrice());
            updateBean.setPriceRatio(serviceOperation.getPriceRatio());
            updateBean.setDuration(serviceOperation.getDuration());
            updateBean.setStartTime(serviceOperation.getStartTime());
            serviceOperationService.update(updateBean);
        }
    }

    private void updateOrderPrice(MoeGroomingPetDetail before, MoeGroomingPetDetail after) {

        // 如果修改了 service id，或者直接修改了价格，都需要更新 order

        // 注意：这里不要因为 serviceId 不同就去更新 PetDetail 上的价格！
        // PetDetail 的价格是一个快照，应该由调用方自行更新

        if (!Objects.equals(before.getServiceId(), after.getServiceId())
                || before.getServicePrice().compareTo(after.getServicePrice()) != 0) {
            // check extra order by appointment id
            OrderModel order = orderRemoteService.getLatestOrderByAppointmentId(after.getGroomingId());
            if (!order.hasId() || !CommonUtil.isNormal(order.getId())) {
                return;
            }

            if (OrderModel.OrderType.ORIGIN.equals(order.getOrderType())) {
                orderRemoteService.updateOrder(appointmentService.mustGet(after.getGroomingId()));
            } else {
                orderRemoteService.updateExtraOrder(
                        UpdateExtraOrderByPetDetailParams.builder() // update service id or price
                                .extraOrderId(order.getId())
                                .appointment(appointmentService.mustGet(after.getGroomingId()))
                                .beforePetDetail(before)
                                .afterPetDetail(after)
                                .build());
            }
        }
    }

    private void updateAutoRollover(MoeGroomingPetDetail before, MoeGroomingPetDetail after) {
        if (Objects.equals(before.getServiceId(), after.getServiceId())) {
            return;
        }

        ThreadPool.execute(() -> {
            tryDeleteDaycareAutoRolloverRecord(before);
            tryCreateDaycareAutoRolloverRecord(after);
        });
    }

    void handleDeletedEvent(PetDetailEvent.Deleted deletedEvent) {
        var before = deletedEvent.getEntity();

        // 由于 PetDetail 现在是先删后增，提供一个参数防止重复调用，当 pet detail 改为原地更新时，移除 isShouldUpdateOrder
        if (deletedEvent.isShouldUpdateOrder()) {
            var appointment = appointmentService.mustGet(before.getGroomingId());
            orderRemoteService.updateOrder(appointment);
        }

        // 更新 extra order
        updateExtraOrder(before);

        // 移除 daycare auto rollover 记录
        ThreadPool.execute(() -> tryDeleteDaycareAutoRolloverRecord(before));
    }

    private void updateExtraOrder(MoeGroomingPetDetail before) {
        // delete extra order line item and relation if needed
        OrderModel order = orderRemoteService.getLatestOrderByAppointmentId(before.getGroomingId());
        if (order.getId() > 0 && !OrderModel.OrderType.ORIGIN.equals(order.getOrderType())) {
            orderRemoteService.updateExtraOrder(
                    UpdateExtraOrderByPetDetailParams.builder() // delete extra order line item and relation
                            .extraOrderId(order.getId())
                            .appointment(appointmentService.mustGet(before.getGroomingId()))
                            .deletedPetDetails(List.of(before))
                            .build());
        }
    }

    private void tryDeleteDaycareAutoRolloverRecord(MoeGroomingPetDetail petDetail) {
        if (!isDaycare(petDetail.getServiceItemType())) {
            return;
        }

        daycareAutoRolloverRecordService.deleteByPetDetailId(petDetail.getId());
    }

    private void tryCreateDaycareAutoRolloverRecord(MoeGroomingPetDetail petDetail) {
        if (!isDaycare(petDetail.getServiceItemType())
                || !isAutoRolloverEnabled(petDetail.getServiceId())
                || !isInStore(petDetail.getGroomingId())) {
            return;
        }

        insertDaycareAutoRolloverRecord(petDetail.getId());
    }

    private boolean isAutoRolloverEnabled(long serviceId) {
        var rolloverRule = autoRolloverRuleStub
                .batchGetAutoRolloverRule(BatchGetAutoRolloverRuleRequest.newBuilder()
                        .addServiceIds(serviceId)
                        .build())
                .getServiceIdToAutoRolloverRuleMap()
                .get(serviceId);
        return rolloverRule != null && rolloverRule.getEnabled();
    }

    private boolean isInStore(int appointmentId) {
        var appointment = appointmentService.mustGet(appointmentId);
        return Objects.equals(appointment.getStatus(), (byte) AppointmentStatus.CHECKED_IN_VALUE)
                || Objects.equals(appointment.getStatus(), (byte) AppointmentStatus.READY_VALUE);
    }

    private void insertDaycareAutoRolloverRecord(long pedDetailId) {
        var autoRollover = new DaycareAutoRolloverRecord();
        autoRollover.setDaycareServiceDetailId(pedDetailId);

        daycareAutoRolloverRecordService.insert(autoRollover);
    }

    private static boolean isDaycare(Number serviceItemType) {
        return Objects.equals(serviceItemType.intValue(), ServiceItemType.DAYCARE_VALUE);
    }

    private void updateAppointmentDateTime(MoeGroomingPetDetail before, MoeGroomingPetDetail after) {
        if (noNeedUpdateDateTime(before, after)) {
            return;
        }
        var appointment = appointmentService.get(after.getGroomingId());
        if (appointment == null) {
            return;
        }
        appointmentService.refreshAppointmentDateTime(appointment);
    }

    private static boolean noNeedUpdateDateTime(MoeGroomingPetDetail before, MoeGroomingPetDetail after) {
        return Objects.equals(before.getStartDate(), after.getStartDate())
                && Objects.equals(before.getStartTime(), after.getStartTime())
                && Objects.equals(before.getEndDate(), after.getEndDate())
                && Objects.equals(before.getEndTime(), after.getEndTime());
    }
}
