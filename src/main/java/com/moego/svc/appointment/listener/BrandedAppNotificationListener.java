package com.moego.svc.appointment.listener;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.account.v1.AccountNamespaceType;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.notification.v1.AppPushDef;
import com.moego.idl.models.notification.v1.AppointmentBookedDef;
import com.moego.idl.models.notification.v1.AppointmentCancelledDef;
import com.moego.idl.models.notification.v1.AppointmentRescheduledDef;
import com.moego.idl.models.notification.v1.NotificationExtraDef;
import com.moego.idl.models.notification.v1.NotificationType;
import com.moego.idl.models.notification.v1.PushTokenSource;
import com.moego.idl.service.account.v1.AccountServiceGrpc.AccountServiceBlockingStub;
import com.moego.idl.service.account.v1.GetAccountRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.GetCustomerRequest;
import com.moego.idl.service.notification.v1.AppPushServiceGrpc.AppPushServiceBlockingStub;
import com.moego.idl.service.notification.v1.CreateAppPushRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc.BusinessServiceBlockingStub;
import com.moego.idl.service.organization.v1.GetLocationDetailRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.listener.event.CreateAppointmentEvent;
import com.moego.svc.appointment.listener.event.RescheduleCalendarCardEvent;
import com.moego.svc.appointment.listener.event.RescheduleGroomingServiceEvent;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/7/29
 */
@Component
@RequiredArgsConstructor
public class BrandedAppNotificationListener {

    private final AppointmentServiceProxy appointmentService;
    private final PetDetailServiceProxy petDetailService;

    private final AccountServiceBlockingStub accountService;
    private final AppPushServiceBlockingStub appPushService;
    private final BusinessCustomerServiceBlockingStub businessCustomerService;
    private final BusinessCustomerPetServiceBlockingStub businessCustomerPetService;
    private final BusinessServiceBlockingStub businessService;

    @EventListener
    public void notification(CreateAppointmentEvent event) {
        ThreadPool.execute(() -> {
            var appointment = appointmentService.getAppointment(event.getCompanyId(), event.getAppointmentId());
            var account = getRelatedAccount(appointment.getCustomerId());
            if (account == null || !isFromBrandedApp(account)) {
                return;
            }
            var request = CreateAppPushRequest.newBuilder()
                    .setAccountId(account.getId())
                    .setType(NotificationType.NOTIFICATION_TYPE_APPOINTMENT_BOOKED)
                    .setExtra(buildNotificationExtraDef(
                            NotificationType.NOTIFICATION_TYPE_APPOINTMENT_BOOKED, appointment))
                    .setAppPush(AppPushDef.newBuilder()
                            .setSource(PushTokenSource.PUSH_TOKEN_SOURCE_CLIENT)
                            .setFromTemplate(true)
                            .build())
                    .build();
            appPushService.createAppPush(request);
        });
    }

    private boolean isBlock(MoeGroomingAppointment appointment) {
        return Objects.equals(appointment.getIsBlock().intValue(), GroomingAppointmentEnum.IS_BLOCK_TRUE);
    }

    @EventListener
    public void notification(RescheduleCalendarCardEvent event) {
        ThreadPool.execute(() -> notification(event.getAppointmentId()));
    }

    private void notification(long appointmentId) {
        var appointment = appointmentService.mustGet(appointmentId);
        if (isBlock(appointment)) {
            return;
        }
        var account = getRelatedAccount(appointment.getCustomerId());
        if (account == null || !isFromBrandedApp(account)) {
            return;
        }
        var request = CreateAppPushRequest.newBuilder()
                .setAccountId(account.getId())
                .setType(NotificationType.NOTIFICATION_TYPE_APPOINTMENT_RESCHEDULED)
                .setExtra(buildNotificationExtraDef(
                        NotificationType.NOTIFICATION_TYPE_APPOINTMENT_RESCHEDULED, appointment))
                .setAppPush(AppPushDef.newBuilder()
                        .setSource(PushTokenSource.PUSH_TOKEN_SOURCE_CLIENT)
                        .setFromTemplate(true)
                        .build())
                .build();
        appPushService.createAppPush(request);
    }

    @EventListener
    public void notification(RescheduleGroomingServiceEvent event) {
        ThreadPool.execute(() -> notification(event.getAppointmentId()));
    }

    private boolean isFromBrandedApp(AccountModel account) {
        return !Objects.equals(account.getNamespace().getType(), AccountNamespaceType.MOEGO);
    }

    private AccountModel getRelatedAccount(long customerId) {
        var customer = businessCustomerService
                .getCustomer(GetCustomerRequest.newBuilder().setId(customerId).build())
                .getCustomer();
        if (customer.getAccountId() == 0L) {
            return null;
        }
        return accountService.getAccount(
                GetAccountRequest.newBuilder().setId(customer.getAccountId()).build());
    }

    private NotificationExtraDef buildNotificationExtraDef(NotificationType type, MoeGroomingAppointment appointment) {
        var location = businessService
                .getLocationDetail(GetLocationDetailRequest.newBuilder()
                        .setId(appointment.getBusinessId())
                        .build())
                .getLocation();
        var pets = getPets(appointment.getId());
        var appointmentDate = LocalDate.parse(appointment.getAppointmentDate());
        String dayOfWeek = appointmentDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH);
        String month = appointmentDate.getMonth().getDisplayName(TextStyle.SHORT, Locale.ENGLISH);
        String day = appointmentDate.format(DateTimeFormatter.ofPattern("dd"));
        String petName =
                pets.stream().map(BusinessCustomerPetInfoModel::getPetName).collect(Collectors.joining(" & "));
        String verb = pets.size() == 1 ? "is" : "are";
        return switch (type) {
            case NOTIFICATION_TYPE_APPOINTMENT_BOOKED -> NotificationExtraDef.newBuilder()
                    .setAppointmentBooked(AppointmentBookedDef.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setBusinessId(appointment.getBusinessId())
                            .setBusinessName(location.getName())
                            .setPetName(petName)
                            .setVerb(verb)
                            .setDayOfWeek(dayOfWeek)
                            .setMonth(month)
                            .setDay(day)
                            .build())
                    .build();
            case NOTIFICATION_TYPE_APPOINTMENT_RESCHEDULED -> NotificationExtraDef.newBuilder()
                    .setAppointmentRescheduled(AppointmentRescheduledDef.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setBusinessId(appointment.getBusinessId())
                            .setBusinessName(location.getName())
                            .setDayOfWeek(dayOfWeek)
                            .setMonth(month)
                            .setDay(day)
                            .build())
                    .build();
            case NOTIFICATION_TYPE_APPOINTMENT_CANCELLED -> NotificationExtraDef.newBuilder()
                    .setAppointmentCancelled(AppointmentCancelledDef.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setBusinessId(appointment.getBusinessId())
                            .setBusinessName(location.getName())
                            .setDayOfWeek(dayOfWeek)
                            .setMonth(month)
                            .setDay(day)
                            .build())
                    .build();
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Unsupported notification type: " + type);
        };
    }

    private List<BusinessCustomerPetInfoModel> getPets(long appointmentId) {
        var petDetails = petDetailService.getPetDetailList(appointmentId);
        if (CollectionUtils.isEmpty(petDetails)) {
            return List.of();
        }
        var petIds = petDetails.stream()
                .map(MoeGroomingPetDetail::getPetId)
                .map(Integer::longValue)
                .collect(Collectors.toSet());
        return businessCustomerPetService
                .batchGetPetInfo(
                        BatchGetPetInfoRequest.newBuilder().addAllIds(petIds).build())
                .getPetsList();
    }
}
