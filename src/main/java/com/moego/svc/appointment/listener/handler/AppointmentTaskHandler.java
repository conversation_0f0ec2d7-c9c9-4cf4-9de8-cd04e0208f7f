package com.moego.svc.appointment.listener.handler;

import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.PetScheduleDef;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.listener.event.AppointmentEvent;
import com.moego.svc.appointment.listener.event.CancelAppointmentEvent;
import com.moego.svc.appointment.listener.event.DeleteAppointmentPetDetailEvent;
import com.moego.svc.appointment.listener.event.ReschedulePetDetailsEvent;
import com.moego.svc.appointment.listener.event.ReschedulePetFeedingMedicationEvent;
import com.moego.svc.appointment.listener.event.SaveOrUpdatePetDetailEvent;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.AppointmentTaskService;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Appointment task 的创建、修改、删除处理器 <br>
 * 只对 boarding 和 daycare 白名单所在商家有效 <br>
 * 由于 add-on 类型的 pet detail 存在依赖关系，无法基于 {@link com.moego.svc.appointment.listener.event.PetDetailEvent} 实现
 *
 * <AUTHOR>
 * @since 2024/12/29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AppointmentTaskHandler {

    private final AppointmentTaskService appointmentTaskService;
    private final AppointmentServiceProxy appointmentService;
    private final PetDetailServiceProxy petDetailService;

    /**
     * 单只 pet 的 service 保存、修改时生成 feeding/medication/add-on task
     *
     * @param event save or update pet detail
     */
    @EventListener({SaveOrUpdatePetDetailEvent.class, ReschedulePetDetailsEvent.class})
    @Transactional
    public void handleSaveOrUpdatePetDetail(ApplicationEvent event) {
        var context = getRescheduleContext(event);
        var appointment = appointmentService.mustGet(context.appointmentId);
        var petDetails = petDetailService.getPetDetailList(context.appointmentId);

        appointmentTaskService.recreateAllTasksByAppointment(appointment, petDetails);
    }

    /**
     * 删除某只 pet 及其关联的 service 时删除 task
     *
     * @param event delete pet's all details
     */
    @EventListener
    public void deleteTasksByPet(DeleteAppointmentPetDetailEvent event) {
        appointmentTaskService.deleteByPets(event.getAppointmentId(), event.getPetId());
    }

    /**
     * Cancel appointment 时删除所有 task
     *
     * @param event cancel appointment event
     */
    @EventListener
    public void cancelAppointment(CancelAppointmentEvent event) {
        appointmentTaskService.deleteByAppointment(event.getAppointmentId());
    }

    private record RescheduleContext(long appointmentId, List<Long> petIds) {}

    private static RescheduleContext getRescheduleContext(ApplicationEvent event) {
        if (event instanceof SaveOrUpdatePetDetailEvent sdEvent) {
            var petIds = sdEvent.getPetDetailDefs().stream()
                    .map(PetDetailDef::getPetId)
                    .distinct()
                    .toList();
            return new RescheduleContext(sdEvent.getAppointmentId(), petIds);
        }
        if (event instanceof ReschedulePetFeedingMedicationEvent fmEvent) {
            var petIds = fmEvent.getPetSchedules().stream()
                    .map(PetScheduleDef::getPetId)
                    .distinct()
                    .toList();
            return new RescheduleContext(fmEvent.getAppointmentId(), petIds);
        }
        if (event instanceof ReschedulePetDetailsEvent pdEvent) {
            var petIds = pdEvent.getBeforePetDetails().stream()
                    .map(MoeGroomingPetDetail::getPetId)
                    .map(Integer::longValue)
                    .distinct()
                    .toList();
            return new RescheduleContext(pdEvent.getAppointmentId(), petIds);
        }
        if (event instanceof AppointmentEvent.Updated updatedEvent) {
            var appointmentId = updatedEvent.getAfter().getId();
            return new RescheduleContext(appointmentId, List.of());
        }
        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unknown reschedule event");
    }

    /**
     * 多只 pet 的 feeding/medication 保存、修改时重新生成 task
     *
     * @param event reschedule pet feeding and medication
     */
    @EventListener
    public void rescheduleBasicTasks(ReschedulePetFeedingMedicationEvent event) {
        var context = getRescheduleContext(event);
        var appointment = appointmentService.mustGet(context.appointmentId);
        var petDetails = petDetailService.getPetDetailList(context.appointmentId, context.petIds);

        appointmentTaskService.recreateBasicTasksByAppointment(appointment, petDetails);
    }
}
