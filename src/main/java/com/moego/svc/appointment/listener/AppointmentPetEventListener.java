package com.moego.svc.appointment.listener;

import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.event_bus.v1.AppointmentPetServiceInfoEvent;
import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.idl.models.event_bus.v1.EventType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.lib.event_bus.producer.Producer;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.listener.event.DeleteAppointmentPetDetailEvent;
import com.moego.svc.appointment.listener.event.SaveOrUpdatePetDetailEvent;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import com.moego.svc.appointment.utils.PetDetailUtil;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AppointmentPetEventListener {
    private final PetDetailServiceProxy petDetailService;
    private final Producer producer;
    private static final String EVENT_TOPIC = "moego.erp.appointment";

    @EventListener
    public void handleSaveOrUpdatePetDetail(SaveOrUpdatePetDetailEvent event) {
        ThreadPool.execute(() -> {
            producer.send(
                    EVENT_TOPIC,
                    EventRecord.builder()
                            .id(event.getAppointmentId().toString())
                            .detail(EventData.newBuilder()
                                    .setTenant(Tenant.newBuilder()
                                            .setCompanyId(event.getCompanyId())
                                            .setBusinessId(event.getBusinessId())
                                            .build())
                                    .setAppointmentPetServiceEvent(AppointmentPetServiceInfoEvent.newBuilder()
                                            .setId(event.getAppointmentId())
                                            .addAllPetDetails(event.getPetDetailDefs())
                                            .build())
                                    .build())
                            .time(Instant.now())
                            .type(EventType.APPOINTMENT_PET_SERVICE_INFO)
                            .build());
        });
    }

    @EventListener
    public void handleDeleteAppointmentPetDetail(DeleteAppointmentPetDetailEvent event) {

        ThreadPool.execute(() -> {
            List<MoeGroomingPetDetail> petDetailList = petDetailService.getPetDetailList(event.getAppointmentId());
            // 过滤出 service 类型的 petDetail，并按 petId -> serviceId -> serviceDetail 的形式
            Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap =
                    PetDetailUtil.getPetServiceMap(petDetailList);

            List<PetDetailDef> petDetailDefs = petServiceMap.entrySet().stream()
                    .map(entry -> {
                        Integer petId = entry.getKey();
                        Map<Integer, MoeGroomingPetDetail> serviceMap = entry.getValue();
                        List<SelectedServiceDef> serviceDefList = serviceMap.values().stream()
                                .filter(serviceDetail -> serviceDetail.getServiceType() == 1)
                                .map(serviceDetail -> SelectedServiceDef.newBuilder()
                                        .setServiceId(serviceDetail.getServiceId())
                                        .setDateType(PetDetailUtil.getDateType(serviceDetail))
                                        .setStartDate(serviceDetail.getStartDate())
                                        .setStartTime(Math.toIntExact(serviceDetail.getStartTime()))
                                        .setEndDate(serviceDetail.getEndDate())
                                        .setEndTime(Math.toIntExact(serviceDetail.getEndTime()))
                                        .build())
                                .toList();
                        List<SelectedAddOnDef> addOnDefList = serviceMap.values().stream()
                                .filter(serviceDetail -> serviceDetail.getServiceType() == 2)
                                .map(serviceDetail -> SelectedAddOnDef.newBuilder()
                                        .setAddOnId(serviceDetail.getServiceId())
                                        .build())
                                .toList();
                        return PetDetailDef.newBuilder()
                                .setPetId(petId)
                                .addAllServices(serviceDefList)
                                .addAllAddOns(addOnDefList)
                                .build();
                    })
                    .toList();

            producer.send(
                    EVENT_TOPIC,
                    EventRecord.builder()
                            .id(event.getAppointmentId().toString())
                            .detail(EventData.newBuilder()
                                    .setTenant(Tenant.newBuilder()
                                            .setCompanyId(event.getCompanyId())
                                            .setBusinessId(event.getBusinessId())
                                            .build())
                                    .setAppointmentPetServiceEvent(AppointmentPetServiceInfoEvent.newBuilder()
                                            .setId(event.getAppointmentId())
                                            .addAllPetDetails(petDetailDefs)
                                            .build())
                                    .build())
                            .time(Instant.now())
                            .type(EventType.APPOINTMENT_PET_SERVICE_INFO)
                            .build());
        });
    }
}
