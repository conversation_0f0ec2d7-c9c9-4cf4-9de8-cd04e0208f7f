package com.moego.svc.appointment.domain;

import java.util.Date;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_wait_list
 */
public class MoeWaitList {
    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source field: moe_wait_list.id")
    private Long id;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.business_id")
    private Long businessId;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.company_id")
    private Long companyId;

    /**
     * Database Column Remarks:
     *   预约订单主表id
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.appointment_id")
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   support 1: Exact date 2: Day of the week (multiple choice) 3: Any date
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.date_preference")
    private String datePreference;

    /**
     * Database Column Remarks:
     *   support 1: Exact start time 2: Morning/Afternoon/Evening 3: Any time
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.time_preference")
    private String timePreference;

    /**
     * Database Column Remarks:
     *   support 1: Select specific staff (multiple choice)  2: Anyone
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.staff_preference")
    private String staffPreference;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.valid_from")
    private Date validFrom;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.valid_till")
    private Date validTill;

    /**
     * Database Column Remarks:
     *   create time
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.created_at")
    private Date createdAt;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.created_by")
    private Long createdBy;

    /**
     * Database Column Remarks:
     *   update time
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.updated_at")
    private Date updatedAt;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.updated_by")
    private Long updatedBy;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.deleted_at")
    private Date deletedAt;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.idx_deleted_at")
    private Date idxDeletedAt;

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source field: moe_wait_list.id")
    public Long getId() {
        return id;
    }

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source field: moe_wait_list.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.business_id")
    public Long getBusinessId() {
        return businessId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.business_id")
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.company_id")
    public Long getCompanyId() {
        return companyId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.company_id")
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.appointment_id")
    public Long getAppointmentId() {
        return appointmentId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.appointment_id")
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.date_preference")
    public String getDatePreference() {
        return datePreference;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.date_preference")
    public void setDatePreference(String datePreference) {
        this.datePreference = datePreference == null ? null : datePreference.trim();
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.time_preference")
    public String getTimePreference() {
        return timePreference;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.time_preference")
    public void setTimePreference(String timePreference) {
        this.timePreference = timePreference == null ? null : timePreference.trim();
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.staff_preference")
    public String getStaffPreference() {
        return staffPreference;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.staff_preference")
    public void setStaffPreference(String staffPreference) {
        this.staffPreference = staffPreference == null ? null : staffPreference.trim();
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.valid_from")
    public Date getValidFrom() {
        return validFrom;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.valid_from")
    public void setValidFrom(Date validFrom) {
        this.validFrom = validFrom;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.valid_till")
    public Date getValidTill() {
        return validTill;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.valid_till")
    public void setValidTill(Date validTill) {
        this.validTill = validTill;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.created_by")
    public Long getCreatedBy() {
        return createdBy;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.created_by")
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.updated_by")
    public Long getUpdatedBy() {
        return updatedBy;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.updated_by")
    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.deleted_at")
    public Date getDeletedAt() {
        return deletedAt;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.deleted_at")
    public void setDeletedAt(Date deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.idx_deleted_at")
    public Date getIdxDeletedAt() {
        return idxDeletedAt;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_wait_list.idx_deleted_at")
    public void setIdxDeletedAt(Date idxDeletedAt) {
        this.idxDeletedAt = idxDeletedAt;
    }
}
