package com.moego.svc.appointment.domain;

import jakarta.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table appointment_extra_info
 */
public class AppointmentExtraInfo {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_extra_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   The appointment ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_extra_info.appointment_id")
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   Whether the appointment was created by a new order process
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_extra_info.is_new_order")
    private Boolean isNewOrder;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_extra_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_extra_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_extra_info.appointment_id")
    public Long getAppointmentId() {
        return appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_extra_info.appointment_id")
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_extra_info.is_new_order")
    public Boolean getIsNewOrder() {
        return isNewOrder;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_extra_info.is_new_order")
    public void setIsNewOrder(Boolean isNewOrder) {
        this.isNewOrder = isNewOrder;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appointmentId=").append(appointmentId);
        sb.append(", isNewOrder=").append(isNewOrder);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AppointmentExtraInfo other = (AppointmentExtraInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAppointmentId() == null ? other.getAppointmentId() == null : this.getAppointmentId().equals(other.getAppointmentId()))
            && (this.getIsNewOrder() == null ? other.getIsNewOrder() == null : this.getIsNewOrder().equals(other.getIsNewOrder()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAppointmentId() == null) ? 0 : getAppointmentId().hashCode());
        result = prime * result + ((getIsNewOrder() == null) ? 0 : getIsNewOrder().hashCode());
        return result;
    }
}