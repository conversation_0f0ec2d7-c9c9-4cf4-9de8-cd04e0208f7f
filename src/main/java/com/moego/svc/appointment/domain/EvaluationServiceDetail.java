package com.moego.svc.appointment.domain;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table evaluation_service_detail
 */
public class EvaluationServiceDetail {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   预约 id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.appointment_id")
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   宠物 id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.pet_id")
    private Long petId;

    /**
     * Database Column Remarks:
     *   服务 id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.service_id")
    private Long serviceId;

    /**
     * Database Column Remarks:
     *   服务费用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.service_price")
    private BigDecimal servicePrice;

    /**
     * Database Column Remarks:
     *   服务时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.service_time")
    private Integer serviceTime;

    /**
     * Database Column Remarks:
     *   开始日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.start_date")
    private LocalDate startDate;

    /**
     * Database Column Remarks:
     *   开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.start_time")
    private Integer startTime;

    /**
     * Database Column Remarks:
     *   结束日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.end_date")
    private LocalDate endDate;

    /**
     * Database Column Remarks:
     *   结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.end_time")
    private Integer endTime;

    /**
     * Database Column Remarks:
     *   create time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.created_at")
    private LocalDateTime createdAt;

    /**
     * Database Column Remarks:
     *   update time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.updated_at")
    private LocalDateTime updatedAt;

    /**
     * Database Column Remarks:
     *   The staff id responsible for this evaluation service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.staff_id")
    private Long staffId;

    /**
     * Database Column Remarks:
     *   lodging id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.lodging_id")
    private Long lodgingId;

    /**
     * Database Column Remarks:
     *   Order line item id, 0 if not created from order
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.order_line_item_id")
    private Long orderLineItemId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.appointment_id")
    public Long getAppointmentId() {
        return appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.appointment_id")
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.pet_id")
    public Long getPetId() {
        return petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.pet_id")
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.service_id")
    public Long getServiceId() {
        return serviceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.service_id")
    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.service_price")
    public BigDecimal getServicePrice() {
        return servicePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.service_price")
    public void setServicePrice(BigDecimal servicePrice) {
        this.servicePrice = servicePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.service_time")
    public Integer getServiceTime() {
        return serviceTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.service_time")
    public void setServiceTime(Integer serviceTime) {
        this.serviceTime = serviceTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.start_date")
    public LocalDate getStartDate() {
        return startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.start_date")
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.start_time")
    public Integer getStartTime() {
        return startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.start_time")
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.end_date")
    public LocalDate getEndDate() {
        return endDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.end_date")
    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.end_time")
    public Integer getEndTime() {
        return endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.end_time")
    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.staff_id")
    public Long getStaffId() {
        return staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.staff_id")
    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.lodging_id")
    public Long getLodgingId() {
        return lodgingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.lodging_id")
    public void setLodgingId(Long lodgingId) {
        this.lodgingId = lodgingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.order_line_item_id")
    public Long getOrderLineItemId() {
        return orderLineItemId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_service_detail.order_line_item_id")
    public void setOrderLineItemId(Long orderLineItemId) {
        this.orderLineItemId = orderLineItemId;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_service_detail")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appointmentId=").append(appointmentId);
        sb.append(", petId=").append(petId);
        sb.append(", serviceId=").append(serviceId);
        sb.append(", servicePrice=").append(servicePrice);
        sb.append(", serviceTime=").append(serviceTime);
        sb.append(", startDate=").append(startDate);
        sb.append(", startTime=").append(startTime);
        sb.append(", endDate=").append(endDate);
        sb.append(", endTime=").append(endTime);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", staffId=").append(staffId);
        sb.append(", lodgingId=").append(lodgingId);
        sb.append(", orderLineItemId=").append(orderLineItemId);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_service_detail")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        EvaluationServiceDetail other = (EvaluationServiceDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAppointmentId() == null ? other.getAppointmentId() == null : this.getAppointmentId().equals(other.getAppointmentId()))
            && (this.getPetId() == null ? other.getPetId() == null : this.getPetId().equals(other.getPetId()))
            && (this.getServiceId() == null ? other.getServiceId() == null : this.getServiceId().equals(other.getServiceId()))
            && (this.getServicePrice() == null ? other.getServicePrice() == null : this.getServicePrice().equals(other.getServicePrice()))
            && (this.getServiceTime() == null ? other.getServiceTime() == null : this.getServiceTime().equals(other.getServiceTime()))
            && (this.getStartDate() == null ? other.getStartDate() == null : this.getStartDate().equals(other.getStartDate()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getEndDate() == null ? other.getEndDate() == null : this.getEndDate().equals(other.getEndDate()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getStaffId() == null ? other.getStaffId() == null : this.getStaffId().equals(other.getStaffId()))
            && (this.getLodgingId() == null ? other.getLodgingId() == null : this.getLodgingId().equals(other.getLodgingId()))
            && (this.getOrderLineItemId() == null ? other.getOrderLineItemId() == null : this.getOrderLineItemId().equals(other.getOrderLineItemId()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_service_detail")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAppointmentId() == null) ? 0 : getAppointmentId().hashCode());
        result = prime * result + ((getPetId() == null) ? 0 : getPetId().hashCode());
        result = prime * result + ((getServiceId() == null) ? 0 : getServiceId().hashCode());
        result = prime * result + ((getServicePrice() == null) ? 0 : getServicePrice().hashCode());
        result = prime * result + ((getServiceTime() == null) ? 0 : getServiceTime().hashCode());
        result = prime * result + ((getStartDate() == null) ? 0 : getStartDate().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndDate() == null) ? 0 : getEndDate().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getStaffId() == null) ? 0 : getStaffId().hashCode());
        result = prime * result + ((getLodgingId() == null) ? 0 : getLodgingId().hashCode());
        result = prime * result + ((getOrderLineItemId() == null) ? 0 : getOrderLineItemId().hashCode());
        return result;
    }
}