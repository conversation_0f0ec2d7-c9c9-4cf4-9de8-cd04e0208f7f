package com.moego.svc.appointment.consumer;

import com.moego.idl.models.event_bus.v1.AppointmentCanceledEvent;
import com.moego.idl.models.event_bus.v1.AppointmentCreatedEvent;
import com.moego.idl.models.event_bus.v1.AppointmentDeletedEvent;
import com.moego.idl.models.event_bus.v1.AppointmentUpdatedEvent;
import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.lib.event_bus.consumer.AbstractConsumer;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.AppointmentTaskService;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import com.moego.svc.appointment.service.PetPlaygroupService;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 目前 cancel 动作发生在 server-grooming，未来迁移后该 consumer 需要移除
 *
 * <AUTHOR>
 * @since 2025/2/27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AppointmentConsumer extends AbstractConsumer<EventData> {

    private final PetPlaygroupService petPlaygroupService;
    private final AppointmentServiceProxy appointmentService;
    private final AppointmentTaskService appointmentTaskService;
    private final PetDetailServiceProxy petDetailService;

    @Override
    protected String topicName() {
        return "moego.erp.appointment";
    }

    @Override
    protected void consume(EventRecord<EventData> event) {
        log.info("consume appointment, event: {}", event);
        if (Objects.equals(event.detail().getEventCase(), EventData.EventCase.APPOINTMENT_CANCELED_EVENT)) {
            handleCanceledEvent(event.detail().getAppointmentCanceledEvent());
        } else if (Objects.equals(event.detail().getEventCase(), EventData.EventCase.APPOINTMENT_CREATED_EVENT)) {
            handleCreatedAppointment(event.detail().getAppointmentCreatedEvent());
        } else if (Objects.equals(event.detail().getEventCase(), EventData.EventCase.APPOINTMENT_UPDATED_EVENT)) {
            handleUpdatedAppointment(event.detail().getAppointmentUpdatedEvent());
        } else if (Objects.equals(event.detail().getEventCase(), EventData.EventCase.APPOINTMENT_DELETED_EVENT)) {
            handleDeletedAppointment(event.detail().getAppointmentDeletedEvent());
        }
    }

    private void handleCanceledEvent(AppointmentCanceledEvent event) {
        petPlaygroupService.deleteByAppointmentId(event.getId());
        appointmentTaskService.deleteByAppointment(event.getId());
    }

    private void handleCreatedAppointment(AppointmentCreatedEvent event) {
        var appointment = appointmentService.mustGet(event.getId());
        var petDetails = petDetailService.getPetDetailList(event.getId());
        appointmentTaskService.createTasksByAppointment(appointment, petDetails);
    }

    private void handleUpdatedAppointment(AppointmentUpdatedEvent event) {
        var appointment = appointmentService.mustGet(event.getId());
        var petDetails = petDetailService.getPetDetailList(event.getId());
        appointmentTaskService.recreateAllTasksByAppointment(appointment, petDetails);
    }

    private void handleDeletedAppointment(AppointmentDeletedEvent event) {
        petPlaygroupService.deleteByAppointmentId(event.getId());
        appointmentTaskService.deleteByAppointment(event.getId());
    }
}
