package com.moego.svc.appointment.utils;

import static java.util.stream.Collectors.groupingBy;

import com.moego.idl.models.appointment.v1.LodgingOccupiedStatus;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.util.CollectionUtils;

public class LodgingUtils {
    // 按满载程度由低到高排序
    public static final Map<LodgingOccupiedStatus, Integer> LODGING_STATUS_ORDER_MAP = Map.of(
            LodgingOccupiedStatus.VACANT,
            1,
            LodgingOccupiedStatus.PARTIALLY_OCCUPIED,
            2,
            LodgingOccupiedStatus.FULLY_OCCUPIED,
            3);

    /**
     * 计算一段时间内，每个房间的占用状态。由于每天的占用状态不同，去最大负载的占用状态
     * @param lodgingTypeList 房型列表
     * @param lodgingUnitList 房间列表
     * @param petCntPerLodging 房间在一段时间内，每一天的宠物数量
     * @return <key: lodgingUnitId, value: LodgingOccupiedStatus>  lodging 使用状态 1: 未使用 2: 使用中，未满 3: 使用中，已满
     */
    public static Map<Long, LodgingOccupiedStatus> calLodgingStatus(
            List<LodgingTypeModel> lodgingTypeList,
            List<LodgingUnitModel> lodgingUnitList,
            Map<Long, Integer> petCntPerLodging) {
        Map<Long, LodgingOccupiedStatus> result = new HashMap<>();
        if (CollectionUtils.isEmpty(lodgingUnitList)) {
            return result;
        }
        Map<Long, List<LodgingUnitModel>> lodgingTypeIdToUnits =
                lodgingUnitList.stream().collect(groupingBy(LodgingUnitModel::getLodgingTypeId));
        for (LodgingTypeModel lodgingType : lodgingTypeList) {
            Map<Long, LodgingOccupiedStatus> status =
                    calLodgingStatus(lodgingType, lodgingTypeIdToUnits.get(lodgingType.getId()), petCntPerLodging);
            result.putAll(status);
        }
        return result;
    }

    static Map<Long, LodgingOccupiedStatus> calLodgingStatus(
            LodgingTypeModel lodgingType, List<LodgingUnitModel> lodgingUnitList, Map<Long, Integer> petCntPerLodging) {
        Map<Long, LodgingOccupiedStatus> result = new HashMap<>();
        if (CollectionUtils.isEmpty(lodgingUnitList)) {
            return result;
        }
        // 初始化所有 lodging 为未使用状态
        for (LodgingUnitModel lodgingUnit : lodgingUnitList) {
            result.put(lodgingUnit.getId(), LodgingOccupiedStatus.VACANT);
        }

        // 计算每一个 lodging 占用状态
        petCntPerLodging.forEach((lodgingId, petCnt) -> {
            if (!result.containsKey(lodgingId)) {
                return;
            }
            LodgingOccupiedStatus status = calLodgingStatusPerDay(lodgingType, petCnt);
            if (LODGING_STATUS_ORDER_MAP.get(status) > LODGING_STATUS_ORDER_MAP.get(result.get(lodgingId))) {
                result.put(lodgingId, status);
            }
        });
        return result;
    }

    /**
     * 计算房型下寄养了 petCnt 只宠物时，房间的占用状态
     * @param lodgingType 房型
     * @param petCnt 宠物数量
     * @return 房间的占用状态
     */
    static LodgingOccupiedStatus calLodgingStatusPerDay(LodgingTypeModel lodgingType, Integer petCnt) {
        if (petCnt <= 0) {
            return LodgingOccupiedStatus.VACANT;
        }
        if (petCnt < lodgingType.getMaxPetNum()) {
            return LodgingOccupiedStatus.PARTIALLY_OCCUPIED;
        }
        return LodgingOccupiedStatus.FULLY_OCCUPIED;
    }

    /**
     * 检查 lodging 容量是否足够寄养
     *
     * @param lodgingType     lodging type
     * @param datePetCntNeed  <key: date, value: petCnt>  每一天需要寄养的宠物数量。 petCnt 为 0 时表示不需要寄养，不检查容量
     * @param datePetCntExist <key: date, value: petCnt>  每一天已经寄养的宠物数量
     * @return 是否有足够的容量寄养
     */
    public static boolean isLodgingAvailable(
            LodgingTypeModel lodgingType, Map<String, Integer> datePetCntNeed, Map<String, Integer> datePetCntExist) {
        for (var entry : datePetCntNeed.entrySet()) {
            String date = entry.getKey();
            Integer needCnt = entry.getValue();
            if (needCnt == 0) {
                continue;
            }

            int petCntExist = datePetCntExist.getOrDefault(date, 0);
            // base on room 的情况下，如果 lodging 已被占用，不管是否已满都不允许再 assign
            if (lodgingType.getLodgingUnitType().equals(LodgingUnitType.ROOM) && petCntExist > 0) {
                return false;
            }

            if (needCnt + petCntExist > lodgingType.getMaxPetNum()) {
                return false;
            }
        }
        return true;
    }
}
