spring:
  config:
    import:
      - "aws-secretsmanager:moego/testing/datasource?prefix=secret.datasource."
      - "aws-secretsmanager:moego/testing/camera?prefix=secret.camera."
      - "aws-secretsmanager:moego/testing/redis?prefix=secret.redis."
  datasource:
    driver-class-name: software.amazon.jdbc.Driver
    url: jdbc:aws-wrapper:mysql://${secret.datasource.mysql.url.master}:${secret.datasource.mysql.port}/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.mysql.moego_svc_organization.username}
    password: ${secret.datasource.mysql.moego_svc_organization.password}
    hikari:
      # aws jdbc driver 配置
      data-source-properties:
        keepSessionStateOnFailover: true
        wrapperPlugins: failover2,efm2
  data:
    redis:
      host: ${secret.redis.host}
      port: ${secret.redis.port}
      ssl:
        enabled: ${secret.redis.tls}
      timeout: 60000
      password: ${secret.redis.password}
      key:
        delimiter: ':'
        prefix: apiv2
moego:
  data-sources:
    - name: reader
      driver-class-name: software.amazon.jdbc.Driver
      url: jdbc:aws-wrapper:mysql://${secret.datasource.mysql.url.reader}:${secret.datasource.mysql.port}/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
      username: ${secret.datasource.mysql.moego_svc_organization.username}
      password: ${secret.datasource.mysql.moego_svc_organization.password}
  invite-staff:
    sign-in: https://go.t2.moego.dev/sign_in?inviteCode={0}
    sign-up: https://go.t2.moego.dev/sign_up?inviteCode={0}
camera:
  abckam:
    username: ${secret.camera.abckam.username}
    password: ${secret.camera.abckam.password}
  idogcam:
    key: ${secret.camera.idogcam.key}