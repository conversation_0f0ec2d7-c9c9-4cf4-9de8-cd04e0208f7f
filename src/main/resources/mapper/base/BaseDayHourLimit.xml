<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseDayHourLimit">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.DayHourLimit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="pet_type_id" jdbcType="BIGINT" property="petTypeId" />
    <result column="is_all_breed" jdbcType="BIT" property="isAllBreed" />
    <result column="is_all_service" jdbcType="BIT" property="isAllService" />
    <result column="capacity" jdbcType="INTEGER" property="capacity" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.svc.organization.entity.DayHourLimit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="pet_size_ids" jdbcType="LONGVARCHAR" property="petSizeIds" typeHandler="com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler" />
    <result column="breed_ids" jdbcType="LONGVARCHAR" property="breedIds" typeHandler="com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler" />
    <result column="service_ids" jdbcType="LONGVARCHAR" property="serviceIds" typeHandler="com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, type, pet_type_id, is_all_breed, is_all_service, capacity, created_at, updated_at
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    pet_size_ids, breed_ids, service_ids
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.moego.svc.organization.entity.DayHourLimitExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from day_hour_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.DayHourLimitExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from day_hour_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from day_hour_limit
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from day_hour_limit
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.DayHourLimitExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from day_hour_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.DayHourLimit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into day_hour_limit (type, pet_type_id, is_all_breed, 
      is_all_service, capacity, created_at, 
      updated_at, pet_size_ids, 
      breed_ids, 
      service_ids
      )
    values (#{type,jdbcType=INTEGER}, #{petTypeId,jdbcType=BIGINT}, #{isAllBreed,jdbcType=BIT}, 
      #{isAllService,jdbcType=BIT}, #{capacity,jdbcType=INTEGER}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{petSizeIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler}, 
      #{breedIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler}, 
      #{serviceIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.DayHourLimit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into day_hour_limit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        type,
      </if>
      <if test="petTypeId != null">
        pet_type_id,
      </if>
      <if test="isAllBreed != null">
        is_all_breed,
      </if>
      <if test="isAllService != null">
        is_all_service,
      </if>
      <if test="capacity != null">
        capacity,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="petSizeIds != null">
        pet_size_ids,
      </if>
      <if test="breedIds != null">
        breed_ids,
      </if>
      <if test="serviceIds != null">
        service_ids,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="petTypeId != null">
        #{petTypeId,jdbcType=BIGINT},
      </if>
      <if test="isAllBreed != null">
        #{isAllBreed,jdbcType=BIT},
      </if>
      <if test="isAllService != null">
        #{isAllService,jdbcType=BIT},
      </if>
      <if test="capacity != null">
        #{capacity,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="petSizeIds != null">
        #{petSizeIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      </if>
      <if test="breedIds != null">
        #{breedIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      </if>
      <if test="serviceIds != null">
        #{serviceIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.DayHourLimitExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from day_hour_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update day_hour_limit
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=INTEGER},
      </if>
      <if test="row.petTypeId != null">
        pet_type_id = #{row.petTypeId,jdbcType=BIGINT},
      </if>
      <if test="row.isAllBreed != null">
        is_all_breed = #{row.isAllBreed,jdbcType=BIT},
      </if>
      <if test="row.isAllService != null">
        is_all_service = #{row.isAllService,jdbcType=BIT},
      </if>
      <if test="row.capacity != null">
        capacity = #{row.capacity,jdbcType=INTEGER},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.petSizeIds != null">
        pet_size_ids = #{row.petSizeIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      </if>
      <if test="row.breedIds != null">
        breed_ids = #{row.breedIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      </if>
      <if test="row.serviceIds != null">
        service_ids = #{row.serviceIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update day_hour_limit
    set id = #{row.id,jdbcType=BIGINT},
      type = #{row.type,jdbcType=INTEGER},
      pet_type_id = #{row.petTypeId,jdbcType=BIGINT},
      is_all_breed = #{row.isAllBreed,jdbcType=BIT},
      is_all_service = #{row.isAllService,jdbcType=BIT},
      capacity = #{row.capacity,jdbcType=INTEGER},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      pet_size_ids = #{row.petSizeIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      breed_ids = #{row.breedIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      service_ids = #{row.serviceIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update day_hour_limit
    set id = #{row.id,jdbcType=BIGINT},
      type = #{row.type,jdbcType=INTEGER},
      pet_type_id = #{row.petTypeId,jdbcType=BIGINT},
      is_all_breed = #{row.isAllBreed,jdbcType=BIT},
      is_all_service = #{row.isAllService,jdbcType=BIT},
      capacity = #{row.capacity,jdbcType=INTEGER},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.DayHourLimit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update day_hour_limit
    <set>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="petTypeId != null">
        pet_type_id = #{petTypeId,jdbcType=BIGINT},
      </if>
      <if test="isAllBreed != null">
        is_all_breed = #{isAllBreed,jdbcType=BIT},
      </if>
      <if test="isAllService != null">
        is_all_service = #{isAllService,jdbcType=BIT},
      </if>
      <if test="capacity != null">
        capacity = #{capacity,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="petSizeIds != null">
        pet_size_ids = #{petSizeIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      </if>
      <if test="breedIds != null">
        breed_ids = #{breedIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      </if>
      <if test="serviceIds != null">
        service_ids = #{serviceIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.svc.organization.entity.DayHourLimit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update day_hour_limit
    set type = #{type,jdbcType=INTEGER},
      pet_type_id = #{petTypeId,jdbcType=BIGINT},
      is_all_breed = #{isAllBreed,jdbcType=BIT},
      is_all_service = #{isAllService,jdbcType=BIT},
      capacity = #{capacity,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      pet_size_ids = #{petSizeIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      breed_ids = #{breedIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      service_ids = #{serviceIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.DayHourLimit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update day_hour_limit
    set type = #{type,jdbcType=INTEGER},
      pet_type_id = #{petTypeId,jdbcType=BIGINT},
      is_all_breed = #{isAllBreed,jdbcType=BIT},
      is_all_service = #{isAllService,jdbcType=BIT},
      capacity = #{capacity,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>