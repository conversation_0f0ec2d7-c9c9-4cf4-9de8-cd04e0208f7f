<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.MoeStaffMapper">
  <select id="selectAllStaffByCompanyId" resultType="java.lang.Integer">
    SELECT
    count(s.id)
    FROM
    moe_staff s
    LEFT JOIN moe_business b ON s.business_id = b.id
    WHERE
    b.company_id = #{companyId}
    AND s.status =1
    AND s.employee_category = 0
  </select>
  <select id="queryByInviteCode" parameterType="java.lang.String" resultMap="com.moego.svc.organization.mapper.base.BaseMoeStaffMapper.BaseResultMap">

    select

    <include refid="com.moego.svc.organization.mapper.base.BaseMoeStaffMapper.Base_Column_List" />

    from moe_staff

    where invite_code = #{inviteCode,jdbcType=VARCHAR}

  </select>
  <select id="queryAllowLoginStaffsByAccountId" parameterType="java.lang.Integer" resultMap="com.moego.svc.organization.mapper.base.BaseMoeStaffMapper.BaseResultMap">

    select

    <include refid="com.moego.svc.organization.mapper.base.BaseMoeStaffMapper.Base_Column_List" />

    from moe_staff

    where account_id = #{accountId,jdbcType=INTEGER} and allow_login = 1 and status = 1

  </select>

  <update id="batchUpdateByPrimaryKeySelective">
    <foreach collection="list" item="item" separator=";">
      update moe_staff
      <set>
        <if test="item.businessId != null">
          business_id = #{item.businessId,jdbcType=INTEGER},
        </if>
        <if test="item.accountId != null">
          account_id = #{item.accountId,jdbcType=INTEGER},
        </if>
        <if test="item.roleId != null">
          role_id = #{item.roleId,jdbcType=INTEGER},
        </if>
        <if test="item.avatarPath != null">
          avatar_path = #{item.avatarPath,jdbcType=VARCHAR},
        </if>
        <if test="item.firstName != null">
          first_name = #{item.firstName,jdbcType=VARCHAR},
        </if>
        <if test="item.lastName != null">
          last_name = #{item.lastName,jdbcType=VARCHAR},
        </if>
        <if test="item.employeeCategory != null">
          employee_category = #{item.employeeCategory,jdbcType=TINYINT},
        </if>
        <if test="item.phoneNumber != null">
          phone_number = #{item.phoneNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.hireDate != null">
          hire_date = #{item.hireDate,jdbcType=BIGINT},
        </if>
        <if test="item.fireDate != null">
          fire_date = #{item.fireDate,jdbcType=BIGINT},
        </if>
        <if test="item.allowLogin != null">
          allow_login = #{item.allowLogin,jdbcType=TINYINT},
        </if>
        <if test="item.groupLeaderId != null">
          group_leader_id = #{item.groupLeaderId,jdbcType=INTEGER},
        </if>
        <if test="item.note != null">
          note = #{item.note,jdbcType=VARCHAR},
        </if>
        <if test="item.inactive != null">
          inactive = #{item.inactive,jdbcType=TINYINT},
        </if>
        <if test="item.status != null">
          status = #{item.status,jdbcType=TINYINT},
        </if>
        <if test="item.createById != null">
          create_by_id = #{item.createById,jdbcType=INTEGER},
        </if>
        <if test="item.sort != null">
          sort = #{item.sort,jdbcType=INTEGER},
        </if>
        <if test="item.bookOnlineAvailable != null">
          book_online_available = #{item.bookOnlineAvailable,jdbcType=TINYINT},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=BIGINT},
        </if>
        <if test="item.updateTime != null">
          update_time = #{item.updateTime,jdbcType=BIGINT},
        </if>
        <if test="item.showOnCalendar != null">
          show_on_calendar = #{item.showOnCalendar,jdbcType=TINYINT},
        </if>
        <if test="item.showCalendarStaffAll != null">
          show_calendar_staff_all = #{item.showCalendarStaffAll,jdbcType=TINYINT},
        </if>
        <if test="item.accessCode != null">
          access_code = #{item.accessCode,jdbcType=VARCHAR},
        </if>
        <if test="item.token != null">
          token = #{item.token,jdbcType=VARCHAR},
        </if>
        <if test="item.inviteCode != null">
          invite_code = #{item.inviteCode,jdbcType=VARCHAR},
        </if>
        <if test="item.accountLastVisitedAt != null">
          account_last_visited_at = #{item.accountLastVisitedAt,jdbcType=BIGINT},
        </if>
        <if test="item.accountSort != null">
          account_sort = #{item.accountSort,jdbcType=INTEGER},
        </if>
        <if test="item.companyId != null">
          company_id = #{item.companyId,jdbcType=INTEGER},
        </if>
        <if test="item.enterpriseId != null">
          enterprise_id = #{item.enterpriseId,jdbcType=INTEGER},
        </if>
        <if test="item.workingInAllLocations != null">
          working_in_all_locations = #{item.workingInAllLocations,jdbcType=BIT},
        </if>
        <if test="item.colorCode != null">
          color_code = #{item.colorCode,jdbcType=VARCHAR},
        </if>
        <if test="item.lastVisitBusinessId != null">
          last_visit_business_id = #{item.lastVisitBusinessId,jdbcType=INTEGER},
        </if>
        <if test="item.accessAllWorkingLocationsStaff != null">
          access_all_working_locations_staff = #{item.accessAllWorkingLocationsStaff,jdbcType=TINYINT},
        </if>
        <if test="item.profileEmail != null">
          profile_email = #{item.profileEmail,jdbcType=VARCHAR},
        </if>
        <if test="item.requireAccessCode != null">
          require_access_code = #{item.requireAccessCode,jdbcType=BIT},
        </if>
        <if test="item.isShownOnAllCalendar != null">
          is_shown_on_all_calendar = #{item.isShownOnAllCalendar,jdbcType=BIT},
        </if>
      </set>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper>
