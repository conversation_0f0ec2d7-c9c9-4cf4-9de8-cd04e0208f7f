<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.MoeStaffPayrollSettingMapper">

  <insert id="insertSelectiveForInit" parameterType="com.moego.svc.organization.entity.MoeStaffPayrollSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff_payroll_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="serviceCommissionEnable != null">
        service_commission_enable,
      </if>
      <if test="serviceCommissionType != null">
        service_commission_type,
      </if>
      <if test="tierType != null">
        tier_type,
      </if>
      <if test="servicePayRate != null">
        service_pay_rate,
      </if>
      <if test="addonPayRate != null">
        addon_pay_rate,
      </if>
      <if test="hourlyCommissionEnable != null">
        hourly_commission_enable,
      </if>
      <if test="hourlyPay != null">
        hourly_pay,
      </if>
      <if test="tipsCommissionEnable != null">
        tips_commission_enable,
      </if>
      <if test="tipsPayRate != null">
        tips_pay_rate,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="serviceTierRateConfig != null">
        service_tier_rate_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="serviceCommissionEnable != null">
        #{serviceCommissionEnable,jdbcType=BIT},
      </if>
      <if test="serviceCommissionType != null">
        #{serviceCommissionType,jdbcType=TINYINT},
      </if>
      <if test="tierType != null">
        #{tierType,jdbcType=TINYINT},
      </if>
      <if test="servicePayRate != null">
        #{servicePayRate,jdbcType=DECIMAL},
      </if>
      <if test="addonPayRate != null">
        #{addonPayRate,jdbcType=DECIMAL},
      </if>
      <if test="hourlyCommissionEnable != null">
        #{hourlyCommissionEnable,jdbcType=BIT},
      </if>
      <if test="hourlyPay != null">
        #{hourlyPay,jdbcType=DECIMAL},
      </if>
      <if test="tipsCommissionEnable != null">
        #{tipsCommissionEnable,jdbcType=BIT},
      </if>
      <if test="tipsPayRate != null">
        #{tipsPayRate,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="serviceTierRateConfig != null">
        #{serviceTierRateConfig,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    ON DUPLICATE KEY UPDATE update_time = update_time
  </insert>


</mapper>
