alter table moe_grooming.moe_business_book_online add column group_payment_type tinyint ;
alter table moe_grooming.moe_business_book_online add column group_filter_rule json;
alter table moe_grooming.moe_business_book_online add column group_accept_client tinyint;
alter table moe_grooming.moe_business_book_online add column group_prepay_type tinyint default 1    not null;
alter table moe_grooming.moe_business_book_online add column group_prepay_tip_enable tinyint default 1               not null;
alter table moe_grooming.moe_business_book_online add column group_deposit_type tinyint default 0               not null;
alter table moe_grooming.moe_business_book_online add column group_deposit_percentage int default 30              not null;
alter table moe_grooming.moe_business_book_online add column group_deposit_amount decimal(20,2) default 20.00           not null;
alter table moe_grooming.moe_business_book_online add column group_pre_auth_tip_enable tinyint default 0               not null;
alter table moe_grooming.moe_business_book_online add column group_pre_auth_policy text ;
alter table moe_grooming.moe_business_book_online add column group_cancellation_policy text ;
alter table moe_grooming.moe_business_book_online add column group_prepay_policy text ;

alter table moe_grooming.moe_book_online_abandon_record add column use_payment_seg_setting bool  comment 'if use payment seg setting';
alter table moe_grooming.moe_book_online_abandon_record add column payment_seg_setting_rule json  comment 'if use payment seg setting';


alter table moe_grooming.moe_book_online_deposit add column deposit_type tinyint not null default 1 comment '#DepositPaymentTypeEnum';
UPDATE moe_grooming.moe_book_online_deposit SET deposit_type = 2 WHERE guid LIKE 'preauth_%';


