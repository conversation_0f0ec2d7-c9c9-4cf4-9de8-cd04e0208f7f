server:
  tomcat:
    threads:
      max: 1000
spring:
  datasource:
    driver-class-name: software.amazon.jdbc.Driver
    url: jdbc:aws-wrapper:mysql://${secret.datasource.grooming.mysql.url}:${secret.datasource.grooming.mysql.port}/moe_grooming?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.grooming.mysql.username}
    password: ${secret.datasource.grooming.mysql.password}
    hikari:
      # aws jdbc driver 配置
      data-source-properties:
        keepSessionStateOnFailover: true
        wrapperPlugins: failover2,efm2
      # See https://github.com/brettwooldridge/HikariCP?tab=readme-ov-file#frequently-used
      maximum-pool-size: 100
  config:
    import:
      - "aws-secretsmanager:moego/production/datasource/grooming?prefix=secret.datasource.grooming."
      - "aws-secretsmanager:moego/production/redis?prefix=secret.redis."
      - "aws-secretsmanager:moego/production/google?prefix=secret.google."
      - "aws-secretsmanager:moego/production/growthbook?prefix=secret.growthbook."
      - "aws-secretsmanager:moego/production/quickbooks?prefix=secret.quickbooks."
      - "aws-secretsmanager:moego/production/mq?prefix=secret.mq."
      - "aws-secretsmanager:moego/production/aws?prefix=secret.aws."
      - "aws-secretsmanager:moego/production/apilayer?prefix=secret.apilayer."

customer:
  invoice:
    url: https://client.moego.pet/payonline/
  upcoming:
    key: 'AAAA33*2020_123456789AAA'
    url: 'https://client.moego.pet/appointment/upcoming?id='

exchange:
  rate:
    base: USD
    param:
      symbols: AUD,BRL,CAD,EUR,GBP,NZD,ZAR,USD
    url: http://api.exchangeratesapi.io/latest
    newUrl: https://api.apilayer.com/exchangerates_data/latest?symbols=%s&base=%s

google:
  calendar:
    auth:
      redirect:
        uri: https://go.moego.pet/GoogleCalendarSync/callback
  event:
    web:
      hook: https://beta-api.moego.pet/grooming/google/calendar/event/webhook

quickbooks:
  api:
    host: https://quickbooks.api.intuit.com
  environment: PRODUCTION
  oAuth:
    redirectUri: https://go.moego.pet/quickbook/activate
  web:
    invoice:
      url: https://app.qbo.intuit.com/app/invoice?txnId=

moego:
  messaging:
    pulsar:
      tenant: moego
  google-reserve:
    conversion-tracking-url: https://www.google.com/maps/conversion/collect
  grooming-report:
    share-key: JvliT3e4OmtyigbTRaOQ3tiO
springdoc:
  swagger-ui:
    enabled: false
  api-docs:
    enabled: false
