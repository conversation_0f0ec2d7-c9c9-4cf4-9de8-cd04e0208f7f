package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.List;

public class MoeQbSettingExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    public MoeQbSettingExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andConnectIdIsNull() {
            addCriterion("connect_id is null");
            return (Criteria) this;
        }

        public Criteria andConnectIdIsNotNull() {
            addCriterion("connect_id is not null");
            return (Criteria) this;
        }

        public Criteria andConnectIdEqualTo(Integer value) {
            addCriterion("connect_id =", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdNotEqualTo(Integer value) {
            addCriterion("connect_id <>", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdGreaterThan(Integer value) {
            addCriterion("connect_id >", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("connect_id >=", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdLessThan(Integer value) {
            addCriterion("connect_id <", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdLessThanOrEqualTo(Integer value) {
            addCriterion("connect_id <=", value, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdIn(List<Integer> values) {
            addCriterion("connect_id in", values, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdNotIn(List<Integer> values) {
            addCriterion("connect_id not in", values, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdBetween(Integer value1, Integer value2) {
            addCriterion("connect_id between", value1, value2, "connectId");
            return (Criteria) this;
        }

        public Criteria andConnectIdNotBetween(Integer value1, Integer value2) {
            addCriterion("connect_id not between", value1, value2, "connectId");
            return (Criteria) this;
        }

        public Criteria andEnableSyncIsNull() {
            addCriterion("enable_sync is null");
            return (Criteria) this;
        }

        public Criteria andEnableSyncIsNotNull() {
            addCriterion("enable_sync is not null");
            return (Criteria) this;
        }

        public Criteria andEnableSyncEqualTo(Byte value) {
            addCriterion("enable_sync =", value, "enableSync");
            return (Criteria) this;
        }

        public Criteria andEnableSyncNotEqualTo(Byte value) {
            addCriterion("enable_sync <>", value, "enableSync");
            return (Criteria) this;
        }

        public Criteria andEnableSyncGreaterThan(Byte value) {
            addCriterion("enable_sync >", value, "enableSync");
            return (Criteria) this;
        }

        public Criteria andEnableSyncGreaterThanOrEqualTo(Byte value) {
            addCriterion("enable_sync >=", value, "enableSync");
            return (Criteria) this;
        }

        public Criteria andEnableSyncLessThan(Byte value) {
            addCriterion("enable_sync <", value, "enableSync");
            return (Criteria) this;
        }

        public Criteria andEnableSyncLessThanOrEqualTo(Byte value) {
            addCriterion("enable_sync <=", value, "enableSync");
            return (Criteria) this;
        }

        public Criteria andEnableSyncIn(List<Byte> values) {
            addCriterion("enable_sync in", values, "enableSync");
            return (Criteria) this;
        }

        public Criteria andEnableSyncNotIn(List<Byte> values) {
            addCriterion("enable_sync not in", values, "enableSync");
            return (Criteria) this;
        }

        public Criteria andEnableSyncBetween(Byte value1, Byte value2) {
            addCriterion("enable_sync between", value1, value2, "enableSync");
            return (Criteria) this;
        }

        public Criteria andEnableSyncNotBetween(Byte value1, Byte value2) {
            addCriterion("enable_sync not between", value1, value2, "enableSync");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateIsNull() {
            addCriterion("sync_begin_date is null");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateIsNotNull() {
            addCriterion("sync_begin_date is not null");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateEqualTo(String value) {
            addCriterion("sync_begin_date =", value, "syncBeginDate");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateNotEqualTo(String value) {
            addCriterion("sync_begin_date <>", value, "syncBeginDate");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateGreaterThan(String value) {
            addCriterion("sync_begin_date >", value, "syncBeginDate");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateGreaterThanOrEqualTo(String value) {
            addCriterion("sync_begin_date >=", value, "syncBeginDate");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateLessThan(String value) {
            addCriterion("sync_begin_date <", value, "syncBeginDate");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateLessThanOrEqualTo(String value) {
            addCriterion("sync_begin_date <=", value, "syncBeginDate");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateLike(String value) {
            addCriterion("sync_begin_date like", value, "syncBeginDate");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateNotLike(String value) {
            addCriterion("sync_begin_date not like", value, "syncBeginDate");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateIn(List<String> values) {
            addCriterion("sync_begin_date in", values, "syncBeginDate");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateNotIn(List<String> values) {
            addCriterion("sync_begin_date not in", values, "syncBeginDate");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateBetween(String value1, String value2) {
            addCriterion("sync_begin_date between", value1, value2, "syncBeginDate");
            return (Criteria) this;
        }

        public Criteria andSyncBeginDateNotBetween(String value1, String value2) {
            addCriterion("sync_begin_date not between", value1, value2, "syncBeginDate");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateIsNull() {
            addCriterion("min_sync_date is null");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateIsNotNull() {
            addCriterion("min_sync_date is not null");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateEqualTo(String value) {
            addCriterion("min_sync_date =", value, "minSyncDate");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateNotEqualTo(String value) {
            addCriterion("min_sync_date <>", value, "minSyncDate");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateGreaterThan(String value) {
            addCriterion("min_sync_date >", value, "minSyncDate");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateGreaterThanOrEqualTo(String value) {
            addCriterion("min_sync_date >=", value, "minSyncDate");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateLessThan(String value) {
            addCriterion("min_sync_date <", value, "minSyncDate");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateLessThanOrEqualTo(String value) {
            addCriterion("min_sync_date <=", value, "minSyncDate");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateLike(String value) {
            addCriterion("min_sync_date like", value, "minSyncDate");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateNotLike(String value) {
            addCriterion("min_sync_date not like", value, "minSyncDate");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateIn(List<String> values) {
            addCriterion("min_sync_date in", values, "minSyncDate");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateNotIn(List<String> values) {
            addCriterion("min_sync_date not in", values, "minSyncDate");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateBetween(String value1, String value2) {
            addCriterion("min_sync_date between", value1, value2, "minSyncDate");
            return (Criteria) this;
        }

        public Criteria andMinSyncDateNotBetween(String value1, String value2) {
            addCriterion("min_sync_date not between", value1, value2, "minSyncDate");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andTaxSyncTypeIsNull() {
            addCriterion("tax_sync_type is null");
            return (Criteria) this;
        }

        public Criteria andTaxSyncTypeIsNotNull() {
            addCriterion("tax_sync_type is not null");
            return (Criteria) this;
        }

        public Criteria andTaxSyncTypeEqualTo(Byte value) {
            addCriterion("tax_sync_type =", value, "taxSyncType");
            return (Criteria) this;
        }

        public Criteria andTaxSyncTypeNotEqualTo(Byte value) {
            addCriterion("tax_sync_type <>", value, "taxSyncType");
            return (Criteria) this;
        }

        public Criteria andTaxSyncTypeGreaterThan(Byte value) {
            addCriterion("tax_sync_type >", value, "taxSyncType");
            return (Criteria) this;
        }

        public Criteria andTaxSyncTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("tax_sync_type >=", value, "taxSyncType");
            return (Criteria) this;
        }

        public Criteria andTaxSyncTypeLessThan(Byte value) {
            addCriterion("tax_sync_type <", value, "taxSyncType");
            return (Criteria) this;
        }

        public Criteria andTaxSyncTypeLessThanOrEqualTo(Byte value) {
            addCriterion("tax_sync_type <=", value, "taxSyncType");
            return (Criteria) this;
        }

        public Criteria andTaxSyncTypeIn(List<Byte> values) {
            addCriterion("tax_sync_type in", values, "taxSyncType");
            return (Criteria) this;
        }

        public Criteria andTaxSyncTypeNotIn(List<Byte> values) {
            addCriterion("tax_sync_type not in", values, "taxSyncType");
            return (Criteria) this;
        }

        public Criteria andTaxSyncTypeBetween(Byte value1, Byte value2) {
            addCriterion("tax_sync_type between", value1, value2, "taxSyncType");
            return (Criteria) this;
        }

        public Criteria andTaxSyncTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("tax_sync_type not between", value1, value2, "taxSyncType");
            return (Criteria) this;
        }

        public Criteria andUserVersionIsNull() {
            addCriterion("user_version is null");
            return (Criteria) this;
        }

        public Criteria andUserVersionIsNotNull() {
            addCriterion("user_version is not null");
            return (Criteria) this;
        }

        public Criteria andUserVersionEqualTo(Integer value) {
            addCriterion("user_version =", value, "userVersion");
            return (Criteria) this;
        }

        public Criteria andUserVersionNotEqualTo(Integer value) {
            addCriterion("user_version <>", value, "userVersion");
            return (Criteria) this;
        }

        public Criteria andUserVersionGreaterThan(Integer value) {
            addCriterion("user_version >", value, "userVersion");
            return (Criteria) this;
        }

        public Criteria andUserVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_version >=", value, "userVersion");
            return (Criteria) this;
        }

        public Criteria andUserVersionLessThan(Integer value) {
            addCriterion("user_version <", value, "userVersion");
            return (Criteria) this;
        }

        public Criteria andUserVersionLessThanOrEqualTo(Integer value) {
            addCriterion("user_version <=", value, "userVersion");
            return (Criteria) this;
        }

        public Criteria andUserVersionIn(List<Integer> values) {
            addCriterion("user_version in", values, "userVersion");
            return (Criteria) this;
        }

        public Criteria andUserVersionNotIn(List<Integer> values) {
            addCriterion("user_version not in", values, "userVersion");
            return (Criteria) this;
        }

        public Criteria andUserVersionBetween(Integer value1, Integer value2) {
            addCriterion("user_version between", value1, value2, "userVersion");
            return (Criteria) this;
        }

        public Criteria andUserVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("user_version not between", value1, value2, "userVersion");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeIsNull() {
            addCriterion("last_disconnected_time is null");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeIsNotNull() {
            addCriterion("last_disconnected_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeEqualTo(String value) {
            addCriterion("last_disconnected_time =", value, "lastDisconnectedTime");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeNotEqualTo(String value) {
            addCriterion("last_disconnected_time <>", value, "lastDisconnectedTime");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeGreaterThan(String value) {
            addCriterion("last_disconnected_time >", value, "lastDisconnectedTime");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeGreaterThanOrEqualTo(String value) {
            addCriterion("last_disconnected_time >=", value, "lastDisconnectedTime");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeLessThan(String value) {
            addCriterion("last_disconnected_time <", value, "lastDisconnectedTime");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeLessThanOrEqualTo(String value) {
            addCriterion("last_disconnected_time <=", value, "lastDisconnectedTime");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeLike(String value) {
            addCriterion("last_disconnected_time like", value, "lastDisconnectedTime");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeNotLike(String value) {
            addCriterion("last_disconnected_time not like", value, "lastDisconnectedTime");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeIn(List<String> values) {
            addCriterion("last_disconnected_time in", values, "lastDisconnectedTime");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeNotIn(List<String> values) {
            addCriterion("last_disconnected_time not in", values, "lastDisconnectedTime");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeBetween(String value1, String value2) {
            addCriterion("last_disconnected_time between", value1, value2, "lastDisconnectedTime");
            return (Criteria) this;
        }

        public Criteria andLastDisconnectedTimeNotBetween(String value1, String value2) {
            addCriterion("last_disconnected_time not between", value1, value2, "lastDisconnectedTime");
            return (Criteria) this;
        }

        public Criteria andSalesReceiptEnableIsNull() {
            addCriterion("sales_receipt_enable is null");
            return (Criteria) this;
        }

        public Criteria andSalesReceiptEnableIsNotNull() {
            addCriterion("sales_receipt_enable is not null");
            return (Criteria) this;
        }

        public Criteria andSalesReceiptEnableEqualTo(Byte value) {
            addCriterion("sales_receipt_enable =", value, "salesReceiptEnable");
            return (Criteria) this;
        }

        public Criteria andSalesReceiptEnableNotEqualTo(Byte value) {
            addCriterion("sales_receipt_enable <>", value, "salesReceiptEnable");
            return (Criteria) this;
        }

        public Criteria andSalesReceiptEnableGreaterThan(Byte value) {
            addCriterion("sales_receipt_enable >", value, "salesReceiptEnable");
            return (Criteria) this;
        }

        public Criteria andSalesReceiptEnableGreaterThanOrEqualTo(Byte value) {
            addCriterion("sales_receipt_enable >=", value, "salesReceiptEnable");
            return (Criteria) this;
        }

        public Criteria andSalesReceiptEnableLessThan(Byte value) {
            addCriterion("sales_receipt_enable <", value, "salesReceiptEnable");
            return (Criteria) this;
        }

        public Criteria andSalesReceiptEnableLessThanOrEqualTo(Byte value) {
            addCriterion("sales_receipt_enable <=", value, "salesReceiptEnable");
            return (Criteria) this;
        }

        public Criteria andSalesReceiptEnableIn(List<Byte> values) {
            addCriterion("sales_receipt_enable in", values, "salesReceiptEnable");
            return (Criteria) this;
        }

        public Criteria andSalesReceiptEnableNotIn(List<Byte> values) {
            addCriterion("sales_receipt_enable not in", values, "salesReceiptEnable");
            return (Criteria) this;
        }

        public Criteria andSalesReceiptEnableBetween(Byte value1, Byte value2) {
            addCriterion("sales_receipt_enable between", value1, value2, "salesReceiptEnable");
            return (Criteria) this;
        }

        public Criteria andSalesReceiptEnableNotBetween(Byte value1, Byte value2) {
            addCriterion("sales_receipt_enable not between", value1, value2, "salesReceiptEnable");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_qb_setting
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
