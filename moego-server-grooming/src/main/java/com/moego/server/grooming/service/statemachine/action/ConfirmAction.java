package com.moego.server.grooming.service.statemachine.action;

import com.google.protobuf.util.Timestamps;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc.AppointmentServiceBlockingStub;
import com.moego.idl.service.appointment.v1.UpdateAppointmentSelectiveRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.enums.AppointmentConfirmTypeEnum;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.statemachine.context.ActionContext;
import com.moego.server.grooming.service.statemachine.context.ConfirmActionContext;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class ConfirmAction implements IStateTransitionAction {

    private final AppointmentServiceBlockingStub appointmentStub;

    @Override
    public boolean suit(AppointmentStatusEnum newStatus) {
        return AppointmentStatusEnum.CONFIRMED.equals(newStatus);
    }

    @Override
    public int execute(MoeGroomingAppointment moeGroomingAppointment, ActionContext actionContext) {
        // finish 和 cancel 的预约不能再进入 confirmed 状态
        if (Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.FINISHED.getValue())
                || Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.CANCELED.getValue())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "Appointment[%d] status is %s, can not confirm",
                            moeGroomingAppointment.getId(), moeGroomingAppointment.getStatus()));
        }

        // 保留之前的逻辑：where updatedById = xx and status in (unconfirmed)
        if (!Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.UNCONFIRMED_VALUE)) {
            return 0;
        }

        ConfirmActionContext confirmActionContext = (ConfirmActionContext) actionContext;

        var builder = UpdateAppointmentSelectiveRequest.newBuilder();
        builder.setId(moeGroomingAppointment.getId());
        builder.setStatus(AppointmentStatus.CONFIRMED);
        Optional.ofNullable(confirmActionContext.getConfirmedByAccountId()).ifPresent(builder::setConfirmBy);
        Optional.ofNullable(confirmActionContext.getConfirmedType())
                .map(AppointmentConfirmTypeEnum::getValue)
                .ifPresent(builder::setConfirmByType);
        builder.setUpdateTime(Timestamps.now());
        builder.setConfirmedTime(Instant.now().getEpochSecond());
        Long updatedById = Objects.equals(confirmActionContext.getConfirmedType(), AppointmentConfirmTypeEnum.BUSINESS)
                ? AuthContext.get().staffId()
                : -1L;
        Optional.of(updatedById).ifPresent(builder::setUpdatedById);

        return appointmentStub.updateAppointmentSelective(builder.build()).getAffectedRows();
    }

    @Override
    public int revert(MoeGroomingAppointment moeGroomingAppointment) {
        return 0;
    }
}
