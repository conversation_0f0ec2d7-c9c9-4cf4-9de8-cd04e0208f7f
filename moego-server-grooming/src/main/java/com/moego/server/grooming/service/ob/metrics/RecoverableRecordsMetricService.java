package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Service
@RequiredArgsConstructor
public class RecoverableRecordsMetricService implements IOBMetricsService {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;
    private final AbandonedRecordsMetricService abandonedRecordsMetricService;

    @Override
    public Integer sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        // count(distinct abandoned) + count(recovered)
        int recoverableClients = abandonRecordMapper.countRecoverableAbandonedClients(
                timeRangeDTO.businessId(),
                timeRangeDTO.startTime(),
                timeRangeDTO.endTime(),
                OBStepEnum.listRecoverableSteps());
        int recoveredRecords = abandonRecordMapper.countRecoveredRecords(
                timeRangeDTO.businessId(),
                timeRangeDTO.startTime(),
                timeRangeDTO.endTime(),
                OBStepEnum.listRecoverableSteps());
        return recoverableClients + recoveredRecords;
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        // recoverable records / abandoned records
        int recoverableRecords = this.sumMetrics(timeRangeDTO);
        int abandonedRecords = abandonedRecordsMetricService.sumMetrics(timeRangeDTO);
        if (abandonedRecords == 0) {
            return "";
        }
        return BigDecimal.valueOf(recoverableRecords)
                .divide(BigDecimal.valueOf(abandonedRecords), 2, RoundingMode.HALF_UP);
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.recoverable_records;
    }
}
