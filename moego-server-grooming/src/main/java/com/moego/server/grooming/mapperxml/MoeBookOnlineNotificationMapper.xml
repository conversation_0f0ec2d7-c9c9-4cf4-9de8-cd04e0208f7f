<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineNotificationMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineNotification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="submit_client_type" jdbcType="TINYINT" property="submitClientType" />
    <result column="submit_business_type" jdbcType="TINYINT" property="submitBusinessType" />
    <result column="submit_email_subject_template" jdbcType="VARCHAR" property="submitEmailSubjectTemplate" />
    <result column="accept_client_type" jdbcType="TINYINT" property="acceptClientType" />
    <result column="accept_email_subject_template" jdbcType="VARCHAR" property="acceptEmailSubjectTemplate" />
    <result column="accept_business_type" jdbcType="TINYINT" property="acceptBusinessType" />
    <result column="auto_move_client_type" jdbcType="TINYINT" property="autoMoveClientType" />
    <result column="auto_move_business_type" jdbcType="TINYINT" property="autoMoveBusinessType" />
    <result column="auto_move_email_subject_template" jdbcType="VARCHAR" property="autoMoveEmailSubjectTemplate" />
    <result column="decline_client_type" jdbcType="TINYINT" property="declineClientType" />
    <result column="decline_business_type" jdbcType="TINYINT" property="declineBusinessType" />
    <result column="decline_email_subject_template" jdbcType="VARCHAR" property="declineEmailSubjectTemplate" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeBookOnlineNotification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="submit_template" jdbcType="LONGVARCHAR" property="submitTemplate" />
    <result column="submit_email_content_template" jdbcType="LONGVARCHAR" property="submitEmailContentTemplate" />
    <result column="accept_template" jdbcType="LONGVARCHAR" property="acceptTemplate" />
    <result column="accept_email_content_template" jdbcType="LONGVARCHAR" property="acceptEmailContentTemplate" />
    <result column="auto_move_template" jdbcType="LONGVARCHAR" property="autoMoveTemplate" />
    <result column="auto_move_email_content_template" jdbcType="LONGVARCHAR" property="autoMoveEmailContentTemplate" />
    <result column="decline_template" jdbcType="LONGVARCHAR" property="declineTemplate" />
    <result column="decline_email_content_template" jdbcType="LONGVARCHAR" property="declineEmailContentTemplate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, submit_client_type, submit_business_type, submit_email_subject_template, 
    accept_client_type, accept_email_subject_template, accept_business_type, auto_move_client_type, 
    auto_move_business_type, auto_move_email_subject_template, decline_client_type, decline_business_type, 
    decline_email_subject_template, company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    submit_template, submit_email_content_template, accept_template, accept_email_content_template, 
    auto_move_template, auto_move_email_content_template, decline_template, decline_email_content_template
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_notification
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_notification
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineNotification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_notification (business_id, submit_client_type, submit_business_type, 
      submit_email_subject_template, accept_client_type, 
      accept_email_subject_template, accept_business_type, 
      auto_move_client_type, auto_move_business_type, 
      auto_move_email_subject_template, decline_client_type, 
      decline_business_type, decline_email_subject_template, 
      company_id, submit_template, submit_email_content_template, 
      accept_template, accept_email_content_template, 
      auto_move_template, auto_move_email_content_template, 
      decline_template, decline_email_content_template
      )
    values (#{businessId,jdbcType=INTEGER}, #{submitClientType,jdbcType=TINYINT}, #{submitBusinessType,jdbcType=TINYINT}, 
      #{submitEmailSubjectTemplate,jdbcType=VARCHAR}, #{acceptClientType,jdbcType=TINYINT}, 
      #{acceptEmailSubjectTemplate,jdbcType=VARCHAR}, #{acceptBusinessType,jdbcType=TINYINT}, 
      #{autoMoveClientType,jdbcType=TINYINT}, #{autoMoveBusinessType,jdbcType=TINYINT}, 
      #{autoMoveEmailSubjectTemplate,jdbcType=VARCHAR}, #{declineClientType,jdbcType=TINYINT}, 
      #{declineBusinessType,jdbcType=TINYINT}, #{declineEmailSubjectTemplate,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=BIGINT}, #{submitTemplate,jdbcType=LONGVARCHAR}, #{submitEmailContentTemplate,jdbcType=LONGVARCHAR}, 
      #{acceptTemplate,jdbcType=LONGVARCHAR}, #{acceptEmailContentTemplate,jdbcType=LONGVARCHAR}, 
      #{autoMoveTemplate,jdbcType=LONGVARCHAR}, #{autoMoveEmailContentTemplate,jdbcType=LONGVARCHAR}, 
      #{declineTemplate,jdbcType=LONGVARCHAR}, #{declineEmailContentTemplate,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineNotification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_notification
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="submitClientType != null">
        submit_client_type,
      </if>
      <if test="submitBusinessType != null">
        submit_business_type,
      </if>
      <if test="submitEmailSubjectTemplate != null">
        submit_email_subject_template,
      </if>
      <if test="acceptClientType != null">
        accept_client_type,
      </if>
      <if test="acceptEmailSubjectTemplate != null">
        accept_email_subject_template,
      </if>
      <if test="acceptBusinessType != null">
        accept_business_type,
      </if>
      <if test="autoMoveClientType != null">
        auto_move_client_type,
      </if>
      <if test="autoMoveBusinessType != null">
        auto_move_business_type,
      </if>
      <if test="autoMoveEmailSubjectTemplate != null">
        auto_move_email_subject_template,
      </if>
      <if test="declineClientType != null">
        decline_client_type,
      </if>
      <if test="declineBusinessType != null">
        decline_business_type,
      </if>
      <if test="declineEmailSubjectTemplate != null">
        decline_email_subject_template,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="submitTemplate != null">
        submit_template,
      </if>
      <if test="submitEmailContentTemplate != null">
        submit_email_content_template,
      </if>
      <if test="acceptTemplate != null">
        accept_template,
      </if>
      <if test="acceptEmailContentTemplate != null">
        accept_email_content_template,
      </if>
      <if test="autoMoveTemplate != null">
        auto_move_template,
      </if>
      <if test="autoMoveEmailContentTemplate != null">
        auto_move_email_content_template,
      </if>
      <if test="declineTemplate != null">
        decline_template,
      </if>
      <if test="declineEmailContentTemplate != null">
        decline_email_content_template,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="submitClientType != null">
        #{submitClientType,jdbcType=TINYINT},
      </if>
      <if test="submitBusinessType != null">
        #{submitBusinessType,jdbcType=TINYINT},
      </if>
      <if test="submitEmailSubjectTemplate != null">
        #{submitEmailSubjectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="acceptClientType != null">
        #{acceptClientType,jdbcType=TINYINT},
      </if>
      <if test="acceptEmailSubjectTemplate != null">
        #{acceptEmailSubjectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="acceptBusinessType != null">
        #{acceptBusinessType,jdbcType=TINYINT},
      </if>
      <if test="autoMoveClientType != null">
        #{autoMoveClientType,jdbcType=TINYINT},
      </if>
      <if test="autoMoveBusinessType != null">
        #{autoMoveBusinessType,jdbcType=TINYINT},
      </if>
      <if test="autoMoveEmailSubjectTemplate != null">
        #{autoMoveEmailSubjectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="declineClientType != null">
        #{declineClientType,jdbcType=TINYINT},
      </if>
      <if test="declineBusinessType != null">
        #{declineBusinessType,jdbcType=TINYINT},
      </if>
      <if test="declineEmailSubjectTemplate != null">
        #{declineEmailSubjectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="submitTemplate != null">
        #{submitTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="submitEmailContentTemplate != null">
        #{submitEmailContentTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="acceptTemplate != null">
        #{acceptTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="acceptEmailContentTemplate != null">
        #{acceptEmailContentTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="autoMoveTemplate != null">
        #{autoMoveTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="autoMoveEmailContentTemplate != null">
        #{autoMoveEmailContentTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="declineTemplate != null">
        #{declineTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="declineEmailContentTemplate != null">
        #{declineEmailContentTemplate,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineNotification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_notification
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="submitClientType != null">
        submit_client_type = #{submitClientType,jdbcType=TINYINT},
      </if>
      <if test="submitBusinessType != null">
        submit_business_type = #{submitBusinessType,jdbcType=TINYINT},
      </if>
      <if test="submitEmailSubjectTemplate != null">
        submit_email_subject_template = #{submitEmailSubjectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="acceptClientType != null">
        accept_client_type = #{acceptClientType,jdbcType=TINYINT},
      </if>
      <if test="acceptEmailSubjectTemplate != null">
        accept_email_subject_template = #{acceptEmailSubjectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="acceptBusinessType != null">
        accept_business_type = #{acceptBusinessType,jdbcType=TINYINT},
      </if>
      <if test="autoMoveClientType != null">
        auto_move_client_type = #{autoMoveClientType,jdbcType=TINYINT},
      </if>
      <if test="autoMoveBusinessType != null">
        auto_move_business_type = #{autoMoveBusinessType,jdbcType=TINYINT},
      </if>
      <if test="autoMoveEmailSubjectTemplate != null">
        auto_move_email_subject_template = #{autoMoveEmailSubjectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="declineClientType != null">
        decline_client_type = #{declineClientType,jdbcType=TINYINT},
      </if>
      <if test="declineBusinessType != null">
        decline_business_type = #{declineBusinessType,jdbcType=TINYINT},
      </if>
      <if test="declineEmailSubjectTemplate != null">
        decline_email_subject_template = #{declineEmailSubjectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="submitTemplate != null">
        submit_template = #{submitTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="submitEmailContentTemplate != null">
        submit_email_content_template = #{submitEmailContentTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="acceptTemplate != null">
        accept_template = #{acceptTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="acceptEmailContentTemplate != null">
        accept_email_content_template = #{acceptEmailContentTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="autoMoveTemplate != null">
        auto_move_template = #{autoMoveTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="autoMoveEmailContentTemplate != null">
        auto_move_email_content_template = #{autoMoveEmailContentTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="declineTemplate != null">
        decline_template = #{declineTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="declineEmailContentTemplate != null">
        decline_email_content_template = #{declineEmailContentTemplate,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineNotification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_notification
    set business_id = #{businessId,jdbcType=INTEGER},
      submit_client_type = #{submitClientType,jdbcType=TINYINT},
      submit_business_type = #{submitBusinessType,jdbcType=TINYINT},
      submit_email_subject_template = #{submitEmailSubjectTemplate,jdbcType=VARCHAR},
      accept_client_type = #{acceptClientType,jdbcType=TINYINT},
      accept_email_subject_template = #{acceptEmailSubjectTemplate,jdbcType=VARCHAR},
      accept_business_type = #{acceptBusinessType,jdbcType=TINYINT},
      auto_move_client_type = #{autoMoveClientType,jdbcType=TINYINT},
      auto_move_business_type = #{autoMoveBusinessType,jdbcType=TINYINT},
      auto_move_email_subject_template = #{autoMoveEmailSubjectTemplate,jdbcType=VARCHAR},
      decline_client_type = #{declineClientType,jdbcType=TINYINT},
      decline_business_type = #{declineBusinessType,jdbcType=TINYINT},
      decline_email_subject_template = #{declineEmailSubjectTemplate,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT},
      submit_template = #{submitTemplate,jdbcType=LONGVARCHAR},
      submit_email_content_template = #{submitEmailContentTemplate,jdbcType=LONGVARCHAR},
      accept_template = #{acceptTemplate,jdbcType=LONGVARCHAR},
      accept_email_content_template = #{acceptEmailContentTemplate,jdbcType=LONGVARCHAR},
      auto_move_template = #{autoMoveTemplate,jdbcType=LONGVARCHAR},
      auto_move_email_content_template = #{autoMoveEmailContentTemplate,jdbcType=LONGVARCHAR},
      decline_template = #{declineTemplate,jdbcType=LONGVARCHAR},
      decline_email_content_template = #{declineEmailContentTemplate,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineNotification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_notification
    set business_id = #{businessId,jdbcType=INTEGER},
      submit_client_type = #{submitClientType,jdbcType=TINYINT},
      submit_business_type = #{submitBusinessType,jdbcType=TINYINT},
      submit_email_subject_template = #{submitEmailSubjectTemplate,jdbcType=VARCHAR},
      accept_client_type = #{acceptClientType,jdbcType=TINYINT},
      accept_email_subject_template = #{acceptEmailSubjectTemplate,jdbcType=VARCHAR},
      accept_business_type = #{acceptBusinessType,jdbcType=TINYINT},
      auto_move_client_type = #{autoMoveClientType,jdbcType=TINYINT},
      auto_move_business_type = #{autoMoveBusinessType,jdbcType=TINYINT},
      auto_move_email_subject_template = #{autoMoveEmailSubjectTemplate,jdbcType=VARCHAR},
      decline_client_type = #{declineClientType,jdbcType=TINYINT},
      decline_business_type = #{declineBusinessType,jdbcType=TINYINT},
      decline_email_subject_template = #{declineEmailSubjectTemplate,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByBusinessId" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_book_online_notification
    where business_id = #{businessId,jdbcType=INTEGER}
  </select>
  <update id="updateByBusinessIdWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineNotification">
    update moe_book_online_notification
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="submitClientType != null">
        submit_client_type = #{submitClientType,jdbcType=TINYINT},
      </if>
      <if test="submitBusinessType != null">
        submit_business_type = #{submitBusinessType,jdbcType=TINYINT},
      </if>
      <if test="acceptClientType != null">
        accept_client_type = #{acceptClientType,jdbcType=TINYINT},
      </if>
      <if test="acceptBusinessType != null">
        accept_business_type = #{acceptBusinessType,jdbcType=TINYINT},
      </if>
      <if test="autoMoveClientType != null">
        auto_move_client_type = #{autoMoveClientType,jdbcType=TINYINT},
      </if>
      <if test="autoMoveBusinessType != null">
        auto_move_business_type = #{autoMoveBusinessType,jdbcType=TINYINT},
      </if>
      <if test="declineClientType != null">
        decline_client_type = #{declineClientType,jdbcType=TINYINT},
      </if>
      <if test="declineBusinessType != null">
        decline_business_type = #{declineBusinessType,jdbcType=TINYINT},
      </if>
      <if test="submitTemplate != null">
        submit_template = #{submitTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="acceptTemplate != null">
        accept_template = #{acceptTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="autoMoveTemplate != null">
        auto_move_template = #{autoMoveTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="declineTemplate != null">
        decline_template = #{declineTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="submitEmailContentTemplate != null">
        submit_email_content_template = #{submitEmailContentTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="submitEmailSubjectTemplate != null">
        submit_email_subject_template = #{submitEmailSubjectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="acceptEmailContentTemplate != null">
        accept_email_content_template = #{acceptEmailContentTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="acceptEmailSubjectTemplate != null">
        accept_email_subject_template = #{acceptEmailSubjectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="autoMoveEmailContentTemplate != null">
        auto_move_email_content_template = #{autoMoveEmailContentTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="autoMoveEmailSubjectTemplate != null">
        auto_move_email_subject_template = #{autoMoveEmailSubjectTemplate,jdbcType=VARCHAR},
      </if>
      <if test="declineEmailContentTemplate != null">
        decline_email_content_template = #{declineEmailContentTemplate,jdbcType=LONGVARCHAR},
      </if>
      <if test="declineEmailSubjectTemplate != null">
        decline_email_subject_template = #{declineEmailSubjectTemplate,jdbcType=VARCHAR},
      </if>
    </set>
    where business_id = #{businessId,jdbcType=INTEGER}
  </update>
</mapper>