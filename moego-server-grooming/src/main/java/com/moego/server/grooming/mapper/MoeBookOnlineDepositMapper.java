package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDepositExample;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

public interface MoeBookOnlineDepositMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    long countByExample(MoeBookOnlineDepositExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBookOnlineDepositExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineDeposit record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineDeposit record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    List<MoeBookOnlineDeposit> selectByExample(MoeBookOnlineDepositExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    MoeBookOnlineDeposit selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeBookOnlineDeposit record, @Param("example") MoeBookOnlineDepositExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeBookOnlineDeposit record, @Param("example") MoeBookOnlineDepositExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineDeposit record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_deposit
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineDeposit record);

    MoeBookOnlineDeposit selectByGuid(String guid);

    MoeBookOnlineDeposit selectByPaymentId(
            @Param("businessId") Integer businessId, @Param("paymentId") Integer paymentId);

    List<MoeBookOnlineDeposit> selectByPaymentIdsAndDepositType(
            @Param("businessId") Integer businessId,
            @Param("paymentIds") Set<Integer> paymentId,
            @Param("depositType") Byte depositType);

    List<MoeBookOnlineDeposit> selectByPaymentIdsV2(
            @Param("companyId") Long companyId, @Param("paymentIds") Set<Integer> paymentId);

    MoeBookOnlineDeposit selectByGroomingId(
            @Param("businessId") Integer businessId, @Param("groomingId") Integer groomingId);

    List<MoeBookOnlineDeposit> selectByGroomingIds(
            @Param("businessId") Integer businessId, @Param("groomingIds") Set<Integer> groomingIds);
    // groomingIds must not be empty, otherwise it will return a sql error, check it before calling this method
    List<MoeBookOnlineDeposit> selectByGroomingIdsV2(@Param("groomingIds") Set<Integer> groomingIds);

    BigDecimal countPrepaidRevenue(
            @Param("businessId") Integer businessId,
            @Param("startTime") long startTime,
            @Param("endTime") long endTime);

    int updateDiscountCodeId(@Param("discountCodeIdMap") Map<Long, Long> discountCodeIdMap);
}
