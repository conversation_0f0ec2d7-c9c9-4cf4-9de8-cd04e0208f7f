package com.moego.server.grooming.service.params;

import com.moego.common.utils.AmountUtils;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CalCollectedPriceParams {

    private BigDecimal paidAmount;
    private BigDecimal refundAmount;

    private BigDecimal expectedTips;

    private BigDecimal expectedServiceTax;
    private BigDecimal expectedProductTax;
    private BigDecimal expectedServiceChargeTax;

    private BigDecimal serviceDiscountAmount;
    private BigDecimal productDiscountAmount;
    private BigDecimal serviceChargeDiscountAmount;

    private BigDecimal totalServiceSale;
    private BigDecimal totalProductSale;
    private BigDecimal totalServiceChargeSale;

    public BigDecimal getTotalCollectedRevenue() {
        return AmountUtils.subtract(paidAmount, refundAmount);
    }

    public BigDecimal getTotalSale() {
        return AmountUtils.sum(totalServiceSale, totalProductSale, totalServiceChargeSale);
    }
}
