package com.moego.server.grooming.web.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class CreateCalendarSyncSettingVo {

    @Schema(description = "通过oauth/save 获取到的数据，需要回传")
    @NotNull
    private Integer googleAuthId;

    @Schema(description = "同步方式 1 import and export   2export only   3import only")
    @NotNull
    private Byte syncType;

    @Schema(description = "被同步的staff id 数组")
    @NotNull
    private List<Integer> syncedStaffIdList;
}
