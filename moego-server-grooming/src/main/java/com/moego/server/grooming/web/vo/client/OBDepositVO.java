package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/8
 */
@Data
@Accessors(chain = true)
public class OBDepositVO {

    @Schema(description = "Service total")
    private BigDecimal serviceTotal;

    @Schema(description = "Tax")
    private BigDecimal tax;

    @Schema(description = "Tips")
    private BigDecimal tips;

    @Schema(description = "Service charge amount")
    private BigDecimal serviceChargeAmount;

    @Schema(description = "Fees, booking fee + convenience fee")
    private BigDecimal fees;

    @Schema(description = "Deposit amount, amount - convenience fee")
    private BigDecimal depositAmount;

    @Schema(description = "Paid amount")
    private BigDecimal paidAmount;

    @Schema(description = "Prepay type, 0-full amount, 1-deposit")
    private Byte prepayType;
}
