package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table appointment_pet_feeding
 */
public class AppointmentPetFeeding {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.appointment_id
     *
     * @mbg.generated
     */
    private Long appointmentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.pet_detail_id
     *
     * @mbg.generated
     */
    private Long petDetailId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.pet_id
     *
     * @mbg.generated
     */
    private Long petId;

    /**
     * Database Column Remarks:
     *   such as 1.2, 1/2, 1 etc.
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.feeding_amount
     *
     * @mbg.generated
     */
    private String feedingAmount;

    /**
     * Database Column Remarks:
     *   pet_metadata.metadata_value, metadata_name = 2
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.feeding_unit
     *
     * @mbg.generated
     */
    private String feedingUnit;

    /**
     * Database Column Remarks:
     *   pet_metadata.metadata_value, metadata_name = 3
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.feeding_type
     *
     * @mbg.generated
     */
    private String feedingType;

    /**
     * Database Column Remarks:
     *   pet_metadata.metadata_value, metadata_name = 4
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.feeding_source
     *
     * @mbg.generated
     */
    private String feedingSource;

    /**
     * Database Column Remarks:
     *   pet_metadata.metadata_value, metadata_name = 5
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.feeding_instruction
     *
     * @mbg.generated
     */
    private String feedingInstruction;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.created_at
     *
     * @mbg.generated
     */
    private Date createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.updated_at
     *
     * @mbg.generated
     */
    private Date updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.deleted_at
     *
     * @mbg.generated
     */
    private Date deletedAt;

    /**
     * Database Column Remarks:
     *   feeding note, user input
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column appointment_pet_feeding.feeding_note
     *
     * @mbg.generated
     */
    private String feedingNote;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.id
     *
     * @return the value of appointment_pet_feeding.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.id
     *
     * @param id the value for appointment_pet_feeding.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.company_id
     *
     * @return the value of appointment_pet_feeding.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.company_id
     *
     * @param companyId the value for appointment_pet_feeding.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.appointment_id
     *
     * @return the value of appointment_pet_feeding.appointment_id
     *
     * @mbg.generated
     */
    public Long getAppointmentId() {
        return appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.appointment_id
     *
     * @param appointmentId the value for appointment_pet_feeding.appointment_id
     *
     * @mbg.generated
     */
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.pet_detail_id
     *
     * @return the value of appointment_pet_feeding.pet_detail_id
     *
     * @mbg.generated
     */
    public Long getPetDetailId() {
        return petDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.pet_detail_id
     *
     * @param petDetailId the value for appointment_pet_feeding.pet_detail_id
     *
     * @mbg.generated
     */
    public void setPetDetailId(Long petDetailId) {
        this.petDetailId = petDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.pet_id
     *
     * @return the value of appointment_pet_feeding.pet_id
     *
     * @mbg.generated
     */
    public Long getPetId() {
        return petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.pet_id
     *
     * @param petId the value for appointment_pet_feeding.pet_id
     *
     * @mbg.generated
     */
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.feeding_amount
     *
     * @return the value of appointment_pet_feeding.feeding_amount
     *
     * @mbg.generated
     */
    public String getFeedingAmount() {
        return feedingAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.feeding_amount
     *
     * @param feedingAmount the value for appointment_pet_feeding.feeding_amount
     *
     * @mbg.generated
     */
    public void setFeedingAmount(String feedingAmount) {
        this.feedingAmount = feedingAmount == null ? null : feedingAmount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.feeding_unit
     *
     * @return the value of appointment_pet_feeding.feeding_unit
     *
     * @mbg.generated
     */
    public String getFeedingUnit() {
        return feedingUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.feeding_unit
     *
     * @param feedingUnit the value for appointment_pet_feeding.feeding_unit
     *
     * @mbg.generated
     */
    public void setFeedingUnit(String feedingUnit) {
        this.feedingUnit = feedingUnit == null ? null : feedingUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.feeding_type
     *
     * @return the value of appointment_pet_feeding.feeding_type
     *
     * @mbg.generated
     */
    public String getFeedingType() {
        return feedingType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.feeding_type
     *
     * @param feedingType the value for appointment_pet_feeding.feeding_type
     *
     * @mbg.generated
     */
    public void setFeedingType(String feedingType) {
        this.feedingType = feedingType == null ? null : feedingType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.feeding_source
     *
     * @return the value of appointment_pet_feeding.feeding_source
     *
     * @mbg.generated
     */
    public String getFeedingSource() {
        return feedingSource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.feeding_source
     *
     * @param feedingSource the value for appointment_pet_feeding.feeding_source
     *
     * @mbg.generated
     */
    public void setFeedingSource(String feedingSource) {
        this.feedingSource = feedingSource == null ? null : feedingSource.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.feeding_instruction
     *
     * @return the value of appointment_pet_feeding.feeding_instruction
     *
     * @mbg.generated
     */
    public String getFeedingInstruction() {
        return feedingInstruction;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.feeding_instruction
     *
     * @param feedingInstruction the value for appointment_pet_feeding.feeding_instruction
     *
     * @mbg.generated
     */
    public void setFeedingInstruction(String feedingInstruction) {
        this.feedingInstruction = feedingInstruction == null ? null : feedingInstruction.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.created_at
     *
     * @return the value of appointment_pet_feeding.created_at
     *
     * @mbg.generated
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.created_at
     *
     * @param createdAt the value for appointment_pet_feeding.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.updated_at
     *
     * @return the value of appointment_pet_feeding.updated_at
     *
     * @mbg.generated
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.updated_at
     *
     * @param updatedAt the value for appointment_pet_feeding.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.deleted_at
     *
     * @return the value of appointment_pet_feeding.deleted_at
     *
     * @mbg.generated
     */
    public Date getDeletedAt() {
        return deletedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.deleted_at
     *
     * @param deletedAt the value for appointment_pet_feeding.deleted_at
     *
     * @mbg.generated
     */
    public void setDeletedAt(Date deletedAt) {
        this.deletedAt = deletedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column appointment_pet_feeding.feeding_note
     *
     * @return the value of appointment_pet_feeding.feeding_note
     *
     * @mbg.generated
     */
    public String getFeedingNote() {
        return feedingNote;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column appointment_pet_feeding.feeding_note
     *
     * @param feedingNote the value for appointment_pet_feeding.feeding_note
     *
     * @mbg.generated
     */
    public void setFeedingNote(String feedingNote) {
        this.feedingNote = feedingNote == null ? null : feedingNote.trim();
    }
}
