package com.moego.server.grooming.service.ob.component;

import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.web.vo.ob.component.BaseComponentVO;
import com.moego.server.grooming.web.vo.ob.component.ShowcaseComponentVO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Service(value = LandingPageComponentEnum.COMPONENT_SHOWCASE)
public class ShowcaseComponentService implements ILandingPageComponentService {

    @Override
    public BaseComponentVO getPageComponent(MoeBookOnlineLandingPageConfig landingPageConfig) {
        return new ShowcaseComponentVO()
                .setBeforeImage(landingPageConfig.getShowcaseBeforeImage())
                .setAfterImage(landingPageConfig.getShowcaseAfterImage());
    }
}
