package com.moego.server.grooming.config;

import com.moego.lib.common.util.ThreadPoolUtil;
import java.time.Duration;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@RequiredArgsConstructor
public class DrivingInfoConfiguration {

    public static final String DRIVING_INFO_EXECUTOR_SERVICE = "drivingInfoExecutorService";
    private static final String DRIVING_INFO_THREAD_NAME_PREFIX = "moego-driving-info-";

    public final DrivingInfoProperties properties;

    @Bean(name = DRIVING_INFO_EXECUTOR_SERVICE)
    public ExecutorService drivingInfoExecutorService() {
        return ThreadPoolUtil.newExecutorService(
                properties.getCorePoolSize(),
                properties.getMaximumPoolSize(),
                Duration.ofSeconds(properties.getAliveTimeSeconds()),
                properties.getQueueCapacity(),
                DRIVING_INFO_THREAD_NAME_PREFIX,
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
