package com.moego.server.grooming.service.ob.component;

import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO;
import com.moego.server.grooming.web.vo.ob.component.AmenitiesComponentVO;
import com.moego.server.grooming.web.vo.ob.component.BaseComponentVO;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Service(value = LandingPageComponentEnum.COMPONENT_AMENITIES)
public class AmenitiesComponentService implements ILandingPageComponentService {

    @Override
    public BaseComponentVO getPageComponent(MoeBookOnlineLandingPageConfig landingPageConfig) {
        AmenitiesComponentVO amenitiesComponentVO = new AmenitiesComponentVO();
        String amenities = landingPageConfig.getAmenities();
        if (StringUtils.hasText(amenities)) {
            OBLandingPageConfigVO.LandingPageAmenitiesVO amenitiesVO =
                    JsonUtil.toBean(amenities, OBLandingPageConfigVO.LandingPageAmenitiesVO.class);
            amenitiesComponentVO.setAmenities(amenitiesVO);
        }
        return amenitiesComponentVO;
    }
}
