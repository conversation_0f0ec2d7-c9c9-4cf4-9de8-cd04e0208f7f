package com.moego.server.grooming.service.dto;

import com.moego.lib.utils.model.Pair;
import com.moego.server.grooming.dto.LimitDto;
import java.util.List;
import java.util.Set;
import lombok.Data;

@Data
public class CapacityTimeslotDTO {

    private Integer startTime;
    private Integer capacity;
    private LimitDto limitDto;
    private List<Long> limitIds;

    private Integer endTime;

    private Set<Pair<Integer, Integer>> usedAppointmentPetPairs;

    public boolean contains(int time) {
        return startTime <= time && time < endTime;
    }
}
