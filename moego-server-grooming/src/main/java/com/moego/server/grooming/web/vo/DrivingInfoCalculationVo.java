package com.moego.server.grooming.web.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/8/10 7:05 PM
 */
@Data
public class DrivingInfoCalculationVo {
    @Schema(description = "customer id to driving info")
    Map<Long, DrivingCalculationVo> clientDrivingFromMap;

    @Schema(description = "customer id to driving info")
    Map<Long, DrivingCalculationVo> clientDrivingToMap;

    @Schema(description = "van staff id to driving info")
    Map<Long, DrivingCalculationVo> vanStaffDrivingFromMap;

    @Schema(description = "van staff id to driving info")
    Map<Long, DrivingCalculationVo> vanStaffDrivingToMap;

    @Data
    @AllArgsConstructor
    public static class DrivingCalculationVo {
        Integer drivingMinutes;
        Double drivingMiles;
    }
}
