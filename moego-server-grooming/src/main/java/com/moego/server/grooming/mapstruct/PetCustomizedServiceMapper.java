package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.PetCustomizedServiceDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface PetCustomizedServiceMapper {
    PetCustomizedServiceMapper INSTANCE = Mappers.getMapper(PetCustomizedServiceMapper.class);

    PetCustomizedServiceDTO entity2DTO(MoeGroomingCustomerServices entity);
}
