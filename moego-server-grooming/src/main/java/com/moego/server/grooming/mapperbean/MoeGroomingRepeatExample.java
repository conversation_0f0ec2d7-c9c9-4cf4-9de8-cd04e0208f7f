package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class MoeGroomingRepeatExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    public MoeGroomingRepeatExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Integer value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Integer value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Integer value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Integer value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Integer> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Integer> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andStaffIdIsNull() {
            addCriterion("staff_id is null");
            return (Criteria) this;
        }

        public Criteria andStaffIdIsNotNull() {
            addCriterion("staff_id is not null");
            return (Criteria) this;
        }

        public Criteria andStaffIdEqualTo(Integer value) {
            addCriterion("staff_id =", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotEqualTo(Integer value) {
            addCriterion("staff_id <>", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdGreaterThan(Integer value) {
            addCriterion("staff_id >", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("staff_id >=", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdLessThan(Integer value) {
            addCriterion("staff_id <", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdLessThanOrEqualTo(Integer value) {
            addCriterion("staff_id <=", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdIn(List<Integer> values) {
            addCriterion("staff_id in", values, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotIn(List<Integer> values) {
            addCriterion("staff_id not in", values, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdBetween(Integer value1, Integer value2) {
            addCriterion("staff_id between", value1, value2, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotBetween(Integer value1, Integer value2) {
            addCriterion("staff_id not between", value1, value2, "staffId");
            return (Criteria) this;
        }

        public Criteria andRepeatTypeIsNull() {
            addCriterion("repeat_type is null");
            return (Criteria) this;
        }

        public Criteria andRepeatTypeIsNotNull() {
            addCriterion("repeat_type is not null");
            return (Criteria) this;
        }

        public Criteria andRepeatTypeEqualTo(Byte value) {
            addCriterion("repeat_type =", value, "repeatType");
            return (Criteria) this;
        }

        public Criteria andRepeatTypeNotEqualTo(Byte value) {
            addCriterion("repeat_type <>", value, "repeatType");
            return (Criteria) this;
        }

        public Criteria andRepeatTypeGreaterThan(Byte value) {
            addCriterion("repeat_type >", value, "repeatType");
            return (Criteria) this;
        }

        public Criteria andRepeatTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("repeat_type >=", value, "repeatType");
            return (Criteria) this;
        }

        public Criteria andRepeatTypeLessThan(Byte value) {
            addCriterion("repeat_type <", value, "repeatType");
            return (Criteria) this;
        }

        public Criteria andRepeatTypeLessThanOrEqualTo(Byte value) {
            addCriterion("repeat_type <=", value, "repeatType");
            return (Criteria) this;
        }

        public Criteria andRepeatTypeIn(List<Byte> values) {
            addCriterion("repeat_type in", values, "repeatType");
            return (Criteria) this;
        }

        public Criteria andRepeatTypeNotIn(List<Byte> values) {
            addCriterion("repeat_type not in", values, "repeatType");
            return (Criteria) this;
        }

        public Criteria andRepeatTypeBetween(Byte value1, Byte value2) {
            addCriterion("repeat_type between", value1, value2, "repeatType");
            return (Criteria) this;
        }

        public Criteria andRepeatTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("repeat_type not between", value1, value2, "repeatType");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryIsNull() {
            addCriterion("repeat_every is null");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryIsNotNull() {
            addCriterion("repeat_every is not null");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryEqualTo(Integer value) {
            addCriterion("repeat_every =", value, "repeatEvery");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryNotEqualTo(Integer value) {
            addCriterion("repeat_every <>", value, "repeatEvery");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryGreaterThan(Integer value) {
            addCriterion("repeat_every >", value, "repeatEvery");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryGreaterThanOrEqualTo(Integer value) {
            addCriterion("repeat_every >=", value, "repeatEvery");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryLessThan(Integer value) {
            addCriterion("repeat_every <", value, "repeatEvery");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryLessThanOrEqualTo(Integer value) {
            addCriterion("repeat_every <=", value, "repeatEvery");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryIn(List<Integer> values) {
            addCriterion("repeat_every in", values, "repeatEvery");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryNotIn(List<Integer> values) {
            addCriterion("repeat_every not in", values, "repeatEvery");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryBetween(Integer value1, Integer value2) {
            addCriterion("repeat_every between", value1, value2, "repeatEvery");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryNotBetween(Integer value1, Integer value2) {
            addCriterion("repeat_every not between", value1, value2, "repeatEvery");
            return (Criteria) this;
        }

        public Criteria andRepeatByIsNull() {
            addCriterion("repeat_by is null");
            return (Criteria) this;
        }

        public Criteria andRepeatByIsNotNull() {
            addCriterion("repeat_by is not null");
            return (Criteria) this;
        }

        public Criteria andRepeatByEqualTo(String value) {
            addCriterion("repeat_by =", value, "repeatBy");
            return (Criteria) this;
        }

        public Criteria andRepeatByNotEqualTo(String value) {
            addCriterion("repeat_by <>", value, "repeatBy");
            return (Criteria) this;
        }

        public Criteria andRepeatByGreaterThan(String value) {
            addCriterion("repeat_by >", value, "repeatBy");
            return (Criteria) this;
        }

        public Criteria andRepeatByGreaterThanOrEqualTo(String value) {
            addCriterion("repeat_by >=", value, "repeatBy");
            return (Criteria) this;
        }

        public Criteria andRepeatByLessThan(String value) {
            addCriterion("repeat_by <", value, "repeatBy");
            return (Criteria) this;
        }

        public Criteria andRepeatByLessThanOrEqualTo(String value) {
            addCriterion("repeat_by <=", value, "repeatBy");
            return (Criteria) this;
        }

        public Criteria andRepeatByLike(String value) {
            addCriterion("repeat_by like", value, "repeatBy");
            return (Criteria) this;
        }

        public Criteria andRepeatByNotLike(String value) {
            addCriterion("repeat_by not like", value, "repeatBy");
            return (Criteria) this;
        }

        public Criteria andRepeatByIn(List<String> values) {
            addCriterion("repeat_by in", values, "repeatBy");
            return (Criteria) this;
        }

        public Criteria andRepeatByNotIn(List<String> values) {
            addCriterion("repeat_by not in", values, "repeatBy");
            return (Criteria) this;
        }

        public Criteria andRepeatByBetween(String value1, String value2) {
            addCriterion("repeat_by between", value1, value2, "repeatBy");
            return (Criteria) this;
        }

        public Criteria andRepeatByNotBetween(String value1, String value2) {
            addCriterion("repeat_by not between", value1, value2, "repeatBy");
            return (Criteria) this;
        }

        public Criteria andStartsOnIsNull() {
            addCriterion("starts_on is null");
            return (Criteria) this;
        }

        public Criteria andStartsOnIsNotNull() {
            addCriterion("starts_on is not null");
            return (Criteria) this;
        }

        public Criteria andStartsOnEqualTo(Date value) {
            addCriterionForJDBCDate("starts_on =", value, "startsOn");
            return (Criteria) this;
        }

        public Criteria andStartsOnNotEqualTo(Date value) {
            addCriterionForJDBCDate("starts_on <>", value, "startsOn");
            return (Criteria) this;
        }

        public Criteria andStartsOnGreaterThan(Date value) {
            addCriterionForJDBCDate("starts_on >", value, "startsOn");
            return (Criteria) this;
        }

        public Criteria andStartsOnGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("starts_on >=", value, "startsOn");
            return (Criteria) this;
        }

        public Criteria andStartsOnLessThan(Date value) {
            addCriterionForJDBCDate("starts_on <", value, "startsOn");
            return (Criteria) this;
        }

        public Criteria andStartsOnLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("starts_on <=", value, "startsOn");
            return (Criteria) this;
        }

        public Criteria andStartsOnIn(List<Date> values) {
            addCriterionForJDBCDate("starts_on in", values, "startsOn");
            return (Criteria) this;
        }

        public Criteria andStartsOnNotIn(List<Date> values) {
            addCriterionForJDBCDate("starts_on not in", values, "startsOn");
            return (Criteria) this;
        }

        public Criteria andStartsOnBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("starts_on between", value1, value2, "startsOn");
            return (Criteria) this;
        }

        public Criteria andStartsOnNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("starts_on not between", value1, value2, "startsOn");
            return (Criteria) this;
        }

        public Criteria andTimesIsNull() {
            addCriterion("times is null");
            return (Criteria) this;
        }

        public Criteria andTimesIsNotNull() {
            addCriterion("times is not null");
            return (Criteria) this;
        }

        public Criteria andTimesEqualTo(Integer value) {
            addCriterion("times =", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesNotEqualTo(Integer value) {
            addCriterion("times <>", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesGreaterThan(Integer value) {
            addCriterion("times >", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesGreaterThanOrEqualTo(Integer value) {
            addCriterion("times >=", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesLessThan(Integer value) {
            addCriterion("times <", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesLessThanOrEqualTo(Integer value) {
            addCriterion("times <=", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesIn(List<Integer> values) {
            addCriterion("times in", values, "times");
            return (Criteria) this;
        }

        public Criteria andTimesNotIn(List<Integer> values) {
            addCriterion("times not in", values, "times");
            return (Criteria) this;
        }

        public Criteria andTimesBetween(Integer value1, Integer value2) {
            addCriterion("times between", value1, value2, "times");
            return (Criteria) this;
        }

        public Criteria andTimesNotBetween(Integer value1, Integer value2) {
            addCriterion("times not between", value1, value2, "times");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andEndOnIsNull() {
            addCriterion("end_on is null");
            return (Criteria) this;
        }

        public Criteria andEndOnIsNotNull() {
            addCriterion("end_on is not null");
            return (Criteria) this;
        }

        public Criteria andEndOnEqualTo(String value) {
            addCriterion("end_on =", value, "endOn");
            return (Criteria) this;
        }

        public Criteria andEndOnNotEqualTo(String value) {
            addCriterion("end_on <>", value, "endOn");
            return (Criteria) this;
        }

        public Criteria andEndOnGreaterThan(String value) {
            addCriterion("end_on >", value, "endOn");
            return (Criteria) this;
        }

        public Criteria andEndOnGreaterThanOrEqualTo(String value) {
            addCriterion("end_on >=", value, "endOn");
            return (Criteria) this;
        }

        public Criteria andEndOnLessThan(String value) {
            addCriterion("end_on <", value, "endOn");
            return (Criteria) this;
        }

        public Criteria andEndOnLessThanOrEqualTo(String value) {
            addCriterion("end_on <=", value, "endOn");
            return (Criteria) this;
        }

        public Criteria andEndOnLike(String value) {
            addCriterion("end_on like", value, "endOn");
            return (Criteria) this;
        }

        public Criteria andEndOnNotLike(String value) {
            addCriterion("end_on not like", value, "endOn");
            return (Criteria) this;
        }

        public Criteria andEndOnIn(List<String> values) {
            addCriterion("end_on in", values, "endOn");
            return (Criteria) this;
        }

        public Criteria andEndOnNotIn(List<String> values) {
            addCriterion("end_on not in", values, "endOn");
            return (Criteria) this;
        }

        public Criteria andEndOnBetween(String value1, String value2) {
            addCriterion("end_on between", value1, value2, "endOn");
            return (Criteria) this;
        }

        public Criteria andEndOnNotBetween(String value1, String value2) {
            addCriterion("end_on not between", value1, value2, "endOn");
            return (Criteria) this;
        }

        public Criteria andIsNoticeIsNull() {
            addCriterion("is_notice is null");
            return (Criteria) this;
        }

        public Criteria andIsNoticeIsNotNull() {
            addCriterion("is_notice is not null");
            return (Criteria) this;
        }

        public Criteria andIsNoticeEqualTo(Byte value) {
            addCriterion("is_notice =", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotEqualTo(Byte value) {
            addCriterion("is_notice <>", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThan(Byte value) {
            addCriterion("is_notice >", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_notice >=", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThan(Byte value) {
            addCriterion("is_notice <", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThanOrEqualTo(Byte value) {
            addCriterion("is_notice <=", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeIn(List<Byte> values) {
            addCriterion("is_notice in", values, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotIn(List<Byte> values) {
            addCriterion("is_notice not in", values, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeBetween(Byte value1, Byte value2) {
            addCriterion("is_notice between", value1, value2, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotBetween(Byte value1, Byte value2) {
            addCriterion("is_notice not between", value1, value2, "isNotice");
            return (Criteria) this;
        }

        public Criteria andSetEndOnIsNull() {
            addCriterion("set_end_on is null");
            return (Criteria) this;
        }

        public Criteria andSetEndOnIsNotNull() {
            addCriterion("set_end_on is not null");
            return (Criteria) this;
        }

        public Criteria andSetEndOnEqualTo(Date value) {
            addCriterionForJDBCDate("set_end_on =", value, "setEndOn");
            return (Criteria) this;
        }

        public Criteria andSetEndOnNotEqualTo(Date value) {
            addCriterionForJDBCDate("set_end_on <>", value, "setEndOn");
            return (Criteria) this;
        }

        public Criteria andSetEndOnGreaterThan(Date value) {
            addCriterionForJDBCDate("set_end_on >", value, "setEndOn");
            return (Criteria) this;
        }

        public Criteria andSetEndOnGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("set_end_on >=", value, "setEndOn");
            return (Criteria) this;
        }

        public Criteria andSetEndOnLessThan(Date value) {
            addCriterionForJDBCDate("set_end_on <", value, "setEndOn");
            return (Criteria) this;
        }

        public Criteria andSetEndOnLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("set_end_on <=", value, "setEndOn");
            return (Criteria) this;
        }

        public Criteria andSetEndOnIn(List<Date> values) {
            addCriterionForJDBCDate("set_end_on in", values, "setEndOn");
            return (Criteria) this;
        }

        public Criteria andSetEndOnNotIn(List<Date> values) {
            addCriterionForJDBCDate("set_end_on not in", values, "setEndOn");
            return (Criteria) this;
        }

        public Criteria andSetEndOnBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("set_end_on between", value1, value2, "setEndOn");
            return (Criteria) this;
        }

        public Criteria andSetEndOnNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("set_end_on not between", value1, value2, "setEndOn");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryTypeIsNull() {
            addCriterion("repeat_every_type is null");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryTypeIsNotNull() {
            addCriterion("repeat_every_type is not null");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryTypeEqualTo(Byte value) {
            addCriterion("repeat_every_type =", value, "repeatEveryType");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryTypeNotEqualTo(Byte value) {
            addCriterion("repeat_every_type <>", value, "repeatEveryType");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryTypeGreaterThan(Byte value) {
            addCriterion("repeat_every_type >", value, "repeatEveryType");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("repeat_every_type >=", value, "repeatEveryType");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryTypeLessThan(Byte value) {
            addCriterion("repeat_every_type <", value, "repeatEveryType");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryTypeLessThanOrEqualTo(Byte value) {
            addCriterion("repeat_every_type <=", value, "repeatEveryType");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryTypeIn(List<Byte> values) {
            addCriterion("repeat_every_type in", values, "repeatEveryType");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryTypeNotIn(List<Byte> values) {
            addCriterion("repeat_every_type not in", values, "repeatEveryType");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryTypeBetween(Byte value1, Byte value2) {
            addCriterion("repeat_every_type between", value1, value2, "repeatEveryType");
            return (Criteria) this;
        }

        public Criteria andRepeatEveryTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("repeat_every_type not between", value1, value2, "repeatEveryType");
            return (Criteria) this;
        }

        public Criteria andMonthDayIsNull() {
            addCriterion("month_day is null");
            return (Criteria) this;
        }

        public Criteria andMonthDayIsNotNull() {
            addCriterion("month_day is not null");
            return (Criteria) this;
        }

        public Criteria andMonthDayEqualTo(Byte value) {
            addCriterion("month_day =", value, "monthDay");
            return (Criteria) this;
        }

        public Criteria andMonthDayNotEqualTo(Byte value) {
            addCriterion("month_day <>", value, "monthDay");
            return (Criteria) this;
        }

        public Criteria andMonthDayGreaterThan(Byte value) {
            addCriterion("month_day >", value, "monthDay");
            return (Criteria) this;
        }

        public Criteria andMonthDayGreaterThanOrEqualTo(Byte value) {
            addCriterion("month_day >=", value, "monthDay");
            return (Criteria) this;
        }

        public Criteria andMonthDayLessThan(Byte value) {
            addCriterion("month_day <", value, "monthDay");
            return (Criteria) this;
        }

        public Criteria andMonthDayLessThanOrEqualTo(Byte value) {
            addCriterion("month_day <=", value, "monthDay");
            return (Criteria) this;
        }

        public Criteria andMonthDayIn(List<Byte> values) {
            addCriterion("month_day in", values, "monthDay");
            return (Criteria) this;
        }

        public Criteria andMonthDayNotIn(List<Byte> values) {
            addCriterion("month_day not in", values, "monthDay");
            return (Criteria) this;
        }

        public Criteria andMonthDayBetween(Byte value1, Byte value2) {
            addCriterion("month_day between", value1, value2, "monthDay");
            return (Criteria) this;
        }

        public Criteria andMonthDayNotBetween(Byte value1, Byte value2) {
            addCriterion("month_day not between", value1, value2, "monthDay");
            return (Criteria) this;
        }

        public Criteria andMonthWeekTimesIsNull() {
            addCriterion("month_week_times is null");
            return (Criteria) this;
        }

        public Criteria andMonthWeekTimesIsNotNull() {
            addCriterion("month_week_times is not null");
            return (Criteria) this;
        }

        public Criteria andMonthWeekTimesEqualTo(Byte value) {
            addCriterion("month_week_times =", value, "monthWeekTimes");
            return (Criteria) this;
        }

        public Criteria andMonthWeekTimesNotEqualTo(Byte value) {
            addCriterion("month_week_times <>", value, "monthWeekTimes");
            return (Criteria) this;
        }

        public Criteria andMonthWeekTimesGreaterThan(Byte value) {
            addCriterion("month_week_times >", value, "monthWeekTimes");
            return (Criteria) this;
        }

        public Criteria andMonthWeekTimesGreaterThanOrEqualTo(Byte value) {
            addCriterion("month_week_times >=", value, "monthWeekTimes");
            return (Criteria) this;
        }

        public Criteria andMonthWeekTimesLessThan(Byte value) {
            addCriterion("month_week_times <", value, "monthWeekTimes");
            return (Criteria) this;
        }

        public Criteria andMonthWeekTimesLessThanOrEqualTo(Byte value) {
            addCriterion("month_week_times <=", value, "monthWeekTimes");
            return (Criteria) this;
        }

        public Criteria andMonthWeekTimesIn(List<Byte> values) {
            addCriterion("month_week_times in", values, "monthWeekTimes");
            return (Criteria) this;
        }

        public Criteria andMonthWeekTimesNotIn(List<Byte> values) {
            addCriterion("month_week_times not in", values, "monthWeekTimes");
            return (Criteria) this;
        }

        public Criteria andMonthWeekTimesBetween(Byte value1, Byte value2) {
            addCriterion("month_week_times between", value1, value2, "monthWeekTimes");
            return (Criteria) this;
        }

        public Criteria andMonthWeekTimesNotBetween(Byte value1, Byte value2) {
            addCriterion("month_week_times not between", value1, value2, "monthWeekTimes");
            return (Criteria) this;
        }

        public Criteria andMonthWeekDayIsNull() {
            addCriterion("month_week_day is null");
            return (Criteria) this;
        }

        public Criteria andMonthWeekDayIsNotNull() {
            addCriterion("month_week_day is not null");
            return (Criteria) this;
        }

        public Criteria andMonthWeekDayEqualTo(Byte value) {
            addCriterion("month_week_day =", value, "monthWeekDay");
            return (Criteria) this;
        }

        public Criteria andMonthWeekDayNotEqualTo(Byte value) {
            addCriterion("month_week_day <>", value, "monthWeekDay");
            return (Criteria) this;
        }

        public Criteria andMonthWeekDayGreaterThan(Byte value) {
            addCriterion("month_week_day >", value, "monthWeekDay");
            return (Criteria) this;
        }

        public Criteria andMonthWeekDayGreaterThanOrEqualTo(Byte value) {
            addCriterion("month_week_day >=", value, "monthWeekDay");
            return (Criteria) this;
        }

        public Criteria andMonthWeekDayLessThan(Byte value) {
            addCriterion("month_week_day <", value, "monthWeekDay");
            return (Criteria) this;
        }

        public Criteria andMonthWeekDayLessThanOrEqualTo(Byte value) {
            addCriterion("month_week_day <=", value, "monthWeekDay");
            return (Criteria) this;
        }

        public Criteria andMonthWeekDayIn(List<Byte> values) {
            addCriterion("month_week_day in", values, "monthWeekDay");
            return (Criteria) this;
        }

        public Criteria andMonthWeekDayNotIn(List<Byte> values) {
            addCriterion("month_week_day not in", values, "monthWeekDay");
            return (Criteria) this;
        }

        public Criteria andMonthWeekDayBetween(Byte value1, Byte value2) {
            addCriterion("month_week_day between", value1, value2, "monthWeekDay");
            return (Criteria) this;
        }

        public Criteria andMonthWeekDayNotBetween(Byte value1, Byte value2) {
            addCriterion("month_week_day not between", value1, value2, "monthWeekDay");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andSsFlagIsNull() {
            addCriterion("ss_flag is null");
            return (Criteria) this;
        }

        public Criteria andSsFlagIsNotNull() {
            addCriterion("ss_flag is not null");
            return (Criteria) this;
        }

        public Criteria andSsFlagEqualTo(Byte value) {
            addCriterion("ss_flag =", value, "ssFlag");
            return (Criteria) this;
        }

        public Criteria andSsFlagNotEqualTo(Byte value) {
            addCriterion("ss_flag <>", value, "ssFlag");
            return (Criteria) this;
        }

        public Criteria andSsFlagGreaterThan(Byte value) {
            addCriterion("ss_flag >", value, "ssFlag");
            return (Criteria) this;
        }

        public Criteria andSsFlagGreaterThanOrEqualTo(Byte value) {
            addCriterion("ss_flag >=", value, "ssFlag");
            return (Criteria) this;
        }

        public Criteria andSsFlagLessThan(Byte value) {
            addCriterion("ss_flag <", value, "ssFlag");
            return (Criteria) this;
        }

        public Criteria andSsFlagLessThanOrEqualTo(Byte value) {
            addCriterion("ss_flag <=", value, "ssFlag");
            return (Criteria) this;
        }

        public Criteria andSsFlagIn(List<Byte> values) {
            addCriterion("ss_flag in", values, "ssFlag");
            return (Criteria) this;
        }

        public Criteria andSsFlagNotIn(List<Byte> values) {
            addCriterion("ss_flag not in", values, "ssFlag");
            return (Criteria) this;
        }

        public Criteria andSsFlagBetween(Byte value1, Byte value2) {
            addCriterion("ss_flag between", value1, value2, "ssFlag");
            return (Criteria) this;
        }

        public Criteria andSsFlagNotBetween(Byte value1, Byte value2) {
            addCriterion("ss_flag not between", value1, value2, "ssFlag");
            return (Criteria) this;
        }

        public Criteria andSsBeforeDaysIsNull() {
            addCriterion("ss_before_days is null");
            return (Criteria) this;
        }

        public Criteria andSsBeforeDaysIsNotNull() {
            addCriterion("ss_before_days is not null");
            return (Criteria) this;
        }

        public Criteria andSsBeforeDaysEqualTo(Integer value) {
            addCriterion("ss_before_days =", value, "ssBeforeDays");
            return (Criteria) this;
        }

        public Criteria andSsBeforeDaysNotEqualTo(Integer value) {
            addCriterion("ss_before_days <>", value, "ssBeforeDays");
            return (Criteria) this;
        }

        public Criteria andSsBeforeDaysGreaterThan(Integer value) {
            addCriterion("ss_before_days >", value, "ssBeforeDays");
            return (Criteria) this;
        }

        public Criteria andSsBeforeDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("ss_before_days >=", value, "ssBeforeDays");
            return (Criteria) this;
        }

        public Criteria andSsBeforeDaysLessThan(Integer value) {
            addCriterion("ss_before_days <", value, "ssBeforeDays");
            return (Criteria) this;
        }

        public Criteria andSsBeforeDaysLessThanOrEqualTo(Integer value) {
            addCriterion("ss_before_days <=", value, "ssBeforeDays");
            return (Criteria) this;
        }

        public Criteria andSsBeforeDaysIn(List<Integer> values) {
            addCriterion("ss_before_days in", values, "ssBeforeDays");
            return (Criteria) this;
        }

        public Criteria andSsBeforeDaysNotIn(List<Integer> values) {
            addCriterion("ss_before_days not in", values, "ssBeforeDays");
            return (Criteria) this;
        }

        public Criteria andSsBeforeDaysBetween(Integer value1, Integer value2) {
            addCriterion("ss_before_days between", value1, value2, "ssBeforeDays");
            return (Criteria) this;
        }

        public Criteria andSsBeforeDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("ss_before_days not between", value1, value2, "ssBeforeDays");
            return (Criteria) this;
        }

        public Criteria andSsAfterDaysIsNull() {
            addCriterion("ss_after_days is null");
            return (Criteria) this;
        }

        public Criteria andSsAfterDaysIsNotNull() {
            addCriterion("ss_after_days is not null");
            return (Criteria) this;
        }

        public Criteria andSsAfterDaysEqualTo(Integer value) {
            addCriterion("ss_after_days =", value, "ssAfterDays");
            return (Criteria) this;
        }

        public Criteria andSsAfterDaysNotEqualTo(Integer value) {
            addCriterion("ss_after_days <>", value, "ssAfterDays");
            return (Criteria) this;
        }

        public Criteria andSsAfterDaysGreaterThan(Integer value) {
            addCriterion("ss_after_days >", value, "ssAfterDays");
            return (Criteria) this;
        }

        public Criteria andSsAfterDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("ss_after_days >=", value, "ssAfterDays");
            return (Criteria) this;
        }

        public Criteria andSsAfterDaysLessThan(Integer value) {
            addCriterion("ss_after_days <", value, "ssAfterDays");
            return (Criteria) this;
        }

        public Criteria andSsAfterDaysLessThanOrEqualTo(Integer value) {
            addCriterion("ss_after_days <=", value, "ssAfterDays");
            return (Criteria) this;
        }

        public Criteria andSsAfterDaysIn(List<Integer> values) {
            addCriterion("ss_after_days in", values, "ssAfterDays");
            return (Criteria) this;
        }

        public Criteria andSsAfterDaysNotIn(List<Integer> values) {
            addCriterion("ss_after_days not in", values, "ssAfterDays");
            return (Criteria) this;
        }

        public Criteria andSsAfterDaysBetween(Integer value1, Integer value2) {
            addCriterion("ss_after_days between", value1, value2, "ssAfterDays");
            return (Criteria) this;
        }

        public Criteria andSsAfterDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("ss_after_days not between", value1, value2, "ssAfterDays");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysIsNull() {
            addCriterion("repeat_by_days is null");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysIsNotNull() {
            addCriterion("repeat_by_days is not null");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysEqualTo(String value) {
            addCriterion("repeat_by_days =", value, "repeatByDays");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysNotEqualTo(String value) {
            addCriterion("repeat_by_days <>", value, "repeatByDays");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysGreaterThan(String value) {
            addCriterion("repeat_by_days >", value, "repeatByDays");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysGreaterThanOrEqualTo(String value) {
            addCriterion("repeat_by_days >=", value, "repeatByDays");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysLessThan(String value) {
            addCriterion("repeat_by_days <", value, "repeatByDays");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysLessThanOrEqualTo(String value) {
            addCriterion("repeat_by_days <=", value, "repeatByDays");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysLike(String value) {
            addCriterion("repeat_by_days like", value, "repeatByDays");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysNotLike(String value) {
            addCriterion("repeat_by_days not like", value, "repeatByDays");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysIn(List<String> values) {
            addCriterion("repeat_by_days in", values, "repeatByDays");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysNotIn(List<String> values) {
            addCriterion("repeat_by_days not in", values, "repeatByDays");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysBetween(String value1, String value2) {
            addCriterion("repeat_by_days between", value1, value2, "repeatByDays");
            return (Criteria) this;
        }

        public Criteria andRepeatByDaysNotBetween(String value1, String value2) {
            addCriterion("repeat_by_days not between", value1, value2, "repeatByDays");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_repeat
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
