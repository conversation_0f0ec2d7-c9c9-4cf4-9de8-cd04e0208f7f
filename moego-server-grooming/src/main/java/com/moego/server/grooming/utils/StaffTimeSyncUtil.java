package com.moego.server.grooming.utils;

import static com.moego.server.grooming.service.StaffTimeSyncService.DEFAULT_WEEK_MAP;

import com.google.type.DayOfWeek;
import com.moego.common.constant.CommonConstant;
import com.moego.idl.models.organization.v1.BookingLimitation;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.SlotDailySetting;
import com.moego.idl.models.organization.v1.SlotHourSetting;
import com.moego.idl.models.organization.v1.StaffAvailability;
import com.moego.idl.models.organization.v1.TimeAvailabilityDay;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.dto.BookOnlineStaffTimeDTO;
import com.moego.server.grooming.dto.LimitDto;
import com.moego.server.grooming.dto.PetBreedLimitDto;
import com.moego.server.grooming.dto.PetSizeLimitDto;
import com.moego.server.grooming.dto.ServiceLimitDto;
import com.moego.server.grooming.service.dto.CapacityTimeslotDTO;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.springframework.util.CollectionUtils;

@UtilityClass
public class StaffTimeSyncUtil {

    public static BookOnlineStaffTimeDTO convertProtoStaffAvailabilityToDto(
            Integer businessId, StaffAvailability staffAvailability) {
        var staffTimeDto = new BookOnlineStaffTimeDTO();
        staffTimeDto.setBusinessId(businessId);
        staffTimeDto.setStaffId((int) staffAvailability.getStaffId());
        staffTimeDto.setStatus(CommonConstant.NORMAL);
        staffTimeDto.setStaffTimes(convertTimeProtoToDto(staffAvailability.getTimeAvailabilityDayListList()));
        staffTimeDto.setStaffSlots(convertSlotProtoToDto(staffAvailability.getSlotAvailabilityDayListList()));
        return staffTimeDto;
    }

    private static Map<String, StaffTime> convertTimeProtoToDto(List<TimeAvailabilityDay> timeDayList) {
        if (CollectionUtils.isEmpty(timeDayList)) {
            return Map.of();
        }
        Map<String, StaffTime> staffTimeMap = new HashMap<>();
        for (TimeAvailabilityDay timeAvailabilityDay : timeDayList) {
            var staffTime = new StaffTime();
            staffTime.setIsSelected(timeAvailabilityDay.getIsAvailable());
            staffTime.setTimeRange(timeAvailabilityDay.getTimeHourSettingListList().stream()
                    .map(timeHourSetting ->
                            new TimeRangeDto(timeHourSetting.getStartTime(), timeHourSetting.getEndTime()))
                    .toList());
            staffTime
                    .getLimitDto()
                    .setServiceLimitList(convertProtoServiceLimitationToDto(
                            timeAvailabilityDay.getTimeDailySetting().getLimit().getServiceLimitsList()));
            staffTime
                    .getLimitDto()
                    .setPetSizeLimitList(convertProtoPetSizeLimitationToDto(
                            timeAvailabilityDay.getTimeDailySetting().getLimit().getPetSizeLimitsList()));
            staffTime
                    .getLimitDto()
                    .setPetBreedLimitList(convertProtoPetBreedLimitationToDto(
                            timeAvailabilityDay.getTimeDailySetting().getLimit().getPetBreedLimitsList()));
            staffTimeMap.put(convertGoogleWeekTOString(timeAvailabilityDay.getDayOfWeek()), staffTime);
        }
        return staffTimeMap;
    }

    private static Map<String, BookOnlineStaffTimeDTO.StaffSlot> convertSlotProtoToDto(
            List<SlotAvailabilityDay> slotDayList) {
        if (CollectionUtils.isEmpty(slotDayList)) {
            return Map.of();
        }
        Map<String, BookOnlineStaffTimeDTO.StaffSlot> staffSlotMap = new HashMap<>();
        for (SlotAvailabilityDay slotAvailabilityDay : slotDayList) {
            var staffSlot = new BookOnlineStaffTimeDTO.StaffSlot();
            staffSlot.setIsSelected(slotAvailabilityDay.getIsAvailable());
            staffSlot.setTimeSlot(slotAvailabilityDay.getSlotHourSettingListList().stream()
                    .map(StaffTimeSyncUtil::convertProtoHourSettingToDto)
                    .toList());
            staffSlot.setDailyTimeSlot(
                    StaffTimeSyncUtil.convertDailySettingToInternalDto(slotAvailabilityDay.getSlotDailySetting()));
            staffSlotMap.put(convertGoogleWeekTOString(slotAvailabilityDay.getDayOfWeek()), staffSlot);
        }
        return staffSlotMap;
    }

    private static BookOnlineStaffTimeDTO.StaffSlot.CapacityTimeslot convertProtoHourSettingToDto(
            SlotHourSetting slotHourSetting) {
        var capacityTimeSlot = new BookOnlineStaffTimeDTO.StaffSlot.CapacityTimeslot();
        capacityTimeSlot.setStartTime(slotHourSetting.getStartTime());
        capacityTimeSlot.setCapacity(slotHourSetting.getCapacity());
        capacityTimeSlot
                .getLimitDto()
                .setServiceLimitList(convertProtoServiceLimitationToDto(
                        slotHourSetting.getLimit().getServiceLimitsList()));
        capacityTimeSlot
                .getLimitDto()
                .setPetSizeLimitList(convertProtoPetSizeLimitationToDto(
                        slotHourSetting.getLimit().getPetSizeLimitsList()));
        capacityTimeSlot
                .getLimitDto()
                .setPetBreedLimitList(convertProtoPetBreedLimitationToDto(
                        slotHourSetting.getLimit().getPetBreedLimitsList()));
        return capacityTimeSlot;
    }

    public static CapacityTimeslotDTO convertDailySettingToDto(SlotDailySetting slotDailySetting) {
        var capacityTimeSlot = new CapacityTimeslotDTO();
        capacityTimeSlot.setStartTime(slotDailySetting.getStartTime());
        capacityTimeSlot.setCapacity(slotDailySetting.getCapacity());
        capacityTimeSlot.setLimitDto(new LimitDto());
        capacityTimeSlot
                .getLimitDto()
                .setServiceLimitList(convertProtoServiceLimitationToDto(
                        slotDailySetting.getLimit().getServiceLimitsList()));
        capacityTimeSlot
                .getLimitDto()
                .setPetSizeLimitList(convertProtoPetSizeLimitationToDto(
                        slotDailySetting.getLimit().getPetSizeLimitsList()));
        capacityTimeSlot
                .getLimitDto()
                .setPetBreedLimitList(convertProtoPetBreedLimitationToDto(
                        slotDailySetting.getLimit().getPetBreedLimitsList()));
        capacityTimeSlot.setUsedAppointmentPetPairs(new HashSet<>());
        return capacityTimeSlot;
    }

    public static BookOnlineStaffTimeDTO.StaffSlot.CapacityTimeslot convertDailySettingToInternalDto(
            SlotDailySetting slotDailySetting) {
        var capacityTimeSlot = new BookOnlineStaffTimeDTO.StaffSlot.CapacityTimeslot();
        capacityTimeSlot.setStartTime(slotDailySetting.getStartTime());
        capacityTimeSlot.setCapacity(slotDailySetting.getCapacity());
        capacityTimeSlot.setLimitDto(new LimitDto());
        capacityTimeSlot
                .getLimitDto()
                .setServiceLimitList(convertProtoServiceLimitationToDto(
                        slotDailySetting.getLimit().getServiceLimitsList()));
        capacityTimeSlot
                .getLimitDto()
                .setPetSizeLimitList(convertProtoPetSizeLimitationToDto(
                        slotDailySetting.getLimit().getPetSizeLimitsList()));
        capacityTimeSlot
                .getLimitDto()
                .setPetBreedLimitList(convertProtoPetBreedLimitationToDto(
                        slotDailySetting.getLimit().getPetBreedLimitsList()));
        return capacityTimeSlot;
    }

    private static String convertGoogleWeekTOString(DayOfWeek input) {
        return DEFAULT_WEEK_MAP.entrySet().stream()
                .filter(entry -> input.equals(entry.getValue()))
                .findFirst()
                .map(Map.Entry::getKey)
                .orElse(null);
    }

    public static List<ServiceLimitDto> convertProtoServiceLimitationToDto(
            List<BookingLimitation.ServiceLimitation> serviceLimitationList) {
        if (CollectionUtils.isEmpty(serviceLimitationList)) {
            return List.of();
        }
        return serviceLimitationList.stream()
                .map(serviceLimitation -> new ServiceLimitDto(
                        serviceLimitation.getServiceIdsList(),
                        serviceLimitation.getIsAllService(),
                        serviceLimitation.getCapacity()))
                .collect(Collectors.toList());
    }

    public static List<PetSizeLimitDto> convertProtoPetSizeLimitationToDto(
            List<BookingLimitation.PetSizeLimitation> petSizeLimitationList) {
        if (CollectionUtils.isEmpty(petSizeLimitationList)) {
            return List.of();
        }
        return petSizeLimitationList.stream()
                .map(petBreedLimitation -> new PetSizeLimitDto(
                        petBreedLimitation.getPetSizeIdsList(),
                        petBreedLimitation.getIsAllSize(),
                        petBreedLimitation.getCapacity()))
                .collect(Collectors.toList());
    }

    public static List<PetBreedLimitDto> convertProtoPetBreedLimitationToDto(
            List<BookingLimitation.PetBreedLimitation> petBreedLimitationList) {
        if (CollectionUtils.isEmpty(petBreedLimitationList)) {
            return List.of();
        }
        return petBreedLimitationList.stream()
                .map(petBreedLimitation -> new PetBreedLimitDto(
                        petBreedLimitation.getPetTypeId(),
                        petBreedLimitation.getBreedIdsList(),
                        petBreedLimitation.getIsAllBreed(),
                        petBreedLimitation.getCapacity()))
                .collect(Collectors.toList());
    }
}
