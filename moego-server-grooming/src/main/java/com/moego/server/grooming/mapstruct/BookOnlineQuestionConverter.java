package com.moego.server.grooming.mapstruct;

import static com.moego.common.utils.CommonUtil.isNormal;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.models.online_booking.v1.AcceptPetEntryType;
import com.moego.idl.models.online_booking.v1.ExistingClientAccessMode;
import com.moego.idl.models.online_booking.v1.ExistingPetAccessMode;
import com.moego.idl.models.online_booking.v1.NewClientAccessMode;
import com.moego.idl.models.online_booking.v1.NewPetAccessMode;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.dto.GroomingQuestionDTO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion;
import com.moego.server.grooming.params.BookOnlineQuestionParams;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/2/11
 */
public final class BookOnlineQuestionConverter {

    private static final String PHONE_NUMBER = "Phone number";
    private static final String REFERRAL_SOURCE = "Referral source";
    private static final String PET_TYPE = "Pet type";
    private static final String PET_BREED = "Pet breed";

    public static GroomingQuestionDTO entityToDTO(MoeBookOnlineQuestion entity) {
        var dto = new GroomingQuestionDTO();
        dto.setBusinessId(entity.getBusinessId());
        dto.setId(entity.getId());
        dto.setQuestion(entity.getQuestion());
        dto.setPlaceholder(entity.getPlaceholder());
        dto.setIsShow(entity.getIsShow());
        dto.setIsRequired(entity.getIsRequired());
        dto.setType(entity.getType());
        dto.setIsAllowDelete(entity.getIsAllowDelete());
        dto.setIsAllowChange(entity.getIsAllowChange());
        dto.setIsAllowEdit(entity.getIsAllowEdit());
        dto.setSort(entity.getSort());
        dto.setStatus(entity.getStatus());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setQuestionType(entity.getQuestionType());
        dto.setExtraJson(entity.getExtraJson());
        dto.setAcceptCustomerType(getAcceptCustomerType(entity).getNumber());
        dto.setAcceptPetEntryType(getAcceptPetEntryType(entity).getNumber());
        dto.setNewPetAccessMode(entity.getNewPetAccessMode().getNumber());
        dto.setExistingPetAccessMode(entity.getExistingPetAccessMode().getNumber());
        dto.setNewClientAccessMode(entity.getNewClientAccessMode().getNumber());
        dto.setExistingClientAccessMode(entity.getExistingClientAccessMode().getNumber());
        return dto;
    }

    public static MoeBookOnlineQuestion paramToEntity(BookOnlineQuestionParams param) {
        var entity = new MoeBookOnlineQuestion();
        entity.setId(param.getId());
        entity.setBusinessId(param.getBusinessId());
        entity.setQuestion(param.getQuestion());
        entity.setPlaceholder(param.getPlaceholder());
        entity.setIsShow(param.getIsShow());
        entity.setIsRequired(param.getIsRequired());
        entity.setType(param.getType());
        entity.setIsAllowDelete(param.getIsAllowDelete());
        entity.setIsAllowChange(param.getIsAllowChange());
        entity.setIsAllowEdit(param.getIsAllowEdit());
        entity.setSort(param.getSort());
        entity.setStatus(param.getStatus());
        entity.setCreateTime(param.getCreateTime());
        entity.setUpdateTime(param.getUpdateTime());
        entity.setQuestionType(param.getQuestionType());
        entity.setCompanyId(param.getCompanyId());
        entity.setExtraJson(param.getExtraJson());
        if (isNormal(param.getAcceptCustomerType())) {
            entity.setNewClientAccessMode(getNewClientAccessMode(param.getAcceptCustomerType()));
            entity.setExistingClientAccessMode(
                    getExistingClientAccessMode(param.getAcceptCustomerType(), param.getQuestion()));
        }
        if (isNormal(param.getAcceptPetEntryType())) {
            entity.setNewPetAccessMode(getNewPetAccessMode(param.getAcceptPetEntryType()));
            entity.setExistingPetAccessMode(
                    getExistingPetAccessMode(param.getAcceptPetEntryType(), param.getQuestion()));
        }
        if (isNormal(param.getNewPetAccessMode())) {
            entity.setNewPetAccessMode(NewPetAccessMode.forNumber(param.getNewPetAccessMode()));
        }
        if (isNormal(param.getExistingPetAccessMode())) {
            entity.setExistingPetAccessMode(ExistingPetAccessMode.forNumber(param.getExistingPetAccessMode()));
        }
        if (isNormal(param.getNewClientAccessMode())) {
            entity.setNewClientAccessMode(NewClientAccessMode.forNumber(param.getNewClientAccessMode()));
        }
        if (isNormal(param.getExistingClientAccessMode())) {
            entity.setExistingClientAccessMode(ExistingClientAccessMode.forNumber(param.getExistingClientAccessMode()));
        }
        return entity;
    }

    private static ExistingClientAccessMode getExistingClientAccessMode(Integer acceptedCustomerType, String question) {
        return switch (acceptedCustomerType) {
            case AcceptCustomerType.NEW_CUSTOMER_VALUE -> ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_DISABLED;
            case AcceptCustomerType.EXISTING_CUSTOMER_VALUE,
                    AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE -> switch (question) {
                    // phone number/referral source are not allowed to edit
                case PHONE_NUMBER, REFERRAL_SOURCE -> ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW;
                default -> ExistingClientAccessMode.EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT;
            };
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Invalid accept customer type: " + acceptedCustomerType);
        };
    }

    private static NewClientAccessMode getNewClientAccessMode(Integer acceptedCustomerType) {
        return switch (acceptedCustomerType) {
            case AcceptCustomerType.NEW_CUSTOMER_VALUE,
                    AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE -> NewClientAccessMode
                    .NEW_CLIENT_ACCESS_MODE_ENABLED;
            case AcceptCustomerType.EXISTING_CUSTOMER_VALUE -> NewClientAccessMode.NEW_CLIENT_ACCESS_MODE_DISABLED;
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Invalid accept customer type: " + acceptedCustomerType);
        };
    }

    private static NewPetAccessMode getNewPetAccessMode(Integer acceptPetEntryType) {
        return switch (acceptPetEntryType) {
            case AcceptPetEntryType.NEW_VALUE, AcceptPetEntryType.NEW_AND_EXISTING_VALUE -> NewPetAccessMode
                    .NEW_PET_ACCESS_MODE_ENABLED;
            case AcceptPetEntryType.EXISTING_VALUE -> NewPetAccessMode.NEW_PET_ACCESS_MODE_DISABLED;
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Invalid accept pet entry type: " + acceptPetEntryType);
        };
    }

    private static ExistingPetAccessMode getExistingPetAccessMode(Integer acceptPetEntryType, String question) {
        return switch (acceptPetEntryType) {
            case AcceptPetEntryType.NEW_VALUE -> ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_DISABLED;
            case AcceptPetEntryType.EXISTING_VALUE, AcceptPetEntryType.NEW_AND_EXISTING_VALUE -> switch (question) {
                    // pet type/breed are not allowed to edit
                case PET_TYPE, PET_BREED -> ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW;
                default -> ExistingPetAccessMode.EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT;
            };
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Invalid accept pet entry type: " + acceptPetEntryType);
        };
    }

    private static AcceptCustomerType getAcceptCustomerType(MoeBookOnlineQuestion entity) {
        if (!Objects.equals(entity.getType(), GroomingQuestionDTO.Type.PET_OWNER)) {
            return AcceptCustomerType.ACCEPT_CUSTOMER_TYPE_UNSPECIFIED;
        }
        var newClientAccessMode = entity.getNewClientAccessMode();
        var existingClientAccessMode = entity.getExistingClientAccessMode();
        return switch (newClientAccessMode) {
            case NEW_CLIENT_ACCESS_MODE_ENABLED -> switch (existingClientAccessMode) {
                case EXISTING_CLIENT_ACCESS_MODE_VIEW, EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT -> AcceptCustomerType
                        .BOTH_EXISTING_AND_NEW_CUSTOMER;
                case EXISTING_CLIENT_ACCESS_MODE_DISABLED -> AcceptCustomerType.NEW_CUSTOMER;
                default -> throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "Invalid existing client access mode: " + existingClientAccessMode);
            };
            case NEW_CLIENT_ACCESS_MODE_DISABLED -> switch (existingClientAccessMode) {
                case EXISTING_CLIENT_ACCESS_MODE_VIEW, EXISTING_CLIENT_ACCESS_MODE_VIEW_AND_EDIT -> AcceptCustomerType
                        .EXISTING_CUSTOMER;
                case EXISTING_CLIENT_ACCESS_MODE_DISABLED -> throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "Invalid accept customer type");
                default -> throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "Invalid existing client access mode: " + existingClientAccessMode);
            };
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Invalid new client access mode: " + newClientAccessMode);
        };
    }

    private static AcceptPetEntryType getAcceptPetEntryType(MoeBookOnlineQuestion entity) {
        if (!Objects.equals(entity.getType(), GroomingQuestionDTO.Type.PET)) {
            return AcceptPetEntryType.ACCEPT_PET_ENTRY_TYPE_UNSPECIFIED;
        }
        var newPetAccessMode = entity.getNewPetAccessMode();
        var existingPetAccessMode = entity.getExistingPetAccessMode();
        return switch (newPetAccessMode) {
            case NEW_PET_ACCESS_MODE_ENABLED -> switch (existingPetAccessMode) {
                case EXISTING_PET_ACCESS_MODE_VIEW, EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT -> AcceptPetEntryType
                        .NEW_AND_EXISTING;
                case EXISTING_PET_ACCESS_MODE_DISABLED -> AcceptPetEntryType.NEW;
                default -> throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "Invalid existing pet access mode: " + existingPetAccessMode);
            };
            case NEW_PET_ACCESS_MODE_DISABLED -> switch (existingPetAccessMode) {
                case EXISTING_PET_ACCESS_MODE_VIEW, EXISTING_PET_ACCESS_MODE_VIEW_AND_EDIT -> AcceptPetEntryType
                        .EXISTING;
                case EXISTING_PET_ACCESS_MODE_DISABLED -> throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "Invalid accept pet entry type");
                default -> throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "Invalid existing pet access mode: " + existingPetAccessMode);
            };
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Invalid new pet access mode: " + newPetAccessMode);
        };
    }
}
