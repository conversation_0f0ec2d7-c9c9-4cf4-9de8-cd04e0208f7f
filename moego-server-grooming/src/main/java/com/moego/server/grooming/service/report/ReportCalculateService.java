package com.moego.server.grooming.service.report;

import static com.moego.server.grooming.constant.AppointmentStatusSet.ACTIVE_STATUS_SET;
import static com.moego.server.grooming.service.utils.ReportBeanUtil.buildInitEmployeeReport;
import static com.moego.server.grooming.service.utils.ReportUtil.calculateCollectedAmount;
import static com.moego.server.grooming.service.utils.ReportUtil.calculateDiscount;
import static com.moego.server.grooming.service.utils.ReportUtil.calculateTax;
import static com.moego.server.grooming.service.utils.ReportUtil.calculateTips;
import static com.moego.server.grooming.service.utils.ReportUtil.getProductAmount;
import static com.moego.server.grooming.service.utils.ReportUtil.getStaffProducts;
import static java.math.RoundingMode.HALF_EVEN;
import static java.math.RoundingMode.HALF_UP;
import static java.util.Optional.ofNullable;

import com.moego.common.dto.PaymentSummary;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.PaymentStatusEnum;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.common.utils.AmountUtils;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.PaymentUtil;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.server.customer.client.ICustomerReportClient;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.ReportWebSale;
import com.moego.server.grooming.dto.report.CustomerSpend;
import com.moego.server.grooming.dto.report.MobileSummaryDTO;
import com.moego.server.grooming.dto.report.ReportRecurringCustomerDTO;
import com.moego.server.grooming.dto.report.ReportWebEmployee;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.mapstruct.ReportBeanMapper;
import com.moego.server.grooming.service.AppointmentServiceDetailService;
import com.moego.server.grooming.service.OrderService;
import com.moego.server.grooming.service.SplitTipsService;
import com.moego.server.grooming.service.dto.EmployeeReportCalculateDTO;
import com.moego.server.grooming.service.dto.GroomingReportApptDetail;
import com.moego.server.grooming.service.dto.GroomingReportApptInvoice;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import com.moego.server.grooming.service.dto.StaffPaymentForReportDTO;
import com.moego.server.grooming.service.dto.report.AppointmentTipsSplitDetail;
import com.moego.server.grooming.service.dto.report.CollectedAmountCollection;
import com.moego.server.grooming.service.dto.report.CollectedPriceDTO;
import com.moego.server.grooming.service.dto.report.StaffRevenueDetail;
import com.moego.server.grooming.service.params.CalCollectedAmountParams;
import com.moego.server.grooming.service.params.CalCollectedPriceParams;
import com.moego.server.grooming.service.utils.AllotmentAmountUtil;
import com.moego.server.grooming.service.utils.ReportBeanUtil;
import com.moego.server.grooming.service.utils.ReportUtil;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.dto.InvoicePaymentDto;
import com.moego.server.payment.params.GetPaymentListParams;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReportCalculateService {

    private final OrderService orderService;
    private final SplitTipsService splitTipsService;
    private final IPaymentPaymentClient paymentService;
    private final AppointmentServiceDetailService serviceDetailService;
    private final ReportAppointmentService reportAppointmentService;
    private final ReportOrderService reportOrderService;
    private final PayrollCalculateService payrollCalculateService;
    private final ICustomerReportClient iCustomerReportClient;

    public void setRecurringClientSaleAndServices(
            ReportRecurringCustomerDTO resultDto,
            List<GroomingReportApptDetail> apptList,
            Integer customerId,
            String nowDate,
            Map<Integer, BigDecimal> refundMap) {
        // lastService  less than nowDate
        // nextService  bigger than nowDate
        apptList.forEach(appt -> {
            if (appt.getCustomerId().equals(customerId)) {
                appt.getInvoices().forEach(invoice -> {
                    BigDecimal paidAmount = invoice.getPaidAmount();
                    BigDecimal refundAmount = refundMap.getOrDefault(invoice.getInvoiceId(), BigDecimal.ZERO);
                    resultDto.setCollectedRevenue(
                            resultDto.getCollectedRevenue().add(paidAmount).subtract(refundAmount));
                    resultDto.setTotalPayment(resultDto.getTotalPayment().add(paidAmount));
                    resultDto.setTotalRefund(resultDto.getTotalRefund().add(refundAmount));
                });
                // find last date less than nowDate
                if (nowDate.compareTo(appt.getAppointmentDate()) > 0) {
                    resultDto.setLastApptId(appt.getId());
                    resultDto.setLastService(appt.getAppointmentDate());
                }
                // find first date bigger than nowDate
                if (resultDto.getNextService() == null && nowDate.compareTo(appt.getAppointmentDate()) <= 0) {
                    resultDto.setNextApptId(appt.getId());
                    resultDto.setNextService(appt.getAppointmentDate());
                }
            }
        });
    }

    public List<GroomingReportApptDetail> processAppointment(
            Integer businessId,
            String startDate,
            String endDate,
            MobileSummaryDTO.MobileSummaryDTOBuilder summaryBuilder) {
        List<GroomingReportApptDetail> appointments =
                reportAppointmentService.getReportApptByStartDateRange(businessId, startDate, endDate, true);
        reportAppointmentService.fillReportApptPetDetail(businessId, appointments, false, false);
        reportOrderService.fillReportApptInvoiceInfo(businessId, appointments, null, true);
        int finished = 0, confirmed = 0, unconfirmed = 0, cancelled = 0, noShow = 0;
        for (GroomingReportApptDetail appointment : appointments) {
            switch (AppointmentStatusEnum.fromValue(appointment.getStatus())) {
                case UNCONFIRMED -> unconfirmed++;
                case CONFIRMED, READY, CHECK_IN -> confirmed++;
                case FINISHED -> finished++;
                case CANCELED -> cancelled++;
                default -> log.warn(
                        "Invalid appointment status: {}, for appointment: {}",
                        appointment.getStatus(),
                        appointment.getId());
            }
            if (BooleanEnum.VALUE_TRUE.equals(appointment.getNoShow())) {
                noShow++;
            }
        }

        summaryBuilder
                .totalAppts(unconfirmed + confirmed + finished)
                .unconfirmedAppts(unconfirmed)
                .confirmedAppts(confirmed)
                .finishedAppts(finished)
                .cancelledAppts(cancelled)
                .noShowAppts(noShow);

        return appointments.stream()
                .filter(a -> ACTIVE_STATUS_SET.contains(AppointmentStatusEnum.fromValue(a.getStatus())))
                .collect(Collectors.toList());
    }

    public void processInvoice(
            List<GroomingReportApptDetail> appointments, MobileSummaryDTO.MobileSummaryDTOBuilder summaryBuilder) {
        if (CollectionUtils.isEmpty(appointments)) {
            return;
        }
        BigDecimal totalUnpaid = BigDecimal.ZERO;

        BigDecimal collectedRevenue = BigDecimal.ZERO;
        BigDecimal collectedServicePrice = BigDecimal.ZERO;
        BigDecimal collectedServiceChargePrice = BigDecimal.ZERO;
        BigDecimal collectedTips = BigDecimal.ZERO;
        BigDecimal collectedTax = BigDecimal.ZERO;
        BigDecimal collectedDiscount = BigDecimal.ZERO;

        BigDecimal expectedServicePrice = BigDecimal.ZERO;
        BigDecimal expectedServiceChargePrice = BigDecimal.ZERO;
        BigDecimal expectedTips = BigDecimal.ZERO;
        BigDecimal expectedTax = BigDecimal.ZERO;
        BigDecimal expectedDiscount = BigDecimal.ZERO;
        BigDecimal expectedRevenue = BigDecimal.ZERO; // expectedRevenue 总共的应收款，包括tips, tax, discount
        BigDecimal netSaleRevenue = BigDecimal.ZERO;

        // 查询refund记录
        Set<Integer> invoiceIds = appointments.stream()
                .map(a -> a.getInvoices().stream()
                        .map(GroomingReportApptInvoice::getInvoiceId)
                        .collect(Collectors.toSet()))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        Map<Integer, BigDecimal> refundMap = reportOrderService.getRefundMap(invoiceIds);
        // 统计总的 refund
        BigDecimal totalRefund = refundMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        for (GroomingReportApptDetail a : appointments) {
            List<GroomingReportApptInvoice> invoices = a.getInvoices();
            if (!CollectionUtils.isEmpty(invoices)) {
                for (GroomingReportApptInvoice invoice : invoices) {
                    BigDecimal discountAmount = invoice.getDiscountAmount();
                    BigDecimal tipsAmount = ofNullable(invoice.getTipsAmount()).orElse(BigDecimal.ZERO);
                    // taxAmount 数据库存的是4位小数，在计算前需要先约分成2位，tax 采用HALF_UP
                    BigDecimal taxAmount = ofNullable(invoice.getTaxAmount())
                            .orElse(BigDecimal.ZERO)
                            .setScale(2, HALF_UP);
                    BigDecimal paidAmount = ofNullable(invoice.getPaidAmount()).orElse(BigDecimal.ZERO);
                    BigDecimal subTotalAmount =
                            ofNullable(invoice.getSubTotalAmount()).orElse(BigDecimal.ZERO);
                    // collectedAmount = paid - refund - convenience fee
                    BigDecimal collectedAmount = AmountUtils.subtract(
                            paidAmount, refundMap.get(invoice.getInvoiceId()), invoice.getConvenienceFee());
                    BigDecimal totalAmountExcludeConFee =
                            AmountUtils.subtract(invoice.getTotalAmount(), invoice.getConvenienceFee());

                    // expected service/service charge price
                    expectedServicePrice = AmountUtils.sum(expectedServicePrice, invoice.getTotalServicePrice());
                    expectedServiceChargePrice =
                            AmountUtils.sum(expectedServiceChargePrice, invoice.getTotalServiceChargePrice());
                    // expected revenue = subTotal - discount + tips + tax
                    expectedRevenue = expectedRevenue
                            .add(subTotalAmount)
                            .subtract(discountAmount)
                            .add(tipsAmount)
                            .add(taxAmount);
                    totalUnpaid = totalUnpaid.add(invoice.getRemainAmount());

                    expectedTips = expectedTips.add(tipsAmount);
                    expectedTax = expectedTax.add(taxAmount);
                    expectedDiscount = expectedDiscount.add(discountAmount);

                    // 累计 collectedRevenue
                    collectedRevenue = collectedRevenue.add(collectedAmount);
                    // 已支付时才分配
                    if (!GroomingAppointmentEnum.NOT_PAY.equals(a.getIsPaid())) {
                        BigDecimal serviceCollectedAmount = !AmountUtils.isNullOrZero(totalAmountExcludeConFee)
                                ? collectedAmount
                                        .multiply(invoice.getTotalServiceSale())
                                        .divide(totalAmountExcludeConFee, 2, HALF_UP)
                                : BigDecimal.ZERO;
                        BigDecimal serviceChargeCollectedAmount = !AmountUtils.isNullOrZero(totalAmountExcludeConFee)
                                ? collectedAmount
                                        .multiply(invoice.getTotalServiceChargeSale())
                                        .divide(totalAmountExcludeConFee, 2, HALF_UP)
                                : BigDecimal.ZERO;
                        // 计算 collected service/service charge price
                        CollectedAmountCollection serviceChargeCollected =
                                calculateCollectedAmount(new CalCollectedAmountParams(
                                        serviceChargeCollectedAmount,
                                        invoice.getServiceChargeTaxAmount(),
                                        BigDecimal.ZERO,
                                        invoice.getServiceChargeDiscountAmount()));
                        CollectedAmountCollection serviceCollected =
                                calculateCollectedAmount(new CalCollectedAmountParams(
                                        serviceCollectedAmount,
                                        invoice.getServiceTaxAmount(),
                                        tipsAmount,
                                        invoice.getServiceDiscountAmount()));

                        collectedServiceChargePrice =
                                AmountUtils.sum(collectedServiceChargePrice, serviceChargeCollected.itemPrice());
                        collectedServicePrice = AmountUtils.sum(collectedServicePrice, serviceCollected.itemPrice());
                        collectedTips = AmountUtils.sum(collectedTips, serviceCollected.tips()); // tips 只有 service 有
                        collectedTax =
                                AmountUtils.sum(collectedTax, serviceCollected.tax(), serviceChargeCollected.tax());
                        collectedDiscount = AmountUtils.sum(
                                collectedDiscount, serviceCollected.discount(), serviceChargeCollected.discount());
                        netSaleRevenue = AmountUtils.sum(
                                netSaleRevenue,
                                serviceCollected.netSaleRevenue(),
                                serviceChargeCollected.netSaleRevenue());
                    }
                }
            }
        }

        summaryBuilder
                .expectedRevenue(expectedRevenue.setScale(2, HALF_UP))
                .earnedRevenue(collectedRevenue.setScale(2, HALF_UP))
                .netSaleRevenue(netSaleRevenue.setScale(2, HALF_UP))
                .totalRefund(totalRefund.setScale(2, HALF_UP))
                .earnedNoTipTax(collectedServicePrice.setScale(2, HALF_UP))
                .earnedServiceCharge(collectedServiceChargePrice.setScale(2, HALF_UP))
                .expectedNoTipTax(expectedServicePrice.setScale(2, HALF_UP))
                .expectedServiceCharge(expectedServiceChargePrice.setScale(2, HALF_UP))
                .totalUnpaid(totalUnpaid.setScale(2, HALF_UP))
                .earnedTax(collectedTax.setScale(2, HALF_UP))
                .earnedTips(collectedTips.setScale(2, HALF_UP))
                .earnedDiscount(collectedDiscount.setScale(2, HALF_UP))
                .expectedTips(expectedTips.setScale(2, HALF_UP))
                .expectedTax(expectedTax.setScale(2, HALF_UP))
                .expectedDiscount(expectedDiscount.setScale(2, HALF_UP));
    }

    public void processPetAndClientId(
            List<GroomingReportApptDetail> appointments, MobileSummaryDTO.MobileSummaryDTOBuilder summaryDTOBuilder) {
        if (CollectionUtils.isEmpty(appointments)) {
            return;
        }
        Map<Integer, Set<Integer>> apptToPet = new HashMap<>();
        Set<String> finishPetSet = new HashSet<>(); // 统计已完成的pet数，根据apptId + petId 去重
        appointments.forEach(a -> {
            Integer groomId = a.getId();
            Set<Integer> petSet = apptToPet.getOrDefault(groomId, new HashSet<>());
            List<ReportWebApptPetDetail> petDetails = a.getPetDetails();
            if (!CollectionUtils.isEmpty(petDetails)) {
                petDetails.forEach(p -> {
                    String petItem = groomId + "|" + p.getPetId();
                    petSet.add(p.getPetId());
                    if (AppointmentStatusEnum.FINISHED.getValue().equals(a.getStatus())) {
                        finishPetSet.add(petItem);
                    }
                });
            }
            if (!CollectionUtils.isEmpty(a.getEvaluationDetails())) {
                a.getEvaluationDetails().forEach(p -> {
                    String petItem = groomId + "|" + p.getPetId();
                    petSet.add(p.getPetId().intValue());
                    if (AppointmentStatusEnum.FINISHED.getValue().equals(a.getStatus())) {
                        finishPetSet.add(petItem);
                    }
                });
            }
            apptToPet.put(groomId, petSet);
        });

        List<Integer> petIds = new ArrayList<>();
        apptToPet.values().forEach(petIds::addAll);

        List<Integer> customerIds =
                appointments.stream().map(a -> a.getCustomerId()).distinct().collect(Collectors.toList());

        summaryDTOBuilder.petIds(petIds).customerIds(customerIds).finishedPets(finishPetSet.size());
    }

    /**
     * 将payment金额、refund金额按每个service应收收入在总的应收收入中占的比例分配到staff
     * 计算出每个pet detail的实际收入，用paid金额分配到tax、tips、discount
     * service应收收入 = expectedServicePrice + expectedTips + expectedTax - expectedDiscount
     * 计算公式：
     * 实收金额collectedPayment = paidAmount * service应收 / total应收
     * 实际退款collectedRefund = refundAmount * service应收 / total应收
     *
     * @param staffId 员工id
     * @param invoiceApptMap invoiceId 对 appt invoice 映射
     * @param paymentDtoList 支付记录列表
     * @param refundMap invoiceId 对 refund 映射
     * @return
     */
    public Map<String, StaffPaymentForReportDTO> distributeStaffPayment(
            Integer businessId,
            Integer staffId,
            Map<Integer, String> invoiceIdTypeMap,
            Map<Integer, GroomingReportApptDetail> invoiceApptMap,
            List<InvoicePaymentDto> paymentDtoList,
            Map<Integer, BigDecimal> refundMap) {
        Map<String, StaffPaymentForReportDTO> resultMap = new HashMap<>();
        // invoiceId - amountCount(processed)
        Map<Integer, BigDecimal> invoiceTipsMap = new HashMap<>();
        Map<Integer, BigDecimal> invoiceServiceTaxMap = new HashMap<>();
        Map<Integer, BigDecimal> invoiceServiceDiscountMap = new HashMap<>();
        Map<Integer, BigDecimal> invoiceProductTaxMap = new HashMap<>();
        Map<Integer, BigDecimal> invoiceProductDiscountMap = new HashMap<>();
        // invoiceId - paymentMethodCount
        Map<Integer, Long> paymentMethodCountMap = paymentDtoList.stream()
                .collect(Collectors.groupingBy(InvoicePaymentDto::getInvoiceId, Collectors.counting()));
        // 获取所有invoice的items
        List<MoeGroomingInvoiceItem> invoiceItems =
                orderService.getInvoiceItemByInvoiceIds(businessId, new ArrayList<>(invoiceApptMap.keySet()));
        Map<Integer, List<MoeGroomingInvoiceItem>> invoiceItemMap =
                invoiceItems.stream().collect(Collectors.groupingBy(MoeGroomingInvoiceItem::getInvoiceId));

        // 获取订单tip split记录
        Map<Integer, Map<Integer, BigDecimal>> tipSplitMap = getTipSplitMap(businessId, invoiceApptMap);

        Iterator<InvoicePaymentDto> iterator = paymentDtoList.iterator();
        while (iterator.hasNext()) {
            InvoicePaymentDto paymentDto = iterator.next();
            // 根据invoice获取预约
            GroomingReportApptDetail apt = invoiceApptMap.get(paymentDto.getInvoiceId());
            if (apt == null) {
                // 如果没有在预约中有对应的记录，则不进行统计并删除对应支付记录
                iterator.remove();
                continue;
            }
            List<ReportWebApptPetDetail> pds = apt.getPetDetails();
            if (CollectionUtils.isEmpty(pds)) {
                continue;
            }
            // 总的服务价格
            BigDecimal totalServiceAmount =
                    pds.stream().map(ReportWebApptPetDetail::getServicePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            // appt 下有多个 invoice 时，计算总的tips，tax和discount
            BigDecimal serviceDiscountAmount = BigDecimal.ZERO;
            BigDecimal productDiscountAmount = BigDecimal.ZERO;
            BigDecimal tipsAmount = BigDecimal.ZERO;
            BigDecimal serviceTaxAmount = BigDecimal.ZERO;
            BigDecimal productTaxAmount = BigDecimal.ZERO;
            BigDecimal paidAmount = BigDecimal.ZERO;
            // service, product 的收入金额，包含tax、discount
            BigDecimal totalServiceSale = BigDecimal.ZERO;
            BigDecimal totalProductSale = BigDecimal.ZERO;
            List<GroomingReportApptInvoice> invoices = apt.getInvoices();
            if (!CollectionUtils.isEmpty(invoices)) {
                BigDecimal totalAmount = BigDecimal.ZERO; // 临时字段
                for (GroomingReportApptInvoice i : invoices) {
                    // 排除 noshowfee 的 invoice
                    if (!InvoiceStatusEnum.TYPE_NOSHOW.equals(i.getInvoiceType())) {
                        serviceDiscountAmount = serviceDiscountAmount.add(i.getServiceDiscountAmount());
                        productDiscountAmount = productDiscountAmount.add(i.getProductDiscountAmount());
                        tipsAmount = tipsAmount.add(i.getTipsAmount());
                        // taxAmount历史数据存了4位小数，需要对金额先进行约分，银行家算法
                        serviceTaxAmount =
                                serviceTaxAmount.add(i.getServiceTaxAmount().setScale(2, HALF_UP));
                        productTaxAmount =
                                productTaxAmount.add(i.getProductTaxAmount().setScale(2, HALF_UP));
                        paidAmount = paidAmount.add(i.getPaidAmount());
                        totalAmount = AmountUtils.sum(totalAmount, i.getTotalAmount());
                        totalProductSale = AmountUtils.sum(
                                totalProductSale, getProductAmount(invoiceItemMap.get(i.getInvoiceId())));
                    }
                }
                totalServiceSale = AmountUtils.subtract(totalAmount, totalProductSale);
            }
            // accumulate the same payment method amount
            String invoiceType = invoiceIdTypeMap.get(paymentDto.getInvoiceId());
            if (!Objects.equals(apt.getIsPaid(), GroomingAppointmentEnum.NOT_PAY)
                    && !Objects.equals(invoiceType, InvoiceStatusEnum.TYPE_NOSHOW)
                    && paidAmount.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal tmpTipsAmount;
                BigDecimal tmpServiceTaxAmount;
                BigDecimal tmpServiceDiscountAmount;
                BigDecimal tmpProductTaxAmount;
                BigDecimal tmpProductDiscountAmount;
                // The invoice's last payment method to find out the remaining amount
                boolean isInvoiceLastPaymentMethod = paymentMethodCountMap.get(paymentDto.getInvoiceId()) == 1
                        && invoiceTipsMap.containsKey(paymentDto.getInvoiceId());
                if (isInvoiceLastPaymentMethod) {
                    tmpTipsAmount = tipsAmount.subtract(
                            invoiceTipsMap.getOrDefault(paymentDto.getInvoiceId(), BigDecimal.ZERO));
                    tmpServiceTaxAmount = serviceTaxAmount.subtract(
                            invoiceServiceTaxMap.getOrDefault(paymentDto.getInvoiceId(), BigDecimal.ZERO));
                    tmpServiceDiscountAmount = serviceDiscountAmount.subtract(
                            invoiceServiceDiscountMap.getOrDefault(paymentDto.getInvoiceId(), BigDecimal.ZERO));

                    tmpProductTaxAmount = productTaxAmount.subtract(
                            invoiceProductTaxMap.getOrDefault(paymentDto.getInvoiceId(), BigDecimal.ZERO));
                    tmpProductDiscountAmount = productDiscountAmount.subtract(
                            invoiceProductDiscountMap.getOrDefault(paymentDto.getInvoiceId(), BigDecimal.ZERO));
                } else {
                    // service tips、tax、discount
                    tmpTipsAmount = tipsAmount.multiply(paymentDto.getAmount()).divide(paidAmount, 2, HALF_EVEN);
                    invoiceTipsMap.put(
                            paymentDto.getInvoiceId(),
                            invoiceTipsMap
                                    .getOrDefault(paymentDto.getInvoiceId(), BigDecimal.ZERO)
                                    .add(tmpTipsAmount));
                    tmpServiceTaxAmount =
                            serviceTaxAmount.multiply(paymentDto.getAmount()).divide(paidAmount, 2, HALF_UP);
                    invoiceServiceTaxMap.put(
                            paymentDto.getInvoiceId(),
                            invoiceServiceTaxMap
                                    .getOrDefault(paymentDto.getInvoiceId(), BigDecimal.ZERO)
                                    .add(tmpServiceTaxAmount));
                    tmpServiceDiscountAmount = serviceDiscountAmount
                            .multiply(paymentDto.getAmount())
                            .divide(paidAmount, 2, HALF_EVEN);
                    invoiceServiceDiscountMap.put(
                            paymentDto.getInvoiceId(),
                            invoiceServiceDiscountMap
                                    .getOrDefault(paymentDto.getInvoiceId(), BigDecimal.ZERO)
                                    .add(tmpServiceDiscountAmount));
                    // product tax、discount
                    tmpProductTaxAmount =
                            productTaxAmount.multiply(paymentDto.getAmount()).divide(paidAmount, 2, HALF_UP);
                    invoiceProductTaxMap.put(
                            paymentDto.getInvoiceId(),
                            invoiceProductTaxMap
                                    .getOrDefault(paymentDto.getInvoiceId(), BigDecimal.ZERO)
                                    .add(tmpProductTaxAmount));
                    tmpProductDiscountAmount = productDiscountAmount
                            .multiply(paymentDto.getAmount())
                            .divide(paidAmount, 2, HALF_EVEN);
                    invoiceProductDiscountMap.put(
                            paymentDto.getInvoiceId(),
                            invoiceProductDiscountMap
                                    .getOrDefault(paymentDto.getInvoiceId(), BigDecimal.ZERO)
                                    .add(tmpProductDiscountAmount));

                    paymentMethodCountMap.put(
                            paymentDto.getInvoiceId(), paymentMethodCountMap.get(paymentDto.getInvoiceId()) - 1);
                }
                CalCollectedPriceParams params = new CalCollectedPriceParams()
                        .setPaidAmount(paymentDto.getAmount())
                        .setRefundAmount(refundMap.getOrDefault(paymentDto.getPaymentId(), BigDecimal.ZERO))
                        .setExpectedServiceTax(tmpServiceTaxAmount)
                        .setExpectedProductTax(tmpProductTaxAmount)
                        .setExpectedTips(tmpTipsAmount)
                        .setServiceDiscountAmount(tmpServiceDiscountAmount)
                        .setProductDiscountAmount(tmpProductDiscountAmount)
                        .setTotalServiceSale(totalServiceSale)
                        .setTotalProductSale(totalProductSale);
                CollectedPriceDTO collectedDTO = calculateCollectedPrice(params);
                StaffPaymentForReportDTO reportDTO = resultMap.computeIfAbsent(
                        paymentDto.getMethod(), p -> StaffPaymentForReportDTO.buildInitializedBean());
                reportDTO.setCollectedServicePrice(
                        AmountUtils.sum(reportDTO.getCollectedServicePrice(), collectedDTO.getCollectedServicePrice()));
                reportDTO.setCollectedProductPrice(
                        AmountUtils.sum(reportDTO.getCollectedProductPrice(), collectedDTO.getCollectedProductPrice()));
                reportDTO.setNetSaleRevenue(
                        AmountUtils.sum(reportDTO.getNetSaleRevenue(), collectedDTO.getNetSaleRevenue()));
                reportDTO.setCollectedTips(
                        AmountUtils.sum(reportDTO.getCollectedTips(), collectedDTO.getCollectedTips()));
                reportDTO.setCollectedTax(AmountUtils.sum(
                        reportDTO.getCollectedTax(),
                        collectedDTO.getCollectedServiceTax(),
                        collectedDTO.getCollectedProductTax()));
                reportDTO.setDiscount(AmountUtils.sum(
                        reportDTO.getDiscount(),
                        collectedDTO.getServiceDiscountAmount(),
                        collectedDTO.getProductDiscountAmount()));
            }
            // 计算tips,discount,tax
            Map<Integer, Map<Integer, BigDecimal>> tipsMap = calculateTips(
                    tipSplitMap.getOrDefault(paymentDto.getInvoiceId(), Collections.emptyMap()), apt.getPetDetails());
            Map<Integer, BigDecimal> discountMap =
                    calculateDiscount(apt.getPetDetails(), invoiceItemMap.get(paymentDto.getInvoiceId()));
            Map<Integer, BigDecimal> taxMap =
                    calculateTax(apt.getPetDetails(), invoiceItemMap.get(paymentDto.getInvoiceId()));

            BigDecimal totalRevenue =
                    totalServiceAmount.add(tipsAmount).add(serviceTaxAmount).subtract(serviceDiscountAmount);

            BigDecimal paymentAmount = paymentDto.getAmount();
            if (paymentAmount == null) {
                paymentAmount = BigDecimal.ZERO;
            }
            BigDecimal totalRefund = refundMap.getOrDefault(paymentDto.getPaymentId(), BigDecimal.ZERO);
            BigDecimal tempTotalPayment = BigDecimal.ZERO;
            BigDecimal tempTotalRefund = BigDecimal.ZERO;

            for (int i = 0; i < pds.size(); i++) {
                ReportWebApptPetDetail petDetail = apt.getPetDetails().get(i);
                // tips, discount: 按service价格占比分配, tax: 计算tax和创建invoice时保持一致：保留两位小数，使用HALF_UP取舍小数
                BigDecimal tips = tipsMap.getOrDefault(petDetail.getId(), new HashMap<>()).values().stream()
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal discount = discountMap.getOrDefault(petDetail.getId(), BigDecimal.ZERO);
                BigDecimal tax = taxMap.getOrDefault(petDetail.getId(), BigDecimal.ZERO);

                // 服务的实际金额，包括tips，tax和discount
                BigDecimal curServiceRevenue =
                        petDetail.getServicePrice().add(tips).add(tax).subtract(discount);
                BigDecimal paymentRevenue, refundAmount;

                // 一个预约有多个员工时，根据员工每个服务的实际金额占比分配
                if (totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
                    if (i == pds.size() - 1) {
                        paymentRevenue = paymentAmount.subtract(tempTotalPayment);
                        refundAmount = totalRefund.subtract(tempTotalRefund);
                    } else {
                        paymentRevenue =
                                curServiceRevenue.multiply(paymentAmount).divide(totalRevenue, 2, HALF_UP);
                        tempTotalPayment = tempTotalPayment.add(paymentRevenue);
                        // refund也按比例分配
                        refundAmount = curServiceRevenue.multiply(totalRefund).divide(totalRevenue, 2, HALF_UP);
                        tempTotalRefund = tempTotalRefund.add(refundAmount);
                    }
                } else {
                    if (i == pds.size() - 1) {
                        paymentRevenue = paymentAmount.subtract(tempTotalPayment);
                        refundAmount = totalRefund.subtract(tempTotalRefund);
                    } else {
                        // totalRevenue 为 0 时，平分到每个 pet_detail
                        paymentRevenue = paymentAmount.divide(BigDecimal.valueOf(pds.size()), 2, HALF_UP);
                        tempTotalPayment = tempTotalPayment.add(paymentRevenue);
                        refundAmount = totalRefund.divide(BigDecimal.valueOf(pds.size()), 2, HALF_UP);
                        tempTotalRefund = tempTotalRefund.add(refundAmount);
                    }
                }
                if (staffId != null && !Objects.equals(staffId, petDetail.getStaffId())) {
                    continue;
                }
                // 赋值
                StaffPaymentForReportDTO reportDTO = resultMap.computeIfAbsent(
                        paymentDto.getMethod(), p -> StaffPaymentForReportDTO.buildInitializedBean());
                reportDTO.setTotalPayment(reportDTO.getTotalPayment().add(paymentRevenue));
                reportDTO.setTotalRefund(reportDTO.getTotalRefund().add(refundAmount));
                if (InvoiceStatusEnum.TYPE_NOSHOW.equals(invoiceType)) {
                    reportDTO.setNoShowPayment(reportDTO.getNoShowPayment().add(paymentRevenue));
                    reportDTO.setNoShowRefund(reportDTO.getNoShowRefund().add(refundAmount));
                } else if (InvoiceStatusEnum.TYPE_APPOINTMENT.equals(invoiceType)) {
                    reportDTO.setAptPayment(reportDTO.getAptPayment().add(paymentRevenue));
                    reportDTO.setAptRefund(reportDTO.getAptRefund().add(refundAmount));
                }
            }
        }
        return resultMap;
    }

    public Map<Integer, Map<Integer, BigDecimal>> getTipSplitMapV2(
            Integer businessId, List<GroomingReportWebAppointment> appointments) {
        var appointmentsWithInvoiceId = appointments.stream()
                .filter(a -> CommonUtil.isNormal(a.getInvoiceId()))
                .toList();
        Map<Integer, List<ReportWebApptPetDetail>> petDetailMap = appointments.stream()
                .filter(appointment -> !CollectionUtils.isEmpty(appointment.getInvoiceIdToPetDetailsMap()))
                .flatMap(appointment -> appointment.getInvoiceIdToPetDetailsMap().entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        Map<Integer, List<EvaluationServiceDetailDTO>> evaluationServiceDetailMap = appointmentsWithInvoiceId.stream()
                .collect(Collectors.toMap(
                        GroomingReportWebAppointment::getInvoiceId,
                        k -> CollectionUtils.isEmpty(k.getEvaluationDetails())
                                ? new ArrayList<>()
                                : k.getEvaluationDetails()));
        Map<Integer, BigDecimal> tipsMap = appointmentsWithInvoiceId.stream()
                .collect(Collectors.toMap(
                        GroomingReportWebAppointment::getInvoiceId, GroomingReportWebAppointment::getTipsAmount));
        Map<Integer, BigDecimal> tipsBaseAmountMap = appointmentsWithInvoiceId.stream()
                .collect(Collectors.toMap(
                        GroomingReportWebAppointment::getInvoiceId, GroomingReportWebAppointment::getTipsBaseAmount));
        // 查询tip split记录
        return splitTipsService.getOrdersTipSplitMap(
                businessId, tipsMap, tipsBaseAmountMap, petDetailMap, evaluationServiceDetailMap);
    }

    public Map<Integer, Map<Integer, BigDecimal>> getTipSplitMap(
            Integer businessId, List<GroomingReportWebAppointment> appointments) {
        var appointmentsWithInvoiceId = appointments.stream()
                .filter(a -> CommonUtil.isNormal(a.getInvoiceId()))
                .toList();
        // 增加判空
        if (businessId == null || CollectionUtils.isEmpty(appointmentsWithInvoiceId)) {
            return Collections.emptyMap();
        }

        Map<Integer, List<ReportWebApptPetDetail>> petDetailMap = appointmentsWithInvoiceId.stream()
                .collect(Collectors.toMap(
                        GroomingReportWebAppointment::getInvoiceId, GroomingReportWebAppointment::getPetDetails));
        Map<Integer, List<EvaluationServiceDetailDTO>> evaluationServiceDetailMap = appointmentsWithInvoiceId.stream()
                .collect(Collectors.toMap(GroomingReportWebAppointment::getInvoiceId, k -> {
                    if (k.getEvaluationDetails() == null) {
                        return new ArrayList<>();
                    } else {
                        return k.getEvaluationDetails();
                    }
                }));
        Map<Integer, BigDecimal> tipsMap = appointmentsWithInvoiceId.stream()
                .collect(Collectors.toMap(
                        GroomingReportWebAppointment::getInvoiceId, GroomingReportWebAppointment::getTipsAmount));
        Map<Integer, BigDecimal> tipsBaseAmountMap = appointmentsWithInvoiceId.stream()
                .collect(Collectors.toMap(
                        GroomingReportWebAppointment::getInvoiceId, GroomingReportWebAppointment::getTipsBaseAmount));
        // 查询tip split记录
        return splitTipsService.getOrdersTipSplitMap(
                businessId, tipsMap, tipsBaseAmountMap, petDetailMap, evaluationServiceDetailMap);
    }

    /**
     * 查询订单的tip split记录
     *
     * @param businessId
     * @param invoiceApptMap
     * @return
     */
    public Map<Integer, Map<Integer, BigDecimal>> getTipSplitMap(
            Integer businessId, Map<Integer, GroomingReportApptDetail> invoiceApptMap) {
        Map<Integer, List<ReportWebApptPetDetail>> petDetailMap = new HashMap<>();
        Map<Integer, List<EvaluationServiceDetailDTO>> evaluationServiceDetailMap = new HashMap<>();
        Map<Integer, BigDecimal> tipsMap = new HashMap<>();
        Map<Integer, BigDecimal> tipsBaseAmountMap = new HashMap<>();
        invoiceApptMap.forEach((invoiceId, appt) -> {
            Optional<GroomingReportApptInvoice> optionalInvoice = appt.getInvoices().stream()
                    .filter(i -> Objects.equals(invoiceId, i.getInvoiceId()))
                    .findFirst();
            // appointment类型的订单才有tips
            if (optionalInvoice.isPresent()
                    && optionalInvoice.get().getInvoiceType().equals(OrderSourceType.APPOINTMENT.getSource())) {
                petDetailMap.put(invoiceId, appt.getPetDetails());
                evaluationServiceDetailMap.put(invoiceId, appt.getEvaluationDetails());
                tipsMap.put(invoiceId, optionalInvoice.get().getTipsAmount());
                tipsBaseAmountMap.put(invoiceId, optionalInvoice.get().getTipsAmount());
            }
        });

        // 查询tip split记录
        return splitTipsService.getOrdersTipSplitMap(
                businessId, tipsMap, tipsBaseAmountMap, petDetailMap, evaluationServiceDetailMap);
    }

    /**
     * 处理单个预约中每个 item 的金额
     * 2022-03-24 实收金额加入refund的计算
     * 2023-01-04 修改tipsMap计算方式，加入Split tips
     * 2023-01-13 V2增加Product相关金额的计算
     * 2023-02-01 修改Commission计算，Staff Commission V2(Fixed rate, Tier rate, Payroll exception)
     * 2023-03-27 去掉 excludeFee 入参，默认都减去 convenience fee
     *
     * @param apt appt详情
     * @param idToStaff employee report map
     * @param invoiceItems invoice item列表
     */
    public void dealApptMoneyReportV2(
            GroomingReportWebAppointment apt,
            Map<Integer, ReportWebEmployee> idToStaff,
            List<MoeGroomingInvoiceItem> invoiceItems,
            Map<Integer, BigDecimal> staffTipMap) {
        EmployeeReportCalculateDTO calculateDTO = ReportUtil.allocateAmount(apt, invoiceItems, staffTipMap);
        fillStaffAmount(calculateDTO, apt);
        // 计算 service 金额
        calculateServiceItem(
                idToStaff,
                apt.getPetDetails().stream()
                        .filter(k -> CommonUtil.isNormal(k.getStaffId()))
                        .toList(),
                calculateDTO);
        // 计算 product 金额
        invoiceItems.stream()
                .filter(item -> Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_PRODUCT.getType())
                        && !PrimitiveTypeUtil.isNullOrZero(item.getStaffId()))
                .forEach(item -> {
                    // product item 可以指定任意 staff，因此这里的 staff 可能在前面没有初始化过，这里单独初始化一下
                    ReportWebEmployee employee = idToStaff.computeIfAbsent(
                            item.getStaffId(),
                            staff -> buildInitEmployeeReport(
                                    item.getStaffId(),
                                    apt,
                                    Collections.emptySet(),
                                    getStaffProducts(item.getStaffId(), invoiceItems)));
                    calculateProductItem(employee, item, calculateDTO);
                });
    }

    public List<StaffRevenueDetail> calculateAppointments(
            Integer businessId, List<GroomingReportWebAppointment> appointments) {
        // 查询新的 appt tips split 记录
        var apptTipSplitMap = splitTipsService.getAppointmentTipsSplitMap(appointments.stream()
                .map(GroomingReportWebAppointment::getId)
                .distinct()
                .collect(Collectors.toList()));
        // 查询旧的 order tip split 记录
        var orderTipSplitMap = getTipSplitMapV2(businessId, appointments);
        List<StaffRevenueDetail> staffRevenueDetails = new ArrayList<>();
        for (GroomingReportWebAppointment appt : appointments) {
            Map<Integer, BigDecimal> staffTipSplit = Collections.emptyMap();
            // 有新的 appt split 分配记录时，忽略旧表分配记录
            if (!apptTipSplitMap.containsKey(appt.getId()) && CommonUtil.isNormal(appt.getInvoiceId())) {
                staffTipSplit = orderTipSplitMap.get(appt.getInvoiceId());
            }
            var apptRevenueDetails = calculateAppointmentAmount(appt, staffTipSplit);
            // 把新的 tips 记录单独算入 staff revenue
            fillUpTipsAmount(apptRevenueDetails, apptTipSplitMap.get(appt.getId()));
            staffRevenueDetails.addAll(ReportBeanUtil.mergeRevenueDetails(apptRevenueDetails));
        }
        // commission计算
        payrollCalculateService.processStaffCommission(businessId, staffRevenueDetails);
        return staffRevenueDetails;
    }

    public void fillUpTipsAmount(
            List<StaffRevenueDetail> staffRevenueDetails, AppointmentTipsSplitDetail tipsSplitDetail) {
        if (CollectionUtils.isEmpty(staffRevenueDetails) || Objects.isNull(tipsSplitDetail)) {
            return;
        }
        var staffRevenueMap = staffRevenueDetails.stream()
                .filter(d -> tipsSplitDetail.collectedOrderIds().contains((long) d.getInvoiceId()))
                .collect(Collectors.groupingBy(StaffRevenueDetail::getStaffId));
        var tipSplitMap = tipsSplitDetail.staffTipsMap();
        for (var staffRevenues : staffRevenueMap.entrySet()) {
            if (!tipSplitMap.containsKey(staffRevenues.getKey())) {
                continue;
            }
            var staffTotalTips = tipSplitMap.get(staffRevenues.getKey());
            var totalServicePrice = staffRevenues.getValue().stream()
                    .map(StaffRevenueDetail::getServicePrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            var tempTotalTips = BigDecimal.ZERO;
            for (int i = 0, size = staffRevenues.getValue().size(); i < size; i++) {
                var staffRevenue = staffRevenues.getValue().get(i);
                if (i == size - 1) {
                    staffRevenue.setTotalTips(staffTotalTips.subtract(tempTotalTips));
                } else {
                    BigDecimal tips;
                    if (totalServicePrice.compareTo(BigDecimal.ZERO) <= 0) {
                        // totalServicePrice = 0 时，平分 tips
                        tips = staffTotalTips.divide(BigDecimal.valueOf(size), 2, HALF_UP);
                    } else { // 按 service 比例分配
                        tips = staffTotalTips
                                .multiply(staffRevenue.getServicePrice())
                                .divide(totalServicePrice, 2, HALF_UP);
                    }
                    staffRevenue.setTotalTips(tips);
                    tempTotalTips = tempTotalTips.add(tips);
                }
                staffRevenue.setCollectedTips(staffRevenue.getTotalTips());
                staffRevenue.setFinishExpectedTips(staffRevenue.getTotalTips());
                staffRevenue.setExpectedRevenue(
                        AmountUtils.sum(staffRevenue.getExpectedRevenue(), staffRevenue.getTotalTips()));
                staffRevenue.setCollectedRevenue(
                        AmountUtils.sum(staffRevenue.getCollectedRevenue(), staffRevenue.getTotalTips()));
            }
        }
    }

    public List<StaffRevenueDetail> calculateAppointmentAmount(
            GroomingReportWebAppointment appt, Map<Integer, BigDecimal> staffTipMap) {
        List<StaffRevenueDetail> staffRevenueDetails = new ArrayList<>();

        if (CollectionUtils.isEmpty(appt.getInvoiceIdToPetDetailsMap())) {
            return staffRevenueDetails;
        }

        for (var entry : appt.getInvoiceIdToPetDetailsMap().entrySet()) {
            var invoice = appt.getInvoiceMap().get(entry.getKey());
            if (invoice == null) {
                continue;
            }
            var petDetails = entry.getValue();
            List<MoeGroomingInvoiceItem> invoiceItems = invoice.getItems();
            // 填充当前 petDetails，分配当前 invoice 的金额
            appt.setPetDetails(petDetails);
            // 填充当前 invoice 的金额
            reportOrderService.resetInvoice(appt, invoice);
            // 针对某些 feature 对 order item 特殊处理
            presetInvoiceItems(invoiceItems);
            EmployeeReportCalculateDTO calculateDTO = ReportUtil.allocateAmount(appt, invoiceItems, staffTipMap);
            if (!CollectionUtils.isEmpty(petDetails)) {
                // 把金额分配给 staff 和 non-staff service，只需要计算 staff 的部分
                fillStaffAmount(calculateDTO, appt);
                // 计算 service 应收、实收金额
                staffRevenueDetails.addAll(calculateServiceItems(appt, calculateDTO));
            }
            // 计算 product 金额
            staffRevenueDetails.addAll(calculateProductItems(appt, invoiceItems, calculateDTO));
        }
        return staffRevenueDetails;
    }

    // 计算分配给 staff 的 amount
    void fillStaffAmount(EmployeeReportCalculateDTO calculateDTO, GroomingReportWebAppointment apt) {
        var staffPetDetails = apt.getPetDetails().stream()
                .filter(pd -> CommonUtil.isNormal(pd.getStaffId()))
                .toList();
        BigDecimal staffServicePrice = serviceDetailService.calculateAmount(
                ReportBeanMapper.INSTANCE.toMoeGroomingPetDetailList(apt.getPetDetails()),
                ReportBeanMapper.INSTANCE.toMoeGroomingPetDetailList(staffPetDetails),
                null);
        BigDecimal staffTipsAmount = BigDecimal.ZERO;
        BigDecimal staffDiscount = BigDecimal.ZERO;
        BigDecimal staffTax = BigDecimal.ZERO;
        for (ReportWebApptPetDetail petDetail : staffPetDetails) {
            staffTipsAmount = staffTipsAmount.add(
                    calculateDTO.getTipsMap().getOrDefault(petDetail.getId(), new HashMap<>()).values().stream()
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
            staffDiscount =
                    staffDiscount.add(calculateDTO.getDiscountMap().getOrDefault(petDetail.getId(), BigDecimal.ZERO));
            staffTax = staffTax.add(calculateDTO.getTaxMap().getOrDefault(petDetail.getId(), BigDecimal.ZERO));
        }
        // 后面的计算逻辑会减去 convenience_fee，在这里分配金额的时候先加上
        var staffAmount = staffServicePrice.add(staffTipsAmount).add(staffTax).subtract(staffDiscount);
        var noStaffAmount = apt.getTotalServiceSale().compareTo(staffAmount) >= 0
                ? apt.getTotalServiceSale().subtract(staffAmount)
                : BigDecimal.ZERO;
        List<BigDecimal> servicePriceAllocate = List.of(staffAmount, noStaffAmount);

        calculateDTO.setStaffServicePaidAmount(
                ReportUtil.allocateAmountByPrice(calculateDTO.getServicePaidAmount(), servicePriceAllocate)
                        .get(0));
        calculateDTO.setStaffServicePaidExcludeConFee(
                ReportUtil.allocateAmountByPrice(calculateDTO.getServicePaidExcludeConFee(), servicePriceAllocate)
                        .get(0));
        calculateDTO.setStaffServiceRefundedAmount(
                ReportUtil.allocateAmountByPrice(calculateDTO.getServiceRefundedAmount(), servicePriceAllocate)
                        .get(0));
        calculateDTO.setStaffServiceUnpaidAmount(
                ReportUtil.allocateAmountByPrice(calculateDTO.getServiceUnpaidAmount(), servicePriceAllocate)
                        .get(0));
        calculateDTO.setStaffServiceExpectedRevenue(staffAmount);
    }

    /**
     * 计算 product item 金额并分配到employee report
     */
    private void calculateProductItem(
            ReportWebEmployee employee, MoeGroomingInvoiceItem item, EmployeeReportCalculateDTO calculateDTO) {
        BigDecimal productPaidAmount = calculateDTO.getProductPaidExcludeConFee();
        BigDecimal productUnpaidAmount = calculateDTO.getProductUnpaidAmount();
        BigDecimal productRefundedAmount = calculateDTO.getProductRefundedAmount();
        BigDecimal productExpectedRevenue = calculateDTO.getProductExpectedRevenue();

        // product 应收金额: tax, discount, productPrice
        BigDecimal curExpectedTax = item.getTaxAmount();
        BigDecimal curDiscount = item.getDiscountAmount();
        BigDecimal curExpectedProductRevenue = item.getTotalSalePrice().add(curExpectedTax);

        employee.setTotalProductAmount(AmountUtils.sum(employee.getTotalProductAmount(), item.getTotalListPrice()));
        employee.setTotalTax(AmountUtils.sum(employee.getTotalTax(), item.getTaxAmount()));
        employee.setDiscount(AmountUtils.sum(employee.getDiscount(), item.getDiscountAmount()));

        BigDecimal unpaidRevenue = BigDecimal.ZERO;
        // 未收金额
        if (!AmountUtils.isNullOrZero(productExpectedRevenue)) {
            unpaidRevenue =
                    productUnpaidAmount.multiply(curExpectedProductRevenue).divide(productExpectedRevenue, 2, HALF_UP);
            employee.setUnpaidRevenue(AmountUtils.sum(employee.getUnpaidRevenue(), unpaidRevenue));
        }

        // product 实收金额: paid, refunded, collected, tax, discount, productPrice
        BigDecimal curProductRevenue = BigDecimal.ZERO;
        BigDecimal curPaidAmount = BigDecimal.ZERO;
        BigDecimal curRefundedAmount = BigDecimal.ZERO;
        BigDecimal curNetSaleRevenue = BigDecimal.ZERO;
        // 未支付时不需要计算实收
        if (!calculateDTO.isAptPaid()) {
            employee.setRevenue(AmountUtils.sum(employee.getRevenue(), curPaidAmount, unpaidRevenue));
            employee.setNetSaleRevenue(AmountUtils.sum(employee.getNetSaleRevenue(), curNetSaleRevenue));
            return;
        }

        if (!AmountUtils.isNullOrZero(productExpectedRevenue)) {
            curPaidAmount =
                    productPaidAmount.multiply(curExpectedProductRevenue).divide(productExpectedRevenue, 2, HALF_UP);
            curRefundedAmount = productRefundedAmount
                    .multiply(curExpectedProductRevenue)
                    .divide(productExpectedRevenue, 2, HALF_UP);
            curProductRevenue = curPaidAmount.subtract(curRefundedAmount);
        }

        // staff分配到的总金额（应收：已支付+未支付）
        employee.setRevenue(AmountUtils.sum(employee.getRevenue(), curPaidAmount, unpaidRevenue));
        // net sale revenue
        curNetSaleRevenue = curNetSaleRevenue.subtract(curRefundedAmount);
        employee.setNetSaleRevenue(AmountUtils.sum(employee.getNetSaleRevenue(), curNetSaleRevenue));

        BigDecimal curCollectedTax;
        if (curProductRevenue.compareTo(curExpectedTax) > 0) {
            curCollectedTax = curExpectedTax;
        } else {
            curCollectedTax = curProductRevenue;
        }
        BigDecimal curCollectedProductPrice =
                curProductRevenue.subtract(curCollectedTax).add(curDiscount);

        employee.setCollectedTax(AmountUtils.sum(employee.getCollectedTax(), curCollectedTax));
        employee.setCollectedDiscount(AmountUtils.sum(employee.getCollectedDiscount(), curDiscount));
        employee.setCollectedProductPrice(
                AmountUtils.sum(employee.getCollectedProductPrice(), curCollectedProductPrice));
        // net sale revenue
        curNetSaleRevenue =
                curCollectedProductPrice.subtract(curDiscount); // net sale revenue = collectedProductPrice - discount

        employee.setTotalPayment(AmountUtils.sum(employee.getTotalPayment(), curPaidAmount));
        employee.setTotalRefund(AmountUtils.sum(employee.getTotalRefund(), curRefundedAmount));
        employee.setCollectedRevenue(AmountUtils.sum(employee.getCollectedRevenue(), curProductRevenue));
        employee.setCollectedRevAll(AmountUtils.sum(employee.getCollectedRevAll(), curCollectedProductPrice));
        employee.setNetSaleRevenue(AmountUtils.sum(employee.getNetSaleRevenue(), curNetSaleRevenue));
    }

    private void calculateServiceItem(
            Map<Integer, ReportWebEmployee> idToStaff,
            List<ReportWebApptPetDetail> petDetails,
            EmployeeReportCalculateDTO calculateDTO) {
        BigDecimal servicePaidAmount = calculateDTO.getStaffServicePaidAmount();
        BigDecimal servicePaidExcludeConFee = calculateDTO.getStaffServicePaidExcludeConFee();
        BigDecimal serviceRefundedAmount = calculateDTO.getStaffServiceRefundedAmount();
        BigDecimal serviceUnpaidAmount = calculateDTO.getStaffServiceUnpaidAmount();
        BigDecimal serviceExpectedRevenue = calculateDTO.getStaffServiceExpectedRevenue();
        boolean isAptPaid = calculateDTO.isAptPaid();

        // 保存需要按服务金额比例分配的金额，确保最后所有相加等于总数
        BigDecimal tempTotalUnpaid = BigDecimal.ZERO;
        BigDecimal tempTotalPaidAmount = BigDecimal.ZERO;
        BigDecimal tempTotalPaidExcludeConFee = BigDecimal.ZERO;
        BigDecimal tempTotalRefund = BigDecimal.ZERO;

        // 遍历 petDetail，计算每个员工的收入
        for (int i = 0, size = petDetails.size(); i < size; i++) {
            ReportWebApptPetDetail petDetail = petDetails.get(i);

            List<ReportWebEmployee> employeeList;
            Map<Integer, Function<BigDecimal, BigDecimal>> staffPriceRateMap;
            if (CollectionUtils.isEmpty(petDetail.getOperationList())) {
                ReportWebEmployee employee = idToStaff.get(petDetail.getStaffId());
                if (employee == null) {
                    continue;
                }
                employeeList = Collections.singletonList(employee);
                staffPriceRateMap = Collections.singletonMap(employee.getStaffId(), BigDecimal.ONE::multiply);
            } else {
                employeeList = petDetail.getOperationList().stream()
                        .map(GroomingServiceOperationDTO::getStaffId)
                        .distinct()
                        .map(idToStaff::get)
                        .toList();
                staffPriceRateMap = petDetail.getOperationList().stream()
                        .collect(Collectors.groupingBy(GroomingServiceOperationDTO::getStaffId))
                        .entrySet()
                        .stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                            if (petDetail.getServicePrice().compareTo(BigDecimal.ZERO) == 0) {
                                return BigDecimal.ZERO::multiply;
                            }
                            return number -> entry.getValue().stream()
                                    .map(GroomingServiceOperationDTO::getPrice)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .multiply(number)
                                    .divide(petDetail.getServicePrice(), 2, HALF_UP);
                        }));
            }

            BigDecimal curServicePrice = petDetail.getServicePrice();
            // 当前 petDetail 是 multi staff 时，对应 staff 分配到的 tips
            Map<Integer, BigDecimal> petDetailStaffTips =
                    calculateDTO.getTipsMap().getOrDefault(petDetail.getId(), Map.of());
            // 当前 petDetail 总的 tips
            BigDecimal curExpectedTips = petDetailStaffTips.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal curDiscount = calculateDTO.getDiscountMap().getOrDefault(petDetail.getId(), BigDecimal.ZERO);
            BigDecimal curExpectedTax = calculateDTO.getTaxMap().getOrDefault(petDetail.getId(), BigDecimal.ZERO);
            BigDecimal curServiceExpectedRev =
                    curServicePrice.add(curExpectedTips).add(curExpectedTax).subtract(curDiscount);
            boolean isAddon = ReportUtil.isAddon(petDetail.getServiceType());
            // 应收金额：service/add-on 金额、tax、tips、discount
            if (isAddon) {
                setEmployeePriceByRate(
                        employeeList,
                        staffPriceRateMap,
                        curServicePrice,
                        ReportWebEmployee::getTotalAddOnsAmount,
                        ReportWebEmployee::setTotalAddOnsAmount);
            } else {
                setEmployeePriceByRate(
                        employeeList,
                        staffPriceRateMap,
                        curServicePrice,
                        ReportWebEmployee::getTotalServicesAmount,
                        ReportWebEmployee::setTotalServicesAmount);
            }
            setEmployeePriceByRate(
                    employeeList,
                    staffPriceRateMap,
                    curExpectedTax,
                    ReportWebEmployee::getTotalTax,
                    ReportWebEmployee::setTotalTax);

            for (ReportWebEmployee employee : employeeList) {
                employee.setTotalTips(AmountUtils.sum(
                        employee.getTotalTips(),
                        calculateDTO
                                .getTipsMap()
                                .getOrDefault(petDetail.getId(), new HashMap<>())
                                .getOrDefault(employee.getStaffId(), BigDecimal.ZERO)));
                // 设置当前订单总的 tips
                employee.setOrderTotalTips(calculateDTO.getOrderTotalTips());
            }

            setEmployeePriceByRate(
                    employeeList,
                    staffPriceRateMap,
                    curDiscount,
                    ReportWebEmployee::getDiscount,
                    ReportWebEmployee::setDiscount);
            // 未收金额
            BigDecimal unpaidRev = BigDecimal.ZERO;
            if (!AmountUtils.isNullOrZero(serviceExpectedRevenue)) {
                if (i != size - 1) {
                    unpaidRev = serviceUnpaidAmount
                            .multiply(curServiceExpectedRev)
                            .divide(serviceExpectedRevenue, 2, HALF_UP);
                    tempTotalUnpaid = AmountUtils.sum(tempTotalUnpaid, unpaidRev);
                } else {
                    // 最后一个服务不按比例分配，用总数减去之前的总和以保证各个服务相加等于总数
                    unpaidRev = AmountUtils.subtract(serviceUnpaidAmount, tempTotalUnpaid);
                }
                setEmployeePriceByRate(
                        employeeList,
                        staffPriceRateMap,
                        unpaidRev,
                        ReportWebEmployee::getUnpaidRevenue,
                        ReportWebEmployee::setUnpaidRevenue);
            }

            // payroll 相关金额统计
            Integer serviceId = petDetail.getServiceId();
            if (calculateDTO.isAptFinish()) {
                // add to expectedMap, for commission calculation
                setEmployeePriceByRate(
                        employeeList,
                        staffPriceRateMap,
                        curServicePrice,
                        reportWebEmployee -> (isAddon
                                        ? reportWebEmployee.getAddonExpectedMap()
                                        : reportWebEmployee.getServiceExpectedMap())
                                .get(serviceId),
                        (reportWebEmployee, bigDecimal) -> (isAddon
                                        ? reportWebEmployee.getAddonExpectedMap()
                                        : reportWebEmployee.getServiceExpectedMap())
                                .put(serviceId, bigDecimal));

                // 统计总的 finish service 应收金额
                if (isAddon) {
                    setEmployeePriceByRate(
                            employeeList,
                            staffPriceRateMap,
                            curServicePrice,
                            ReportWebEmployee::getFinishExpectedAddonPrice,
                            ReportWebEmployee::setFinishExpectedAddonPrice);
                } else {
                    setEmployeePriceByRate(
                            employeeList,
                            staffPriceRateMap,
                            curServicePrice,
                            ReportWebEmployee::getFinishExpectedServicePrice,
                            ReportWebEmployee::setFinishExpectedServicePrice);
                }

                // tips 不按比例分配，按 splitTips 分配
                petDetailStaffTips.forEach((staffId, tips) -> {
                    ReportWebEmployee employee = idToStaff.get(staffId);
                    if (employee == null) {
                        return;
                    }
                    employee.setFinishExpectedTips(AmountUtils.sum(employee.getFinishExpectedTips(), tips));
                });
            }

            BigDecimal curPaidAmount = BigDecimal.ZERO;
            BigDecimal curPaidExcludeConFee = BigDecimal.ZERO;
            BigDecimal curRefundedAmount = BigDecimal.ZERO;
            BigDecimal curNetSaleRevenue = BigDecimal.ZERO;

            // 未支付时不计算实收金额
            if (!isAptPaid) {
                // staff分配到的总金额（应收：已支付+未支付）
                setEmployeePriceByRate(
                        employeeList,
                        staffPriceRateMap,
                        AmountUtils.sum(curPaidExcludeConFee, unpaidRev),
                        ReportWebEmployee::getRevenue,
                        ReportWebEmployee::setRevenue);
                // 新增 Net sale revenue 字段
                setEmployeePriceByRate(
                        employeeList,
                        staffPriceRateMap,
                        curNetSaleRevenue,
                        ReportWebEmployee::getNetSaleRevenue,
                        ReportWebEmployee::setNetSaleRevenue);
                continue;
            }

            // 1.实收金额、退款金额
            if (!AmountUtils.isNullOrZero(serviceExpectedRevenue)) {
                if (i != size - 1) {
                    curPaidAmount = servicePaidAmount
                            .multiply(curServiceExpectedRev)
                            .divide(serviceExpectedRevenue, 2, HALF_UP);
                    tempTotalPaidAmount = AmountUtils.sum(tempTotalPaidAmount, curPaidAmount);

                    curPaidExcludeConFee = servicePaidExcludeConFee
                            .multiply(curServiceExpectedRev)
                            .divide(serviceExpectedRevenue, 2, HALF_UP);
                    tempTotalPaidExcludeConFee = AmountUtils.sum(tempTotalPaidExcludeConFee, curPaidExcludeConFee);
                } else {
                    // 最后一个服务不按比例分配，用总数减去之前的总和以保证各个服务相加等于总数
                    curPaidAmount = AmountUtils.subtract(servicePaidAmount, tempTotalPaidAmount);
                    curPaidExcludeConFee = AmountUtils.subtract(servicePaidExcludeConFee, tempTotalPaidExcludeConFee);
                }

                if (!AmountUtils.isNullOrZero(serviceRefundedAmount)) {
                    if (i != size - 1) {
                        curRefundedAmount = serviceRefundedAmount
                                .multiply(curServiceExpectedRev)
                                .divide(serviceExpectedRevenue, 2, HALF_UP);
                        tempTotalRefund = AmountUtils.sum(tempTotalRefund, curRefundedAmount);
                    } else {
                        curRefundedAmount = AmountUtils.subtract(serviceRefundedAmount, tempTotalRefund);
                    }
                }
            }

            // staff分配到的总金额（应收：已支付+未支付）
            setEmployeePriceByRate(
                    employeeList,
                    staffPriceRateMap,
                    AmountUtils.sum(curPaidExcludeConFee, unpaidRev),
                    ReportWebEmployee::getRevenue,
                    ReportWebEmployee::setRevenue);

            // 计算当前service实收金额
            CollectedPriceDTO collectedPriceDTO = calculateCollectedPrice(new CalCollectedPriceParams()
                    .setPaidAmount(curPaidExcludeConFee)
                    .setRefundAmount(curRefundedAmount)
                    .setExpectedServiceTax(curExpectedTax)
                    .setExpectedProductTax(BigDecimal.ZERO)
                    .setExpectedTips(curExpectedTips)
                    .setServiceDiscountAmount(curDiscount)
                    .setProductDiscountAmount(BigDecimal.ZERO)
                    .setTotalServiceSale(curServiceExpectedRev)
                    .setTotalProductSale(BigDecimal.ZERO));

            // 当前service分配到的实收金额（不包含tips、tax、discount）
            BigDecimal curServiceCollectedPrice = collectedPriceDTO.getCollectedServicePrice();
            setEmployeePriceByRate(
                    employeeList,
                    staffPriceRateMap,
                    curServiceCollectedPrice,
                    ReportWebEmployee::getCollectedRevAll,
                    ReportWebEmployee::setCollectedRevAll);

            // Net sale revenue
            curNetSaleRevenue = collectedPriceDTO.getNetSaleRevenue();
            setEmployeePriceByRate(
                    employeeList,
                    staffPriceRateMap,
                    curNetSaleRevenue,
                    ReportWebEmployee::getNetSaleRevenue,
                    ReportWebEmployee::setNetSaleRevenue);

            if (isAddon) {
                setEmployeePriceByRate(
                        employeeList,
                        staffPriceRateMap,
                        curServiceCollectedPrice,
                        ReportWebEmployee::getCollectedAddonPrice,
                        ReportWebEmployee::setCollectedAddonPrice);
            } else {
                setEmployeePriceByRate(
                        employeeList,
                        staffPriceRateMap,
                        curServiceCollectedPrice,
                        ReportWebEmployee::getCollectedServicePrice,
                        ReportWebEmployee::setCollectedServicePrice);
            }

            // 当前 service 中每个 staff 的 collected tips
            Map<Integer, BigDecimal> curServicePerStaffCollectedTips = new HashMap<>();
            BigDecimal serviceCollectedTips = collectedPriceDTO.getCollectedTips();
            if (!AmountUtils.isNullOrZero(serviceCollectedTips) && !AmountUtils.isNullOrZero(curExpectedTips)) {
                BigDecimal tempTotalTips = BigDecimal.ZERO;
                for (int sequence = 0; sequence < employeeList.size(); sequence++) {
                    ReportWebEmployee employee = employeeList.get(sequence);
                    // 当前 operation staff 应收 tips
                    BigDecimal curEmployeeExpectedTips = calculateDTO
                            .getTipsMap()
                            .getOrDefault(petDetail.getId(), new HashMap<>())
                            .getOrDefault(employee.getStaffId(), BigDecimal.ZERO);
                    BigDecimal curEmployeeCollectedTips;
                    if (sequence == employeeList.size() - 1) {
                        // 最后一个倒减
                        curEmployeeCollectedTips = serviceCollectedTips.subtract(tempTotalTips);
                    } else {
                        // 根据 operation staff 应收 tips 在 service 总应收 tips 中占比计算 collected tips
                        curEmployeeCollectedTips = serviceCollectedTips
                                .multiply(curEmployeeExpectedTips)
                                .divide(curExpectedTips, 2, HALF_UP);
                        tempTotalTips = tempTotalTips.add(curEmployeeCollectedTips);
                    }

                    curServicePerStaffCollectedTips.put(employee.getStaffId(), curEmployeeCollectedTips);
                    employee.setCollectedTips(AmountUtils.sum(employee.getCollectedTips(), curEmployeeCollectedTips));
                }
            }

            setEmployeePriceByRate(
                    employeeList,
                    staffPriceRateMap,
                    collectedPriceDTO.getCollectedServiceTax(),
                    ReportWebEmployee::getCollectedTax,
                    ReportWebEmployee::setCollectedTax);
            setEmployeePriceByRate(
                    employeeList,
                    staffPriceRateMap,
                    curDiscount,
                    ReportWebEmployee::getCollectedDiscount,
                    ReportWebEmployee::setCollectedDiscount);
            setEmployeePriceByRate(
                    employeeList,
                    staffPriceRateMap,
                    curPaidAmount,
                    ReportWebEmployee::getTotalPayment,
                    ReportWebEmployee::setTotalPayment);
            setEmployeePriceByRate(
                    employeeList,
                    staffPriceRateMap,
                    curRefundedAmount,
                    ReportWebEmployee::getTotalRefund,
                    ReportWebEmployee::setTotalRefund);

            BigDecimal remainServiceCollectedPrice = curServiceCollectedPrice,
                    remainCollectedServiceTax = collectedPriceDTO.getCollectedServiceTax(),
                    remainCollectedDiscount = curDiscount;
            for (int sequence = 0; sequence < employeeList.size(); sequence++) {
                ReportWebEmployee employee = employeeList.get(sequence);
                // 再计算一次 collectedServicePrice、collectedTax、collectedDiscount
                BigDecimal collectedServicePrice, collectedTax, collectedDiscount;
                if (sequence == employeeList.size() - 1) {
                    collectedServicePrice = remainServiceCollectedPrice;
                    collectedTax = remainCollectedServiceTax;
                    collectedDiscount = remainCollectedDiscount;
                } else {
                    collectedServicePrice =
                            staffPriceRateMap.get(employee.getStaffId()).apply(curServiceCollectedPrice);
                    remainServiceCollectedPrice = remainServiceCollectedPrice.subtract(collectedServicePrice);

                    collectedTax = staffPriceRateMap
                            .get(employee.getStaffId())
                            .apply(collectedPriceDTO.getCollectedServiceTax());
                    remainCollectedServiceTax = remainCollectedServiceTax.subtract(collectedTax);

                    collectedDiscount =
                            staffPriceRateMap.get(employee.getStaffId()).apply(curDiscount);
                    remainCollectedDiscount = remainCollectedDiscount.subtract(collectedDiscount);
                }
                // 根据 collectedServicePrice + collectedTax + collectedTips - collectedDiscount 计算
                // curCollectedRevenue，并累加到 employee.setCollectedRevenue
                BigDecimal collectedTips =
                        curServicePerStaffCollectedTips.getOrDefault(employee.getStaffId(), BigDecimal.ZERO);
                BigDecimal collectedRevenue = collectedServicePrice
                        .add(collectedTax)
                        .add(collectedTips)
                        .subtract(collectedDiscount);
                employee.setCollectedRevenue(AmountUtils.sum(employee.getCollectedRevenue(), collectedRevenue));
            }

            // payroll 相关金额统计
            // add to collectedMap, for commission calculation
            setEmployeePriceByRate(
                    employeeList,
                    staffPriceRateMap,
                    curServiceCollectedPrice,
                    reportWebEmployee -> {
                        Map<Integer, BigDecimal> collectedMap = isAddon
                                ? reportWebEmployee.getAddonCollectedMap()
                                : reportWebEmployee.getServiceCollectedMap();
                        return collectedMap.get(serviceId);
                    },
                    (reportWebEmployee, bigDecimal) -> {
                        Map<Integer, BigDecimal> collectedMap = isAddon
                                ? reportWebEmployee.getAddonCollectedMap()
                                : reportWebEmployee.getServiceCollectedMap();
                        collectedMap.put(serviceId, bigDecimal);
                    });
        }
    }

    private static void setEmployeePriceByRate(
            List<ReportWebEmployee> employeeList,
            Map<Integer, Function<BigDecimal, BigDecimal>> staffPriceRateMap,
            BigDecimal totalPrice,
            Function<ReportWebEmployee, BigDecimal> getFunction,
            BiConsumer<ReportWebEmployee, BigDecimal> setFunction) {
        BigDecimal remainAmount = totalPrice;
        for (int sequence = 0; sequence < employeeList.size(); sequence++) {
            ReportWebEmployee employee = employeeList.get(sequence);
            if (sequence == employeeList.size() - 1) {
                setFunction.accept(employee, AmountUtils.sum(getFunction.apply(employee), remainAmount));
            } else {
                BigDecimal currentAmount =
                        staffPriceRateMap.get(employee.getStaffId()).apply(totalPrice);
                setFunction.accept(employee, AmountUtils.sum(getFunction.apply(employee), currentAmount));
                remainAmount = remainAmount.subtract(currentAmount);
            }
        }
    }

    public void postProcessEmployeeMoneyFormat(List<ReportWebEmployee> employees) {
        if (CollectionUtils.isEmpty(employees)) {
            return;
        }
        employees.forEach(staff -> {
            if (staff.getCollectedRevenue() != null) {
                staff.setCollectedRevenue(staff.getCollectedRevenue().setScale(2, HALF_UP));
            } else {
                staff.setCollectedRevenue(BigDecimal.ZERO);
            }
            if (staff.getUnpaidRevenue() != null) {
                staff.setUnpaidRevenue(staff.getUnpaidRevenue().setScale(2, HALF_UP));
            } else {
                staff.setUnpaidRevenue(BigDecimal.ZERO);
            }
            if (staff.getTotalTips() != null) {
                staff.setTotalTips(staff.getTotalTips().setScale(2, HALF_UP));
            } else {
                staff.setTotalTips(BigDecimal.ZERO);
            }
            if (staff.getTotalTax() != null) {
                staff.setTotalTax(staff.getTotalTax().setScale(2, HALF_UP));
            } else {
                staff.setTotalTax(BigDecimal.ZERO);
            }
            if (staff.getDiscount() != null) {
                staff.setDiscount(staff.getDiscount().setScale(2, HALF_UP));
            } else {
                staff.setDiscount(BigDecimal.ZERO);
            }
            if (staff.getTotalServicesAmount() != null) {
                staff.setTotalServicesAmount(staff.getTotalServicesAmount().setScale(2, HALF_UP));
            } else {
                staff.setTotalServicesAmount(BigDecimal.ZERO);
            }
            if (staff.getTotalAddOnsAmount() != null) {
                staff.setTotalAddOnsAmount(staff.getTotalAddOnsAmount().setScale(2, HALF_UP));
            } else {
                staff.setTotalAddOnsAmount(BigDecimal.ZERO);
            }
            if (staff.getCollectedServicePrice() != null) {
                staff.setCollectedServicePrice(staff.getCollectedServicePrice().setScale(2, HALF_UP));
            } else {
                staff.setCollectedServicePrice(BigDecimal.ZERO);
            }
            if (staff.getCollectedAddonPrice() != null) {
                staff.setCollectedAddonPrice(staff.getCollectedAddonPrice().setScale(2, HALF_UP));
            } else {
                staff.setCollectedAddonPrice(BigDecimal.ZERO);
            }
        });
    }

    public void postProcessMoneyFormat(List<ReportWebSale> sales) {
        if (CollectionUtils.isEmpty(sales)) {
            return;
        }
        for (ReportWebSale sale : sales) {
            if (sale.getRevenue() != null) {
                sale.setRevenue(sale.getRevenue().setScale(2, HALF_UP));
            } else {
                sale.setRevenue(BigDecimal.ZERO);
            }
            if (sale.getTips() != null) {
                sale.setTips(sale.getTips().setScale(2, HALF_UP));
            } else {
                sale.setTips(BigDecimal.ZERO);
            }
            if (sale.getTax() != null) {
                sale.setTax(sale.getTax().setScale(2, HALF_UP));
            } else {
                sale.setTax(BigDecimal.ZERO);
            }
            if (sale.getDiscount() != null) {
                sale.setDiscount(sale.getDiscount().setScale(2, HALF_UP));
            } else {
                sale.setDiscount(BigDecimal.ZERO);
            }
            if (sale.getUnpaidRevenue() != null) {
                sale.setUnpaidRevenue(sale.getUnpaidRevenue().setScale(2, HALF_UP));
            } else {
                sale.setUnpaidRevenue(BigDecimal.ZERO);
            }
            if (sale.getCollectedRevenue() != null) {
                sale.setCollectedRevenue(sale.getCollectedRevenue().setScale(2, HALF_UP));
            } else {
                sale.setCollectedRevenue(BigDecimal.ZERO);
            }
        }
    }

    public void processCounts(List<GroomingReportWebAppointment> appointments, List<ReportWebEmployee> employees) {
        Map<Integer, Set<Integer>> staffToAppts = new HashMap<>();
        Map<Integer, Set<String>> staffToPets = new HashMap<>();

        // finished appt
        Map<Integer, Set<Integer>> staffToFinishedAppts = new HashMap<>();
        // finished appt 对应的pet统计
        Map<Integer, Set<String>> staffToFinishedPets = new HashMap<>();
        // partial/fully paid appt
        Map<Integer, Set<Integer>> staffToPaidAppts = new HashMap<>();

        Map<Integer, Set<Integer>> staffToClients = new HashMap<>();
        Map<Integer, Integer> staffToTime = new HashMap<>();
        Map<Integer, Set<Integer>> staffToServicedPets = new HashMap<>(); // 去重

        appointments.forEach(a -> {
            a.getPetDetails().stream()
                    .flatMap(petDetail -> {
                        if (!CollectionUtils.isEmpty(petDetail.getOperationList())) {
                            return petDetail.getOperationList().stream();
                        }
                        // 将 petDetail 转为 operation，方便后续统一处理
                        GroomingServiceOperationDTO operationDTO = new GroomingServiceOperationDTO();
                        operationDTO.setStaffId(petDetail.getStaffId());
                        operationDTO.setPetId(petDetail.getPetId());
                        operationDTO.setDuration(Math.toIntExact(petDetail.getEndTime() - petDetail.getStartTime()));
                        return Stream.of(operationDTO);
                    })
                    .forEach(operation -> {
                        Integer staffId = operation.getStaffId();
                        staffToAppts
                                .computeIfAbsent(staffId, k -> new HashSet<>())
                                .add(a.getId());

                        // 当前计算方式为（服务和pet）为一个单位
                        staffToPets
                                .computeIfAbsent(staffId, k -> new HashSet<>())
                                .add(operation.getPetId() + "|" + a.getId());

                        if (AppointmentStatusEnum.FINISHED.getValue().equals(a.getStatus())) {
                            // 如果appt 为 finished
                            staffToFinishedAppts
                                    .computeIfAbsent(staffId, k -> new HashSet<>())
                                    .add(a.getId());

                            staffToFinishedPets
                                    .computeIfAbsent(staffId, k -> new HashSet<>())
                                    .add(operation.getPetId() + "|" + a.getId());
                        }
                        // 统计已支付订单数
                        if (!GroomingAppointmentEnum.NOT_PAY.equals(a.getIsPaid())) {
                            staffToPaidAppts
                                    .computeIfAbsent(staffId, v -> new HashSet<>())
                                    .add(a.getId());
                        }

                        // 当前计算方式为（服务和pet）为一个单位
                        staffToServicedPets
                                .computeIfAbsent(staffId, k -> new HashSet<>())
                                .add(operation.getPetId());

                        staffToClients
                                .computeIfAbsent(staffId, k -> new HashSet<>())
                                .add(a.getCustomerId());

                        staffToTime.put(staffId, staffToTime.getOrDefault(staffId, 0) + operation.getDuration());
                    });
        });

        employees.forEach(staff -> {
            Integer staffId = staff.getStaffId();
            if (staffToAppts.containsKey(staffId)) {
                staff.setTotalAppts(staffToAppts.get(staffId).size());
            } else {
                staff.setTotalAppts(0);
            }

            if (staffToPets.containsKey(staffId)) {
                staff.setTotalPets(staffToPets.get(staffId).size());
            } else {
                staff.setTotalPets(0);
            }

            if (staffToFinishedAppts.containsKey(staffId)) {
                staff.setFinishedApptNum(staffToFinishedAppts.get(staffId).size());
            } else {
                staff.setFinishedApptNum(0);
            }

            if (staffToFinishedPets.containsKey(staffId)) {
                staff.setFinishedPetNum(staffToFinishedPets.get(staffId).size());
            } else {
                staff.setFinishedPetNum(0);
            }

            staff.setPaidApptNum(staffToPaidAppts
                    .getOrDefault(staffId, Collections.emptySet())
                    .size());

            if (staffToClients.containsKey(staffId)) {
                staff.setTotalClients(staffToClients.get(staffId).size());
            } else {
                staff.setTotalClients(0);
            }
            if (staffToTime.containsKey(staffId)) {
                staff.setTotalServiceHour(BigDecimal.valueOf(staffToTime.get(staffId))
                        .divide(BigDecimal.valueOf(60), 2, HALF_UP)
                        .doubleValue());
                BigDecimal petNum = BigDecimal.valueOf(staff.getTotalPets());
                if (petNum.compareTo(BigDecimal.ZERO) > 0) {
                    staff.setAvgMinutePerPet(BigDecimal.valueOf(staffToTime.get(staffId))
                            .divide((petNum), 2, HALF_UP)
                            .doubleValue());
                } else {
                    staff.setAvgMinutePerPet(0.0);
                }
            } else {
                staff.setTotalServiceHour(0.0);
                staff.setAvgMinutePerPet(0.0);
            }

            if (staffToServicedPets.containsKey(staffId)) {
                staff.setServicedPetNum(staffToServicedPets.get(staffId).size());
            } else {
                staff.setServicedPetNum(0);
            }
        });
    }

    public void processMoney(
            Integer businessId,
            List<GroomingReportWebAppointment> appointments,
            List<ReportWebEmployee> employees,
            Boolean queryItems) {
        Map<Integer, ReportWebEmployee> idToStaff =
                employees.stream().collect(Collectors.toMap(ReportWebEmployee::getStaffId, Function.identity()));

        // 获取invoice items
        Map<Integer, List<MoeGroomingInvoiceItem>> invoiceItemMap;
        if (queryItems) {
            invoiceItemMap = reportOrderService.getInvoiceItemMap(appointments);
        } else {
            invoiceItemMap = buildInvoiceItemMap(appointments);
        }

        // 获取refund记录，并赋值到appt
        Set<Integer> invoiceIds = appointments.stream()
                .map(GroomingReportWebAppointment::getInvoiceId)
                .collect(Collectors.toSet());
        Map<Integer, BigDecimal> refundMap = reportOrderService.getRefundMap(invoiceIds);
        appointments.forEach(
                appt -> appt.setRefundedAmount(refundMap.getOrDefault(appt.getInvoiceId(), BigDecimal.ZERO)));

        // 针对某些 feature 对 order item 特殊处理
        presetInvoiceItems(invoiceItemMap);

        // 查询tip split记录
        Map<Integer, Map<Integer, BigDecimal>> tipSplitMap = getTipSplitMap(businessId, appointments);

        for (GroomingReportWebAppointment apt : appointments) {
            dealApptMoneyReportV2(
                    apt,
                    idToStaff,
                    invoiceItemMap.getOrDefault(apt.getId(), Collections.emptyList()),
                    tipSplitMap.getOrDefault(apt.getInvoiceId(), Collections.emptyMap()));
        }
    }

    /**
     * payment method部分从business迁移过来
     *
     * @param employees
     */
    public void processPaymentMethod(List<ReportWebEmployee> employees) {
        // 获取payment信息
        List<Integer> invoiceIds = new ArrayList<>();
        employees.forEach(s -> invoiceIds.add(s.getInvoiceId()));
        GetPaymentListParams params = new GetPaymentListParams();
        params.setModule(PaymentMethodEnum.MODULE_GROOMING);
        params.setInvoiceIds(invoiceIds);
        List<PaymentSummary> paymentList = paymentService.getPaymentList(params).getData();
        if (!CollectionUtils.isEmpty(paymentList)) {
            // 同一个invoice的employee map
            Map<Integer, List<ReportWebEmployee>> employeeMap = new HashMap<>();
            employees.forEach(employee -> {
                if (!employeeMap.containsKey(employee.getInvoiceId())) {
                    employeeMap.put(employee.getInvoiceId(), new ArrayList<>());
                }
                employeeMap.get(employee.getInvoiceId()).add(employee);
            });
            // 设置使用过的payment method对应的支付金额
            for (PaymentSummary paymentSummary : paymentList) {
                List<PaymentSummary.PaymentDto> payments = paymentSummary.getPayments();
                if (CollectionUtils.isEmpty(payments)) {
                    continue;
                }
                Map<Integer, List<PaymentSummary.PaymentRefundDto>> refundMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(paymentSummary.getRefunds())) {
                    refundMap = paymentSummary.getRefunds().stream()
                            .filter(r -> !Objects.equals(r.getStatus(), PaymentStatusEnum.FAILED_STR))
                            .collect(Collectors.groupingBy(PaymentSummary.PaymentRefundDto::getOriginPaymentId));
                }
                // 这个invoice相关的employee
                List<ReportWebEmployee> employeeList = employeeMap.get(paymentSummary.getInvoiceId());
                for (ReportWebEmployee employee : employeeList) {
                    Map<String, BigDecimal> paymentMap =
                            Optional.ofNullable(employee.getPaymentMap()).orElse(new HashMap<>());
                    BigDecimal totalAmount = new BigDecimal(0);
                    for (PaymentSummary.PaymentDto p : payments) {
                        if (!PaymentStatusEnum.COMPLETED_STR.equals(p.getStatus())) {
                            continue;
                        }
                        BigDecimal collectedAmount = p.getAmount();
                        if (refundMap.containsKey(p.getId())) {
                            BigDecimal refundAmount = refundMap.get(p.getId()).stream()
                                    .map(PaymentSummary.PaymentRefundDto::getAmount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            collectedAmount = collectedAmount.subtract(refundAmount);
                        }
                        String method =
                                PaymentUtil.getCreditCardReportMethod(p.getMethod(), p.getSquarePaymentMethod());
                        BigDecimal amount = paymentMap.get(method);
                        if (amount == null) {
                            amount = collectedAmount;
                        } else {
                            amount = amount.add(collectedAmount);
                        }
                        paymentMap.put(method, amount);
                        // 计算总的支付金额
                        totalAmount = totalAmount.add(collectedAmount);
                    }

                    int index = 0;
                    int last = paymentMap.size() - 1;
                    BigDecimal totalTemp = new BigDecimal(0);
                    for (var entry : paymentMap.entrySet()) {
                        String key = entry.getKey();
                        BigDecimal amount;
                        if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                            if (index != last) {
                                // 当前method的金额 amount = 实际收入 * （当前method支付金额 / 总的支付金额）
                                amount = employee.getCollectedRevenue()
                                        .multiply(paymentMap.get(key))
                                        .divide(totalAmount, 2, HALF_UP);
                                paymentMap.put(key, amount);
                                totalTemp = totalTemp.add(amount);
                            } else {
                                // 最后一个由总数减去，避免小数精度带来的误差
                                amount = employee.getCollectedRevenue().subtract(totalTemp);
                            }
                        } else {
                            if (index != last) {
                                amount = employee.getCollectedRevenue()
                                        .divide(BigDecimal.valueOf(paymentMap.size()), 2, HALF_UP);
                                totalTemp = totalTemp.add(amount);
                            } else {
                                amount = employee.getCollectedRevenue().subtract(totalTemp);
                            }
                        }
                        // 更新重新计算的金额
                        paymentMap.put(key, amount);
                        index++;
                    }
                    employee.setPaymentMap(paymentMap);
                }
            }
        }
    }

    /**
     * Get avatar path for each customer (client)
     */
    public void fillCustomerAvatarAndName(List<CustomerSpend> customers) {
        List<MoeBusinessCustomerDTO> customerDTOS = iCustomerReportClient.queryCustomerBasicInfo(
                customers.stream().map(CustomerSpend::getId).toList());

        for (CustomerSpend customer : customers) {
            for (MoeBusinessCustomerDTO customerDTO : customerDTOS) {
                if (customer.getId().equals(customerDTO.getCustomerId())) {
                    customer.setAvatarPath(customerDTO.getAvatarPath());
                    customer.setName(customerDTO.getFirstName() + " " + customerDTO.getLastName());
                }
            }
        }
    }

    public List<ReportWebEmployee> buildEmployeeReports(
            Integer businessId, String startDate, String endDate, List<GroomingReportWebAppointment> appointments) {
        // 计算 staff 收入
        var staffRevenueDetails = calculateAppointments(businessId, appointments).stream()
                // 过滤不在查询时间范围内的 payroll details
                .filter(payroll -> ReportUtil.isDateBetween(payroll.getDate(), startDate, endDate))
                .collect(Collectors.toList());
        return ReportBeanUtil.convertToStaffPayrollDetail(staffRevenueDetails);
    }

    /**
     * 实收款计算
     *
     * @param params invoice detail
     * @return 实收款 detail
     */
    public CollectedPriceDTO calculateCollectedPrice(CalCollectedPriceParams params) {
        // 总实收
        BigDecimal collectedRevenue = params.getTotalCollectedRevenue();
        // 按比例分配给service、product
        BigDecimal totalSale = params.getTotalSale();
        BigDecimal serviceCollectedRevenue = collectedRevenue;
        BigDecimal serviceChargeCollectedRevenue = BigDecimal.ZERO;
        BigDecimal productCollectedRevenue = BigDecimal.ZERO;
        if (!AmountUtils.isNullOrZero(totalSale)) {
            serviceCollectedRevenue =
                    collectedRevenue.multiply(params.getTotalServiceSale()).divide(totalSale, 2, HALF_UP);

            if (!AmountUtils.isNullOrZero(params.getTotalServiceChargeSale())) {
                serviceChargeCollectedRevenue = collectedRevenue
                        .multiply(params.getTotalServiceChargeSale())
                        .divide(totalSale, 2, HALF_UP);
            }
            productCollectedRevenue =
                    AmountUtils.subtract(collectedRevenue, serviceCollectedRevenue, serviceChargeCollectedRevenue);
        }

        // 计算service实收金额
        CollectedAmountCollection serviceCollected = calculateCollectedAmount(new CalCollectedAmountParams(
                serviceCollectedRevenue,
                params.getExpectedServiceTax(),
                params.getExpectedTips(),
                params.getServiceDiscountAmount()));

        // 计算service charge实收金额
        CollectedAmountCollection serviceChargeCollected = calculateCollectedAmount(new CalCollectedAmountParams(
                serviceChargeCollectedRevenue,
                params.getExpectedServiceChargeTax(),
                BigDecimal.ZERO,
                params.getServiceChargeDiscountAmount()));

        // 分配 product collected
        CollectedAmountCollection productCollected = calculateCollectedAmount(new CalCollectedAmountParams(
                productCollectedRevenue,
                params.getExpectedProductTax(),
                BigDecimal.ZERO,
                params.getProductDiscountAmount()));

        return new CollectedPriceDTO()
                .setCollectedRevenue(collectedRevenue)
                .setCollectedTips(serviceCollected.tips())
                .setCollectedServiceTax(serviceCollected.tax())
                .setServiceDiscountAmount(serviceCollected.discount())
                .setCollectedServicePrice(serviceCollected.itemPrice())
                .setCollectedProductTax(productCollected.tax())
                .setCollectedProductPrice(productCollected.itemPrice())
                .setProductDiscountAmount(productCollected.discount())
                .setCollectedServiceChargeTax(serviceChargeCollected.tax())
                .setServiceChargeDiscountAmount(serviceChargeCollected.discount())
                .setCollectedServiceChargePrice(serviceChargeCollected.itemPrice())
                .setNetSaleRevenue(AmountUtils.sum(
                        serviceCollected.netSaleRevenue(),
                        productCollected.netSaleRevenue(),
                        serviceChargeCollected.netSaleRevenue()));
    }

    public BigDecimal getPetDetailTips(
            Map<Integer, Map<Integer, BigDecimal>> petDetailStaffTipsMap, ReportWebApptPetDetail p) {
        return petDetailStaffTipsMap.getOrDefault(p.getId(), Map.of()).values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private void presetInvoiceItems(Map<Integer, List<MoeGroomingInvoiceItem>> invoiceItemMap) {
        invoiceItemMap.values().stream()
                .flatMap(Collection::stream)
                .filter(item -> item.getPurchasedQuantity() > 0)
                .forEach(item -> {
                    // 把 package 抵消的金额当作 discount，在后面算 Payroll 时加回来
                    BigDecimal packageDiscount =
                            item.getServiceUnitPrice().multiply(BigDecimal.valueOf(item.getPurchasedQuantity()));
                    item.setDiscountAmount(item.getDiscountAmount().add(packageDiscount));
                    item.setTotalListPrice(item.getTotalListPrice().add(packageDiscount));
                });
    }

    private void presetInvoiceItems(List<MoeGroomingInvoiceItem> invoiceItems) {
        invoiceItems.stream().filter(item -> item.getPurchasedQuantity() > 0).forEach(item -> {
            // 把 package 抵消的金额当作 discount，在后面算 Payroll 时加回来
            BigDecimal packageDiscount =
                    item.getServiceUnitPrice().multiply(BigDecimal.valueOf(item.getPurchasedQuantity()));
            item.setDiscountAmount(item.getDiscountAmount().add(packageDiscount));
            item.setTotalListPrice(item.getTotalListPrice().add(packageDiscount));
        });
    }

    public Map<Integer, Function<BigDecimal, BigDecimal>> getStaffPriceRateMap(ReportWebApptPetDetail petDetail) {
        if (CollectionUtils.isEmpty(petDetail.getOperationList())) {
            return Collections.singletonMap(petDetail.getStaffId(), BigDecimal.ONE::multiply);
        }
        return petDetail.getOperationList().stream()
                .collect(Collectors.groupingBy(GroomingServiceOperationDTO::getStaffId))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                    if (petDetail.getServicePrice().compareTo(BigDecimal.ZERO) == 0) {
                        return BigDecimal.ZERO::multiply;
                    }
                    return number -> entry.getValue().stream()
                            .map(GroomingServiceOperationDTO::getPrice)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .multiply(number)
                            .divide(petDetail.getServicePrice(), 2, HALF_UP);
                }));
    }

    // addon/service/
    private void calculateStaffExpectedAmount(
            List<ReportWebApptPetDetail> petDetails,
            Map<Integer, List<StaffRevenueDetail>> staffRevenueMap,
            EmployeeReportCalculateDTO calculateDTO) {
        // 遍历 petDetail，计算每个员工的收入
        for (ReportWebApptPetDetail petDetail : petDetails) {
            List<StaffRevenueDetail> employees = staffRevenueMap.get(petDetail.getId());
            Map<Integer, Function<BigDecimal, BigDecimal>> staffPriceRateMap = getStaffPriceRateMap(petDetail);
            var amountUtil = new AllotmentAmountUtil(employees, staffPriceRateMap);

            BigDecimal curServicePrice = petDetail.getServicePrice();
            // 当前 petDetail 是 multi staff 时，对应 staff 分配到的 tips
            Map<Integer, BigDecimal> petDetailStaffTips =
                    calculateDTO.getTipsMap().getOrDefault(petDetail.getId(), Map.of());
            // 当前 petDetail 总的 tips
            BigDecimal curDiscount = calculateDTO.getDiscountMap().getOrDefault(petDetail.getId(), BigDecimal.ZERO);
            BigDecimal curExpectedTax = calculateDTO.getTaxMap().getOrDefault(petDetail.getId(), BigDecimal.ZERO);
            boolean isAddon = ReportUtil.isAddon(petDetail.getServiceType());
            // 应收金额：service/add-on 金额、tax、tips、discount
            if (isAddon) {
                amountUtil.allocate(
                        curServicePrice, StaffRevenueDetail::getAddonPrice, StaffRevenueDetail::setAddonPrice);
            } else {
                amountUtil.allocate(
                        curServicePrice, StaffRevenueDetail::getServicePrice, StaffRevenueDetail::setServicePrice);
            }
            amountUtil.allocate(curExpectedTax, StaffRevenueDetail::getTotalTax, StaffRevenueDetail::setTotalTax);

            for (StaffRevenueDetail employee : employees) {
                employee.setTotalTips(AmountUtils.sum(
                        employee.getTotalTips(),
                        calculateDTO
                                .getTipsMap()
                                .getOrDefault(petDetail.getId(), new HashMap<>())
                                .getOrDefault(employee.getStaffId(), BigDecimal.ZERO)));
                // 设置当前订单总的 tips
                employee.setOrderTotalTips(calculateDTO.getOrderTotalTips());
            }

            amountUtil.allocate(curDiscount, StaffRevenueDetail::getDiscount, StaffRevenueDetail::setDiscount);

            // payroll 相关金额统计
            Integer serviceId = petDetail.getServiceId();
            if (calculateDTO.isAptFinish()) {
                // add to expectedMap, for commission calculation
                amountUtil.allocate(
                        curServicePrice,
                        staffRevenue -> (isAddon
                                        ? staffRevenue.getAddonExpectedMap()
                                        : staffRevenue.getServiceExpectedMap())
                                .get(serviceId),
                        (staffRevenue, price) -> (isAddon
                                        ? staffRevenue.getAddonExpectedMap()
                                        : staffRevenue.getServiceExpectedMap())
                                .put(serviceId, price));

                // 统计总的 finish service 应收金额
                if (isAddon) {
                    amountUtil.allocate(
                            curServicePrice,
                            StaffRevenueDetail::getFinishExpectedAddonPrice,
                            StaffRevenueDetail::setFinishExpectedAddonPrice);
                } else {
                    amountUtil.allocate(
                            curServicePrice,
                            StaffRevenueDetail::getFinishExpectedServicePrice,
                            StaffRevenueDetail::setFinishExpectedServicePrice);
                }

                // tips 不按比例分配，按 splitTips 分配
                var idToStaff = employees.stream()
                        .collect(Collectors.toMap(StaffRevenueDetail::getStaffId, Function.identity()));
                petDetailStaffTips.forEach((staffId, tips) -> {
                    StaffRevenueDetail employee = idToStaff.get(staffId);
                    if (employee == null) {
                        return;
                    }
                    employee.setFinishExpectedTips(AmountUtils.sum(employee.getFinishExpectedTips(), tips));
                });
            }
        }
    }

    private void calculateStaffCollectedAmount(
            List<ReportWebApptPetDetail> petDetails,
            Map<Integer, List<StaffRevenueDetail>> staffRevenueMap,
            EmployeeReportCalculateDTO calculateDTO) {
        BigDecimal servicePaidAmount = calculateDTO.getStaffServicePaidAmount();
        BigDecimal servicePaidExcludeConFee = calculateDTO.getStaffServicePaidExcludeConFee();
        BigDecimal serviceRefundedAmount = calculateDTO.getStaffServiceRefundedAmount();
        BigDecimal serviceExpectedRevenue = calculateDTO.getStaffServiceExpectedRevenue();
        BigDecimal serviceUnpaidAmount = calculateDTO.getStaffServiceUnpaidAmount();
        // 保存需要按服务金额比例分配的金额，确保最后所有相加等于总数
        BigDecimal tempTotalUnpaid = BigDecimal.ZERO;
        BigDecimal tempTotalPaidAmount = BigDecimal.ZERO;
        BigDecimal tempTotalPaidExcludeConFee = BigDecimal.ZERO;
        BigDecimal tempTotalRefund = BigDecimal.ZERO;
        boolean isAptPaid = calculateDTO.isAptPaid();

        for (int i = 0, size = petDetails.size(); i < size; i++) {
            var petDetail = petDetails.get(i);
            BigDecimal curServicePrice = petDetail.getServicePrice();
            List<StaffRevenueDetail> employees = staffRevenueMap.get(petDetail.getId());
            Map<Integer, Function<BigDecimal, BigDecimal>> staffPriceRateMap = getStaffPriceRateMap(petDetail);
            var amountUtil = new AllotmentAmountUtil(employees, staffPriceRateMap);

            BigDecimal curPaidAmount = BigDecimal.ZERO;
            BigDecimal curPaidExcludeConFee = BigDecimal.ZERO;
            BigDecimal curRefundedAmount = BigDecimal.ZERO;
            BigDecimal curNetSaleRevenue = BigDecimal.ZERO;
            boolean isAddon = ReportUtil.isAddon(petDetail.getServiceType());
            // 当前 petDetail 总的 tips
            // 当前 petDetail 是 multi staff 时，对应 staff 分配到的 tips
            Map<Integer, BigDecimal> petDetailStaffTips =
                    calculateDTO.getTipsMap().getOrDefault(petDetail.getId(), Map.of());
            BigDecimal curExpectedTips = petDetailStaffTips.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal curDiscount = calculateDTO.getDiscountMap().getOrDefault(petDetail.getId(), BigDecimal.ZERO);
            BigDecimal curExpectedTax = calculateDTO.getTaxMap().getOrDefault(petDetail.getId(), BigDecimal.ZERO);
            BigDecimal curServiceExpectedRev =
                    curServicePrice.add(curExpectedTips).add(curExpectedTax).subtract(curDiscount);

            // unpaid revenue
            BigDecimal unpaidRevenue = BigDecimal.ZERO;
            if (!AmountUtils.isNullOrZero(serviceExpectedRevenue)) {
                if (i != size - 1) {
                    unpaidRevenue = serviceUnpaidAmount
                            .multiply(curServiceExpectedRev)
                            .divide(serviceExpectedRevenue, 2, HALF_UP);
                    tempTotalUnpaid = AmountUtils.sum(tempTotalUnpaid, unpaidRevenue);
                } else {
                    // 最后一个服务不按比例分配，用总数减去之前的总和以保证各个服务相加等于总数
                    unpaidRevenue = AmountUtils.subtract(serviceUnpaidAmount, tempTotalUnpaid);
                }
                amountUtil.allocate(
                        unpaidRevenue, StaffRevenueDetail::getUnpaidRevenue, StaffRevenueDetail::setUnpaidRevenue);
            }
            if (!isAptPaid) {
                // 未支付时不计算实收金额
                continue;
            }

            // 1.实收金额、退款金额
            if (!AmountUtils.isNullOrZero(serviceExpectedRevenue)) {
                if (i != size - 1) {
                    curPaidAmount = servicePaidAmount
                            .multiply(curServiceExpectedRev)
                            .divide(serviceExpectedRevenue, 2, HALF_UP);
                    tempTotalPaidAmount = AmountUtils.sum(tempTotalPaidAmount, curPaidAmount);

                    curPaidExcludeConFee = servicePaidExcludeConFee
                            .multiply(curServiceExpectedRev)
                            .divide(serviceExpectedRevenue, 2, HALF_UP);
                    tempTotalPaidExcludeConFee = AmountUtils.sum(tempTotalPaidExcludeConFee, curPaidExcludeConFee);
                } else {
                    // 最后一个服务不按比例分配，用总数减去之前的总和以保证各个服务相加等于总数
                    curPaidAmount = AmountUtils.subtract(servicePaidAmount, tempTotalPaidAmount);
                    curPaidExcludeConFee = AmountUtils.subtract(servicePaidExcludeConFee, tempTotalPaidExcludeConFee);
                }

                if (!AmountUtils.isNullOrZero(serviceRefundedAmount)) {
                    if (i != size - 1) {
                        curRefundedAmount = serviceRefundedAmount
                                .multiply(curServiceExpectedRev)
                                .divide(serviceExpectedRevenue, 2, HALF_UP);
                        tempTotalRefund = AmountUtils.sum(tempTotalRefund, curRefundedAmount);
                    } else {
                        curRefundedAmount = AmountUtils.subtract(serviceRefundedAmount, tempTotalRefund);
                    }
                }
            }

            // staff分配到的总金额（应收：已支付+未支付）
            amountUtil.allocate(
                    AmountUtils.sum(curPaidExcludeConFee, unpaidRevenue),
                    StaffRevenueDetail::getExpectedRevenue,
                    StaffRevenueDetail::setExpectedRevenue);

            // 计算当前service实收金额
            CollectedPriceDTO collectedPriceDTO = calculateCollectedPrice(new CalCollectedPriceParams()
                    .setPaidAmount(curPaidExcludeConFee)
                    .setRefundAmount(curRefundedAmount)
                    .setExpectedServiceTax(curExpectedTax)
                    .setExpectedProductTax(BigDecimal.ZERO)
                    .setExpectedTips(curExpectedTips)
                    .setServiceDiscountAmount(curDiscount)
                    .setProductDiscountAmount(BigDecimal.ZERO)
                    .setTotalServiceSale(curServiceExpectedRev)
                    .setTotalProductSale(BigDecimal.ZERO));

            // 当前service分配到的实收金额（不包含tips、tax、discount）
            BigDecimal curServiceCollectedPrice = collectedPriceDTO.getCollectedServicePrice();
            // Net sale revenue
            curNetSaleRevenue = collectedPriceDTO.getNetSaleRevenue();
            amountUtil.allocate(
                    curNetSaleRevenue, StaffRevenueDetail::getNetSaleRevenue, StaffRevenueDetail::setNetSaleRevenue);

            if (isAddon) {
                amountUtil.allocate(
                        curServiceCollectedPrice,
                        StaffRevenueDetail::getCollectedAddonPrice,
                        StaffRevenueDetail::setCollectedAddonPrice);
            } else {
                amountUtil.allocate(
                        curServiceCollectedPrice,
                        StaffRevenueDetail::getCollectedServicePrice,
                        StaffRevenueDetail::setCollectedServicePrice);
            }

            // 当前 service 中每个 staff 的 collected tips
            Map<Integer, BigDecimal> curServicePerStaffCollectedTips = new HashMap<>();
            BigDecimal serviceCollectedTips = collectedPriceDTO.getCollectedTips();
            if (!AmountUtils.isNullOrZero(serviceCollectedTips) && !AmountUtils.isNullOrZero(curExpectedTips)) {
                BigDecimal tempTotalTips = BigDecimal.ZERO;
                for (int sequence = 0; sequence < employees.size(); sequence++) {
                    StaffRevenueDetail employee = employees.get(sequence);
                    // 当前 operation staff 应收 tips
                    BigDecimal curEmployeeExpectedTips = calculateDTO
                            .getTipsMap()
                            .getOrDefault(petDetail.getId(), new HashMap<>())
                            .getOrDefault(employee.getStaffId(), BigDecimal.ZERO);
                    BigDecimal curEmployeeCollectedTips;
                    if (sequence == employees.size() - 1) {
                        // 最后一个倒减
                        curEmployeeCollectedTips = serviceCollectedTips.subtract(tempTotalTips);
                    } else {
                        // 根据 operation staff 应收 tips 在 service 总应收 tips 中占比计算 collected tips
                        curEmployeeCollectedTips = serviceCollectedTips
                                .multiply(curEmployeeExpectedTips)
                                .divide(curExpectedTips, 2, HALF_UP);
                        tempTotalTips = tempTotalTips.add(curEmployeeCollectedTips);
                    }

                    curServicePerStaffCollectedTips.put(employee.getStaffId(), curEmployeeCollectedTips);
                    employee.setCollectedTips(AmountUtils.sum(employee.getCollectedTips(), curEmployeeCollectedTips));
                }
            }

            amountUtil.allocate(
                    collectedPriceDTO.getCollectedServiceTax(),
                    StaffRevenueDetail::getCollectedTax,
                    StaffRevenueDetail::setCollectedTax);
            amountUtil.allocate(
                    curPaidAmount, StaffRevenueDetail::getTotalPayment, StaffRevenueDetail::setTotalPayment);
            amountUtil.allocate(
                    curRefundedAmount, StaffRevenueDetail::getTotalRefund, StaffRevenueDetail::setTotalRefund);

            BigDecimal remainServiceCollectedPrice = curServiceCollectedPrice,
                    remainCollectedServiceTax = collectedPriceDTO.getCollectedServiceTax(),
                    remainCollectedDiscount = curDiscount;
            for (int sequence = 0; sequence < employees.size(); sequence++) {
                StaffRevenueDetail employee = employees.get(sequence);
                // 再计算一次 collectedServicePrice、collectedTax、collectedDiscount
                BigDecimal collectedServicePrice, collectedTax, collectedDiscount;
                if (sequence == employees.size() - 1) {
                    collectedServicePrice = remainServiceCollectedPrice;
                    collectedTax = remainCollectedServiceTax;
                    collectedDiscount = remainCollectedDiscount;
                } else {
                    collectedServicePrice =
                            staffPriceRateMap.get(employee.getStaffId()).apply(curServiceCollectedPrice);
                    remainServiceCollectedPrice = remainServiceCollectedPrice.subtract(collectedServicePrice);

                    collectedTax = staffPriceRateMap
                            .get(employee.getStaffId())
                            .apply(collectedPriceDTO.getCollectedServiceTax());
                    remainCollectedServiceTax = remainCollectedServiceTax.subtract(collectedTax);

                    collectedDiscount =
                            staffPriceRateMap.get(employee.getStaffId()).apply(curDiscount);
                    remainCollectedDiscount = remainCollectedDiscount.subtract(collectedDiscount);
                }
                // 根据 collectedServicePrice + collectedTax + collectedTips - collectedDiscount 计算
                // curCollectedRevenue，并累加到 employee.setCollectedRevenue
                BigDecimal collectedTips =
                        curServicePerStaffCollectedTips.getOrDefault(employee.getStaffId(), BigDecimal.ZERO);
                BigDecimal collectedRevenue = collectedServicePrice
                        .add(collectedTax)
                        .add(collectedTips)
                        .subtract(collectedDiscount);
                employee.setCollectedRevenue(AmountUtils.sum(employee.getCollectedRevenue(), collectedRevenue));
            }

            // payroll 相关金额统计
            // add to collectedMap, for commission calculation
            amountUtil.allocate(
                    curServiceCollectedPrice,
                    staffRevenue -> {
                        Map<Integer, BigDecimal> collectedMap =
                                isAddon ? staffRevenue.getAddonCollectedMap() : staffRevenue.getServiceCollectedMap();
                        return collectedMap.get(petDetail.getServiceId());
                    },
                    (staffRevenue, price) -> {
                        Map<Integer, BigDecimal> collectedMap =
                                isAddon ? staffRevenue.getAddonCollectedMap() : staffRevenue.getServiceCollectedMap();
                        collectedMap.put(petDetail.getServiceId(), price);
                    });
        }
    }

    public List<StaffRevenueDetail> calculateServiceItems(
            GroomingReportWebAppointment appt, EmployeeReportCalculateDTO calculateDTO) {
        var staffPetDetails = appt.getPetDetails().stream()
                .filter(k -> CommonUtil.isNormal(k.getStaffId()))
                .toList();
        Map<Integer, List<StaffRevenueDetail>> staffRevenueMap =
                ReportBeanUtil.buildStaffRevenueDetails(appt, staffPetDetails);
        calculateStaffExpectedAmount(staffPetDetails, staffRevenueMap, calculateDTO);
        calculateStaffCollectedAmount(staffPetDetails, staffRevenueMap, calculateDTO);
        return staffRevenueMap.values().stream().flatMap(Collection::stream).toList();
    }

    public List<StaffRevenueDetail> calculateProductItems(
            GroomingReportWebAppointment appt,
            List<MoeGroomingInvoiceItem> invoiceItems,
            EmployeeReportCalculateDTO calculateDTO) {
        invoiceItems.stream()
                .filter(item -> Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_PRODUCT.getType())
                        && CommonUtil.isNormal(item.getStaffId()))
                .forEach(item -> {
                    List<String> products = getStaffProducts(item.getStaffId(), invoiceItems);
                    StaffRevenueDetail staffRevenue =
                            ReportBeanUtil.buildStaffRevenueDetail(item.getStaffId(), appt, products);
                    calculateProductItemV2(staffRevenue, item, calculateDTO);
                });
        return List.of();
    }

    private void calculateProductItemV2(
            StaffRevenueDetail employee, MoeGroomingInvoiceItem item, EmployeeReportCalculateDTO calculateDTO) {
        BigDecimal productPaidAmount = calculateDTO.getProductPaidExcludeConFee();
        BigDecimal productUnpaidAmount = calculateDTO.getProductUnpaidAmount();
        BigDecimal productRefundedAmount = calculateDTO.getProductRefundedAmount();
        BigDecimal productExpectedRevenue = calculateDTO.getProductExpectedRevenue();

        // product 应收金额: tax, discount, productPrice
        BigDecimal curExpectedTax = item.getTaxAmount();
        BigDecimal curDiscount = item.getDiscountAmount();
        BigDecimal curExpectedProductRevenue = item.getTotalSalePrice().add(curExpectedTax);

        employee.setProductPrice(AmountUtils.sum(employee.getProductPrice(), item.getTotalListPrice()));
        employee.setTotalTax(AmountUtils.sum(employee.getTotalTax(), item.getTaxAmount()));
        employee.setDiscount(AmountUtils.sum(employee.getDiscount(), item.getDiscountAmount()));

        BigDecimal unpaidRevenue = BigDecimal.ZERO;
        // 未收金额
        if (!AmountUtils.isNullOrZero(productExpectedRevenue)) {
            unpaidRevenue =
                    productUnpaidAmount.multiply(curExpectedProductRevenue).divide(productExpectedRevenue, 2, HALF_UP);
            employee.setUnpaidRevenue(AmountUtils.sum(employee.getUnpaidRevenue(), unpaidRevenue));
        }

        // product 实收金额: paid, refunded, collected, tax, discount, productPrice
        BigDecimal curProductRevenue = BigDecimal.ZERO;
        BigDecimal curPaidAmount = BigDecimal.ZERO;
        BigDecimal curRefundedAmount = BigDecimal.ZERO;
        BigDecimal curNetSaleRevenue = BigDecimal.ZERO;
        // 未支付时不需要计算实收
        if (!calculateDTO.isAptPaid()) {
            return;
        }

        if (!AmountUtils.isNullOrZero(productExpectedRevenue)) {
            curPaidAmount =
                    productPaidAmount.multiply(curExpectedProductRevenue).divide(productExpectedRevenue, 2, HALF_UP);
            curRefundedAmount = productRefundedAmount
                    .multiply(curExpectedProductRevenue)
                    .divide(productExpectedRevenue, 2, HALF_UP);
            curProductRevenue = curPaidAmount.subtract(curRefundedAmount);
        }

        // staff分配到的总金额（应收：已支付+未支付）
        employee.setExpectedRevenue(AmountUtils.sum(employee.getExpectedRevenue(), curPaidAmount, unpaidRevenue));
        // net sale revenue
        curNetSaleRevenue = curNetSaleRevenue.subtract(curRefundedAmount);
        employee.setNetSaleRevenue(AmountUtils.sum(employee.getNetSaleRevenue(), curNetSaleRevenue));

        BigDecimal curCollectedTax;
        if (curProductRevenue.compareTo(curExpectedTax) > 0) {
            curCollectedTax = curExpectedTax;
        } else {
            curCollectedTax = curProductRevenue;
        }
        BigDecimal curCollectedProductPrice =
                curProductRevenue.subtract(curCollectedTax).add(curDiscount);

        employee.setCollectedTax(AmountUtils.sum(employee.getCollectedTax(), curCollectedTax));
        employee.setDiscount(AmountUtils.sum(employee.getDiscount(), curDiscount));
        employee.setCollectedProductPrice(
                AmountUtils.sum(employee.getCollectedProductPrice(), curCollectedProductPrice));
        // net sale revenue
        curNetSaleRevenue =
                curCollectedProductPrice.subtract(curDiscount); // net sale revenue = collectedProductPrice - discount

        employee.setTotalPayment(AmountUtils.sum(employee.getTotalPayment(), curPaidAmount));
        employee.setTotalRefund(AmountUtils.sum(employee.getTotalRefund(), curRefundedAmount));
        employee.setCollectedRevenue(AmountUtils.sum(employee.getCollectedRevenue(), curProductRevenue));
        employee.setNetSaleRevenue(AmountUtils.sum(employee.getNetSaleRevenue(), curNetSaleRevenue));
    }

    private Map<Integer, List<MoeGroomingInvoiceItem>> buildInvoiceItemMap(
            List<GroomingReportWebAppointment> appointments) {
        Map<Integer, List<MoeGroomingInvoiceItem>> invoiceItemMap = new HashMap<>();
        for (GroomingReportWebAppointment appointment : appointments) {
            invoiceItemMap.put(appointment.getId(), appointment.getInvoiceItems());
        }
        return invoiceItemMap;
    }
}
