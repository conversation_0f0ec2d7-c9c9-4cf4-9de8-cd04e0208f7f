package com.moego.server.grooming.config;

import com.moego.lib.common.util.ThreadPoolUtil;
import com.moego.server.grooming.properties.ClientPortalProperties;
import java.time.Duration;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/11/07
 */
@Configuration(proxyBeanMethods = false)
@RequiredArgsConstructor
public class ClientPortalConfiguration {

    private final ClientPortalProperties properties;

    public static final String CLIENT_PORTAL_EXECUTOR_SERVICE = "clientPortalExecutorService";

    @Bean(name = CLIENT_PORTAL_EXECUTOR_SERVICE)
    public ExecutorService clientPortalExecutorService() {
        return ThreadPoolUtil.newExecutorService(
                properties.getCorePoolSize(),
                properties.getMaximumPoolSize(),
                Duration.ofSeconds(properties.getAliveTimeSeconds()),
                properties.getQueueCapacity(),
                "moego-client-portal-",
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
