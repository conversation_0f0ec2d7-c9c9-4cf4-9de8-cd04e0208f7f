package com.moego.server.grooming.service.intuit.helper;

import com.intuit.ipp.data.Invoice;
import com.intuit.ipp.data.Line;
import com.intuit.ipp.data.LineDetailTypeEnum;
import com.intuit.ipp.data.LinkedTxn;
import com.intuit.ipp.data.Payment;
import com.intuit.ipp.data.PaymentMethod;
import com.intuit.ipp.data.ReferenceType;
import com.intuit.ipp.data.TxnTypeEnum;
import com.moego.common.utils.QuickBooksDateUtils;
import com.moego.server.business.dto.MoeBusinessDto;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.springframework.util.StringUtils;

public class PaymentHelper {

    public static Payment getPaymentById(String qbPaymentId) {
        Payment payment = new Payment();
        payment.setId(qbPaymentId);
        return payment;
    }

    public static Payment getPaymentByPaymentDto(
            Invoice invoice,
            ReferenceType customerType,
            BigDecimal amount,
            Long createTime,
            ReferenceType depositAccount,
            PaymentMethod paymentMethod) {
        Payment payment = new Payment();
        payment.setCustomerRef(customerType);
        // 添加line
        List<Line> lineList = new ArrayList<Line>();
        List<LinkedTxn> linkedTxnList = new ArrayList<LinkedTxn>();
        LinkedTxn linkedTxn = new LinkedTxn();
        linkedTxn.setTxnId(invoice.getId());
        linkedTxn.setTxnType(TxnTypeEnum.INVOICE.value());
        linkedTxnList.add(linkedTxn);
        Line line1 = new Line();
        line1.setAmount(amount);
        line1.setDetailType(LineDetailTypeEnum.ACCOUNT_BASED_EXPENSE_LINE_DETAIL);
        line1.setLinkedTxn(linkedTxnList);
        lineList.add(line1);
        if (depositAccount != null) {
            payment.setDepositToAccountRef(depositAccount);
        }
        payment.setPaymentMethodRef(PaymentMethodHelper.getPaymentMethodRef(paymentMethod));
        payment.setLine(lineList);
        payment.setTotalAmt(amount);
        if (createTime != null && createTime != 0) {
            payment.setTxnDate(QuickBooksDateUtils.getDateTimeByLong(createTime * 1000));
        }
        return payment;
    }

    public static void paymentAddNoteByLocationInfo(Payment payment, String note, MoeBusinessDto info) {
        StringBuilder sb = new StringBuilder(note);
        if (Objects.nonNull(info) && StringUtils.hasText(info.getBusinessName())) {
            // result: "note     location: business name"
            sb.append("    location: ").append(info.getBusinessName());
        }
        paymentAddNote(payment, sb.toString());
    }

    public static void paymentAddNote(Payment payment, String note) {
        if (StringUtils.hasText(note)) {
            payment.setPrivateNote(note);
        }
    }

    public static void paymentAddDocNumber(Payment payment, String docNumber) {
        if (StringUtils.hasText(docNumber)) {
            payment.setDocNumber(docNumber);
        }
    }
}
