package com.moego.server.grooming.web;

import com.moego.common.distributed.LockManager;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.grooming.dto.BookOnlineDepositForClientDTO;
import com.moego.server.grooming.dto.BookOnlineLocationDTO;
import com.moego.server.grooming.dto.PreAuthAmountDTO;
import com.moego.server.grooming.dto.PrepayAmountDTO;
import com.moego.server.grooming.dto.ob.OBServiceListDto;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapper.po.BusinessCompanyPO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeBookOnlineGallery;
import com.moego.server.grooming.mapperbean.MoeBookOnlineProfile;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.server.grooming.params.PreAuthAmountParams;
import com.moego.server.grooming.params.PrepayAmountParams;
import com.moego.server.grooming.params.QueryServiceByPetIdsParams;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.MoeBookOnlineDepositService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import com.moego.server.grooming.web.dto.ob.GalleryDto;
import com.moego.server.grooming.web.dto.ob.GalleryListDto;
import com.moego.server.grooming.web.dto.ob.InfoDto;
import com.moego.server.grooming.web.dto.ob.ProfileDto;
import com.moego.server.grooming.web.dto.ob.QuestionListDto;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/grooming/bookOnline/client")
public class MoeGroomingBookOnlineClientController {

    @Autowired
    private MoeGroomingBookOnlineService moeGroomingBookOnlineService;

    @Autowired
    private GroomingServiceService groomingServiceService;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private MoeBookOnlineDepositService moeBookOnlineDepositService;

    @Autowired
    private LockManager moegoLockManager;

    @Autowired
    private OBLandingPageConfigService landingPageConfigService;

    @Autowired
    private FeatureFlagApi featureFlagApi;

    @Autowired
    private CompanyHelper companyHelper;

    /**
     * C端接口
     *
     * @param businessName 对应book_online_name字段
     * @return
     */
    @GetMapping("/gallery")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<GalleryListDto> getGallery(@RequestParam String businessName) {
        List<MoeBookOnlineGallery> galleryList = moeGroomingBookOnlineService.getGalleryByBookOnlineName(businessName);
        List<GalleryDto> clientGalleryList = new ArrayList<>();
        galleryList.forEach(gallery -> {
            GalleryDto tmpMap = new GalleryDto();
            tmpMap.setImageId(gallery.getId());
            tmpMap.setSort(gallery.getSort());
            tmpMap.setIsStar(gallery.getIsStar());
            tmpMap.setImagePath(gallery.getImagePath());
            clientGalleryList.add(tmpMap);
        });
        GalleryListDto result = new GalleryListDto();
        result.setList(clientGalleryList);
        return ResponseResult.success(result);
    }

    @GetMapping("/info")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<InfoDto> getInfo(@RequestParam String businessName) {
        return ResponseResult.success(moeGroomingBookOnlineService.getOBSettingByBookOnlineName(businessName));
    }

    @GetMapping("/profile")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<ProfileDto> getProfile(@RequestParam String businessName) {
        Integer businessId = moeGroomingBookOnlineService.getBusinessIdByBookOnlineName(businessName);
        ProfileDto result = new ProfileDto();
        result.setProfile(moeGroomingBookOnlineService.getProfileByBusinessId(businessId));
        return ResponseResult.success(result);
    }

    @Operation(summary = "获取商家对应公司下所有的location信息")
    @GetMapping("/company/locations")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<List<BookOnlineLocationDTO>> getRelevantBiz(@RequestParam String businessName) {
        Integer businessId = moeGroomingBookOnlineService.getBusinessIdByBookOnlineName(businessName);
        List<Integer> biz = iBusinessBusinessClient.getAllRelevantBusinessIds(businessId);
        List<BookOnlineLocationDTO> result = new ArrayList<>();
        for (Integer tmpBizId : biz) {
            BookOnlineLocationDTO currentLocation = new BookOnlineLocationDTO();
            MoeBookOnlineProfile profile = moeGroomingBookOnlineService.getProfileByBusinessId(tmpBizId);
            if (profile == null) {
                continue;
            }
            BeanUtils.copyProperties(profile, currentLocation);
            MoeBusinessBookOnline obInfo = moeGroomingBookOnlineService.getSettingInfoByBusinessId(tmpBizId);
            currentLocation.setBookOnlineName(obInfo.getBookOnlineName());
            currentLocation.setIsEnable(obInfo.getIsEnable());
            currentLocation.setIsAvailable(true);
            result.add(currentLocation);
        }
        return ResponseResult.success(result);
    }

    @GetMapping("/business/preference")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<MoeBusinessDto> getBusinessPreference(@RequestParam String businessName) {
        Integer businessId = moeGroomingBookOnlineService.getBusinessIdByBookOnlineName(businessName);
        MoeBusinessDto businessInfo = moeGroomingBookOnlineService.getBusinessInfo(businessId);
        businessInfo.setBookOnlineName(businessName);
        return ResponseResult.success(businessInfo);
    }

    @GetMapping("/question")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<QuestionListDto> getQuestionForClient(
            @RequestParam String businessName, @RequestParam Integer type) {
        Integer businessId = moeGroomingBookOnlineService.getBusinessIdByBookOnlineName(businessName);
        QuestionListDto result = new QuestionListDto();
        result.setQuestions(moeGroomingBookOnlineService.getQuestionsByBusinessId(businessId, type).stream()
                .filter(question -> question.getIsShow() == 1)
                .collect(Collectors.toList()));
        return ResponseResult.success(result);
    }

    /**
     * C 端查询 service服务
     * @param businessName book_online_name
     * @param queryServiceParams
     * @return
     */
    @PostMapping("/services")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<OBServiceListDto> queryBookOnlinePetServicesForClient(
            @RequestParam String businessName, @RequestBody QueryServiceByPetIdsParams queryServiceParams) {
        Integer businessId = moeGroomingBookOnlineService.getBusinessIdByBookOnlineName(businessName);
        queryServiceParams.setBusinessId(businessId);
        return ResponseResult.success(groomingServiceService.queryPetServicesForOBClient(queryServiceParams));
    }

    @PostMapping("/submit/check")
    @Auth(AuthType.ANONYMOUS)
    public Boolean checkSubmitInfo(
            @Deprecated @RequestParam(required = false) String businessName,
            @Valid @RequestBody BookOnlineSubmitParams submitParams,
            OBAnonymousParams anonymousParams) {
        MoeBusinessBookOnline obBusiness =
                moeGroomingBookOnlineService.getSettingInfoByBusinessId(toBusinessId(businessName, anonymousParams));
        submitParams.setBusinessId(obBusiness.getBusinessId());
        submitParams.setCompanyId(obBusiness.getCompanyId());

        // check ob config、apptTime
        moeGroomingBookOnlineService.submitParamsCheck(obBusiness, submitParams);

        // disable select time submit
        if (Objects.isNull(submitParams.getStaffId())
                || !StringUtils.hasText(submitParams.getAppointmentDate())
                || !StringUtils.hasText(submitParams.getPrepayGuid())) { // prepay guid 为空时跳过，prepay 过程才需要加锁
            return true;
        }

        // ob提交参数校验后，对staff的时间加锁，避免冲突 加锁时间：60秒
        String lockKey = moegoLockManager.getResourceKey(
                LockManager.OB_SUBMIT, submitParams.getStaffId().toString() + submitParams.getAppointmentDate());
        boolean lockResult = moegoLockManager.lockWithRetry(lockKey, submitParams.getPrepayGuid());
        if (!lockResult) {
            // 检查是否是相同的value，如果是则重新锁，时间刷新，前端在重试支付时可以复用上次的锁
            if (Objects.equals(submitParams.getPrepayGuid(), moegoLockManager.getLockValue(lockKey))) {
                moegoLockManager.refreshLockTime(lockKey);
                return true;
            }

            throw new CommonException(ResponseCodeEnum.APPOINTMENT_TIME_LOCKED);
        }

        return true;
    }

    @PostMapping("/prepay/amount")
    @Auth(AuthType.ANONYMOUS)
    public PrepayAmountDTO calculateServiceDeposit(
            @Deprecated @RequestParam(required = false) String businessName,
            @Valid @RequestBody PrepayAmountParams prepayAmountParams,
            OBAnonymousParams anonymousParams) {
        Integer businessId = toBusinessId(businessName, anonymousParams);

        if (featureFlagApi.isOn(FeatureFlags.DYNAMIC_DEPOSIT, buildFeatureFlagContextForDynamicDeposit(businessId))) {
            return moeGroomingBookOnlineService.calculateOBPrepayAmountForDynamicDeposit(
                    businessId, prepayAmountParams);
        } else {
            return moeGroomingBookOnlineService.calculateOBPrepayAmount(businessId, prepayAmountParams);
        }
    }

    private FeatureFlagContext buildFeatureFlagContextForDynamicDeposit(Integer businessId) {

        var company = companyHelper.mustGetCompany(companyHelper.mustGetCompanyId(businessId));

        var builder = FeatureFlagContext.builder();
        builder.company(company.getId());
        if (CommonUtil.isNormal(company.getEnterpriseId())) {
            builder.enterprise(company.getEnterpriseId());
        }

        return builder.build();
    }

    @PostMapping("/preauth/amount")
    @Auth(AuthType.ANONYMOUS)
    public PreAuthAmountDTO showServicePreAuth(
            @Valid @RequestBody PreAuthAmountParams prepayAmountParams, OBAnonymousParams anonymousParams) {
        Integer businessId = toBusinessId("", anonymousParams);
        return moeGroomingBookOnlineService.calculateOBPreAuthAmount(businessId, prepayAmountParams);
    }

    @GetMapping("/prepay/detail")
    @Auth(AuthType.ANONYMOUS)
    public BookOnlineDepositForClientDTO getOBDepositDetail(@RequestParam("guid") String guid) {
        MoeBookOnlineDeposit deposit = moeBookOnlineDepositService.getOBDepositByDeGuid(guid);
        if (deposit == null) {
            return null;
        }
        BookOnlineDepositForClientDTO depositDTO = new BookOnlineDepositForClientDTO();
        BeanUtils.copyProperties(deposit, depositDTO);
        depositDTO.setGuid(deposit.getGuid());
        return depositDTO;
    }

    private Integer toBusinessId(String businessName, OBAnonymousParams anonymousParams) {
        if (Objects.nonNull(anonymousParams.getName()) || Objects.nonNull(anonymousParams.getDomain())) {
            BusinessCompanyPO businessCompanyPO =
                    landingPageConfigService.getBusinessIdAndCompanyIdByAnonymous(anonymousParams);
            if (Objects.isNull(businessCompanyPO) || Objects.isNull(businessCompanyPO.getBusinessId())) {
                throw ExceptionUtil.bizException(Code.CODE_BOOK_ONLINE_SITE_NOT_FOUND);
            }
            return businessCompanyPO.getBusinessId();
        }
        if (StringUtils.hasText(businessName)) {
            return moeGroomingBookOnlineService.getBusinessIdByBookOnlineName(businessName);
        }
        throw ExceptionUtil.bizException(Code.CODE_BOOK_ONLINE_SITE_NOT_FOUND);
    }
}
