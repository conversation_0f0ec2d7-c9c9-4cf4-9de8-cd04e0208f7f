package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Service
@RequiredArgsConstructor
public class RecoveredRevenueMetricService implements IOBMetricsService {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;
    private final PetDetailMapperProxy petDetailMapper;

    @Override
    public Object sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        List<Integer> recoveredApptId = abandonRecordMapper.listRecoveredApptId(
                timeRangeDTO.businessId(), timeRangeDTO.startTime(), timeRangeDTO.endTime());
        if (CollectionUtils.isEmpty(recoveredApptId)) {
            return BigDecimal.ZERO;
        }
        BigDecimal sum = petDetailMapper.countApptRevenue(recoveredApptId);
        return Objects.nonNull(sum) ? sum : BigDecimal.ZERO;
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return "";
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.recovered_revenue;
    }
}
