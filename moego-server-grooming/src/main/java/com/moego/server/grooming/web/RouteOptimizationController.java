package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.service.RouteOptimizationService;
import com.moego.server.grooming.service.dto.RouteOptimizationRequest;
import com.moego.server.grooming.service.dto.RouteOptimizationResponse;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grooming")
@Slf4j
public class RouteOptimizationController {

    @Resource
    private RouteOptimizationService routeOptimizationService;

    @PostMapping("/appointment/route/optimization")
    @Auth(AuthType.BUSINESS)
    public RouteOptimizationResponse routeOptimization(@RequestBody @Valid RouteOptimizationRequest request) {
        return routeOptimizationService.getRouteOptimizationResult(
                AuthContext.get().companyId(), AuthContext.get().getBusinessId(), request);
    }

    @GetMapping("/appointment/route/optimization/available/times")
    @Auth(AuthType.BUSINESS)
    public Integer getRouteOptimizationAvailableTimesForSoloPlan(AuthContext authContext) {
        return routeOptimizationService.getRouteOptimizationAvailableTimesForSoloPlan(
                AuthContext.get().companyId());
    }
}
