/*
 * @since 2024-09-17 18:08:02
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.grooming.server;

import com.moego.server.grooming.api.ISmartSchedulingServiceBase;
import com.moego.server.grooming.dto.ss.SmartScheduleResultDto;
import com.moego.server.grooming.params.ss.SmartScheduleRequest;
import com.moego.server.grooming.service.SmartScheduleService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class SmartSchedulingServer extends ISmartSchedulingServiceBase {
    private final SmartScheduleService smartScheduleService;

    @Override
    public SmartScheduleResultDto smartSchedule(Integer businessId, SmartScheduleRequest request) {
        return smartScheduleService.smartSchedule2(businessId, request);
    }
}
