package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IBookOnlineStaffTimeServiceBase;
import com.moego.server.grooming.dto.BookOnlineStaffTimeDTO;
import com.moego.server.grooming.service.StaffTimeSyncService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class BookOnlineStaffTimeServer extends IBookOnlineStaffTimeServiceBase {

    private final StaffTimeSyncService staffTimeSyncService;

    @Override
    public List<BookOnlineStaffTimeDTO> listBookOnlineStaffTimeByBusinessId(Integer businessId) {
        return staffTimeSyncService.queryStaffTime(businessId, null).values().stream()
                .toList();
    }
}
