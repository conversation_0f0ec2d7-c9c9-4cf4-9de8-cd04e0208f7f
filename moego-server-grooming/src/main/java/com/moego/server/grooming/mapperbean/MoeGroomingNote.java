package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_note
 */
public class MoeGroomingNote {
    /**
     * Database Column Remarks:
     *   预约订单id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   客户id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     * Database Column Remarks:
     *   grooming_id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.grooming_id
     *
     * @mbg.generated
     */
    private Integer groomingId;

    /**
     * Database Column Remarks:
     *   note 类型
     *   1 alertNotes(特殊提示 只给business看  后续不关注 有时效性 (ob customer提交，后续customer无法查看))
     *   2 comment(订单描述 只给business看  后续持续review 有history ticket comments)
     *   3 cancel reason (只给business 看)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     * Database Column Remarks:
     *   创建人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.create_by
     *
     * @mbg.generated
     */
    private Integer createBy;

    /**
     * Database Column Remarks:
     *   最后修改人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.update_by
     *
     * @mbg.generated
     */
    private Integer updateBy;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   最后修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   is deleted
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.is_deleted
     *
     * @mbg.generated
     */
    private Boolean isDeleted;

    /**
     * Database Column Remarks:
     *   宠物 id, 0 表示 general comment
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.pet_id
     *
     * @mbg.generated
     */
    private Long petId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   note内容
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_note.note
     *
     * @mbg.generated
     */
    private String note;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.id
     *
     * @return the value of moe_grooming_note.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.id
     *
     * @param id the value for moe_grooming_note.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.business_id
     *
     * @return the value of moe_grooming_note.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.business_id
     *
     * @param businessId the value for moe_grooming_note.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.customer_id
     *
     * @return the value of moe_grooming_note.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.customer_id
     *
     * @param customerId the value for moe_grooming_note.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.grooming_id
     *
     * @return the value of moe_grooming_note.grooming_id
     *
     * @mbg.generated
     */
    public Integer getGroomingId() {
        return groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.grooming_id
     *
     * @param groomingId the value for moe_grooming_note.grooming_id
     *
     * @mbg.generated
     */
    public void setGroomingId(Integer groomingId) {
        this.groomingId = groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.type
     *
     * @return the value of moe_grooming_note.type
     *
     * @mbg.generated
     */
    public Byte getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.type
     *
     * @param type the value for moe_grooming_note.type
     *
     * @mbg.generated
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.create_by
     *
     * @return the value of moe_grooming_note.create_by
     *
     * @mbg.generated
     */
    public Integer getCreateBy() {
        return createBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.create_by
     *
     * @param createBy the value for moe_grooming_note.create_by
     *
     * @mbg.generated
     */
    public void setCreateBy(Integer createBy) {
        this.createBy = createBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.update_by
     *
     * @return the value of moe_grooming_note.update_by
     *
     * @mbg.generated
     */
    public Integer getUpdateBy() {
        return updateBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.update_by
     *
     * @param updateBy the value for moe_grooming_note.update_by
     *
     * @mbg.generated
     */
    public void setUpdateBy(Integer updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.create_time
     *
     * @return the value of moe_grooming_note.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.create_time
     *
     * @param createTime the value for moe_grooming_note.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.update_time
     *
     * @return the value of moe_grooming_note.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.update_time
     *
     * @param updateTime the value for moe_grooming_note.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.is_deleted
     *
     * @return the value of moe_grooming_note.is_deleted
     *
     * @mbg.generated
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.is_deleted
     *
     * @param isDeleted the value for moe_grooming_note.is_deleted
     *
     * @mbg.generated
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.pet_id
     *
     * @return the value of moe_grooming_note.pet_id
     *
     * @mbg.generated
     */
    public Long getPetId() {
        return petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.pet_id
     *
     * @param petId the value for moe_grooming_note.pet_id
     *
     * @mbg.generated
     */
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.company_id
     *
     * @return the value of moe_grooming_note.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.company_id
     *
     * @param companyId the value for moe_grooming_note.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_note.note
     *
     * @return the value of moe_grooming_note.note
     *
     * @mbg.generated
     */
    public String getNote() {
        return note;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_note.note
     *
     * @param note the value for moe_grooming_note.note
     *
     * @mbg.generated
     */
    public void setNote(String note) {
        this.note = note == null ? null : note.trim();
    }
}
