package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IBookOnlineQuestionServiceBase;
import com.moego.server.grooming.dto.GroomingQuestionDTO;
import com.moego.server.grooming.dto.ob.BookOnlineQuestionSaveDTO;
import com.moego.server.grooming.service.ob.OBQuestionService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/7/3
 */
@RestController
@RequiredArgsConstructor
public class BookOnlineQuestionServer extends IBookOnlineQuestionServiceBase {

    private final OBQuestionService questionService;

    @Override
    public List<GroomingQuestionDTO> listByCondition(ListByConditionParam param) {
        return questionService.getQuestionsByBusinessId(param.businessId(), param.type());
    }

    @Override
    public BookOnlineQuestionSaveDTO getCustomerLatestQuestionSave(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId) {
        return questionService.getCustomerLatestQuestionSave(businessId, customerId);
    }

    @Override
    public List<BookOnlineQuestionSaveDTO> listCustomerLatestQuestionSave(
            Integer businessId, List<Integer> customerIds) {
        return questionService.listCustomerLatestQuestionSave(businessId, customerIds);
    }

    @Override
    public Boolean upsertCustomerQuestionSave(
            @RequestBody BookOnlineQuestionSaveDTO questionSaveDTO,
            @RequestParam("autoAcceptConflict") Boolean autoAcceptConflict) {
        questionService.upsertCustomerLatestQuestionSave(questionSaveDTO, autoAcceptConflict);
        return Boolean.TRUE;
    }

    @Override
    public Boolean saveCustomerQuestionSave(@RequestBody BookOnlineQuestionSaveDTO questionSaveDTO) {
        return questionService.saveCustomerQuestionSave(questionSaveDTO);
    }
}
