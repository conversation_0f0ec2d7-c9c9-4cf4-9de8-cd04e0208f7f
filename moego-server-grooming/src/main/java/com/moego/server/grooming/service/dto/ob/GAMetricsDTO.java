package com.moego.server.grooming.service.dto.ob;

import com.google.analytics.data.v1beta.DateRange;
import com.google.analytics.data.v1beta.Dimension;
import com.google.analytics.data.v1beta.FilterExpression;
import com.google.analytics.data.v1beta.Metric;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 * @since 2023/6/7
 */
@Builder
public record GAMetricsDTO(
        List<Dimension> dimensions,
        FilterExpression dimensionFilter,
        List<Metric> metrics,
        List<DateRange> dateRanges) {}
