package com.moego.server.grooming.service.ob;

import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordPetMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Service
@RequiredArgsConstructor
public class OBAbandonUserService {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;
    private final MoeBookOnlineAbandonRecordPetMapper abandonRecordPetMapper;
}
