<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineAvailableStaffMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineAvailableStaff">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="by_working_hour_enable" jdbcType="TINYINT" property="byWorkingHourEnable" />
    <result column="by_slot_enable" jdbcType="TINYINT" property="bySlotEnable" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="sync_with_working_hour" jdbcType="TINYINT" property="syncWithWorkingHour" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, staff_id, by_working_hour_enable, by_slot_enable, create_time, update_time, 
    sync_with_working_hour, company_id
  </sql>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAvailableStaff">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_available_staff (business_id, staff_id, by_working_hour_enable, 
      by_slot_enable, create_time, update_time, 
      sync_with_working_hour, company_id)
    values (#{businessId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, #{byWorkingHourEnable,jdbcType=TINYINT}, 
      #{bySlotEnable,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, 
      #{syncWithWorkingHour,jdbcType=TINYINT}, #{companyId,jdbcType=BIGINT})
  </insert>

    <select id="selectByBusinessId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM moe_grooming.moe_book_online_available_staff
        WHERE business_id = #{businessId}
    </select>

    <select id="selectByBusinessIdAndStaffId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM moe_grooming.moe_book_online_available_staff
        WHERE business_id = #{businessId}
        AND staff_id = #{staffId}
    </select>

    <select id="selectByBusinessIdAndStaffIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM moe_grooming.moe_book_online_available_staff
        WHERE business_id = #{businessId}
        <if test="staffIds != null and staffIds.size &gt; 0">
            AND `staff_id` IN
            <foreach close=")" collection="staffIds" item="id" open="(" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <update id="updateByStaffId" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineAvailableStaff">
        UPDATE moe_grooming.moe_book_online_available_staff
        <set>
            <if test="byWorkingHourEnable != null">
                by_working_hour_enable = #{byWorkingHourEnable,jdbcType=TINYINT},
            </if>
            <if test="bySlotEnable != null">
                by_slot_enable = #{bySlotEnable,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="syncWithWorkingHour != null">
                sync_with_working_hour = #{syncWithWorkingHour,jdbcType=TINYINT},
            </if>
        </set>
        WHERE business_id = #{businessId,jdbcType=INTEGER}
        AND staff_id = #{staffId,jdbcType=INTEGER}
    </update>

  <select id="selectDisableSyncCountByBusinessId" resultType="java.lang.Integer">
    select count(*)
    from moe_book_online_available_staff
    where business_id = #{businessId}
    and sync_with_working_hour = 0
  </select>

  <update id="updateSyncByBusinessId">
    update moe_book_online_available_staff
    set sync_with_working_hour = #{syncWithWorkingHour,jdbcType=TINYINT}
    where business_id = #{businessId,jdbcType=INTEGER}
  </update>

</mapper>