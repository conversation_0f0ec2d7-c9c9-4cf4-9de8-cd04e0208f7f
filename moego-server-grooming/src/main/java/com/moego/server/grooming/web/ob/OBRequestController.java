package com.moego.server.grooming.web.ob;

import static com.moego.common.utils.PermissionUtil.VIEW_CLIENT_EMAIL;
import static com.moego.common.utils.PermissionUtil.VIEW_CLIENT_PHONE;
import static com.moego.common.utils.PermissionUtil.checkStaffPermissionsInfo;
import static com.moego.common.utils.PermissionUtil.emailConfusion;
import static com.moego.common.utils.PermissionUtil.phoneNumberConfusion;
import static com.moego.common.utils.PermissionUtil.verifyStaffPermissions;

import com.moego.common.dto.StaffPermissions;
import com.moego.common.utils.PermissionUtil;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.customer.api.ICustomerProfileRequestService;
import com.moego.server.grooming.params.ob.CustomerPetsParams;
import com.moego.server.grooming.params.ob.CustomerPetsUpdateParams;
import com.moego.server.grooming.service.ob.OBCustomerService;
import com.moego.server.grooming.service.ob.OBRequestService;
import com.moego.server.grooming.web.vo.ob.OBClientPetsDetailVO;
import com.moego.server.grooming.web.vo.ob.OBRequestDetailVO;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/6/30
 */
@Slf4j
@RestController
@RequestMapping("/grooming/ob/v2")
@RequiredArgsConstructor
public class OBRequestController {

    private final OBRequestService obRequestService;
    private final OBCustomerService customerService;
    private final ICustomerProfileRequestService profileRequestService;
    private final IBusinessStaffService businessStaffService;
    private final PermissionHelper permissionHelper;
    private final MigrateHelper migrateHelper;

    @GetMapping("/ob-request")
    @Auth(AuthType.BUSINESS)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_BOOKING_REQUEST_AND_WAITING_LIST)
    public OBRequestDetailVO getOBRequestDetail(@RequestParam("id") Integer apptId) {

        var context = AuthContext.get();
        var companyId = context.companyId();
        var businessId = context.getBusinessId();
        var staffId = context.getStaffId();
        var migrated = migrateHelper.isMigrate(context);

        StaffPermissions staffPermissions = null;
        if (!migrated) {
            staffPermissions = businessStaffService.getBusinessRoleByStaffId(staffId);
            verifyStaffPermissions(staffPermissions, PermissionUtil.CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST);
        }

        OBRequestDetailVO detail = obRequestService.getOBRequestDetail(apptId, businessId);

        if (migrated) {
            var hide = !permissionHelper.hasPermission(
                    companyId, staffId.longValue(), PermissionEnums.ACCESS_CLIENT_EMAIL_AND_PHONE_NUMBER);
            if (hide) {
                return detail.toBuilder()
                        .customer(detail.customer().toBuilder()
                                .phoneNumber(
                                        phoneNumberConfusion(detail.customer().phoneNumber()))
                                .email(emailConfusion(detail.customer().email()))
                                .build())
                        .build();
            }
            return detail;
        }

        boolean isHidePhoneNumber = !checkStaffPermissionsInfo(staffPermissions, VIEW_CLIENT_PHONE);
        boolean isHideEmail = !checkStaffPermissionsInfo(staffPermissions, VIEW_CLIENT_EMAIL);
        if (!isHidePhoneNumber && !isHideEmail) {
            return detail;
        }
        return detail.toBuilder()
                .customer(detail.customer().toBuilder()
                        .phoneNumber(
                                isHidePhoneNumber
                                        ? phoneNumberConfusion(detail.customer().phoneNumber())
                                        : detail.customer().phoneNumber())
                        .email(
                                isHideEmail
                                        ? emailConfusion(detail.customer().email())
                                        : detail.customer().email())
                        .build())
                .build();
    }

    @PostMapping("/client-pets-diff")
    @Auth(AuthType.BUSINESS)
    public OBClientPetsDetailVO getClientPetsDetail(@Valid @RequestBody CustomerPetsParams customerPetsParams) {
        OBClientPetsDetailVO detail = customerService.getClientPetsDetail(customerPetsParams);

        var context = AuthContext.get();
        var companyId = context.companyId();
        var staffId = context.getStaffId();

        var migrated = migrateHelper.isMigrate(context);
        if (migrated) {
            var hide = !permissionHelper.hasPermission(
                    companyId, staffId.longValue(), PermissionEnums.ACCESS_CLIENT_EMAIL_AND_PHONE_NUMBER);
            if (hide) {
                return detail.toBuilder()
                        .customer(detail.customer().toBuilder()
                                .phoneNumber(
                                        phoneNumberConfusion(detail.customer().phoneNumber()))
                                .email(emailConfusion(detail.customer().email()))
                                .build())
                        .build();
            }
            return detail;
        }

        StaffPermissions staffPermissions = businessStaffService.getBusinessRoleByStaffId(staffId);
        boolean isHidePhoneNumber = !checkStaffPermissionsInfo(staffPermissions, VIEW_CLIENT_PHONE);
        boolean isHideEmail = !checkStaffPermissionsInfo(staffPermissions, VIEW_CLIENT_EMAIL);
        if (!isHidePhoneNumber && !isHideEmail) {
            return detail;
        }
        return detail.toBuilder()
                .customer(detail.customer().toBuilder()
                        .phoneNumber(
                                isHidePhoneNumber
                                        ? phoneNumberConfusion(detail.customer().phoneNumber())
                                        : detail.customer().phoneNumber())
                        .email(
                                isHideEmail
                                        ? emailConfusion(detail.customer().email())
                                        : detail.customer().email())
                        .build())
                .requestCustomer(detail.requestCustomer().toBuilder()
                        .phoneNumber(
                                isHidePhoneNumber
                                        ? phoneNumberConfusion(
                                                detail.requestCustomer().phoneNumber())
                                        : detail.requestCustomer().phoneNumber())
                        .email(
                                isHideEmail
                                        ? emailConfusion(
                                                detail.requestCustomer().email())
                                        : detail.requestCustomer().email())
                        .build())
                .build();
    }

    @DeleteMapping("/client-pets-diff")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.DELETE,
            resourceType = ResourceType.CUSTOMER_PROFILE_REQUEST,
            resourceId = "#customerId")
    public Boolean deleteClientPetsRequest(@RequestParam("customerId") Integer customerId) {
        return profileRequestService.deleteCustomerProfileRequest(
                AuthContext.get().getBusinessId(), customerId);
    }

    @PutMapping("/client-pets")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.CUSTOMER_PROFILE_REQUEST,
            resourceId = "#customerPetsUpdateParams")
    public void updateClientPetsDetail(@RequestBody CustomerPetsUpdateParams customerPetsUpdateParams) {
        customerService.updateClientPetsDetail(customerPetsUpdateParams);
    }
}
