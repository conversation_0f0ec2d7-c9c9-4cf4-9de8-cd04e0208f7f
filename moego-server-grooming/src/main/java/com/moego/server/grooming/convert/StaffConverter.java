package com.moego.server.grooming.convert;

import com.moego.idl.models.organization.v1.StaffBasicView;
import com.moego.idl.models.organization.v1.StaffEmployeeCategory;
import com.moego.server.business.dto.MoeStaffDto;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface StaffConverter {

    StaffConverter INSTANCE = Mappers.getMapper(StaffConverter.class);

    @Mapping(
            target = "showOnCalendar",
            expression =
                    "java(staff.getIsShowOnCalendar() ? com.moego.common.enums.StaffEnum.SHOW_ON_CALENDAR_TRUE : com.moego.common.enums.StaffEnum.SHOW_ON_CALENDAR_FALSE)")
    @Mapping(
            target = "status",
            expression =
                    "java(staff.getIsDeleted() ? com.moego.common.enums.StaffEnum.STATUS_DELETE : com.moego.common.enums.StaffEnum.STATUS_NORMAL)")
    @Mapping(
            target = "bookOnlineAvailable",
            expression =
                    "java(staff.getIsBookOnlineAvailable() ? com.moego.common.enums.CustomerContactEnum.OB_AVAILABLE : com.moego.common.enums.CustomerContactEnum.OB_NOT_AVAILABLE)")
    MoeStaffDto toStaffDTO(StaffBasicView staff);

    List<MoeStaffDto> toStaffDTOList(List<StaffBasicView> input);

    default Byte toEmployeeCategory(StaffEmployeeCategory employeeCategory) {
        if (employeeCategory == null) {
            return null;
        }
        return (byte) employeeCategory.getNumber();
    }
}
