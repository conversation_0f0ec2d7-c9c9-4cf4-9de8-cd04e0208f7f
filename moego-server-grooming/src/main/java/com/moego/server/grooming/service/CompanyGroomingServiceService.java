package com.moego.server.grooming.service;

import com.moego.common.dto.CompanyFunctionControlDto;
import com.moego.common.enums.CompanyFunctionControlConst;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PageUtil;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.organization.v1.StaffEmployeeCategory;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.QueryCompaniesByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeBookOnlineServiceMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineStaffServiceMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceBreedBindingMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceCategoryMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceCoatBindingMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceLocationMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineService;
import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffService;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceCoatBinding;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceLocation;
import com.moego.server.grooming.mapstruct.ServiceLocationMapper;
import com.moego.server.grooming.params.PetServicePageParams;
import com.moego.server.grooming.service.dto.CompanyServiceOBSettingUpdateDto;
import com.moego.server.grooming.service.dto.CompanyServiceUpdateDto;
import com.moego.server.grooming.service.dto.ServiceGroupByCategoryDto;
import com.moego.server.grooming.service.dto.ServiceLocationOverrideDto;
import com.moego.server.grooming.service.dto.ServicePetTypeBreedsDTO;
import com.moego.server.grooming.service.dto.ServiceSaveDto;
import com.moego.server.grooming.service.dto.ServiceUpdateDto;
import com.moego.server.grooming.web.vo.PetServicePageVO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class CompanyGroomingServiceService {

    private final MoeBookOnlineStaffServiceMapper moeBookOnlineStaffServiceMapper;
    private final MoeGroomingServiceBreedBindingMapper groomingServiceBreedBindingMapper;
    private final MoeGroomingServiceLocationMapper moeGroomingServiceLocationMapper;
    private final AppointmentMapperProxy moeGroomingAppointmentMapper;
    private final MoeGroomingServiceMapper moeGroomingServiceMapper;
    private final MoeBookOnlineServiceMapper moeBookOnlineServiceMapper;
    private final MoeGroomingServiceCategoryMapper groomingServiceCategoryMapper;
    private final MoeGroomingServiceCoatBindingMapper groomingServiceCoatBindingMapper;
    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyServiceBlockingStub;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;
    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;

    private final MoeGroomingAppointmentService groomingAppointmentService;
    private final CompanyGroomingServiceQueryService companyGroomingServiceQueryService;
    private final CompanyHelper companyHelper;

    /**
     * 更新 location override 数据
     * bid service_id 有唯一索引，insert 的时候也有可能冲突
     *
     * @param companyId
     * @param locationOverrideList
     */
    public void updateServiceLocationOverride(
            Long companyId, Integer serviceId, List<ServiceLocationOverrideDto> locationOverrideList) {
        if (locationOverrideList == null) {
            return;
        }
        // 查询已存在的location override
        List<MoeGroomingServiceLocation> existingLocationOverrideList =
                moeGroomingServiceLocationMapper.selectByServiceId(companyId, serviceId, true);
        Set<Integer> updatedBidList = locationOverrideList.stream()
                .map(ServiceLocationOverrideDto::getBusinessId)
                .collect(Collectors.toSet());
        Set<Integer> exitingBidList = existingLocationOverrideList.stream()
                .map(MoeGroomingServiceLocation::getBusinessId)
                .collect(Collectors.toSet());
        Set<Integer> createdBidList = new HashSet<>(updatedBidList);
        createdBidList.removeAll(exitingBidList);
        Set<Integer> deletedBidList = new HashSet<>(exitingBidList);
        deletedBidList.removeAll(updatedBidList);
        Set<Integer> updateBidList = new HashSet<>(updatedBidList);
        updatedBidList.retainAll(exitingBidList);
        for (ServiceLocationOverrideDto serviceLocationOverrideDto : locationOverrideList) {
            MoeGroomingServiceLocation serviceLocations = new MoeGroomingServiceLocation();
            serviceLocations.setCompanyId(companyId);
            serviceLocations.setServiceId(serviceId);
            serviceLocations.setBusinessId(serviceLocationOverrideDto.getBusinessId());
            serviceLocations.setTaxId(serviceLocationOverrideDto.getTaxId()); // maybe null
            serviceLocations.setPrice(serviceLocationOverrideDto.getPrice()); // maybe null
            serviceLocations.setDuration(serviceLocationOverrideDto.getDuration()); // maybe null
            // 创建
            if (!CollectionUtils.isEmpty(createdBidList)
                    && createdBidList.contains(serviceLocationOverrideDto.getBusinessId())) {
                try {
                    moeGroomingServiceLocationMapper.insertSelective(serviceLocations);
                } catch (DuplicateKeyException e) { // 唯一索引冲突，转更新
                    log.warn(
                            "createServiceLocationOverride duplicate key exception, serviceLocations:{}",
                            JsonUtil.toJson(serviceLocations));
                    updatedServiceLocationOverride(serviceLocations);
                }
                continue;
            }
            // 更新
            if (!CollectionUtils.isEmpty(updateBidList)
                    && updateBidList.contains(serviceLocationOverrideDto.getBusinessId())) {
                updatedServiceLocationOverride(serviceLocations);
                continue;
            }
        }
        // 删除
        if (!CollectionUtils.isEmpty(deletedBidList)) {
            for (Integer bid : deletedBidList) {
                moeGroomingServiceLocationMapper.updateSetDeletedByUniqueIndex(companyId, bid, serviceId);
            }
        }
    }

    /**
     * @param serviceLocation
     */
    public void updatedServiceLocationOverride(MoeGroomingServiceLocation serviceLocation) {
        var slData = moeGroomingServiceLocationMapper.selectWithDeletedByBidServiceId(
                serviceLocation.getCompanyId(), serviceLocation.getBusinessId(), serviceLocation.getServiceId());
        serviceLocation.setId(slData.getId());
        serviceLocation.setCreateTime(slData.getCreateTime());
        serviceLocation.setUpdateTime(DateUtil.getNowDate());
        serviceLocation.setIsDeleted((byte) 0);
        // 更新必须全量更新，部分字段的 null 值有特殊含义
        moeGroomingServiceLocationMapper.updateByPrimaryKey(serviceLocation);
    }

    public Boolean checkServiceNameIsExistInCompanyId(Long companyId, String name, Byte type) {
        return moeGroomingServiceMapper.getServiceCountWithName(companyId, null, name, type, null) > 0;
    }

    public Boolean checkServiceNameIsExistInCompanyId(Long companyId, String name, Byte type, Integer serviceId) {
        return moeGroomingServiceMapper.getServiceCountWithName(companyId, null, name, type, serviceId) > 0;
    }

    public void checkServiceIdWithCidAndNameIsValid(Long companyId, Integer serviceId, String name) {
        MoeGroomingService service = moeGroomingServiceMapper.selectByPrimaryKey(serviceId); // name && auth check
        if (service == null || !service.getCompanyId().equals(companyId)) {
            throw ExceptionUtil.bizException(Code.CODE_SERVICE_NOT_FOUND);
        }
        // 检测名字是否重复
        if (name != null
                && !name.equals(service.getName())
                && checkServiceNameIsExistInCompanyId(service.getCompanyId(), name, service.getType(), serviceId)) {
            throw ExceptionUtil.bizException(Code.CODE_SERVICE_NAME_IS_EXIST);
        }
    }

    /**
     * 查询 service binding 关系，以 Map 返回，key 为 serviceId，值为 bindingList
     *
     * @param companyId
     * @return
     */
    public Map<Integer, List<MoeGroomingServiceBreedBinding>> getServiceBreedBindingMapByCid(Long companyId) {
        return groomingServiceBreedBindingMapper.selectByCidBidsAndServiceId(companyId, null, null).stream()
                .collect(Collectors.groupingBy(MoeGroomingServiceBreedBinding::getServiceId));
    }

    public Map<Integer, List<MoeGroomingServiceLocation>> queryServiceLocationMap(Long companyId) {
        return moeGroomingServiceLocationMapper.selectNoDeletedByCompanyId(companyId).stream()
                .collect(Collectors.groupingBy(MoeGroomingServiceLocation::getServiceId));
    }

    /**
     * 构建service返回对象
     *
     * @param service
     * @param serviceIdMap
     * @param serviceBreedMap
     * @return
     */
    public ServiceUpdateDto buildServiceDTO(
            MoeGroomingService service,
            Map<Integer, List<Integer>> serviceIdMap,
            Map<Integer, List<MoeGroomingServiceBreedBinding>> serviceBreedMap,
            Map<Integer, List<Integer>> serviceCoatBindingMap,
            Map<Integer, List<MoeGroomingServiceLocation>> serviceLocatiomMap,
            Map<Long, List<Long>> bundleServiceMap,
            Map<Integer, MoeBookOnlineService> obServiceMap) {
        ServiceUpdateDto serviceUpdateDto = new ServiceUpdateDto();
        BeanUtils.copyProperties(service, serviceUpdateDto);
        // weightRange, breeds, petSize设置
        serviceUpdateDto.setWeightRange(new BigDecimal[] {service.getWeightDownLimit(), service.getWeightUpLimit()});
        serviceUpdateDto.setCustomizedPetSizes(JsonUtil.toList(service.getAllowedPetSizeList(), Long.class));
        if (serviceBreedMap.containsKey(service.getId())) {
            List<ServicePetTypeBreedsDTO> petTypeBreeds = new ArrayList<>();
            serviceBreedMap.get(service.getId()).forEach(binding -> {
                ServicePetTypeBreedsDTO dto = new ServicePetTypeBreedsDTO();
                dto.setPetTypeId(binding.getPetTypeId());
                dto.setIsAll(binding.getIsAll());
                dto.setBreeds(JsonUtil.toList(binding.getBreedNameList(), String.class));
                petTypeBreeds.add(dto);
            });
            serviceUpdateDto.setCustomizedBreed(petTypeBreeds);
        }
        if (!CollectionUtils.isEmpty(serviceCoatBindingMap) && serviceCoatBindingMap.containsKey(service.getId())) {
            serviceUpdateDto.setCustomizedCoat(serviceCoatBindingMap.get(service.getId()));
        } else {
            serviceUpdateDto.setCustomizedCoat(List.of());
        }

        serviceUpdateDto.setServiceId(service.getId());
        // set for Online Booking  Groom 1553
        serviceUpdateDto.setStaffIdList(serviceIdMap.get(service.getId()));
        // location override 设置
        serviceUpdateDto.setIsAllLocation(service.getIsAllLocation());
        serviceUpdateDto.setLocationOverrideList(
                ServiceLocationMapper.INSTANCE.beanToDto(serviceLocatiomMap.get(service.getId())));
        serviceUpdateDto.setBundleServiceIdList(
                bundleServiceMap.getOrDefault(service.getId().longValue(), List.of()));

        if (obServiceMap.containsKey(service.getId())) {
            MoeBookOnlineService obService = obServiceMap.get(service.getId());
            serviceUpdateDto.setShowBasePrice(obService.getShowBasePrice());
            serviceUpdateDto.setBookOnlineAvailable(obService.getBookOnlineAvailable());
            serviceUpdateDto.setIsAllStaff(obService.getIsAllStaff());
            serviceUpdateDto.setAllowBookingWithOtherCareType(obService.getAllowBookingWithOtherCareType());
        }

        return serviceUpdateDto;
    }

    public boolean isServiceFilterAllowedForCid(Long companyId) {
        CompanyFunctionControlDto controlDto =
                iBusinessBusinessClient.queryCompanyPermissionByCompanyId(companyId.intValue());
        return (controlDto != null
                && Objects.equals(controlDto.getPremiumType(), CompanyFunctionControlConst.PREMIUM_TYPE_69));
    }

    public Integer createService(ServiceSaveDto saveDto, Long companyId) {

        MoeGroomingService service = new MoeGroomingService();
        BeanUtils.copyProperties(saveDto, service);
        service.setCompanyId(companyId);
        service.setCreateTime(DateUtil.get10Timestamp());
        service.setUpdateTime(DateUtil.get10Timestamp());
        service.setType(saveDto.getType());
        boolean isAllowServiceFilter = isServiceFilterAllowedForCid(companyId);
        if (isAllowServiceFilter && Objects.equals(saveDto.getWeightFilter(), ServiceEnum.FILTER_OPEN)) {
            // 只有打开weight_filter开关时才保存weight range
            service.setWeightDownLimit(saveDto.getWeightRange()[0]);
            service.setWeightUpLimit(saveDto.getWeightRange()[1]);
        }
        if (saveDto.getPetSizeFilter() != null && saveDto.getPetSizeFilter()) {
            service.setAllowedPetSizeList(JsonUtil.toJson(saveDto.getCustomizedPetSizes()));
        }
        boolean result = moeGroomingServiceMapper.insertSelective(service) > 0;
        Integer serviceId = service.getId();
        if (result) {
            MoeGroomingService serviceSort = new MoeGroomingService();
            serviceSort.setId(serviceId);
            serviceSort.setSort(serviceId);
            moeGroomingServiceMapper.updateByPrimaryKeySelective(serviceSort);

            // customizedBreed参数检查
            if (isAllowServiceFilter && Objects.equals(saveDto.getBreedFilter(), ServiceEnum.FILTER_OPEN)) {
                // breed filter打开时才更新binding breeding
                updateServiceAndBreedBindingForCid(companyId, serviceId, saveDto.getCustomizedBreed());
            }
            if (Objects.equals(saveDto.getCoatFilter(), ServiceEnum.FILTER_OPEN)) {
                MoeGroomingServiceCoatBinding coatBinding = new MoeGroomingServiceCoatBinding();
                coatBinding.setCompanyId(companyId);
                coatBinding.setServiceId(serviceId);
                coatBinding.setCoatIdList(JsonUtil.toJson(saveDto.getCustomizedCoat()));
                groomingServiceCoatBindingMapper.insertOrUpdate(coatBinding);
            }
            // locationOverrideList 保存
            updateServiceLocationOverride(companyId, serviceId, saveDto.getLocationOverrideList());
        }
        return serviceId;
    }

    public void updateCompanyService(Long companyId, CompanyServiceUpdateDto updateDto, Integer operatorId) {
        MoeGroomingService oldService =
                moeGroomingServiceMapper.selectByPrimaryKey(updateDto.getServiceId()); // auth check && update service
        if (oldService == null || !oldService.getCompanyId().equals(companyId)) {
            throw new BizException(Code.CODE_SERVICE_NOT_FOUND_VALUE);
        }

        // check if enterprise block the operation
        checkEnterpriseBlockServiceEdit(companyId, operatorId.longValue());

        Byte type = oldService.getType();
        // 检测名字是否重复
        if (updateDto.getName() != null
                && !updateDto.getName().equals(oldService.getName())
                && checkServiceNameIsExistInCompanyId(companyId, updateDto.getName(), type, updateDto.getServiceId())) {
            throw new BizException(Code.CODE_SERVICE_NAME_IS_EXIST_VALUE);
        }

        MoeGroomingService updateServiceBean = new MoeGroomingService();
        // 判断是否改动inactive的值，是的话需要修改sort值
        if (updateDto.getInactive() != null && !updateDto.getInactive().equals(oldService.getInactive())) {
            Integer maxSort = moeGroomingServiceMapper.getMaxSortByCategoryIdForCid(
                    companyId, oldService.getCategoryId(), updateDto.getInactive());
            updateServiceBean.setSort(maxSort == null ? 0 : maxSort + 1); // sort值设置成一个比较大的值，排在前面的位置
        }

        BeanUtils.copyProperties(updateDto, updateServiceBean);
        updateServiceBean.setId(updateDto.getServiceId());
        updateServiceBean.setUpdateTime(DateUtil.get10Timestamp());
        updateServiceBean.setType(oldService.getType());
        // 根据权限判断是否更新weight、breed配置
        boolean isAllowServiceFilter = isServiceFilterAllowedForCid(companyId);
        if (isAllowServiceFilter && Objects.equals(updateDto.getWeightFilter(), ServiceEnum.FILTER_OPEN)) {
            // 参数已校验，只有打开weight_filter开关时才保存weight range
            updateServiceBean.setWeightDownLimit(updateDto.getWeightRange()[0]);
            updateServiceBean.setWeightUpLimit(updateDto.getWeightRange()[1]);
        }
        if (updateDto.getPetSizeFilter() != null && updateDto.getPetSizeFilter()) {
            if (CollectionUtils.isEmpty(updateDto.getCustomizedPetSizes())) {
                throw new BizException(Code.CODE_PARAMS_ERROR_VALUE, "pet size list is empty");
            }
            updateServiceBean.setAllowedPetSizeList(JsonUtil.toJson(updateDto.getCustomizedPetSizes()));
        }
        updateServiceBean.setBusinessId(null);
        updateServiceBean.setCompanyId(companyId);

        // 对于 Multi-Location，Mobile 只更新 business Level 的信息，不会修改 Company Level 的 Price/duration/tax
        if (companyHelper.isMultiLocation(companyId)) {
            updateServiceBean.setPrice(null);
            updateServiceBean.setDuration(null);
            updateServiceBean.setTaxId(null);

            var location = moeGroomingServiceLocationMapper.selectWithDeletedByBidServiceId(
                    companyId, updateDto.getSingleLocationId(), updateDto.getServiceId());
            if (location != null && ServiceEnum.LOCATION_IS_DELETED_FALSE.equals(location.getIsDeleted())) {
                // 有 Location Override Rule，需要更新 Location Override 的信息
                var updateLocationBean = new MoeGroomingServiceLocation();
                updateLocationBean.setId(location.getId());
                updateLocationBean.setPrice(updateDto.getPrice());
                updateLocationBean.setDuration(updateDto.getDuration());
                updateLocationBean.setTaxId(updateDto.getTaxId());
                updateLocationBean.setUpdateTime(DateUtil.getNowDate());
                moeGroomingServiceLocationMapper.updateByPrimaryKeySelective(updateLocationBean);
            } else {
                // 没有 Location Override Rule，则对比此次要更新的值，与 Company Level 的值是否一致，不一致则新增 Location Override Rule
                if (!Objects.equals(updateDto.getPrice(), oldService.getPrice())
                        || !Objects.equals(updateDto.getDuration(), oldService.getDuration())
                        || !Objects.equals(updateDto.getTaxId(), oldService.getTaxId())) {
                    var newLocation = new MoeGroomingServiceLocation();
                    newLocation.setCompanyId(companyId);
                    newLocation.setServiceId(updateDto.getServiceId());
                    newLocation.setBusinessId(updateDto.getSingleLocationId());
                    newLocation.setPrice(
                            Objects.equals(updateDto.getPrice(), oldService.getPrice()) ? null : updateDto.getPrice());
                    newLocation.setDuration(
                            Objects.equals(updateDto.getDuration(), oldService.getDuration())
                                    ? null
                                    : updateDto.getDuration());
                    newLocation.setTaxId(
                            Objects.equals(updateDto.getTaxId(), oldService.getTaxId()) ? null : updateDto.getTaxId());
                    newLocation.setCreateTime(DateUtil.getNowDate());
                    newLocation.setUpdateTime(DateUtil.getNowDate());
                    moeGroomingServiceLocationMapper.insertSelective(newLocation);
                }
            }
        }

        moeGroomingServiceMapper.updateByPrimaryKeySelectiveWithBidCid(updateServiceBean);
        // customizedBreed参数检查
        if (isAllowServiceFilter && Objects.equals(updateDto.getBreedFilter(), ServiceEnum.FILTER_OPEN)) {
            // breed filter打开时才更新binding breeding
            updateServiceAndBreedBindingForCid(companyId, updateDto.getServiceId(), updateDto.getCustomizedBreed());
        }
        if (Objects.equals(updateDto.getCoatFilter(), ServiceEnum.FILTER_OPEN)) {
            MoeGroomingServiceCoatBinding coatBinding = new MoeGroomingServiceCoatBinding();
            coatBinding.setCompanyId(companyId);
            coatBinding.setServiceId(updateDto.getServiceId());
            coatBinding.setCoatIdList(JsonUtil.toJson(updateDto.getCustomizedCoat()));
            groomingServiceCoatBindingMapper.insertOrUpdate(coatBinding);
        }

        // 将最新的duration、price、tax应用到upcoming预约上
        if (updateDto.getApplyUpcomingAppt() != null && updateDto.getApplyUpcomingAppt()) {
            ThreadPool.execute(() -> {
                groomingAppointmentService.serviceApplyToUpcomingAppts(
                        updateDto.getSingleLocationId(),
                        updateDto.getServiceId(),
                        operatorId,
                        updateDto.getDuration(),
                        updateDto.getPrice());
            });
        }
    }

    /**
     * 更新service、breeds的绑定关系
     * <p>
     * 1.查询当前service的所有绑定关系
     * 2.新的绑定关系：已存在的更新，不存在的插入
     * 3.老的绑定关系没在新的：更新状态为invalid
     *
     * @param companyId
     * @param serviceId
     * @param updateBreedDTOs
     */
    private void updateServiceAndBreedBindingForCid(
            Long companyId, Integer serviceId, List<ServicePetTypeBreedsDTO> updateBreedDTOs) {
        // 1.查询当前service的所有绑定关系
        List<MoeGroomingServiceBreedBinding> existingRecords =
                groomingServiceBreedBindingMapper.selectByCidBidsAndServiceId(companyId, null, serviceId);
        Map<Integer, MoeGroomingServiceBreedBinding> existingBindingMap = existingRecords.stream()
                .collect(Collectors.toMap(
                        MoeGroomingServiceBreedBinding::getPetTypeId, Function.identity(), (b1, b2) -> b1));

        // 2.新的绑定关系：已存在的更新，不存在的插入
        List<MoeGroomingServiceBreedBinding> updateRecords = new ArrayList<>();
        List<MoeGroomingServiceBreedBinding> newRecords = new ArrayList<>();
        for (ServicePetTypeBreedsDTO dto : updateBreedDTOs) {
            MoeGroomingServiceBreedBinding saveRecord = new MoeGroomingServiceBreedBinding();
            saveRecord.setBreedNames(""); // 老字段填充空字符串表示不使用
            saveRecord.setStatus(ServiceEnum.SERVICE_BREED_BINDING_STATUS_NORMAL);
            if (dto.getIsAll() || CollectionUtils.isEmpty(dto.getBreeds())) {
                saveRecord.setIsAll(true);
                saveRecord.setBreedNameList(JsonUtil.toJson(List.of()));
            } else {
                saveRecord.setIsAll(false);
                saveRecord.setBreedNameList(JsonUtil.toJson(dto.getBreeds()));
            }
            // 使用remove方法获取，这样最后就会剩下不在新的记录里的
            MoeGroomingServiceBreedBinding existingRecord = existingBindingMap.remove(dto.getPetTypeId());
            long now = DateUtil.get10Timestamp();
            if (existingRecord != null) {
                saveRecord.setId(existingRecord.getId());
                saveRecord.setUpdateTime(now);
                updateRecords.add(saveRecord);
            } else {
                saveRecord.setCompanyId(companyId);
                saveRecord.setServiceId(serviceId);
                saveRecord.setPetTypeId(dto.getPetTypeId());
                saveRecord.setCreateTime(now);
                saveRecord.setUpdateTime(now);
                newRecords.add(saveRecord);
            }
        }
        // 3.老的绑定关系没在新的记录：更新状态为 invalid
        existingBindingMap.forEach((petTypeId, record) -> {
            MoeGroomingServiceBreedBinding updateRecord = new MoeGroomingServiceBreedBinding();
            updateRecord.setId(record.getId());
            updateRecord.setStatus(ServiceEnum.SERVICE_BREED_BINDING_STATUS_INVALID);
            updateRecord.setUpdateTime(DateUtil.get10Timestamp());
            updateRecords.add(updateRecord);
        });

        // 插入、更新操作
        if (!CollectionUtils.isEmpty(newRecords)) {
            groomingServiceBreedBindingMapper.batchInsertRecordsWithCid(newRecords);
        }
        if (!CollectionUtils.isEmpty(updateRecords)) {
            groomingServiceBreedBindingMapper.batchUpdateRecords(updateRecords);
        }
    }

    private void updateServiceAndStaffRelation(
            Long companyId, Integer businessId, Integer serviceId, List<Integer> staffIdList) {
        // 清空之前该serviceId下的映射关系
        moeBookOnlineStaffServiceMapper.deleteByServiceId(businessId, serviceId);
        if (staffIdList.isEmpty()) {
            return;
        }
        // 添加新的映射关系
        List<MoeBookOnlineStaffService> records = new ArrayList<>();
        staffIdList.forEach(staffId -> {
            MoeBookOnlineStaffService tmpRecord = new MoeBookOnlineStaffService();
            tmpRecord.setCompanyId(companyId);
            tmpRecord.setBusinessId(businessId);
            tmpRecord.setServiceId(serviceId);
            tmpRecord.setStaffId(staffId);
            tmpRecord.setCreateTime(DateUtil.get10Timestamp());
            tmpRecord.setUpdateTime(tmpRecord.getCreateTime());
            records.add(tmpRecord);
        });
        moeBookOnlineStaffServiceMapper.batchInsertSelective(records);
    }

    public PetServicePageVO getCompanyServiceByPage(Long companyId, PetServicePageParams params) {
        // 查询所有的service
        org.springframework.data.util.Pair<List<MoeGroomingService>, Pagination> paginationPair = PageUtil.selectPage(
                params.getPagination(),
                () -> moeGroomingServiceMapper.selectByCidBidTypeKeyword(
                        companyId, null, params.getType(), params.getInactive(), params.getKeyword()));
        List<ServiceUpdateDto> serviceList = paginationPair.getFirst().stream()
                .map(service -> {
                    ServiceUpdateDto serviceUpdateDto = new ServiceUpdateDto();
                    BeanUtils.copyProperties(service, serviceUpdateDto);
                    serviceUpdateDto.setServiceId(service.getId());
                    return serviceUpdateDto;
                })
                .toList();

        List<ServiceGroupByCategoryDto> categoryWithServiceList = new ArrayList<>();

        List<Integer> categoryIdList = serviceList.stream()
                .map(ServiceUpdateDto::getCategoryId)
                .distinct()
                .toList();
        if (categoryIdList.contains(0)) {
            // 处理默认的分组
            ServiceGroupByCategoryDto defaultCategoryService = new ServiceGroupByCategoryDto();
            defaultCategoryService.setType(params.getType());
            defaultCategoryService.setName(Strings.EMPTY);
            defaultCategoryService.setCategoryId(0);
            defaultCategoryService.setServiceList(serviceList.stream()
                    .filter(service -> Objects.equals(0, service.getCategoryId()))
                    .toList());
            categoryWithServiceList.add(defaultCategoryService);
        }

        // 处理非默认的分组
        // 查询service所有的category
        List<ServiceGroupByCategoryDto> namedCategoryServiceList =
                groomingServiceCategoryMapper
                        .selectByCompanyId(companyId, params.getType(), ServiceItemType.GROOMING.getNumber())
                        .stream()
                        .filter(category -> categoryIdList.contains(category.getId()))
                        .sorted(Comparator.comparing(MoeGroomingServiceCategory::getSort)
                                .reversed())
                        .map(category -> {
                            ServiceGroupByCategoryDto serviceGroupByCategoryDto = new ServiceGroupByCategoryDto();
                            serviceGroupByCategoryDto.setType(category.getType());
                            serviceGroupByCategoryDto.setName(category.getName());
                            serviceGroupByCategoryDto.setCategoryId(category.getId());
                            serviceGroupByCategoryDto.setServiceList(serviceList.stream()
                                    .filter(service -> Objects.equals(category.getId(), service.getCategoryId()))
                                    .toList());
                            return serviceGroupByCategoryDto;
                        })
                        .toList();
        categoryWithServiceList.addAll(namedCategoryServiceList);

        return new PetServicePageVO(paginationPair.getSecond(), categoryWithServiceList);
    }

    public void updateServiceObSetting(Long companyId, Integer locationId, CompanyServiceOBSettingUpdateDto updateDto) {

        var service = moeGroomingServiceMapper.selectByPrimaryKey(updateDto.getServiceId());
        if (service == null || !service.getCompanyId().equals(companyId)) {
            throw ExceptionUtil.bizException(Code.CODE_SERVICE_NOT_FOUND);
        }

        // save staff id list
        if (updateDto.getStaffIdList() != null) {
            // staffIdList 为null，表示不更新， 为empty，表示清空service和staff的关系
            updateServiceAndStaffRelation(companyId, locationId, updateDto.getServiceId(), updateDto.getStaffIdList());
        }
        // override service setting
        companyGroomingServiceQueryService.obServiceQuery(companyId, locationId, Collections.singletonList(service));

        // save ob setting
        var obSetting = moeBookOnlineServiceMapper.selectByUniqueIndexBidSid(companyId, locationId, service.getId());
        MoeBookOnlineService obSettingSaveBean = new MoeBookOnlineService();
        obSettingSaveBean.setCompanyId(companyId);
        obSettingSaveBean.setBusinessId(locationId);
        obSettingSaveBean.setServiceId(service.getId());
        obSettingSaveBean.setShowBasePrice(
                updateDto.getShowBasePrice() != null ? updateDto.getShowBasePrice() : service.getShowBasePrice());
        obSettingSaveBean.setBookOnlineAvailable(
                updateDto.getBookOnlineAvailable() != null
                        ? updateDto.getBookOnlineAvailable()
                        : service.getBookOnlineAvailable());
        obSettingSaveBean.setIsAllStaff(
                updateDto.getIsAllStaff() != null ? updateDto.getIsAllStaff() : service.getIsAllStaff());

        if (updateDto.getAllowBookingWithOtherCareType() != null) {
            obSettingSaveBean.setAllowBookingWithOtherCareType(updateDto.getAllowBookingWithOtherCareType());
        }

        obSettingSaveBean.setUpdateTime(DateUtil.get10Timestamp());
        if (obSetting == null) {
            obSettingSaveBean.setCreateTime(obSettingSaveBean.getUpdateTime());
            try {
                moeBookOnlineServiceMapper.insertSelective(obSettingSaveBean);
            } catch (DuplicateKeyException e) {
                // 唯一索引冲突，再查一次
                obSetting =
                        moeBookOnlineServiceMapper.selectByUniqueIndexBidSid(companyId, locationId, service.getId());
            }
        }
        if (obSetting != null) {
            obSettingSaveBean.setId(obSetting.getId());
            obSettingSaveBean.setCreateTime(obSetting.getCreateTime());
            moeBookOnlineServiceMapper.updateByPrimaryKeySelective(obSettingSaveBean);
        }
    }

    public void checkEnterpriseBlockServiceEdit(Long companyId, Long staffId) {
        var company = companyServiceBlockingStub
                .queryCompaniesByIds(QueryCompaniesByIdsRequest.newBuilder()
                        .addCompanyIds(companyId)
                        .build())
                .getCompanyIdToCompanyMap()
                .get(companyId);
        if (company == null) {
            throw ExceptionUtil.bizException(Code.CODE_COMPANY_NOT_FOUND);
        }
        if (company.getEnterpriseId() == 0) {
            return;
        }
        var isBlock = metadataServiceBlockingStub
                .extractValues(ExtractValuesRequest.newBuilder()
                        .setKeyName("block_franchisee_edit_service_setting")
                        .putOwners(OwnerType.OWNER_TYPE_ENTERPRISE_VALUE, company.getEnterpriseId())
                        .build())
                .getValues()
                .getOrDefault("block_franchisee_edit_service_setting", "false")
                .equals("true");
        if (isBlock) {
            var staff = staffServiceBlockingStub
                    .getStaffDetail(GetStaffDetailRequest.newBuilder()
                            .setCompanyId(companyId)
                            .setId(staffId)
                            .build())
                    .getStaff();
            // enterprise staff won't be blocked
            if (!staff.getEmployeeCategory().equals(StaffEmployeeCategory.ENTERPRISE_OWNER)
                    && !staff.getEmployeeCategory().equals(StaffEmployeeCategory.ENTERPRISE_STAFF)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "You are not allowed to edit service setting");
            }
        }
    }
}
