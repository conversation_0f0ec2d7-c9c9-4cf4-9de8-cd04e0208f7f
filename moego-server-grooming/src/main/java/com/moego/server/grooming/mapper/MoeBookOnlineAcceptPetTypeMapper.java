package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineAcceptPetType;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAcceptPetTypeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBookOnlineAcceptPetTypeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_accept_pet_type
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_accept_pet_type
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineAcceptPetType record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_accept_pet_type
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineAcceptPetType record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_accept_pet_type
     *
     * @mbg.generated
     */
    List<MoeBookOnlineAcceptPetType> selectByExample(MoeBookOnlineAcceptPetTypeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_accept_pet_type
     *
     * @mbg.generated
     */
    MoeBookOnlineAcceptPetType selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_accept_pet_type
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeBookOnlineAcceptPetType record,
            @Param("example") MoeBookOnlineAcceptPetTypeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_accept_pet_type
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeBookOnlineAcceptPetType record,
            @Param("example") MoeBookOnlineAcceptPetTypeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_accept_pet_type
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineAcceptPetType record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_accept_pet_type
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineAcceptPetType record);
}
