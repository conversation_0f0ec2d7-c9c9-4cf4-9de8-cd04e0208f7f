package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table auto_assign
 */
public class AutoAssign {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column auto_assign.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   appointment id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column auto_assign.appointment_id
     *
     * @mbg.generated
     */
    private Integer appointmentId;

    /**
     * Database Column Remarks:
     *   auto assign staff id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column auto_assign.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   auto assign appointment time, unit minute, 540 means 09:00
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column auto_assign.appointment_time
     *
     * @mbg.generated
     */
    private Integer appointmentTime;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column auto_assign.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column auto_assign.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column auto_assign.id
     *
     * @return the value of auto_assign.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column auto_assign.id
     *
     * @param id the value for auto_assign.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column auto_assign.appointment_id
     *
     * @return the value of auto_assign.appointment_id
     *
     * @mbg.generated
     */
    public Integer getAppointmentId() {
        return appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column auto_assign.appointment_id
     *
     * @param appointmentId the value for auto_assign.appointment_id
     *
     * @mbg.generated
     */
    public void setAppointmentId(Integer appointmentId) {
        this.appointmentId = appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column auto_assign.staff_id
     *
     * @return the value of auto_assign.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column auto_assign.staff_id
     *
     * @param staffId the value for auto_assign.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column auto_assign.appointment_time
     *
     * @return the value of auto_assign.appointment_time
     *
     * @mbg.generated
     */
    public Integer getAppointmentTime() {
        return appointmentTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column auto_assign.appointment_time
     *
     * @param appointmentTime the value for auto_assign.appointment_time
     *
     * @mbg.generated
     */
    public void setAppointmentTime(Integer appointmentTime) {
        this.appointmentTime = appointmentTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column auto_assign.create_time
     *
     * @return the value of auto_assign.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column auto_assign.create_time
     *
     * @param createTime the value for auto_assign.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column auto_assign.update_time
     *
     * @return the value of auto_assign.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column auto_assign.update_time
     *
     * @param updateTime the value for auto_assign.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
