package com.moego.server.grooming.convert;

import com.moego.common.enums.CustomerPetEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.agreement.v1.AgreementWithRecentRecordsView;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.business_customer.v1.BatchListBindingPetCodeResponse;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.utils.model.Pair;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.MoePetCodeInfoDTO;
import com.moego.server.customer.dto.PetBelongingDTO;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.printcard.FeedingInstruction;
import com.moego.server.grooming.dto.printcard.MedicationInstruction;
import com.moego.server.grooming.dto.printcard.PetBelonging;
import com.moego.server.grooming.dto.printcard.PrintCardAppointment;
import com.moego.server.grooming.dto.printcard.PrintCardCustomer;
import com.moego.server.grooming.dto.printcard.PrintCardHistory;
import com.moego.server.grooming.dto.printcard.PrintCardOperation;
import com.moego.server.grooming.dto.printcard.PrintCardPet;
import com.moego.server.grooming.dto.printcard.PrintCardServiceInfo;
import com.moego.server.grooming.dto.printcard.PrintPetCodeBinding;
import com.moego.server.grooming.dto.printcard.SplitLodgingInfo;
import com.moego.server.grooming.dto.printcard.StayCardAppointment;
import com.moego.server.grooming.mapperbean.AppointmentPetFeeding;
import com.moego.server.grooming.mapperbean.AppointmentPetMedication;
import com.moego.server.grooming.mapperbean.AppointmentPetScheduleSetting;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.utils.PetDetailUtil;
import jakarta.annotation.Nullable;
import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/8/23
 */
@Mapper()
public interface PrintCardConverter {
    PrintCardConverter INSTANCE = Mappers.getMapper(PrintCardConverter.class);

    String MALE = "Male";

    String FEMALE = "Female";

    default PrintCardPet toPrintCardPet(
            CustomerPetPetCodeDTO pet, Map<Long, List<String>> petNotes, List<PrintPetCodeBinding> petCodeBindings) {
        List<String> petCodeList = List.of();
        if (!CollectionUtils.isEmpty(pet.getMoePetCodeInfos())) {
            petCodeList = pet.getMoePetCodeInfos().stream()
                    .map(MoePetCodeInfoDTO::getDescription)
                    .toList();
        }
        Map<String, String> vaccineMap = Map.of();
        if (!CollectionUtils.isEmpty(pet.getVaccineList())) {
            vaccineMap = pet.getVaccineList().stream()
                    .collect(Collectors.toMap(
                            VaccineBindingRecordDto::getVaccineName,
                            VaccineBindingRecordDto::getExpirationDate,
                            (v1, v2) -> v1.compareTo(v2) > 0 ? v1 : v2));
        }

        return new PrintCardPet(
                pet.getPetName(),
                pet.getPetTypeId(),
                pet.getBreed(),
                toGenderName(pet.getGender()),
                pet.getAvatarPath(),
                pet.getWeight(),
                petNotes.getOrDefault(pet.getPetId().longValue(), List.of()),
                petCodeList,
                vaccineMap,
                pet.getVetName(),
                pet.getVetPhone(),
                pet.getHealthIssues(),
                pet.getPetAppearanceColor(),
                pet.getPetAppearanceNotes(),
                pet.getFixed(),
                pet.getBirthday(),
                petCodeBindings);
    }

    default String toGenderName(Integer gender) {
        return Objects.equals(CustomerPetEnum.GENDER_MALE.intValue(), gender) ? MALE : FEMALE;
    }

    default PrintCardCustomer toPrintCardCustomer(
            BusinessCustomerInfoModel customer,
            Map<Long, String> tagMap,
            Map<Long, List<String>> customerNotes,
            Map<Long, List<AgreementWithRecentRecordsView>> customerAgreements,
            String timezoneName) {
        List<String> tagList = List.of();
        if (!CollectionUtils.isEmpty(customer.getTagIdsList())) {
            tagList = customer.getTagIdsList().stream().map(tagMap::get).toList();
        }
        Map<String, String> agreementMap = customerAgreements.getOrDefault(customer.getId(), List.of()).stream()
                .collect(Collectors.toMap(
                        AgreementWithRecentRecordsView::getAgreementTitle,
                        agreement ->
                                DateUtil.convertTimeZoneBySeconds(agreement.getRecentSignedTime() / 1000, timezoneName),
                        (k1, k2) -> k1.compareTo(k2) > 0 ? k1 : k2));

        return new PrintCardCustomer(
                toCustomerName(customer),
                customer.getLastName(),
                customer.getPhoneNumber(),
                customer.getEmail(),
                tagList,
                customerNotes.getOrDefault(customer.getId(), List.of()),
                agreementMap);
    }

    default String toCustomerName(BusinessCustomerInfoModel customer) {
        if (customer != null) {
            return customer.getFirstName() + " " + customer.getLastName();
        }
        return "";
    }

    default PrintCardAppointment toPrintCardAppointment(
            MoeGroomingAppointment appointment, Map<Integer, List<MoeGroomingNote>> appointmentNotes) {
        var commentNote = toCommentNote(appointmentNotes.get(appointment.getId()));

        var alertNote = appointmentNotes.getOrDefault(appointment.getId(), List.of()).stream()
                .filter(note -> GroomingAppointmentEnum.NOTE_ALERT.equals(note.getType()))
                .findFirst()
                .orElse(null);

        return new PrintCardAppointment(
                appointment.getAppointmentDate(),
                appointment.getAppointmentStartTime(),
                commentNote,
                Objects.isNull(alertNote) ? "" : alertNote.getNote());
    }

    default List<PrintCardServiceInfo> toPrintCardServiceInfo(
            List<GroomingPetDetailDTO> petDetails,
            Map<Integer, MoeStaffDto> staffMap,
            Map<Integer, List<GroomingServiceOperationDTO>> serviceOperationMap,
            AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> lodgingMap,
            final List<BoardingSplitLodgingModel> boardingSplitLodgingModels) {

        List<Long> lodgingUnitIds;
        if (CollectionUtils.isEmpty(boardingSplitLodgingModels)) {
            Long lodgingIdFound = petDetails.stream()
                    .map(GroomingPetDetailDTO::getLodgingId)
                    .filter(lodgingId -> lodgingId > 0)
                    .findFirst()
                    .orElse(0L);
            lodgingUnitIds = List.of(lodgingIdFound);
        } else {
            lodgingUnitIds = boardingSplitLodgingModels.stream()
                    .map(BoardingSplitLodgingModel::getLodgingId)
                    .toList();
        }

        return petDetails.stream()
                .map(k -> toPrintCardServiceInfo(k, lodgingUnitIds, staffMap, serviceOperationMap, lodgingMap))
                .toList();
    }

    default PrintCardServiceInfo toPrintCardServiceInfo(
            GroomingPetDetailDTO petDetail,
            List<Long> lodgingUnitIds,
            Map<Integer, MoeStaffDto> staffMap,
            Map<Integer, List<GroomingServiceOperationDTO>> serviceOperationMap,
            AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> lodgingMap) {
        return new PrintCardServiceInfo(
                petDetail.getServiceName(),
                Math.toIntExact(petDetail.getStartTime()),
                Math.toIntExact(petDetail.getServiceTime()),
                petDetail.getServicePrice().toPlainString(),
                petDetail.getStaffId(),
                toStaffName(staffMap.get(petDetail.getStaffId())),
                petDetail.getEnableOperation(),
                Boolean.TRUE.equals(petDetail.getEnableOperation())
                        ? serviceOperationMap.getOrDefault(petDetail.getId(), List.of()).stream()
                                .map(operation -> new PrintCardOperation(
                                        operation.getStaffId(),
                                        toStaffName(staffMap.get(operation.getStaffId())),
                                        operation.getStartTime(),
                                        operation.getDuration(),
                                        operation.getPrice().toPlainString(),
                                        operation.getOperationName()))
                                .toList()
                        : List.of(),
                ServiceItemEnum.fromServiceItem(petDetail.getServiceItemType()),
                ServiceItemType.forNumber(petDetail.getServiceItemType()),
                toLodgingUnitName(lodgingMap.getKey(), lodgingUnitIds),
                petDetail.getServiceType(),
                petDetail.getPriceUnit());
    }

    default String toStaffName(MoeStaffDto staff) {
        if (staff != null) {
            return staff.getFirstName() + " " + staff.getLastName();
        }
        return "";
    }

    default String toLodgingName(
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap,
            Long lodgingUnitId) {
        LodgingUnitModel lodgingUnitModel = lodgingUnitMap.get(lodgingUnitId);
        if (lodgingUnitModel == null) {
            return "";
        }
        return lodgingUnitModel.getName() + " ("
                + lodgingTypeMap.get(lodgingUnitModel.getLodgingTypeId()).getName() + ")";
    }

    default String toLodgingTypeName(Map<Long, LodgingTypeModel> lodgingTypeMap, Long lodgingTypeId) {
        LodgingTypeModel lodgingTypeModel = lodgingTypeMap.get(lodgingTypeId);
        if (lodgingTypeModel == null) {
            return "";
        }
        return lodgingTypeModel.getName();
    }

    default String toLodgingUnitName(Map<Long, LodgingUnitModel> lodgingUnitMap, Long lodgingUnitId) {
        LodgingUnitModel lodgingUnitModel = lodgingUnitMap.get(lodgingUnitId);
        if (lodgingUnitModel == null) {
            return "";
        }
        return lodgingUnitModel.getName();
    }

    default String toLodgingUnitName(Map<Long, LodgingUnitModel> lodgingUnitMap, List<Long> lodgingUnitId) {
        return lodgingUnitId.stream()
                .map(lodgingUnitMap::get)
                .filter(Objects::nonNull)
                .map(LodgingUnitModel::getName)
                .collect(Collectors.joining(", "));
    }

    default StayCardAppointment toStayCardAppointment(
            MoeGroomingAppointment appointment,
            List<MoeGroomingPetDetail> petDetails,
            AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> lodgingMap,
            Map<Integer, List<MoeGroomingNote>> appointmentNotes,
            final List<BoardingSplitLodgingModel> splitLodgingModels,
            Map<Integer, String> serviceNameMap) {
        var commentNote = toCommentNote(appointmentNotes.get(appointment.getId()));
        var alertNote = appointmentNotes.getOrDefault(appointment.getId(), List.of()).stream()
                .filter(note -> GroomingAppointmentEnum.NOTE_ALERT.equals(note.getType()))
                .findFirst()
                .orElse(null);

        Long lodgingIdFound = petDetails.stream()
                .map(MoeGroomingPetDetail::getLodgingId)
                .filter(lodgingId -> lodgingId > 0)
                .findFirst()
                .orElse(0L);

        var splitLodgingInfos = splitLodgingModels.stream()
                .map(boardingSplitLodgingModel -> {
                    var splitLodgingInfo = new SplitLodgingInfo();
                    splitLodgingInfo.setStartDate(TimestampConverter.INSTANCE
                            .toLocalDateTime(boardingSplitLodgingModel.getStartDateTime())
                            .toLocalDate()
                            .toString());
                    splitLodgingInfo.setEndDate(TimestampConverter.INSTANCE
                            .toLocalDateTime(boardingSplitLodgingModel.getEndDateTime())
                            .toLocalDate()
                            .toString());
                    splitLodgingInfo.setLodgingUnitId(boardingSplitLodgingModel.getLodgingId());
                    splitLodgingInfo.setLodgingUnitName(
                            toLodgingUnitName(lodgingMap.getKey(), boardingSplitLodgingModel.getLodgingId()));
                    return splitLodgingInfo;
                })
                .toList();

        MoeGroomingPetDetail mainService = PetDetailUtil.getMainService(petDetails);
        var mainServiceName = Optional.ofNullable(mainService)
                .map(service -> serviceNameMap.getOrDefault(service.getServiceId(), ""))
                .orElse("");

        return new StayCardAppointment(
                appointment.getAppointmentDate(),
                appointment.getAppointmentEndDate(),
                appointment.getAppointmentStartTime(),
                appointment.getAppointmentEndTime(),
                toLodgingTypeName(
                        lodgingMap.getValue(),
                        lodgingMap
                                .getKey()
                                .getOrDefault(lodgingIdFound, LodgingUnitModel.getDefaultInstance())
                                .getLodgingTypeId()),
                toLodgingUnitName(lodgingMap.getKey(), lodgingIdFound),
                commentNote,
                Objects.isNull(alertNote) ? "" : alertNote.getNote(),
                mainServiceName,
                ServiceItemEnum.getMainServiceItemType(appointment.getServiceTypeInclude()),
                splitLodgingInfos);
    }

    @Nullable
    default PrintCardHistory toPrintCardHistory(
            Integer petId,
            MoeGroomingAppointment appointment,
            Map<Integer, List<MoeGroomingNote>> appointmentComments,
            Map<Integer, List<GroomingPetDetailDTO>> appointmentPetDetails,
            Map<Integer, List<GroomingServiceOperationDTO>> serviceOperations,
            Map<Integer, MoeStaffDto> staffMap) {
        List<GroomingPetDetailDTO> petDetails =
                appointmentPetDetails.getOrDefault(appointment.getId(), List.of()).stream()
                        .filter(k -> petId.equals(k.getPetId()))
                        .toList();
        if (CollectionUtils.isEmpty(petDetails)) {
            return null;
        }
        List<Integer> staffIds = petDetails.stream()
                .flatMap(service -> {
                    if (!serviceOperations.containsKey(service.getId())) {
                        return Stream.of(service.getStaffId());
                    }
                    return serviceOperations.get(service.getId()).stream().map(GroomingServiceOperationDTO::getStaffId);
                })
                .distinct()
                .toList();
        return new PrintCardHistory(
                appointment.getAppointmentDate(),
                appointment.getAppointmentStartTime(),
                petDetails.stream().map(GroomingPetDetailDTO::getServiceName).toList(),
                staffIds.stream()
                        .map(staffId -> PrintCardConverter.INSTANCE.toStaffName(staffMap.get(staffId)))
                        .toList(),
                appointmentComments.getOrDefault(appointment.getId(), List.of()).stream()
                        .limit(1)
                        .map(MoeGroomingNote::getNote)
                        .findFirst()
                        .orElse(""));
    }

    default List<FeedingInstruction> toFeedingInstruction(
            Long petId,
            Map<Long, List<AppointmentPetFeeding>> petFeedingInstructions,
            Map<Long, List<AppointmentPetScheduleSetting>> scheduleSettingMap) {
        return petFeedingInstructions.getOrDefault(petId, List.of()).stream()
                .map(feeding ->
                        toFeedingInstruction(feeding, scheduleSettingMap.getOrDefault(feeding.getId(), List.of())))
                .toList();
    }

    default FeedingInstruction toFeedingInstruction(
            AppointmentPetFeeding appointmentPetFeeding, List<AppointmentPetScheduleSetting> feedingScheduleList) {
        return new FeedingInstruction(
                appointmentPetFeeding.getFeedingAmount(),
                appointmentPetFeeding.getFeedingUnit(),
                appointmentPetFeeding.getFeedingType(),
                appointmentPetFeeding.getFeedingSource(),
                appointmentPetFeeding.getFeedingInstruction(),
                appointmentPetFeeding.getFeedingNote(),
                feedingScheduleList.stream()
                        .map(AppointmentPetScheduleSetting::getScheduleTime)
                        .toList(),
                feedingScheduleList.stream()
                        .map(schedule -> JsonUtil.toBean(schedule.getScheduleExtraJson(), Map.class)
                                .getOrDefault("label", "")
                                .toString())
                        .toList());
    }

    default List<MedicationInstruction> toMedicationInstruction(
            Long petId,
            Map<Long, List<AppointmentPetMedication>> petMedicationInstructions,
            Map<Long, List<AppointmentPetScheduleSetting>> scheduleSettingMap) {
        return petMedicationInstructions.getOrDefault(petId, List.of()).stream()
                .map(medication -> toMedicationInstruction(
                        medication, scheduleSettingMap.getOrDefault(medication.getId(), List.of())))
                .toList();
    }

    default MedicationInstruction toMedicationInstruction(
            AppointmentPetMedication appointmentPetMedication,
            List<AppointmentPetScheduleSetting> medicationScheduleList) {
        return new MedicationInstruction(
                appointmentPetMedication.getMedicationAmount(),
                appointmentPetMedication.getMedicationUnit(),
                appointmentPetMedication.getMedicationName(),
                appointmentPetMedication.getMedicationNote(),
                medicationScheduleList.stream()
                        .map(AppointmentPetScheduleSetting::getScheduleTime)
                        .toList(),
                medicationScheduleList.stream()
                        .map(schedule -> JsonUtil.toBean(schedule.getScheduleExtraJson(), Map.class)
                                .getOrDefault("label", "")
                                .toString())
                        .toList(),
                new MedicationInstruction.SelectedDate(
                        appointmentPetMedication.getDateType().getNumber(),
                        JsonUtil.toList(appointmentPetMedication.getSpecificDates(), String.class)));
    }

    default List<PetBelonging> toPetBelonging(Integer petId, Map<Integer, List<PetBelongingDTO>> petBelongingMap) {
        if (!petBelongingMap.containsKey(Math.toIntExact(petId))) {
            return List.of();
        }
        return petBelongingMap.get(petId).stream().map(this::toPetBelonging).toList();
    }

    default PetBelonging toPetBelonging(PetBelongingDTO petBelonging) {
        return new PetBelonging(petBelonging.getId(), petBelonging.getName(), petBelonging.getArea());
    }

    default Map<Long /*pet_id*/, List<PrintPetCodeBinding>> toPrintCardPetCodeBindingsViews(
            Pair<List<BatchListBindingPetCodeResponse.Binding>, List<BusinessPetCodeModel>> petCodePair) {

        Map<Long, BusinessPetCodeModel> petCodeMap = petCodePair.value().stream()
                .collect(Collectors.toMap(BusinessPetCodeModel::getId, Function.identity()));
        Map<Long, List<BatchListBindingPetCodeResponse.Binding>> petCodeBindingMap = petCodePair.key().stream()
                .collect(Collectors.groupingBy(BatchListBindingPetCodeResponse.Binding::getPetId));

        return petCodeBindingMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                        .map(binding -> toPrintCardPetCodeBindingsView(binding, petCodeMap))
                        .filter(Objects::nonNull)
                        .toList()));
    }

    default PrintPetCodeBinding toPrintCardPetCodeBindingsView(
            BatchListBindingPetCodeResponse.Binding binding, Map<Long, BusinessPetCodeModel> petCodeMap) {
        var petCode = petCodeMap.get(binding.getCodeId());
        if (petCode == null) {
            return null;
        }
        return PrintPetCodeBinding.builder()
                .petCodeId(petCode.getId())
                .description(petCode.getDescription())
                .abbreviation(petCode.getAbbreviation())
                .color(petCode.getColor())
                .uniqueComment(binding.getComment())
                .build();
    }

    default String toCommentNote(List<MoeGroomingNote> appointmentNotes) {
        if (CollectionUtils.isEmpty(appointmentNotes)) {
            return "";
        }
        return appointmentNotes.stream()
                .filter(note -> GroomingAppointmentEnum.NOTE_COMMENT.equals(note.getType()))
                .findFirst()
                .map(MoeGroomingNote::getNote)
                .orElse("");
    }
}
