package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeWaitList;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeWaitListMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_wait_list
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_wait_list
     *
     * @mbg.generated
     */
    int insert(MoeWaitList record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_wait_list
     *
     * @mbg.generated
     */
    int insertSelective(MoeWaitList record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_wait_list
     *
     * @mbg.generated
     */
    MoeWaitList selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_wait_list
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeWaitList record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_wait_list
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeWaitList record);

    MoeWaitList selectById(@Param("businessId") Long businessId, @Param("id") Long id);

    MoeWaitList selectByAppointmentId(@Param("businessId") Long businessId, @Param("appointmentId") Long appointmentId);

    List<MoeWaitList> selectByAppointmentIds(
            @Param("businessId") Long businessId, @Param("appointmentIds") List<Long> appointmentIds);

    List<MoeWaitList> selectByAppointmentIdsV2(@Param("appointmentIds") List<Long> appointmentIds);

    List<MoeWaitList> query(
            @Param("companyId") Long companyId,
            @Param("businessId") Long businessId,
            @Param("validTillGte") LocalDate validTillGte,
            @Param("validTillLt") LocalDate validTillLt,
            @Param("createGte") LocalDateTime createGte,
            @Param("createLte") LocalDateTime createLte,
            @Param("order") String order);

    int updateById(MoeWaitList record);

    int deleteById(
            @Param("businessId") Long businessId, @Param("id") Long id, @Param("deletedAt") LocalDateTime deletedAt);

    int deleteByAppointmentId(
            @Param("companyId") Long companyId,
            @Param("businessId") Long businessId,
            @Param("appointmentIds") List<Long> appointmentIds,
            @Param("deletedAt") LocalDateTime deletedAt);

    void batchInsertRecords(List<MoeWaitList> records);
}
