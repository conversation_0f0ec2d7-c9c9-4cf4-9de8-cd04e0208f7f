package com.moego.server.grooming.service;

import static com.moego.server.grooming.service.utils.OrderUtil.buildItemKey;

import com.google.protobuf.Timestamp;
import com.google.type.Decimal;
import com.google.type.Interval;
import com.moego.common.constant.CommonConstant;
import com.moego.common.constant.OrderConstant;
import com.moego.common.distributed.BWListManager;
import com.moego.common.dto.CustomerPaymentSummary;
import com.moego.common.enums.BookOnlineDepositConst;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.PaymentStripeStatus;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.order.DiscountType;
import com.moego.common.enums.order.ExtraFeeCollectType;
import com.moego.common.enums.order.ExtraFeeType;
import com.moego.common.enums.order.LineApplyType;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.AmountUtils;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.Pagination;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.grooming.v1.AppointmentSource;
import com.moego.idl.models.marketing.v1.DiscountCodeCompositeView;
import com.moego.idl.models.marketing.v1.DiscountCodeStatus;
import com.moego.idl.models.marketing.v1.DiscountCodeType;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineDiscountModel;
import com.moego.idl.models.order.v1.OrderLineExtraFeeModel;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import com.moego.idl.models.order.v1.OrderLineTaxModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderStatus;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.marketing.v1.DiscountCodeServiceGrpc;
import com.moego.idl.service.marketing.v1.GetDiscountCodeListInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeListOutput;
import com.moego.idl.service.membership.v1.GetRecommendBenefitUsageRequest;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc;
import com.moego.idl.service.membership.v1.UpsertRecommendBenefitUsageRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.order.v1.CreateOrderRequest;
import com.moego.idl.service.order.v1.CreateOrderResponse;
import com.moego.idl.service.order.v1.CustomerOrderServiceGrpc;
import com.moego.idl.service.order.v1.GetCustomerPaymentSummaryRequest;
import com.moego.idl.service.order.v1.GetCustomerPaymentSummaryResponse;
import com.moego.idl.service.order.v1.GetOrderItemDetailRequest;
import com.moego.idl.service.order.v1.GetOrderItemDetailResponse;
import com.moego.idl.service.order.v1.GetOrderListRequest;
import com.moego.idl.service.order.v1.GetOrderListResponse;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.GetRetailInvoicesRequest;
import com.moego.idl.service.order.v1.GetTipsOrderListRequest;
import com.moego.idl.service.order.v1.GetTipsOrderListResponse;
import com.moego.idl.service.order.v1.ListOrdersRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.order.v1.UpdateOrderIncrRequest;
import com.moego.idl.service.order.v1.UpdateOrderIncrResponse;
import com.moego.idl.service.order.v1.UpdateOrderRequest;
import com.moego.idl.service.order.v1.UpdateOrderResponse;
import com.moego.idl.service.order.v2.DepositRuleServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.client.IBusinessTaxClient;
import com.moego.server.business.dto.MoeBusinessTaxDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.activity.ActivityLogHelper;
import com.moego.server.grooming.dto.DepositDto;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.GroomingInvoiceDTO;
import com.moego.server.grooming.dto.GroomingPackageServiceInfoDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.InvoiceServiceDTO;
import com.moego.server.grooming.dto.InvoiceSummaryDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.dto.OrderInvoiceSummaryDTO;
import com.moego.server.grooming.dto.PackageServiceDTO;
import com.moego.server.grooming.dto.order.LineDiscountDTO;
import com.moego.server.grooming.dto.order.LineExtraFeeDTO;
import com.moego.server.grooming.dto.order.LineTaxDTO;
import com.moego.server.grooming.enums.OrderAction;
import com.moego.server.grooming.enums.WaitListStatusEnum;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.helper.OrderHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingInvoiceApplyPackageMapper;
import com.moego.server.grooming.mapper.MoeGroomingPackageMapper;
import com.moego.server.grooming.mapper.MoeGroomingPackageServiceMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackage;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackageExample;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.mapperbean.MoeGroomingPackage;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageExample;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageService;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageServiceExample;
import com.moego.server.grooming.mapstruct.GroomingPackageServiceConverter;
import com.moego.server.grooming.mapstruct.InvoiceItemConverter;
import com.moego.server.grooming.mapstruct.PetDetailConverter;
import com.moego.server.grooming.mapstruct.RefundChannelMapper;
import com.moego.server.grooming.params.BookOnlineDepositV2Params;
import com.moego.server.grooming.params.InvoiceAmountVo;
import com.moego.server.grooming.params.InvoiceValueType;
import com.moego.server.grooming.params.PetDetailParams;
import com.moego.server.grooming.params.SetPaymentParams;
import com.moego.server.grooming.service.dto.GetTipsInvoicesDTO;
import com.moego.server.grooming.service.dto.ServiceTaxUpdateForApptDto;
import com.moego.server.grooming.service.ob.OBDepositService;
import com.moego.server.grooming.service.utils.OrderUtil;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.dto.RefundChannelDTO;
import com.moego.server.retail.client.IRetailInvoiceClient;
import com.moego.server.retail.dto.PackageInfoDto;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * Order服务调用封装
 */
@Slf4j
@Service
public class OrderService {

    @Autowired
    private OrderServiceGrpc.OrderServiceBlockingStub orderClient;

    @Autowired
    private DepositRuleServiceGrpc.DepositRuleServiceBlockingStub depositRuleClient;

    @Autowired
    private CustomerOrderServiceGrpc.CustomerOrderServiceBlockingStub customerOrderClient;

    @Autowired
    private MoeGroomingAppointmentService appointmentService;

    @Autowired
    private AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentClient;

    @Autowired
    private MoePackageService moePackageService;

    @Autowired
    private MoeGroomingInvoiceApplyPackageMapper invoiceApplyPackageMapper;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private MoeBookOnlineDepositService bookOnlineDepositService;

    @Autowired
    private GroomingServiceService groomingServiceService;

    @Autowired
    private IBusinessTaxClient iBusinessTaxClient;

    @Autowired
    IPaymentPaymentClient iPaymentService;

    @Autowired
    private ICustomerCustomerClient iCustomerCustomerClient;

    @Autowired
    private IRetailInvoiceClient iRetailInvoiceClient;

    @Value("${order.offset.retail}")
    private Long retailOffset;

    @Value("${order.offset.new}")
    private Long newOffset;

    @Autowired
    private AppointmentServiceDetailService appointmentDetailService;

    @Autowired
    private SplitTipsService splitTipsService;

    @Resource
    private DiscountCodeServiceGrpc.DiscountCodeServiceBlockingStub discountCodeClient;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private BWListManager bwListManager;

    @Autowired
    private MoeGroomingPackageServiceMapper packageServiceMapper;

    @Autowired
    private MoeGroomingPackageMapper packageMapper;

    @Autowired
    private ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;

    @Autowired
    private OrderServiceGrpc.OrderServiceBlockingStub orderStub;

    @Autowired
    private MembershipServiceGrpc.MembershipServiceBlockingStub membershipService;

    @Autowired
    private OrderHelper orderHelper;

    @Autowired
    private MoeGroomingInvoiceApplyPackageMapper moeGroomingInvoiceApplyPackageMapper;

    @Autowired
    private NewOrderHelper newOrderHelper;

    @Autowired
    private OBDepositService obDepositService;

    /**
     * create order
     *
     * @param businessId
     * @param groomingId
     * @return
     */
    public Long saveOrderWhenCreatingAppointment(
            Long companyId, Integer businessId, Integer groomingId, Integer operatorId) {
        MoeGroomingInvoice invoice = prepareInvoiceByGroomingId(companyId, businessId, groomingId, operatorId);
        if (businessId != null && !businessId.equals(invoice.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.UNAUTHORIZED_ERROR, "Not invoice for given business.");
        }

        return createOrderForInvoice(companyId, invoice);
    }

    /**
     * 针对 new order flow：延迟建单，仅按需创建 deposit order
     */
    public long saveDepositOrderWhenCreatingAppointmentFromOB(
            long appointmentId, BookOnlineDepositV2Params obDepositParams) {
        if (obDepositParams.getDepositOrderPreview() != null) {
            return obDepositService
                    .createDepositOrder(appointmentId, obDepositParams.getDepositOrderPreview())
                    .getOrder()
                    .getId();
        }
        // New order flow depositOrderPreview 为 null 表示没有命中 deposit rules，不创建任何单子
        return 0L;
    }

    /**
     * 针对旧的 order flow：创建 sales order
     */
    public Long saveOrderWhenCreatingAppointmentFromOB(
            Long companyId, Integer businessId, Integer groomingId, Integer operatorId) {
        MoeGroomingInvoice invoice = prepareInvoiceByGroomingId(companyId, businessId, groomingId, operatorId);
        if (businessId != null && !businessId.equals(invoice.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.UNAUTHORIZED_ERROR, "Not invoice for given business.");
        }

        invoice.setSource(AppointmentSource.APPOINTMENT_SOURCE_OB);
        return createOrderForInvoice(companyId, invoice);
    }

    private MoeGroomingInvoice prepareInvoiceByGroomingId(
            Long companyId, Integer businessId, Integer groomingId, Integer operatorId) {
        GroomingInvoiceDTO invoiceDTO = appointmentService
                .queryGroomingInvoice(companyId, businessId, groomingId)
                .getData();
        if (invoiceDTO == null) {
            throw new CommonException(ResponseCodeEnum.GROOMING_NOT_FOUND);
        }
        // 查询 tax rate
        Map<Integer, MoeBusinessTaxDto> taxMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(invoiceDTO.getServiceInvoices())) {
            List<Integer> taxIds = invoiceDTO.getServiceInvoices().stream()
                    .map(InvoiceServiceDTO::getTaxId)
                    .filter(Objects::nonNull)
                    .toList();
            List<MoeBusinessTaxDto> taxList = iBusinessTaxClient.getTaxListByIds(taxIds);
            taxMap = taxList.stream().collect(Collectors.toMap(MoeBusinessTaxDto::getId, Function.identity()));
        }

        List<MoeGroomingInvoiceItem> invoiceItems = new ArrayList<>();
        for (InvoiceServiceDTO invoiceItemDto : invoiceDTO.getServiceInvoices()) {
            MoeGroomingInvoiceItem invoiceItem = new MoeGroomingInvoiceItem();
            BeanUtils.copyProperties(invoiceItemDto, invoiceItem);
            invoiceItem.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
            invoiceItem.setServiceUnitPrice(invoiceItemDto.getServicePrice());
            invoiceItem.setPetId(invoiceItemDto.getPetId());

            MoeBusinessTaxDto tax = taxMap.get(invoiceItem.getTaxId());
            if (tax != null && tax.getTaxRate() != null) {
                invoiceItem.setTaxRate(BigDecimal.valueOf(tax.getTaxRate()).setScale(4, RoundingMode.HALF_EVEN));
                invoiceItem.setTaxName(tax.getTaxName());
                invoiceItem.setTaxId(tax.getId());
            }

            invoiceItems.add(invoiceItem);
        }

        MoeGroomingInvoice invoice = new MoeGroomingInvoice();
        invoice.setBusinessId(invoiceDTO.getBusinessId());
        invoice.setGroomingId(invoiceDTO.getGroomingId());
        invoice.setCustomerId(invoiceDTO.getCustomerId());
        invoice.setType(InvoiceStatusEnum.TYPE_APPOINTMENT);
        invoice.setStatus(InvoiceStatusEnum.INVOICE_STATUS_CREATED);
        invoice.setCreateBy(operatorId);
        invoice.setUpdateBy(operatorId);
        invoice.setItems(invoiceItems);
        invoice.setSource(AppointmentSource.forNumber(invoiceDTO.getSource()));

        invoice.initAmount();

        return invoice;
    }

    public boolean setDiscountWithDiscountCodeId(Integer businessId, Integer invoiceId, Long discountCodeId) {
        // 检查invoice是否存在
        getOrderById(businessId, invoiceId);

        OrderLineDiscountModel.Builder discountBuilder = OrderLineDiscountModel.newBuilder()
                .setBusinessId(businessId)
                .setOrderId(invoiceId)
                .setApplyBy(0)
                .setDiscountCodeId(discountCodeId)
                .setApplyType(LineApplyType.TYPE_ALL.getType())
                .setDiscountType(DiscountType.AMOUNT.getType());

        // 增量更新order
        UpdateOrderIncrResponse response = orderClient.updateOrderIncremental(UpdateOrderIncrRequest.newBuilder()
                .setOrderId(invoiceId)
                .setOrder(OrderModel.newBuilder()
                        .setBusinessId(businessId)
                        .setSource(AppointmentSource.APPOINTMENT_SOURCE_OB)
                        .build())
                .addLineDiscounts(discountBuilder.build())
                .build());
        return response.getRecord() > 0;
    }

    public boolean setDiscount(Integer businessId, Integer operatorId, InvoiceAmountVo param) {
        // 检查invoice是否存在
        getOrderById(businessId, param.getInvoiceId());

        String discountType = param.getValueType();
        BigDecimal discountAmount = null;
        BigDecimal discountRate = null;
        if (InvoiceValueType.AMOUNT.check(param.getValueType())) {
            discountAmount = param.getValue();
        } else if (InvoiceValueType.PERCENTAGE.check(param.getValueType())) {
            discountRate = param.getValue();
        } else {
            throw new CommonException(ResponseCodeEnum.INVALID_VALUE_TYPE);
        }

        OrderLineDiscountModel.Builder discountBuilder = OrderLineDiscountModel.newBuilder()
                .setBusinessId(businessId)
                .setOrderId(param.getInvoiceId())
                .setApplyBy(operatorId)
                .setDiscountType(discountType)
                .setApplyType(LineApplyType.TYPE_SERVICE.getType()); // 老接口默认修改service类型

        Optional.ofNullable(discountAmount)
                .ifPresent(amount -> discountBuilder.setDiscountAmount(amount.doubleValue()));
        Optional.ofNullable(discountRate).ifPresent(rate -> discountBuilder.setDiscountRate(rate.doubleValue()));

        // 增量更新order
        UpdateOrderIncrResponse response = orderClient.updateOrderIncremental(UpdateOrderIncrRequest.newBuilder()
                .setOrderId(param.getInvoiceId())
                .addLineDiscounts(discountBuilder.build())
                .build());
        return response.getRecord() > 0;
    }

    public boolean setTips(Integer businessId, Integer operatorId, InvoiceAmountVo param) {
        // 检查invoice是否存在
        MoeGroomingInvoice invoice = getOrderById(businessId, param.getInvoiceId());

        // fix: ERP-975 Invoice tips 修改时增加版本校验 (目前仅通过 /grooming/invoice/set-tips 接口修改 tips 时会进行该校验)
        if (!PrimitiveTypeUtil.isNumberNullOrZero(param.getLastModifiedTime())
                && param.getLastModifiedTime() < invoice.getUpdateTime()) {
            throw new CommonException(ResponseCodeEnum.INVOICE_SET_TIPS_ERROR);
        }

        if (InvoiceValueType.AMOUNT.check(param.getValueType())
                || org.apache.commons.lang3.StringUtils.isBlank(param.getValueType())) {
            if (param.isOmitResult()) {
                // 内部调用时，增量记录tips
                invoice.setTipsAmount(param.getValue().add(invoice.getTipsAmount()));
            } else {
                invoice.setTipsAmount(param.getValue());
            }
        } else if (InvoiceValueType.PERCENTAGE.check(param.getValueType())) {
            invoice.setTipsRate(param.getValue());
        } else {
            throw new CommonException(ResponseCodeEnum.INVALID_VALUE_TYPE);
        }

        // ERP-1095 优化：统一成 amount 传递，无需 valueType 参数
        String valueType =
                !StringUtils.hasLength(param.getValueType()) ? InvoiceValueType.AMOUNT.value() : param.getValueType();
        invoice.setTipsType(valueType);

        OrderModel.Builder orderBuilder = OrderModel.newBuilder()
                .setBusinessId(invoice.getBusinessId())
                .setCustomerId(invoice.getCustomerId())
                .setStatus(invoice.getStatus())
                .setSourceType(invoice.getType())
                .setTipsAmount(invoice.getTipsAmount().doubleValue());

        Optional.ofNullable(operatorId).ifPresent(orderBuilder::setUpdateBy);

        // 增量更新order
        UpdateOrderIncrResponse response = orderClient.updateOrderIncremental(UpdateOrderIncrRequest.newBuilder()
                .setOrderId(param.getInvoiceId())
                .setOrder(orderBuilder.build())
                .build());
        var success = response.getRecord() > 0;
        if (success) {
            ActivityLogRecorder.record(
                    businessId, OrderAction.UPDATE_TIPS, ResourceType.ORDER, param.getInvoiceId(), param);
        }
        return success;
    }

    public InvoiceSummaryDTO applyPackage(
            Integer businessId, Integer orderId, Integer operatorId, Boolean checkRefund) {
        var orderDetail = getOrderDetail(null, orderId, null);
        var order = convertToInvoice(orderDetail);
        if (InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(order.getStatus())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_INVOICE_INVALID_STATUS, "Order is completed, cannot apply package.");
        }

        // membership info for apply packages
        // deletes all membership usages
        removeAllAppliedMemberships(businessId, operatorId, orderDetail);

        // 先删除当前 package 的占用记录
        // NOTE：需要先删除已占用 package，再计算可用的 package services
        deleteAppliedPackages(orderId);

        // order 中还需要的 并且可用的package service list
        var availablePackageServices = listAvailablePackageService(businessId, orderId);
        if (availablePackageServices.isEmpty()) {
            return convertToSummaryDTO(order);
        }

        var updateOrderItems = new ArrayList<MoeGroomingInvoiceItem>();
        var applyPackageServices = new ArrayList<MoeGroomingInvoiceApplyPackage>();

        for (var orderItem : sortOrderItemsByPrice(order.getItems())) { // 优先 apply 价格高的 order item
            if (!Objects.equals(orderItem.getType(), OrderItemType.ITEM_TYPE_SERVICE.getType())) {
                continue;
            }

            var availableList = findAvailablePackageServices(availablePackageServices, orderItem);
            if (availableList.isEmpty()) {
                continue;
            }

            int quantityNeeded = orderItem.getQuantity();
            int quantityDeducted = 0;

            for (var packageServiceToUse : availableList) {
                if (quantityNeeded <= 0) {
                    break;
                }

                int quantityToDeduct = Math.min(quantityNeeded, packageServiceToUse.getRemainingQuantity());

                packageServiceToUse.setRemainingQuantity(packageServiceToUse.getRemainingQuantity() - quantityToDeduct);

                quantityDeducted += quantityToDeduct;
                // 使用多个 package service 时，需要减去已有 package apply 的数量
                quantityNeeded = quantityNeeded - quantityToDeduct;

                // apply 记录
                applyPackageServices.add(buildApplyRecord(orderItem, packageServiceToUse, quantityToDeduct));
            }

            // 更新 order item 的 purchased quantity
            // 一个 order item 参与价格计算时使用的数量是 quantity - purchasedQuantity
            var e = InvoiceItemConverter.INSTANCE.entityToEntity(orderItem);
            e.setPurchasedQuantity(quantityDeducted);
            updateOrderItems.add(e);
        }

        var applyPackageLogDTO = ActivityLogHelper.getApplyPackageLogDTO(applyPackageServices);
        ActivityLogRecorder.recordRoot(OrderAction.APPLY_PACKAGE, ResourceType.ORDER, orderId, applyPackageLogDTO);

        // apply package后如果没有其它service了，则把service type的discount更新为0
        List<OrderLineDiscountModel> lineDiscounts =
                updateDiscountWhenApplyPackage(order, updateOrderItems, operatorId);

        // 增量更新order
        UpdateOrderIncrResponse orderIncrResponse = updateOrderIncremental(
                orderId,
                null,
                convertToInvoiceItem(order, updateOrderItems),
                null,
                CollectionUtils.isEmpty(lineDiscounts) ? null : lineDiscounts,
                null,
                checkRefund);
        // trigger refund
        RefundChannelDTO refundChannelDTO = RefundChannelMapper.INSTANCE.toDTO(orderIncrResponse.getRefundChannel());
        if (refundChannelDTO != null && !CollectionUtils.isEmpty(refundChannelDTO.getChannelList())) {
            InvoiceSummaryDTO invoiceSummaryDTO = convertToSummaryDTO(order);
            invoiceSummaryDTO.setRefundChannel(refundChannelDTO);
            return invoiceSummaryDTO;
        }

        // replace apply 记录
        replaceApplyRecords(orderId, applyPackageServices);

        return convertToSummaryDTO(getOrderWithItemsById(orderId));
    }

    private void deleteAppliedPackages(int orderId) {
        // 删除 apply 记录
        var updateBean = new MoeGroomingInvoiceApplyPackage();
        updateBean.setStatus(CommonConstant.DELETED);
        var exm = new MoeGroomingInvoiceApplyPackageExample();
        exm.createCriteria().andInvoiceIdEqualTo(orderId);
        invoiceApplyPackageMapper.updateByExampleSelective(updateBean, exm);
    }

    private List<MoeGroomingPackage> listGroomingPackage(Collection<Integer> packageIds) {
        var e = new MoeGroomingPackageExample();
        e.createCriteria().andIdIn(List.copyOf(packageIds)).andStatusEqualTo(CommonConstant.NORMAL);
        return packageMapper.selectByExample(e);
    }

    private static List<MoeGroomingInvoiceItem> sortOrderItemsByPrice(List<MoeGroomingInvoiceItem> items) {
        return items.stream()
                .sorted(Comparator.comparing(MoeGroomingInvoiceItem::getServiceUnitPrice)
                        .reversed())
                .toList();
    }

    private List<MoeGroomingPackageService> findAvailablePackageServices(
            List<MoeGroomingPackageService> applicablePackageServices, MoeGroomingInvoiceItem orderItem) {
        var availablePackageServices = applicablePackageServices.stream()
                .filter(e -> e.getRemainingQuantity() > 0)
                .filter(e -> e.getServices().stream()
                        .anyMatch(s -> Objects.equals(s.getServiceId(), orderItem.getServiceId())))
                .toList();
        if (availablePackageServices.size() <= 1) {
            return availablePackageServices;
        }

        // 优先使用“快到期”的 package
        var packageIds = availablePackageServices.stream()
                .map(MoeGroomingPackageService::getPackageId)
                .collect(Collectors.toSet());

        var packageIdToPackage = listGroomingPackage(packageIds).stream()
                .collect(Collectors.toMap(MoeGroomingPackage::getId, Function.identity(), (o, n) -> o));

        return sortPackageServicesByExpirationDate(availablePackageServices, packageIdToPackage);
    }

    static List<MoeGroomingPackageService> sortPackageServicesByExpirationDate(
            List<MoeGroomingPackageService> availablePackageServices,
            Map<Integer, MoeGroomingPackage> packageIdToPackage) {
        return availablePackageServices.stream()
                .sorted(Comparator.comparing(
                        ps -> packageIdToPackage.get(ps.getPackageId()),
                        Comparator.nullsLast(Comparator.comparing(MoeGroomingPackage::getExpirationDate)
                                .thenComparing(MoeGroomingPackage::getId))))
                .toList();
    }

    private MoeGroomingInvoiceApplyPackage buildApplyRecord(
            MoeGroomingInvoiceItem orderItem, MoeGroomingPackageService packageServiceToApply, int quantityToDeduct) {
        var applyPackage = new MoeGroomingInvoiceApplyPackage();
        applyPackage.setInvoiceId(orderItem.getInvoiceId());
        applyPackage.setInvoiceItemId(orderItem.getId());
        applyPackage.setPackageId(packageServiceToApply.getPackageId());
        applyPackage.setServiceId(orderItem.getServiceId());
        applyPackage.setPackageServiceId(packageServiceToApply.getId());
        applyPackage.setPackageName(
                mustGetGroomingPackage(packageServiceToApply.getPackageId()).getPackageName());
        applyPackage.setServiceName(mustGetService(orderItem.getServiceId()).getName());
        applyPackage.setQuantity(quantityToDeduct);
        applyPackage.setCreateTime(Instant.now().getEpochSecond());
        applyPackage.setUpdateTime(Instant.now().getEpochSecond());
        applyPackage.setStatus(CommonConstant.NORMAL);
        return applyPackage;
    }

    private MoeGroomingPackage mustGetGroomingPackage(int groomingPackageId) {
        var groomingPackage = packageMapper.selectByPrimaryKey(groomingPackageId);
        if (groomingPackage == null) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Grooming package not found: " + groomingPackageId);
        }
        return groomingPackage;
    }

    private ServiceBriefView mustGetService(long serviceId) {
        var services = serviceStub
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addServiceIds(serviceId)
                        .build())
                .getServicesList();
        if (services.isEmpty()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + serviceId);
        }
        return services.get(0);
    }

    private void replaceApplyRecords(int orderId, List<MoeGroomingInvoiceApplyPackage> applyRecords) {
        if (applyRecords.isEmpty()) {
            return;
        }

        // 先删除原有的 apply 记录
        var updateBean = new MoeGroomingInvoiceApplyPackage();
        updateBean.setStatus(CommonConstant.DELETED);
        var exm = new MoeGroomingInvoiceApplyPackageExample();
        exm.createCriteria().andInvoiceIdEqualTo(orderId);
        invoiceApplyPackageMapper.updateByExampleSelective(updateBean, exm);

        // 插入新的 apply 记录
        for (var e : applyRecords) {
            invoiceApplyPackageMapper.insertSelective(e);
        }
    }

    /**
     * 移除 package 使用记录
     * 1. 删除或更新 package apply 记录: moe_grooming_invoice_apply_package
     * 2. 删除 package used history: moe_grooming_package_history
     * 3. 如有 package used history, 还需恢复 package 数量: moe_grooming_package 表
     *
     * @param businessId
     * @param invoiceId
     * @param packageServiceId
     * @param quantity
     * @return
     * @deprecated by Bryson, use {@link #removePackageV2} instead
     */
    @Deprecated
    public InvoiceSummaryDTO removePackage(
            Integer businessId,
            Integer invoiceId,
            Integer packageServiceId,
            @Nullable Integer serviceId,
            Integer quantity,
            Boolean checkRefund) {
        MoeGroomingInvoice invoice = getOrderWithItemsById(businessId, invoiceId);
        if (InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(invoice.getStatus())) {
            throw new CommonException(ResponseCodeEnum.INVOICE_INVALID_STATUS);
        }

        var deletingList = listApplyRecord(invoiceId, packageServiceId, serviceId);

        if (CollectionUtils.isEmpty(deletingList)) {
            return convertToSummaryDTO(invoice);
        }
        List<MoeGroomingInvoiceItem> updateItems = buildOrderItems(invoice, quantity, deletingList);
        // 增量更新order
        UpdateOrderIncrResponse orderIncrResponse = updateOrderIncremental(
                invoiceId, null, convertToInvoiceItem(invoice, updateItems), null, null, null, checkRefund);
        // trigger refund
        RefundChannelDTO refundChannelDTO = RefundChannelMapper.INSTANCE.toDTO(orderIncrResponse.getRefundChannel());
        if (refundChannelDTO != null && !CollectionUtils.isEmpty(refundChannelDTO.getChannelList())) {
            InvoiceSummaryDTO invoiceSummaryDTO = convertToSummaryDTO(invoice);
            invoiceSummaryDTO.setRefundChannel(refundChannelDTO);
            return invoiceSummaryDTO;
        }
        // 失效 package used history 记录
        moePackageService.invalidPackageUsedHistory(invoiceId, packageServiceId, quantity);
        // 失效 package apply 记录
        moePackageService.invalidApplyPackageRecord(deletingList);
        return convertToSummaryDTO(getOrderWithItemsById(invoiceId));
    }

    /**
     * 移除 package 使用记录
     * 1. 删除 package apply 记录: moe_grooming_invoice_apply_package
     * 2. 增量更新 order
     * @param businessId
     * @param invoiceId
     * @param applyPackageId
     * @return
     */
    public InvoiceSummaryDTO removePackageV2(
            Long businessId, Long invoiceId, Long applyPackageId, Boolean checkRefund) {

        MoeGroomingInvoice invoice = getOrderWithItemsById(businessId.intValue(), invoiceId.intValue());
        if (InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(invoice.getStatus())) {
            throw new CommonException(ResponseCodeEnum.INVOICE_INVALID_STATUS);
        }

        MoeGroomingInvoiceApplyPackage applyPackage =
                invoiceApplyPackageMapper.selectByPrimaryKey(applyPackageId.intValue());
        if (applyPackage == null) {
            return convertToSummaryDTO(invoice);
        }

        var updateItem = invoice.getItems().stream()
                .filter(e -> e.getId().equals(applyPackage.getInvoiceItemId()))
                .findFirst()
                .map(e -> {
                    e.setPurchasedQuantity(0); // 设置 orderItemQuantity 为 0
                    return e;
                })
                .orElse(null);

        if (Objects.isNull(updateItem)) {
            return convertToSummaryDTO(invoice);
        }

        // 增量更新order
        UpdateOrderIncrResponse orderIncrResponse = updateOrderIncremental(
                invoiceId.intValue(),
                null,
                convertToInvoiceItem(invoice, List.of(updateItem)),
                null,
                null,
                null,
                checkRefund);
        // trigger refund
        RefundChannelDTO refundChannelDTO = RefundChannelMapper.INSTANCE.toDTO(orderIncrResponse.getRefundChannel());
        if (refundChannelDTO != null && !CollectionUtils.isEmpty(refundChannelDTO.getChannelList())) {
            InvoiceSummaryDTO invoiceSummaryDTO = convertToSummaryDTO(invoice);
            invoiceSummaryDTO.setRefundChannel(refundChannelDTO);
            return invoiceSummaryDTO;
        }

        // 失效 package used history 记录
        moePackageService.invalidPackageUsedHistory(applyPackage.getInvoiceId(), applyPackage.getServiceId(), null);

        // 失效 package apply 记录
        moeGroomingInvoiceApplyPackageMapper.updateByPrimaryKeySelective(new MoeGroomingInvoiceApplyPackage() {
            {
                setId(applyPackageId.intValue());
                setStatus(CommonConstant.DELETED);
            }
        });

        return convertToSummaryDTO(getOrderWithItemsById(invoiceId.intValue()));
    }

    public InvoiceSummaryDTO removeAllPackages(Integer invoiceId, Boolean checkRefund) {
        MoeGroomingInvoice invoice = getOrderWithItemsById(null, invoiceId);
        if (InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(invoice.getStatus())) {
            throw new CommonException(ResponseCodeEnum.INVOICE_INVALID_STATUS);
        }

        var deletingList = listAppliedPackageService(invoiceId);

        if (CollectionUtils.isEmpty(deletingList)) {
            return convertToSummaryDTO(invoice);
        }
        List<MoeGroomingInvoiceItem> updateItems = buildOrderItems(invoice, null, deletingList);
        // 增量更新order
        UpdateOrderIncrResponse orderIncrResponse = updateOrderIncremental(
                invoiceId, null, convertToInvoiceItem(invoice, updateItems), null, null, null, checkRefund);
        // trigger refund
        RefundChannelDTO refundChannelDTO = RefundChannelMapper.INSTANCE.toDTO(orderIncrResponse.getRefundChannel());
        if (refundChannelDTO != null && !CollectionUtils.isEmpty(refundChannelDTO.getChannelList())) {
            InvoiceSummaryDTO invoiceSummaryDTO = convertToSummaryDTO(invoice);
            invoiceSummaryDTO.setRefundChannel(refundChannelDTO);
            return invoiceSummaryDTO;
        }
        // 失效 package used history 记录
        deletingList.forEach(
                e -> moePackageService.invalidPackageUsedHistory(invoiceId, e.getPackageServiceId(), null));
        // 失效 package apply 记录
        moePackageService.invalidApplyPackageRecord(deletingList);
        return convertToSummaryDTO(getOrderWithItemsById(invoiceId));
    }

    private List<MoeGroomingInvoiceApplyPackage> listApplyRecord(
            int invoiceId, int packageServiceId, @Nullable Integer serviceId) {
        var e = new MoeGroomingInvoiceApplyPackageExample();
        var criteria = e.createCriteria()
                .andInvoiceIdEqualTo(invoiceId)
                .andPackageServiceIdEqualTo(packageServiceId)
                .andStatusEqualTo(CommonConstant.NORMAL);
        if (serviceId != null) {
            criteria.andServiceIdEqualTo(serviceId);
        }
        return invoiceApplyPackageMapper.selectByExample(e);
    }

    private List<MoeGroomingInvoiceItem> buildOrderItems(
            MoeGroomingInvoice invoice, Integer quantity, List<MoeGroomingInvoiceApplyPackage> deletingList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(invoice.getItems())) {
            return Collections.emptyList();
        }

        Map<Integer, MoeGroomingInvoiceItem> invoiceItemMap = invoice.getItems().stream()
                .filter(t -> OrderItemType.ITEM_TYPE_SERVICE.getType().equals(t.getType()))
                .collect(Collectors.toMap(MoeGroomingInvoiceItem::getId, Function.identity()));

        List<MoeGroomingInvoiceItem> updateItems = new ArrayList<>();
        for (MoeGroomingInvoiceApplyPackage deleting : deletingList) {
            if (!invoiceItemMap.containsKey(deleting.getInvoiceItemId())) {
                return Collections.emptyList();
            }

            if (quantity == null) {
                quantity = deleting.getQuantity();
            } else if (quantity <= 0) {
                break;
            }

            Integer deletingQuantity = Integer.min(deleting.getQuantity(), quantity);
            quantity -= deletingQuantity;

            deleting.setQuantity(deleting.getQuantity() - deletingQuantity);

            MoeGroomingInvoiceItem item = invoiceItemMap.get(deleting.getInvoiceItemId());
            item.setPurchasedQuantity(item.getPurchasedQuantity() - deletingQuantity);
            updateItems.add(item);
        }
        return updateItems;
    }

    public boolean updateItemTax(Integer businessId, ServiceTaxUpdateForApptDto updateParams) {
        // 根据groomingId和invoiceType查询invoice信息（包括invoiceItems）
        // TODO new order flow
        MoeGroomingInvoice invoice =
                getOrderByGroomingIdAndType(updateParams.getGroomingId(), InvoiceStatusEnum.TYPE_APPOINTMENT);
        if (invoice == null) {
            throw new CommonException(ResponseCodeEnum.INVOICE_NOT_FOUND);
        }
        // 检查taxId合法性
        InfoIdParams taxIdParam = new InfoIdParams();
        taxIdParam.setInfoId(updateParams.getTaxId());
        MoeBusinessTaxDto taxDto = iBusinessTaxClient.getTaxById(taxIdParam);
        if (taxDto == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
        }

        List<MoeGroomingInvoiceItem> updateItems = new ArrayList<>();
        boolean matchUpdate = false;
        for (MoeGroomingInvoiceItem item : invoice.getItems()) {
            if (Objects.equals(item.getServiceId(), updateParams.getServiceId())) {
                item.setTaxId(updateParams.getTaxId());
                item.setTaxRate(BigDecimal.valueOf(taxDto.getTaxRate()).setScale(4, RoundingMode.HALF_EVEN));
                updateItems.add(item);
                matchUpdate = true;
            }
        }
        // 重新计算invoice的amount值，并保存invoice和invoiceItem
        if (matchUpdate) {
            updateOrderIncremental(
                    invoice.getId(), null, convertToInvoiceItem(invoice, updateItems), null, null, null, false);
        }
        return matchUpdate;
    }

    /**
     * 更新order paid amount，
     * 1. 如果有 convenienceFee，则更新convenienceFee
     * 2. 如果是 deposit， 不 finish order
     *
     * @param invoice
     * @param params
     * @return
     */
    @Deprecated
    public BigDecimal updateOrderPaidResult(MoeGroomingInvoice invoice, SetPaymentParams params) {
        if (invoice.getOrderVersion() >= OrderConstant.ORDER_VERSION_REFUND) {
            // 通过 order payment 接口已更新 order， 这里直接返回
            return invoice.getRemainAmount();
        }

        OrderModel.Builder orderBuilder = OrderModel.newBuilder()
                .setBusinessId(invoice.getBusinessId())
                .setCustomerId(invoice.getCustomerId())
                .setSourceType(invoice.getType());
        OrderLineExtraFeeModel.Builder extraFeeBuilder = OrderLineExtraFeeModel.newBuilder()
                .setBusinessId(invoice.getBusinessId())
                .setOrderId(invoice.getId())
                .setApplyType(LineApplyType.TYPE_NONE.getType())
                .setFeeType(ExtraFeeType.CONVENIENCE_FEE.getType())
                .setCollectType(ExtraFeeCollectType.ADD.getType())
                .setApplyBy(0L);

        // 如果convenienceFee不为0，则更新invoice总金额、convenienceFee金额
        BigDecimal convenienceFee = params.getConvenienceFee();
        if (convenienceFee != null && convenienceFee.compareTo(BigDecimal.ZERO) > 0) {
            extraFeeBuilder = OrderLineExtraFeeModel.newBuilder()
                    .setBusinessId(invoice.getBusinessId())
                    .setOrderId(invoice.getId())
                    .setApplyType(LineApplyType.TYPE_NONE.getType())
                    .setFeeType(ExtraFeeType.CONVENIENCE_FEE.getType())
                    .setAmount(convenienceFee.doubleValue())
                    .setCollectType(ExtraFeeCollectType.ADD.getType())
                    .setApplyBy(0L);

            invoice.setConvenienceFee(invoice.getConvenienceFee().add(convenienceFee));
            invoice.setPaymentAmount(invoice.getPaymentAmount().add(convenienceFee));
            invoice.setTotalAmount(invoice.getTotalAmount().add(convenienceFee));
        }
        // 剩余待支付金额
        BigDecimal remainAmount = invoice.getPaymentAmount()
                .subtract(params.getPaidAmount())
                .add(params.getRefundedAmount() == null ? BigDecimal.ZERO : params.getRefundedAmount());
        if (remainAmount.compareTo(BigDecimal.ZERO) < 0) {
            remainAmount = BigDecimal.ZERO;
        }

        Integer status = InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(invoice.getStatus())
                ? InvoiceStatusEnum.INVOICE_STATUS_COMPLETED
                : InvoiceStatusEnum.INVOICE_STATUS_PROCESSING;
        if (remainAmount.compareTo(BigDecimal.ZERO) <= 0) {
            if (InvoiceStatusEnum.TYPE_NOSHOW.equals(invoice.getType())) {
                status = InvoiceStatusEnum.INVOICE_STATUS_COMPLETED;
            } else if (Boolean.TRUE.equals(params.getIsDeposit())) { // type: appointment  isDeposit: true
                // fix: #ERP-801 如果是押金，同时待支付金额为0，不直接将状态改为 full paid && finished。由 groomer 通过正常流程（checkin，checkout）手动设置
                status = InvoiceStatusEnum.INVOICE_STATUS_PROCESSING;
            }
        } else if (!bwListManager.isInWhiteList(
                BWListManager.ORDER_REINVENT, invoice.getBusinessId().toString())) {
            // 不在白名单的用户才回退关单状态
            status = InvoiceStatusEnum.INVOICE_STATUS_PROCESSING;
        }
        if (invoice.getPaidAmount().compareTo(params.getPaidAmount()) != 0
                || invoice.getRemainAmount().compareTo(remainAmount) != 0
                || invoice.getRefundedAmount().compareTo(params.getRefundedAmount()) != 0
                || !invoice.getStatus().equals(status)) {
            invoice.setRemainAmount(remainAmount);
            invoice.setPaidAmount(params.getPaidAmount());
            invoice.setRefundedAmount(params.getRefundedAmount());
            invoice.setStatus(status);
            invoice.setUpdateTime(CommonUtil.get10Timestamp());

            orderBuilder
                    .setRemainAmount(remainAmount.doubleValue())
                    .setPaidAmount(invoice.getPaidAmount().doubleValue())
                    .setRefundedAmount(invoice.getRefundedAmount().doubleValue())
                    .setStatus(status);

            UpdateOrderIncrRequest.Builder request = UpdateOrderIncrRequest.newBuilder()
                    .setOrderId(invoice.getId())
                    .setOrder(orderBuilder.build())
                    .addLineExtraFees(extraFeeBuilder);
            // 增量更新order
            orderClient.updateOrderIncremental(request.build());
        }
        return remainAmount;
    }

    public void updateOrderStatus(Integer orderId, Integer status) {
        OrderModel.Builder orderBuilder = OrderModel.newBuilder().setStatus(status);

        // 增量更新order
        orderClient.updateOrderIncremental(UpdateOrderIncrRequest.newBuilder()
                .setOrderId(orderId)
                .setOrder(orderBuilder.build())
                .build());
    }

    public void updateOrderGuid(MoeGroomingInvoice invoice) {
        OrderModel.Builder orderBuilder = OrderModel.newBuilder()
                .setBusinessId(invoice.getBusinessId())
                .setCustomerId(invoice.getCustomerId())
                .setSourceType(invoice.getType())
                .setGuid(invoice.getGuid())
                .setStatus(invoice.getStatus());

        // 增量更新order
        orderClient.updateOrderIncremental(UpdateOrderIncrRequest.newBuilder()
                .setOrderId(invoice.getId())
                .setOrder(orderBuilder.build())
                .build());
    }

    /**
     * update order
     *
     * @param businessId
     * @param groomingId
     * @return
     */
    public void updateOrderByGroomingId(Long companyId, Integer businessId, Integer groomingId, Integer operatorId) {
        if (businessId == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business id is empty.");
        }
        if (companyId == null) {
            var groomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
            if (businessId.equals(groomingAppointment.getBusinessId())) {
                companyId = groomingAppointment.getCompanyId();
            }
        }
        MoeGroomingInvoice existingInvoice =
                getOrderByGroomingIdAndType(groomingId, InvoiceStatusEnum.TYPE_APPOINTMENT);
        MoeGroomingInvoice invoice;
        if (existingInvoice == null) {
            // DONE new order flow
            if (newOrderHelper.isNewOrder(groomingId)) {
                return;
            }
            saveOrderWhenCreatingAppointment(companyId, businessId, groomingId, operatorId);
            return;
        } else {
            invoice = prepareInvoiceByGroomingId(companyId, businessId, groomingId, operatorId);
        }
        if (!businessId.equals(existingInvoice.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.UNAUTHORIZED_ERROR, "Not invoice for given business.");
        }
        invoice.setId(existingInvoice.getId());

        List<MoeGroomingInvoiceItem> updateItems = buildOverrideOrderItems(invoice, existingInvoice);

        List<OrderLineDiscountModel> discounts = updateDiscountWhenApplyPackage(invoice, updateItems, operatorId);

        OrderModel.Builder orderBuilder = OrderModel.newBuilder()
                .setTitle(invoice.getItemNames())
                .setTipsBasedAmount(invoice.getOriginalAmount().doubleValue());

        // 更新order
        updateOrderIncremental(
                invoice.getId(),
                orderBuilder.build(),
                convertToInvoiceItem(invoice, updateItems),
                null,
                CollectionUtils.isEmpty(discounts) ? null : discounts,
                null,
                false);

        // order 更新成功后，更新 package 使用记录
        ThreadPool.execute(() -> moePackageService.updateOrderApplyPackageRecord(invoice.getId(), updateItems));
    }

    private static List<MoeGroomingInvoiceItem> buildUpdatedOrderItems(
            MoeGroomingInvoice invoice, MoeGroomingInvoice existingInvoice) {
        List<MoeGroomingInvoiceItem> updateItems = invoice.getItems();
        Map<String, MoeGroomingInvoiceItem> newItemMap =
                updateItems.stream().collect(Collectors.toMap(OrderUtil::buildItemKey, item -> item));

        // 如果是针对某个item修改，则把item id赋值到新的item，复用同一个item
        for (MoeGroomingInvoiceItem existItem : existingInvoice.getItems()) {
            String key = buildItemKey(existItem);
            MoeGroomingInvoiceItem newItem = newItemMap.get(key);
            if (newItem != null) {
                newItem.setIsDeleted(false);
                newItem.setId(existItem.getId());
                newItem.setLineTaxId(existItem.getLineTaxId());
                // 保留之前使用 package 的 quantity，new item 更新后可能数量会变少，取总数量和旧 purchasedQuantity 的最小值
                Integer purchasedQuantity = Math.min(newItem.getQuantity(), existItem.getPurchasedQuantity());
                newItem.setPurchasedQuantity(purchasedQuantity);
            } else if (existItem.getType().equals(OrderItemType.ITEM_TYPE_SERVICE.getType())) {
                // 旧接口编辑预约的时候不处理product items
                existItem.setIsDeleted(true);
                updateItems.add(existItem);
            }
        }

        return updateItems;
    }

    private static List<MoeGroomingInvoiceItem> buildOverrideOrderItems(
            MoeGroomingInvoice invoice, MoeGroomingInvoice existingInvoice) {
        var newItems = invoice.getItems();
        var deletedItems = existingInvoice.getItems().stream()
                .filter(item -> item.getType().equals(OrderItemType.ITEM_TYPE_SERVICE.getType())
                        || item.getType().equals(OrderItemType.ITEM_TYPE_EVALUATION_SERVICE.getType()))
                .peek(item -> item.setIsDeleted(true))
                .toList();
        return Stream.concat(newItems.stream(), deletedItems.stream()).toList();
    }

    @Deprecated
    public boolean updateOrder(Long companyId, MoeGroomingInvoice invoice) {
        UpdateOrderResponse response = orderClient.updateOrder(UpdateOrderRequest.newBuilder()
                .setOrder(convertToOrder(companyId, invoice, null))
                .addAllLineItems(convertToInvoiceItem(invoice, invoice.getItems()))
                .build());

        return response.getRecord() > 0;
    }

    /**
     * 仅更新 order 状态，不更新各种金额
     *
     * @param order 入参必须包含 id 和 business id
     * @return
     */
    public boolean updateOrderModel(OrderModel order) {
        UpdateOrderIncrResponse response = orderClient.updateOrderIncremental(UpdateOrderIncrRequest.newBuilder()
                .setOrderId(order.getId())
                .setOrder(order)
                .build());
        return response.getRecord() > 0;
    }

    public UpdateOrderIncrResponse updateOrderIncremental(
            Integer orderId,
            OrderModel order,
            List<OrderLineItemModel> lineItems,
            List<OrderLineTaxModel> lineTaxes,
            List<OrderLineDiscountModel> lineDiscounts,
            List<OrderLineExtraFeeModel> lineExtraFees,
            boolean checkRefund) {
        if (PrimitiveTypeUtil.isNullOrZero(orderId)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
        }
        UpdateOrderIncrRequest.Builder requestBuilder = UpdateOrderIncrRequest.newBuilder();
        requestBuilder.setOrderId(orderId);
        requestBuilder.setCheckRefund(checkRefund);
        Optional.ofNullable(order).ifPresent(requestBuilder::setOrder);
        Optional.ofNullable(lineItems).ifPresent(requestBuilder::addAllLineItems);
        Optional.ofNullable(lineTaxes).ifPresent(requestBuilder::addAllLineTaxes);
        Optional.ofNullable(lineDiscounts).ifPresent(requestBuilder::addAllLineDiscounts);
        Optional.ofNullable(lineExtraFees).ifPresent(requestBuilder::addAllLineExtraFees);

        // 增量更新order
        return orderClient.updateOrderIncremental(requestBuilder.build());
    }

    public Long createOrderForInvoice(Long companyId, MoeGroomingInvoice invoice) {
        if (invoice.getBusinessId() == null || invoice.getCustomerId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
        }

        CustomerInfoDto customerInfoDto =
                iCustomerCustomerClient.getCustomerWithDeletedNoBusinessId(invoice.getCustomerId());
        String customerName = null;
        if (customerInfoDto != null) {
            customerName = customerInfoDto.getFirstName() + " " + customerInfoDto.getLastName();
        }
        CreateOrderRequest.Builder createOrderBuilder = CreateOrderRequest.newBuilder();
        createOrderBuilder.setOrder(convertToOrder(companyId, invoice, customerName));
        createOrderBuilder.addAllLineItems(convertToInvoiceItem(invoice, invoice.getItems()));
        CreateOrderResponse response = orderClient.createOrder(createOrderBuilder.build());
        return response.getId();
    }

    public List<OrderDetailModel> listOrdersByGroomingId(Integer appointmentId) {
        return orderClient
                .getOrderList(GetOrderListRequest.newBuilder()
                        .addSourceIds(appointmentId)
                        .setSourceType(InvoiceStatusEnum.TYPE_APPOINTMENT)
                        .setIncludeExtraOrder(true)
                        .build())
                .getOrderListList();
    }

    public MoeGroomingInvoice getOrderByGroomingId(Integer businessId, Integer groomingId) {
        if (businessId == null || groomingId == null) {
            return null;
        }
        OrderModel order = orderClient.getOrder(GetOrderRequest.newBuilder()
                .setSourceId(groomingId)
                .setSourceType(InvoiceStatusEnum.TYPE_APPOINTMENT)
                .build());
        if (order == null) {
            throw new CommonException(ResponseCodeEnum.INVOICE_NOT_FOUND);
        }
        return convertToInvoice(order);
    }

    public OrderModel getOrderModelById(@Nullable Integer businessId, Integer orderId) {
        if (orderId == null) {
            return null;
        }
        GetOrderRequest.Builder requestBuilder = GetOrderRequest.newBuilder().setId(orderId);
        if (businessId != null) {
            requestBuilder.setBusinessId(businessId);
        }
        OrderModel order = orderClient.getOrder(requestBuilder.build());
        if (order == null) {
            throw new CommonException(ResponseCodeEnum.INVOICE_NOT_FOUND);
        }
        return order;
    }

    public MoeGroomingInvoice getOrderById(@Nullable Integer businessId, Integer orderId) {
        OrderModel order = getOrderModelById(businessId, orderId);
        return convertToInvoice(order);
    }

    @Nonnull
    public MoeGroomingInvoice getOrderWithItemsById(Integer orderId) {
        return getOrderWithItemsById(null, orderId);
    }

    /**
     * Get order with items by orderId.
     *
     * @param businessId business, optional
     * @param orderId    orderId
     * @return order with items
     */
    @Nonnull
    public MoeGroomingInvoice getOrderWithItemsById(@Nullable Integer businessId, Integer orderId) {
        OrderDetailModel orderDetail = getOrderDetail(businessId, orderId, null);
        if (orderDetail == null || !orderDetail.hasOrder()) {
            throw ExceptionUtil.bizException(Code.CODE_INVOICE_NOT_FOUND, "Order not found: " + orderId);
        }
        return convertToInvoice(orderDetail);
    }

    public OrderDetailModel getOrderDetail(Integer orderId, String guid) {
        return getOrderDetail(null, orderId, guid);
    }

    /**
     * Get order detail by orderId or guid.
     *
     * @param businessId businessId, optional
     * @param orderId    order id, one of orderId and guid must be provided
     * @param guid       guid, one of orderId and guid must be provided
     * @return order detail
     */
    public OrderDetailModel getOrderDetail(@Nullable Integer businessId, Integer orderId, String guid) {
        if (orderId == null && !StringUtils.hasLength(guid)) {
            return null;
        }

        var requestBuilder = GetOrderRequest.newBuilder();
        Optional.ofNullable(businessId).ifPresent(requestBuilder::setBusinessId);
        Optional.ofNullable(orderId).ifPresent(requestBuilder::setId);
        Optional.ofNullable(guid).ifPresent(requestBuilder::setGuid);
        OrderDetailModel orderDetail = orderClient.getOrderDetail(requestBuilder.build());
        if (!orderDetail.hasOrder()) {
            throw new CommonException(ResponseCodeEnum.INVOICE_NOT_FOUND);
        }

        // getOrderDetail 的实现没有校验 businessId :)
        if (businessId != null && orderDetail.getOrder().getBusinessId() != businessId.longValue()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Order not found: " + orderId);
        }

        return orderDetail;
    }

    @Nullable
    public OrderDetailModel getOrderDetailByOrderId(@Nullable Integer businessId, Integer orderId) {
        if (orderId == null) {
            return null;
        }

        var builder = GetOrderRequest.newBuilder().setId(orderId).setSourceType(InvoiceStatusEnum.TYPE_APPOINTMENT);
        if (businessId != null) {
            builder.setBusinessId(businessId);
        }
        OrderDetailModel orderDetail = orderClient.getOrderDetail(builder.build());
        if (!orderDetail.hasOrder()) {
            throw ExceptionUtil.bizException(Code.CODE_INVOICE_NOT_FOUND);
        }

        return orderDetail;
    }

    public OrderDetailModel mustGetOrderByGroomingId(Integer businessId, Integer groomingId) {
        if (groomingId == null) {
            return null;
        }

        OrderDetailModel orderDetail = orderClient.getOrderDetail(GetOrderRequest.newBuilder()
                .setBusinessId(businessId)
                .setSourceId(groomingId)
                .setSourceType(InvoiceStatusEnum.TYPE_APPOINTMENT)
                .build());
        if (!orderDetail.hasOrder()) {
            throw ExceptionUtil.bizException(Code.CODE_INVOICE_NOT_FOUND);
        }

        return orderDetail;
    }

    public List<OrderDetailModel> batchGetOrderDetailByGroomingIds(Integer businessId, List<Integer> groomingIds) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return Collections.emptyList();
        }
        List<Long> sourceIds = groomingIds.stream().map(Integer::longValue).collect(Collectors.toList());

        GetOrderListResponse response = orderClient.getOrderList(GetOrderListRequest.newBuilder()
                .setBusinessId(businessId)
                .addAllSourceIds(sourceIds)
                .setSourceType(InvoiceStatusEnum.TYPE_APPOINTMENT)
                .build());
        return response.getOrderListList();
    }

    public MoeGroomingInvoice getOrderByGuid(String guid) {
        OrderDetailModel orderDetailModel = getOrderDetail(null, guid);
        if (!orderDetailModel.hasOrder()) {
            return null;
        }
        return convertToInvoice(orderDetailModel);
    }

    public MoeGroomingInvoice getOrderWithItemsByGuid(String guid) {
        OrderDetailModel orderDetailModel = getOrderDetail(null, guid);
        if (!orderDetailModel.hasOrder()) {
            throw new CommonException(ResponseCodeEnum.INVOICE_NOT_FOUND);
        }
        return convertToInvoice(orderDetailModel);
    }

    public MoeGroomingInvoice getOrderByGroomingIdAndType(Integer groomingId, String type) {
        GetOrderRequest.Builder request =
                GetOrderRequest.newBuilder().setSourceId(groomingId).setSourceType(type);
        // 一个预约可能关联多个 no-show 订单。此时需要传 latest=true 来获取最新的 no-show 订单
        if (InvoiceStatusEnum.TYPE_NOSHOW.equals(type)) {
            request.setLatest(true);
        }
        OrderDetailModel orderDetail = orderClient.getOrderDetail(request.build());
        if (!orderDetail.hasOrder()) {
            return null;
        }
        return convertToInvoice(orderDetail);
    }

    public OrderDetailModel getOrderDetailByGroomingIdAndType(Integer businessId, Integer groomingId, String type) {
        GetOrderRequest.Builder request = GetOrderRequest.newBuilder()
                .setBusinessId(businessId)
                .setSourceId(groomingId)
                .setSourceType(type);
        // 一个预约可能关联多个 no-show 订单。此时需要传 latest=true 来获取最新的 no-show 订单
        if (InvoiceStatusEnum.TYPE_NOSHOW.equals(type)) {
            request.setLatest(true);
        }
        OrderDetailModel orderDetail = orderClient.getOrderDetail(request.build());
        if (!orderDetail.hasOrder()) {
            return null;
        }
        return orderDetail;
    }

    public List<MoeGroomingInvoice> getListByIds(List<Integer> invoiceIds) {
        if (CollectionUtils.isEmpty(invoiceIds)) {
            return Collections.emptyList();
        }
        List<Long> orderIds = invoiceIds.stream().map(Integer::longValue).collect(Collectors.toList());
        GetOrderListResponse response = orderClient.getOrderList(
                GetOrderListRequest.newBuilder().addAllOrderIds(orderIds).build());
        return convertToInvoiceWithItems(response.getOrderListList());
    }

    public List<MoeGroomingInvoiceItem> getInvoiceItemByInvoiceIds(Integer businessId, List<Integer> invoiceIds) {
        return getInvoiceItemByInvoiceIds(businessId, invoiceIds, null);
    }

    public List<MoeGroomingInvoiceItem> getInvoiceItemByInvoiceIds(
            Integer businessId, List<Integer> invoiceIds, String itemType) {
        if (CollectionUtils.isEmpty(invoiceIds)) {
            return Collections.emptyList();
        }
        List<Long> orderIds = invoiceIds.stream().map(Integer::longValue).collect(Collectors.toList());
        GetOrderItemDetailRequest.Builder requestBuilder =
                GetOrderItemDetailRequest.newBuilder().addAllOrderIds(orderIds);
        Optional.ofNullable(itemType).ifPresent(requestBuilder::setItemType);
        GetOrderItemDetailResponse response = orderClient.getOrderItemDetail(requestBuilder.build());

        Map<Long, OrderLineTaxModel> taxMap = response.getLineTaxesList().stream()
                .filter(tax -> !tax.getIsDeleted() && tax.getOrderItemId() > 0)
                .collect(Collectors.toMap(OrderLineTaxModel::getOrderItemId, tax -> tax, (t1, t2) -> t2));

        return convertToInvoiceItems(response.getLineItemsList(), taxMap);
    }

    /**
     * 获取顾客的所有retail订单金额信息
     *
     * @param businessId
     * @param customerIds
     * @return
     */
    public List<CustomerPaymentSummary> getCustomerPaymentSummaries(Integer businessId, List<Integer> customerIds) {
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        Long companyId = migrateInfo.companyId();
        if (CollectionUtils.isEmpty(customerIds)) {
            return Collections.emptyList();
        }
        List<Long> customerIdsLongValue =
                customerIds.stream().map(Integer::longValue).collect(Collectors.toList());

        List<Integer> finishApptIds = moeGroomingAppointmentMapper.queryCustomerFinishApptIds(companyId, customerIds);
        List<Long> remainAmountSourceIds =
                finishApptIds.stream().map(Integer::longValue).collect(Collectors.toList());

        GetCustomerPaymentSummaryRequest.Builder requestBuilder = GetCustomerPaymentSummaryRequest.newBuilder()
                .addAllCustomerId(customerIdsLongValue)
                .addAllSourceTypes(Arrays.asList(
                        OrderSourceType.APPOINTMENT.getSource(),
                        OrderSourceType.NO_SHOW.getSource())) // 只查询appointment相关的order
                .addAllSourceId(remainAmountSourceIds);
        Optional.of(businessId).ifPresent(requestBuilder::setBusinessId);

        GetCustomerPaymentSummaryResponse response =
                customerOrderClient.getCustomerPaymentSummary(requestBuilder.build());
        return response.getPaymentSummariesList().stream()
                .map(model -> {
                    CustomerPaymentSummary paymentSummary = new CustomerPaymentSummary();
                    paymentSummary.setCustomerId((int) model.getCustomerId());
                    paymentSummary.setTotalPaymentAmount(BigDecimal.valueOf(model.getTotalPaymentAmount()));
                    paymentSummary.setTotalPaidAmount(BigDecimal.valueOf(model.getTotalPaidAmount()));
                    paymentSummary.setTotalRemainAmount(BigDecimal.valueOf(model.getTotalRemainAmount()));
                    return paymentSummary;
                })
                .collect(Collectors.toList());
    }

    public List<MoeGroomingInvoice> getListByGroomingIds(Integer businessId, List<Integer> groomingIds, String type) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return Collections.emptyList();
        }
        List<Long> sourceIds = groomingIds.stream().map(Integer::longValue).collect(Collectors.toList());
        GetOrderListRequest.Builder requestBuilder =
                GetOrderListRequest.newBuilder().addAllSourceIds(sourceIds);
        Optional.ofNullable(type).ifPresent(requestBuilder::setSourceType);

        GetOrderListResponse response = orderClient.getOrderList(requestBuilder.build());
        return convertToInvoiceWithItems(response.getOrderListList());
    }

    public List<MoeGroomingInvoice> getInvoicesByGroomingIds(Integer businessId, List<Integer> groomingIds) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return Collections.emptyList();
        }
        List<Long> sourceIds = groomingIds.stream().map(Integer::longValue).collect(Collectors.toList());
        GetOrderListRequest.Builder requestBuilder = GetOrderListRequest.newBuilder()
                .addAllSourceIds(sourceIds)
                .setQueryDetail(true)
                .setIncludeExtraOrder(true);

        GetOrderListResponse response = orderClient.getOrderList(requestBuilder.build());
        return convertToInvoiceWithItems(response.getOrderListList());
    }

    public List<MoeGroomingInvoice> getSaleOrderByDateRange(
            Integer businessId, Long startDate, Long endDate, String type) {
        GetRetailInvoicesRequest.Builder builder = GetRetailInvoicesRequest.newBuilder();
        Optional.ofNullable(endDate).ifPresent(builder::setEndTime);
        var resp = orderClient.getRetailOrderList(builder.setBusinessId(businessId)
                .setQueryDetail(true)
                .setStartTime(startDate)
                .setType(type)
                .setPageNum(Pagination.ALL.pageNum())
                .setPageSize(Pagination.ALL.pageSize() * 10)
                .build());
        return convertToInvoiceWithItems(resp.getOrdersList());
    }

    /**
     * 根据Business id 和start time、end time 查询order list, 最大条目1000
     *
     * @param businessId business id
     * @param startTime  update_time start time
     * @param endTime    update_time end time
     * @return order list
     */
    public List<MoeGroomingInvoice> listOrderByTimeRange(Integer businessId, Long startTime, Long endTime) {
        var resp = orderClient.listOrders(ListOrdersRequest.newBuilder()
                .addBusinessIds(businessId)
                .setFilter(ListOrdersRequest.Filter.newBuilder()
                        .setLastUpdatedTimeRange(Interval.newBuilder()
                                .setStartTime(Timestamp.newBuilder()
                                        .setSeconds(startTime)
                                        .build())
                                .setEndTime(Timestamp.newBuilder()
                                        .setSeconds(endTime)
                                        .build())
                                .build())
                        .build())
                .setPagination(PaginationRequest.newBuilder()
                        .setPageNum(Pagination.ALL.pageNum())
                        .setPageSize(Pagination.ALL.pageSize())
                        .build())
                .build());
        return convertToInvoices(resp.getOrdersList());
    }

    private List<MoeGroomingInvoice> convertToInvoiceWithItems(List<OrderDetailModel> orderDetailModels) {
        return orderDetailModels.stream().map(this::convertToInvoice).collect(Collectors.toList());
    }

    private List<MoeGroomingInvoice> convertToInvoices(List<OrderModel> orderModels) {
        return orderModels.stream().map(this::convertToInvoice).collect(Collectors.toList());
    }

    public OrderModel convertToOrder(Long companyId, MoeGroomingInvoice invoice, String customerName) {
        OrderModel.Builder orderBuilder = OrderModel.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(invoice.getBusinessId())
                .setCustomerId(invoice.getCustomerId())
                .setStatus(invoice.getStatus() != null ? invoice.getStatus() : InvoiceStatusEnum.INVOICE_STATUS_CREATED)
                .setSourceType(invoice.getType() != null ? invoice.getType() : InvoiceStatusEnum.TYPE_APPOINTMENT);

        Optional.ofNullable(invoice.getId()).ifPresent(orderBuilder::setId);
        Optional.ofNullable(invoice.getGuid()).ifPresent(orderBuilder::setGuid);
        Optional.ofNullable(invoice.getGroomingId()).ifPresent(orderBuilder::setSourceId);
        Optional.ofNullable(invoice.getCreateBy()).ifPresent(orderBuilder::setCreateBy);
        Optional.ofNullable(invoice.getUpdateBy()).ifPresent(orderBuilder::setUpdateBy);
        Optional.ofNullable(invoice.getTipsAmount())
                .ifPresent(amount -> orderBuilder.setTipsAmount(amount.doubleValue()));
        Optional.ofNullable(invoice.getTaxAmount())
                .ifPresent(amount -> orderBuilder.setTaxAmount(amount.doubleValue()));
        Optional.ofNullable(invoice.getDiscountAmount())
                .ifPresent(amount -> orderBuilder.setDiscountAmount(amount.doubleValue()));
        Optional.ofNullable(invoice.getConvenienceFee())
                .ifPresent(amount -> orderBuilder.setExtraFeeAmount(amount.doubleValue()));
        Optional.ofNullable(invoice.getSubTotalAmount())
                .ifPresent(amount -> orderBuilder.setSubTotalAmount(amount.doubleValue()));
        Optional.ofNullable(invoice.getOriginalAmount())
                .ifPresent(amount -> orderBuilder.setTipsBasedAmount(amount.doubleValue()));
        Optional.ofNullable(invoice.getPaymentAmount())
                .ifPresent(amount -> orderBuilder.setTotalAmount(amount.doubleValue()));
        Optional.ofNullable(invoice.getPaidAmount())
                .ifPresent(amount -> orderBuilder.setPaidAmount(amount.doubleValue()));
        Optional.ofNullable(invoice.getRemainAmount())
                .ifPresent(amount -> orderBuilder.setRemainAmount(amount.doubleValue()));
        Optional.ofNullable(invoice.getRefundedAmount())
                .ifPresent(amount -> orderBuilder.setRefundedAmount(amount.doubleValue()));
        Optional.ofNullable(invoice.getItemNames()).ifPresent(orderBuilder::setTitle);
        Optional.ofNullable(customerName).ifPresent(orderBuilder::setDescription);
        Optional.ofNullable(invoice.getSource()).ifPresent(orderBuilder::setSource);
        // discount
        OrderLineDiscountModel discountModel = convertToDiscountModel(invoice);
        if (discountModel != null) {
            orderBuilder.addLineDiscounts(discountModel);
        }

        return orderBuilder.build();
    }

    public List<OrderLineItemModel> convertToInvoiceItem(
            MoeGroomingInvoice invoice, List<MoeGroomingInvoiceItem> invoiceItems) {
        List<OrderLineItemModel> lineItems = new ArrayList<>();
        for (MoeGroomingInvoiceItem item : invoiceItems) {
            if (item.getServiceId() == null) {
                continue;
            }
            OrderLineItemModel.Builder lineItemBuilder = OrderLineItemModel.newBuilder()
                    .setBusinessId(invoice.getBusinessId())
                    .setObjectId(item.getServiceId())
                    .setType(item.getType() == null ? OrderItemType.ITEM_TYPE_SERVICE.getType() : item.getType())
                    .setName(item.getServiceName())
                    .setUnitPrice(
                            item.getServiceUnitPrice() != null
                                    ? item.getServiceUnitPrice().doubleValue()
                                    : 0d)
                    .setQuantity(item.getQuantity())
                    .setPetId(item.getPetId());
            Optional.ofNullable(item.getTaxId()).ifPresent(lineItemBuilder::setTaxId);
            Optional.ofNullable(item.getTaxName()).ifPresent(lineItemBuilder::setTaxName);
            Optional.ofNullable(item.getTaxRate())
                    .ifPresent(rate ->
                            lineItemBuilder.setTaxRate(Decimal.newBuilder().setValue(rate.toString())));
            Optional.ofNullable(item.getId()).ifPresent(lineItemBuilder::setId);
            Optional.ofNullable(item.getServiceDescription()).ifPresent(lineItemBuilder::setDescription);
            Optional.ofNullable(item.getPurchasedQuantity()).ifPresent(lineItemBuilder::setPurchasedQuantity);
            Optional.ofNullable(item.getTaxAmount())
                    .ifPresent(amount -> lineItemBuilder.setTaxAmount(amount.doubleValue()));
            Optional.ofNullable(item.getDiscountAmount())
                    .ifPresent(amount -> lineItemBuilder.setDiscountAmount(amount.doubleValue()));
            Optional.ofNullable(item.getTotalListPrice())
                    .ifPresent(amount -> lineItemBuilder.setSubTotalAmount(amount.doubleValue()));
            Optional.ofNullable(item.getTotalSalePrice())
                    .ifPresent(amount -> lineItemBuilder.setTotalAmount(amount.doubleValue()));
            Optional.ofNullable(item.getIsDeleted()).ifPresent(lineItemBuilder::setIsDeleted);

            // 这里会存在taxId不为空，但taxRate找不到的情况，需要判断taxRate不为空
            if (!PrimitiveTypeUtil.isNullOrZero(item.getTaxId()) && item.getTaxRate() != null) {
                lineItemBuilder.addLineTaxes(convertToTaxModel(invoice, item));
            }

            lineItems.add(lineItemBuilder.build());
        }
        return lineItems;
    }

    private OrderLineTaxModel convertToTaxModel(MoeGroomingInvoice invoice, MoeGroomingInvoiceItem item) {
        OrderLineTaxModel.Builder taxBuilder = OrderLineTaxModel.newBuilder()
                .setBusinessId(invoice.getBusinessId())
                .setApplyType(LineApplyType.TYPE_ITEM.getType())
                .setTaxId(item.getTaxId())
                .setTaxRate(item.getTaxRate().doubleValue())
                .setApplyBy(invoice.getCreateBy());
        Optional.ofNullable(item.getTaxName()).ifPresent(taxBuilder::setTaxName);
        Optional.ofNullable(item.getLineTaxId()).ifPresent(taxBuilder::setId);
        return taxBuilder.build();
    }

    public OrderLineDiscountModel convertToDiscountModel(MoeGroomingInvoice invoice) {
        if (AmountUtils.isNullOrZero(invoice.getDiscountAmount())) {
            return null;
        }
        // 为0的时候也需要设置discount，有可能是删除discount
        OrderLineDiscountModel.Builder discountBuilder = OrderLineDiscountModel.newBuilder();
        discountBuilder.setBusinessId(invoice.getBusinessId());
        discountBuilder.setApplyType(LineApplyType.TYPE_SERVICE.getType());
        discountBuilder.setDiscountType(
                invoice.getDiscountType() != null ? invoice.getDiscountType() : InvoiceValueType.AMOUNT.value());
        discountBuilder.setDiscountAmount(
                invoice.getDiscountAmount() != null
                        ? invoice.getDiscountAmount().doubleValue()
                        : 0d);
        discountBuilder.setDiscountRate(
                invoice.getDiscountRate() != null ? invoice.getDiscountRate().doubleValue() : 0d);
        discountBuilder.setIsDeleted(invoice.getDiscountAmount() == null
                || invoice.getDiscountAmount().compareTo(BigDecimal.ZERO) == 0); // 是否要删除已有的discount记录
        return discountBuilder.build();
    }

    public MoeGroomingInvoice convertToInvoice(OrderDetailModel orderDetail) {
        OrderModel order = orderDetail.getOrder();
        List<OrderLineItemModel> lineItems = orderDetail.getLineItemsList();
        List<OrderLineTaxModel> lineTaxes = orderDetail.getLineTaxesList();
        List<OrderLineDiscountModel> lineDiscounts = orderDetail.getLineDiscountsList();
        List<OrderLineExtraFeeModel> lineExtraFees = orderDetail.getLineExtraFeesList();

        MoeGroomingInvoice invoice = convertToInvoice(order);

        if (!CollectionUtils.isEmpty(lineDiscounts)) {
            List<LineDiscountDTO> discountDTOs = new ArrayList<>();
            for (OrderLineDiscountModel discount : lineDiscounts) {
                if (discount.getIsDeleted()) {
                    continue;
                }
                if (LineApplyType.TYPE_SERVICE.getType().equals(discount.getApplyType())) {
                    invoice.setDiscountRate(BigDecimal.valueOf(discount.getDiscountRate()));
                    invoice.setDiscountType(discount.getDiscountType());
                }
                LineDiscountDTO lineDiscountDTO = new LineDiscountDTO();
                lineDiscountDTO.setId(discount.getId());
                lineDiscountDTO.setOrderId(discount.getOrderId());
                lineDiscountDTO.setOrderItemId(discount.getOrderItemId());
                lineDiscountDTO.setApplyType(discount.getApplyType());
                lineDiscountDTO.setIsDeleted(discount.getIsDeleted());
                lineDiscountDTO.setDiscountType(discount.getDiscountType());
                lineDiscountDTO.setDiscountAmount(BigDecimal.valueOf(discount.getDiscountAmount()));
                lineDiscountDTO.setDiscountRate(BigDecimal.valueOf(discount.getDiscountRate()));
                lineDiscountDTO.setApplyBy(discount.getApplyBy());
                lineDiscountDTO.setApplySequence(discount.getApplySequence());
                lineDiscountDTO.setCreateTime(new Date(discount.getCreateTime()));
                lineDiscountDTO.setUpdateTime(new Date(discount.getUpdateTime()));
                lineDiscountDTO.setDiscountCodeId(discount.getDiscountCodeId());
                discountDTOs.add(lineDiscountDTO);
            }
            invoice.setLineDiscounts(discountDTOs);
        }

        Map<Long, OrderLineTaxModel> taxMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(lineTaxes)) {
            List<LineTaxDTO> taxDTOs = new ArrayList<>();
            for (OrderLineTaxModel tax : lineTaxes) {
                if (tax.getIsDeleted()) {
                    continue;
                }
                taxMap.put(tax.getOrderItemId(), tax);

                LineTaxDTO lineTaxDTO = new LineTaxDTO();
                lineTaxDTO.setId(tax.getId());
                lineTaxDTO.setOrderId(tax.getOrderId());
                lineTaxDTO.setOrderItemId(tax.getOrderItemId());
                lineTaxDTO.setApplyType(tax.getApplyType());
                lineTaxDTO.setIsDeleted(tax.getIsDeleted());
                lineTaxDTO.setTaxId(tax.getTaxId());
                lineTaxDTO.setTaxRate(BigDecimal.valueOf(tax.getTaxRate()));
                lineTaxDTO.setTaxName(tax.getTaxName());
                lineTaxDTO.setTaxAmount(BigDecimal.valueOf(tax.getTaxAmount()));
                lineTaxDTO.setApplyBy(tax.getApplyBy());
                lineTaxDTO.setApplySequence(tax.getApplySequence());
                lineTaxDTO.setCreateTime(new Date(tax.getCreateTime()));
                lineTaxDTO.setUpdateTime(new Date(tax.getUpdateTime()));
                taxDTOs.add(lineTaxDTO);
            }

            invoice.setLineTaxes(taxDTOs);
        }

        if (!CollectionUtils.isEmpty(lineExtraFees)) {
            List<LineExtraFeeDTO> extraFeeDTOs = new ArrayList<>();
            for (OrderLineExtraFeeModel extraFee : lineExtraFees) {
                if (extraFee.getIsDeleted()) {
                    continue;
                }
                LineExtraFeeDTO lineExtraFeeDTO = new LineExtraFeeDTO();
                lineExtraFeeDTO.setId(extraFee.getId());
                lineExtraFeeDTO.setOrderId(extraFee.getOrderId());
                lineExtraFeeDTO.setOrderItemId(extraFee.getOrderItemId());
                lineExtraFeeDTO.setApplyType(extraFee.getApplyType());
                lineExtraFeeDTO.setIsDeleted(extraFee.getIsDeleted());
                lineExtraFeeDTO.setFeeType(extraFee.getFeeType());
                lineExtraFeeDTO.setAmount(BigDecimal.valueOf(extraFee.getAmount()));
                lineExtraFeeDTO.setName(extraFee.getName());
                lineExtraFeeDTO.setDescription(extraFee.getDescription());
                lineExtraFeeDTO.setCollectType(extraFee.getCollectType());
                lineExtraFeeDTO.setApplyBy(extraFee.getApplyBy());
                lineExtraFeeDTO.setApplySequence(extraFee.getApplySequence());
                lineExtraFeeDTO.setCreateTime(new Date(extraFee.getCreateTime()));
                lineExtraFeeDTO.setUpdateTime(new Date(extraFee.getUpdateTime()));
                extraFeeDTOs.add(lineExtraFeeDTO);
            }
            invoice.setLineExtraFees(extraFeeDTOs);
        }

        List<MoeGroomingInvoiceItem> invoiceItems = convertToInvoiceItems(lineItems, taxMap);
        invoice.setItems(invoiceItems);
        return invoice;
    }

    public MoeGroomingInvoice convertToInvoice(OrderModel order) {
        MoeGroomingInvoice invoice = new MoeGroomingInvoice();
        invoice.setId((int) order.getId());
        invoice.setBusinessId((int) order.getBusinessId());
        invoice.setCustomerId((int) order.getCustomerId());
        invoice.setGroomingId((int) order.getSourceId());
        invoice.setType(order.getSourceType());
        invoice.setSubTotalAmount(BigDecimal.valueOf(order.getSubTotalAmount()));
        invoice.setDiscountAmount(BigDecimal.valueOf(order.getDiscountAmount()));
        invoice.setDepositAmount(BigDecimal.valueOf(order.getDepositAmount()));
        invoice.setTipsAmount(BigDecimal.valueOf(order.getTipsAmount()));
        invoice.setTipsBaseAmount(BigDecimal.valueOf(order.getTipsBasedAmount()));
        invoice.setTaxAmount(BigDecimal.valueOf(order.getTaxAmount()));
        invoice.setTotalAmount(BigDecimal.valueOf(order.getTotalAmount()));
        invoice.setPaymentAmount(BigDecimal.valueOf(order.getTotalAmount()));
        invoice.setPaidAmount(BigDecimal.valueOf(order.getPaidAmount()));
        invoice.setRemainAmount(BigDecimal.valueOf(order.getRemainAmount()));
        invoice.setStatus(order.getStatus());
        invoice.setRefundedAmount(BigDecimal.valueOf(order.getRefundedAmount()));
        invoice.setDiscountedSubTotalAmount(invoice.getSubTotalAmount()
                .subtract(invoice.getDiscountAmount())
                .add(invoice.getTipsAmount()));
        invoice.setGuid(order.getGuid());
        invoice.setConvenienceFee(BigDecimal.valueOf(order.getExtraFeeAmount()));
        invoice.setTipsType("amount"); // 默认都是amount
        invoice.setCreateBy((int) order.getCreateBy());
        invoice.setUpdateBy((int) order.getUpdateBy());
        invoice.setCreateTime(order.getCreateTime());
        invoice.setUpdateTime(order.getUpdateTime());
        invoice.setHasProduct(OrderItemType.hasProductItem(order.getLineItemTypes()));
        invoice.setHasServiceCharge(OrderItemType.hasServiceChargeItem(order.getLineItemTypes()));
        invoice.setPaymentStatus(order.getPaymentStatus());
        invoice.setCompleteTime(order.getCompleteTime());
        invoice.setOrderType(order.getOrderType().name());
        invoice.setFulfillmentStatus(order.getFulfillmentStatus());
        invoice.setOrderRefId(order.getOrderRefId());
        invoice.setOrderVersion(order.getOrderVersion());
        invoice.setHasExtraOrder(order.getHasExtraOrder());
        return invoice;
    }

    private static List<MoeGroomingInvoiceItem> convertToInvoiceItems(
            List<OrderLineItemModel> lineItems, Map<Long, OrderLineTaxModel> taxMap) {
        List<MoeGroomingInvoiceItem> invoiceItems = new ArrayList<>();
        for (OrderLineItemModel lineItem : lineItems) {
            if (lineItem.getIsDeleted()) {
                continue;
            }
            MoeGroomingInvoiceItem item = new MoeGroomingInvoiceItem();
            item.setId((int) lineItem.getId());
            item.setInvoiceId((int) lineItem.getOrderId());
            item.setStaffId((int) lineItem.getStaffId());
            item.setServiceId((int) lineItem.getObjectId());
            item.setServiceName(lineItem.getName());
            item.setServiceDescription(lineItem.getDescription());
            item.setServiceUnitPrice(BigDecimal.valueOf(lineItem.getUnitPrice()));
            item.setQuantity(lineItem.getQuantity());
            item.setPurchasedQuantity(lineItem.getPurchasedQuantity());
            item.setTotalListPrice(BigDecimal.valueOf(lineItem.getSubTotalAmount()));
            item.setTotalSalePrice(BigDecimal.valueOf(lineItem.getTotalAmount()));
            item.setDiscountAmount(BigDecimal.valueOf(lineItem.getDiscountAmount()));
            item.setTaxAmount(BigDecimal.valueOf(lineItem.getTaxAmount()));
            item.setCreateTime(lineItem.getCreateTime());
            item.setUpdateTime(lineItem.getUpdateTime());
            item.setType(lineItem.getType());
            item.setIsDeleted(lineItem.getIsDeleted());
            item.setPetId(lineItem.getPetId());

            if (taxMap.containsKey(lineItem.getId())) {
                OrderLineTaxModel tax = taxMap.get(lineItem.getId());
                item.setLineTaxId(tax.getId());
                item.setTaxId((int) tax.getTaxId());
                item.setTaxRate(BigDecimal.valueOf(tax.getTaxRate()));
            }
            invoiceItems.add(item);
        }
        return invoiceItems;
    }

    public OrderInvoiceSummaryDTO getOrderInvoiceSummaryById(Integer businessId, Integer orderId) {
        OrderInvoiceSummaryDTO invoiceSummaryDTO = new OrderInvoiceSummaryDTO();
        OrderDetailModel orderDetail = getOrderDetail(orderId, null);
        boolean isOriginOrder =
                OrderModel.OrderType.ORIGIN.equals(orderDetail.getOrder().getOrderType());
        MoeGroomingInvoice invoice = convertToInvoice(orderDetail);
        BeanUtils.copyProperties(invoice, invoiceSummaryDTO);
        // 设置extra order 的charge reason
        invoiceSummaryDTO.setExtraChargeReason(orderDetail.getOrder().getExtraChargeReason());
        invoiceSummaryDTO.setRemainAmountV2(
                invoice.getTotalAmount().subtract(invoice.getPaidAmount()).add(invoice.getRefundedAmount()));
        invoiceSummaryDTO.setTipBasedAmount(
                BigDecimal.valueOf(orderDetail.getOrder().getTipsBasedAmount()));

        // fulfillment flow
        if (Objects.equals(
                orderDetail.getOrder().getSourceType(),
                com.moego.idl.models.order.v1.OrderSourceType.FULFILLMENT.name().toLowerCase())) {
            var businessStaffTipPair = splitTipsService.splitTipsBetweenBusinessAndStaffForFulfillment(
                    BigDecimal.valueOf(orderDetail.getOrder().getTipsAmount()),
                    BigDecimal.valueOf(orderDetail.getOrder().getTipsBasedAmount()));
            invoiceSummaryDTO.setTipsSplitAmount(businessStaffTipPair.getRight());
        } else {
            Integer appointmentId = (int) orderDetail.getOrder().getSourceId();
            MoeGroomingAppointment moeGroomingAppointment =
                    appointmentService.getAppointmentById(businessId, appointmentId);
            // 非预约订单，或者 wait list 订单，不需要计算 tips 分配；预约的订单需要按 staff 实际参与的 service 计算用于分配给 staff 的 tips
            if (moeGroomingAppointment == null
                    || moeGroomingAppointment.getWaitListStatus().equals(WaitListStatusEnum.WAITLISTONLY)) {
                invoiceSummaryDTO.setTipsSplitAmount(
                        BigDecimal.valueOf(orderDetail.getOrder().getTipsAmount()));
            } else {
                // 需要过滤当前的invoice 数据
                var allPetDetails = appointmentDetailService
                        .getByAppointmentsAndOrderId(orderId, isOriginOrder, List.of(appointmentId))
                        .get(appointmentId);

                List<GroomingPetDetailDTO> petDetails =
                        PetDetailConverter.INSTANCE.entityToGroomingPetDetailDTOs(allPetDetails.key());
                List<EvaluationServiceDetailDTO> evaluationServiceDetails =
                        appointmentDetailService.toEvaluationServiceDetailDTOListV2(allPetDetails.value());

                Pair<BigDecimal, BigDecimal> businessStaffTipPair = splitTipsService.splitTipsBetweenBusinessAndStaff(
                        BigDecimal.valueOf(orderDetail.getOrder().getTipsAmount()),
                        BigDecimal.valueOf(orderDetail.getOrder().getTipsBasedAmount()),
                        petDetails,
                        evaluationServiceDetails);

                invoiceSummaryDTO.setTipsSplitAmount(businessStaffTipPair.getRight());
            }
        }

        Map<Long, DiscountCodeCompositeView> discountCodeMap = getDiscountCodeMap(orderDetail, businessId);

        if (orderDetail.getLineItemsList().size() > 0) {
            Map<Long, OrderLineTaxModel> taxMap = orderDetail.getLineTaxesList().stream()
                    .filter(t -> LineApplyType.TYPE_ITEM.getType().equals(t.getApplyType()) || !t.getIsDeleted())
                    .collect(Collectors.toMap(OrderLineTaxModel::getOrderItemId, t -> t, (t1, t2) -> t2));
            Map<Long, OrderLineDiscountModel> discountMap = orderDetail.getLineDiscountsList().stream()
                    .filter(d -> LineApplyType.TYPE_ITEM.getType().equals(d.getApplyType()))
                    .collect(Collectors.toMap(OrderLineDiscountModel::getOrderItemId, d -> d, (d1, d2) -> d2));
            invoiceSummaryDTO.setItems(orderDetail.getLineItemsList().stream()
                    .filter(item -> item.hasId() && item.hasIsDeleted() && !item.getIsDeleted())
                    .map(t -> {
                        OrderInvoiceSummaryDTO.OrderItemDTO dto = new OrderInvoiceSummaryDTO.OrderItemDTO();
                        dto.setId((int) t.getId());
                        dto.setOrderId((int) t.getOrderId());
                        dto.setStaffId((int) t.getStaffId());
                        dto.setObjectId((int) t.getObjectId());
                        dto.setPetId((int) t.getPetId());
                        dto.setType(t.getType());
                        dto.setName(t.getName());
                        dto.setDescription(t.getDescription());
                        dto.setUnitPrice(BigDecimal.valueOf(t.getUnitPrice()));
                        dto.setQuantity(t.getQuantity());
                        dto.setPurchasedQuantity(t.getPurchasedQuantity());
                        dto.setCreateTime(t.getCreateTime());
                        dto.setUpdateTime(t.getUpdateTime());
                        dto.setSubTotalAmount(
                                t.hasSubTotalAmount() ? BigDecimal.valueOf(t.getSubTotalAmount()) : BigDecimal.ZERO);
                        dto.setTotalAmount(
                                t.hasTotalAmount() ? BigDecimal.valueOf(t.getTotalAmount()) : BigDecimal.ZERO);
                        dto.setDiscountAmount(
                                t.hasDiscountAmount() ? BigDecimal.valueOf(t.getDiscountAmount()) : BigDecimal.ZERO);
                        dto.setTaxAmount(t.hasTaxAmount() ? BigDecimal.valueOf(t.getTaxAmount()) : BigDecimal.ZERO);

                        // tax、discount
                        OrderLineTaxModel tax = taxMap.get(t.getId());
                        if (tax != null) {
                            OrderInvoiceSummaryDTO.TaxDTO taxInfo = new OrderInvoiceSummaryDTO.TaxDTO();
                            taxInfo.setId(tax.getId());
                            taxInfo.setTaxId((int) tax.getTaxId());
                            taxInfo.setTaxRate(BigDecimal.valueOf(tax.getTaxRate()));
                            taxInfo.setTaxAmount(BigDecimal.valueOf(tax.getTaxAmount()));
                            dto.setTaxInfo(taxInfo);
                        }
                        OrderLineDiscountModel discount = discountMap.get(t.getId());
                        if (discount != null) {
                            OrderInvoiceSummaryDTO.DiscountDTO discountInfo = new OrderInvoiceSummaryDTO.DiscountDTO();
                            discountInfo.setId(discount.getId());
                            discountInfo.setDiscountType(discount.getDiscountType());
                            discountInfo.setDiscountRate(BigDecimal.valueOf(discount.getDiscountRate()));
                            discountInfo.setDiscountAmount(BigDecimal.valueOf(discount.getDiscountAmount()));
                            if (discountCodeMap.containsKey(discount.getDiscountCodeId())) {
                                DiscountCodeCompositeView discountCode =
                                        discountCodeMap.get(discount.getDiscountCodeId());
                                discountInfo.setDiscountCode(discountCode.getDiscountCode());
                            }
                            dto.setDiscountInfo(discountInfo);
                        }
                        return dto;
                    })
                    .collect(Collectors.toList()));
        }

        // 用待支付金额计算convenienceFee，用于C端获取convenienceFee
        invoiceSummaryDTO.setInitProcessingFee(iPaymentService.getConvenienceFee(
                invoice.getBusinessId(), invoice.getRemainAmount(), PaymentStripeStatus.CARD_PAY));
        invoiceSummaryDTO.setTotalCollected(
                invoiceSummaryDTO.getPaidAmount().subtract(invoiceSummaryDTO.getRefundedAmount()));
        invoiceSummaryDTO.setTotalAmountWithFee(
                invoiceSummaryDTO.getTotalAmount().add(invoiceSummaryDTO.getInitProcessingFee()));

        // 查询是否有booking fee，用于C端展示
        MoeBookOnlineDeposit obDeposit =
                bookOnlineDepositService.getOBDepositByGroomingId(invoice.getBusinessId(), invoice.getGroomingId());
        if (obDeposit != null && obDeposit.getStatus().equals(BookOnlineDepositConst.PAID)) {
            invoiceSummaryDTO.setBookingFeeAmount(obDeposit.getBookingFee());
        }

        if (InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(invoice.getStatus())) {
            invoiceSummaryDTO.setStatus("completed");
        } else if (InvoiceStatusEnum.INVOICE_STATUS_PROCESSING.equals(invoice.getStatus())) {
            invoiceSummaryDTO.setStatus("processing");
        } else if (InvoiceStatusEnum.INVOICE_STATUS_REMOVED.equals(invoice.getStatus())) {
            invoiceSummaryDTO.setStatus("removed");
        } else {
            invoiceSummaryDTO.setStatus("created");
        }

        if (invoice.getItems() != null && invoice.getItems().stream().anyMatch(t -> t.getPurchasedQuantity() > 0)) {
            List<MoeGroomingInvoiceApplyPackage> invoicePackages =
                    invoiceApplyPackageMapper.selectByInvoiceId(invoice.getId());
            if (!CollectionUtils.isEmpty(invoicePackages)) {
                Map<Integer, BigDecimal> servicePriceMap = invoice.getItems().stream()
                        .collect(Collectors.toMap(
                                MoeGroomingInvoiceItem::getId, MoeGroomingInvoiceItem::getServiceUnitPrice));

                invoiceSummaryDTO.setAppliedPackageServices(invoicePackages.stream()
                        .map(t -> {
                            PackageServiceDTO dto = new PackageServiceDTO();
                            BeanUtils.copyProperties(t, dto);
                            BigDecimal servicePrice = servicePriceMap.get(t.getInvoiceItemId());
                            if (servicePrice != null) {
                                dto.setServicePrice(servicePrice);
                            }
                            return dto;
                        })
                        .collect(Collectors.toList()));
            }
        }

        Map<Long, LineDiscountDTO> itemDiscountMap = new HashMap<>();
        Map<String, OrderInvoiceSummaryDTO.DiscountDTO> discountMap = new HashMap<>();
        BigDecimal discountExceptOrder = BigDecimal.ZERO;
        // 按apply type划分discount
        if (!CollectionUtils.isEmpty(invoice.getLineDiscounts())) {
            for (LineDiscountDTO discount : invoice.getLineDiscounts()) {
                if (discount.getIsDeleted()) {
                    continue;
                }
                if (!LineApplyType.TYPE_ITEM.getType().equals(discount.getApplyType())) {
                    OrderInvoiceSummaryDTO.DiscountDTO dto =
                            discountMap.getOrDefault(discount.getApplyType(), new OrderInvoiceSummaryDTO.DiscountDTO());
                    if (Objects.isNull(dto.getDiscountAmount())) {
                        dto.setDiscountAmount(BigDecimal.ZERO);
                    }
                    dto.setDiscountAmount(dto.getDiscountAmount().add(discount.getDiscountAmount()));
                    discountMap.put(discount.getApplyType(), dto);
                } else {
                    itemDiscountMap.put(discount.getOrderItemId(), discount);
                }
                if (!LineApplyType.TYPE_ALL.getType().equalsIgnoreCase(discount.getApplyType())
                        && !LineApplyType.TYPE_ITEM.getType().equalsIgnoreCase(discount.getApplyType())) {
                    discountExceptOrder = discountExceptOrder.add(discount.getDiscountAmount());
                }
            }
            invoiceSummaryDTO.setDiscountMap(discountMap);
        }

        // 按type区分subTotal，用于前端展示
        if (!CollectionUtils.isEmpty(invoice.getItems())) {
            Map<String, BigDecimal> subTotalMap = new HashMap<>();
            BigDecimal totalSectionsSubTotal = BigDecimal.ZERO;
            for (MoeGroomingInvoiceItem item : invoice.getItems()) {
                BigDecimal itemDiscount =
                        itemDiscountMap.containsKey(item.getId().longValue())
                                ? itemDiscountMap.get(item.getId().longValue()).getDiscountAmount()
                                : BigDecimal.ZERO;
                discountExceptOrder = discountExceptOrder.add(itemDiscount);

                BigDecimal itemSubTotal = item.getTotalListPrice().subtract(itemDiscount);
                if (OrderItemType.ITEM_TYPE_NO_SHOW.getType().equalsIgnoreCase(item.getType())
                        || OrderItemType.ITEM_TYPE_EVALUATION_SERVICE.getType().equals(item.getType())
                        || OrderItemType.ITEM_TYPE_SERVICE_CHARGE.getType().equals(item.getType())) {
                    String serviceType = OrderItemType.ITEM_TYPE_SERVICE.getType();
                    subTotalMap.put(
                            serviceType,
                            subTotalMap
                                    .getOrDefault(serviceType, BigDecimal.ZERO)
                                    .add(itemSubTotal));
                } else {
                    subTotalMap.put(
                            item.getType(),
                            subTotalMap
                                    .getOrDefault(item.getType(), BigDecimal.ZERO)
                                    .add(itemSubTotal));
                }
                totalSectionsSubTotal = totalSectionsSubTotal.add(item.getTotalListPrice());
            }
            subTotalMap.put(
                    LineApplyType.TYPE_ALL_NO_TIP.getType(), totalSectionsSubTotal.subtract(discountExceptOrder));
            subTotalMap.put(
                    LineApplyType.TYPE_ALL.getType(),
                    totalSectionsSubTotal
                            .add(invoice.getTipsAmount() != null ? invoice.getTipsAmount() : BigDecimal.ZERO)
                            .subtract(discountExceptOrder));
            invoiceSummaryDTO.setSubTotalAmountMap(subTotalMap);
        }

        // 查询customer信息，可能该customer已被删除
        CustomerInfoDto customerInfoDto =
                iCustomerCustomerClient.getCustomerWithDeletedNoBusinessId(invoiceSummaryDTO.getCustomerId());
        if (customerInfoDto != null) {
            invoiceSummaryDTO.setCustomerFirstName(customerInfoDto.getFirstName());
            invoiceSummaryDTO.setCustomerLastName(customerInfoDto.getLastName());
            invoiceSummaryDTO.setCustomerEmail(customerInfoDto.getEmail());
        }

        invoiceSummaryDTO.setDiscountList(getDiscountInfoList(orderDetail, discountCodeMap));

        // Deposit order 兼容 depositInfo 字段
        if (OrderModel.OrderType.DEPOSIT.equals(orderDetail.getOrder().getOrderType())) {
            invoiceSummaryDTO.setDepositInfo(convertToDepositDto(orderDetail));
        }

        return invoiceSummaryDTO;
    }

    private DepositDto convertToDepositDto(OrderDetailModel orderDetail) {
        DepositDto depositDto = new DepositDto();
        // Hack：使用 DepositOrder 的 id 作为 dto 里的 id，防一手前端拿这个字段做合法性校验
        depositDto.setId((int) orderDetail.getOrder().getId());
        depositDto.setDeGuid(orderDetail.getOrder().getGuid());
        depositDto.setAmount(BigDecimal.valueOf(orderDetail.getOrder().getPaidAmount()));
        depositDto.setInvoiceId((int) orderDetail.getOrder().getId());
        depositDto.setStatus(
                (byte) OrderModel.PaymentStatus.valueOf(orderDetail.getOrder().getPaymentStatus())
                        .getNumber());
        depositDto.setBusinessId((int) orderDetail.getOrder().getBusinessId());
        depositDto.setStaffId((int) orderDetail.getOrder().getUpdateBy());
        depositDto.setUpdateTime(new Date(orderDetail.getOrder().getUpdateTime() * 1000L));
        depositDto.setCompanyId(orderDetail.getOrder().getCompanyId());
        return depositDto;
    }

    public List<OrderInvoiceSummaryDTO.DiscountDTO> getDiscountInfoList(Integer invoiceId) {
        OrderDetailModel orderDetail = getOrderDetail(null, invoiceId, null);
        Map<Long, DiscountCodeCompositeView> discountCodeMap = getDiscountCodeMap(
                orderDetail, Math.toIntExact(orderDetail.getOrder().getBusinessId()));
        return getDiscountInfoList(orderDetail, discountCodeMap);
    }

    private List<OrderInvoiceSummaryDTO.DiscountDTO> getDiscountInfoList(
            OrderDetailModel orderDetail, Map<Long, DiscountCodeCompositeView> discountCodeMap) {
        if (CollectionUtils.isEmpty(discountCodeMap)) {
            return orderDetail.getLineDiscountsList().stream()
                    .filter(discount -> !discount.getIsDeleted())
                    .map(discount -> {
                        OrderInvoiceSummaryDTO.DiscountDTO discountInfo = new OrderInvoiceSummaryDTO.DiscountDTO();
                        discountInfo.setId(discount.getId());
                        discountInfo.setDiscountType(discount.getDiscountType());
                        discountInfo.setDiscountRate(BigDecimal.valueOf(discount.getDiscountRate()));
                        discountInfo.setDiscountAmount(BigDecimal.valueOf(discount.getDiscountAmount()));
                        discountInfo.setDiscountCodeId(discount.getDiscountCodeId());
                        discountInfo.setApplySequence(discount.getApplySequence());
                        discountInfo.setApplyType(discount.getApplyType());
                        return discountInfo;
                    })
                    .toList();
        }

        Set<Long> dicountCodeIdSet = new HashSet<>();
        List<OrderInvoiceSummaryDTO.DiscountDTO> result = new ArrayList<>();
        for (OrderLineDiscountModel discount : orderDetail.getLineDiscountsList()) {
            if (Boolean.TRUE.equals(discount.getIsDeleted())) {
                continue;
            }
            long discountCodeId = discount.getDiscountCodeId();

            OrderInvoiceSummaryDTO.DiscountDTO discountInfo = new OrderInvoiceSummaryDTO.DiscountDTO();
            discountInfo.setId(discount.getId());
            discountInfo.setDiscountType(discount.getDiscountType());
            discountInfo.setApplySequence(discount.getApplySequence());
            discountInfo.setDiscountCodeId(discountCodeId);
            discountInfo.setApplyType(discount.getApplyType());

            if (0 == discountCodeId) {
                discountInfo.setDiscountRate(BigDecimal.valueOf(discount.getDiscountRate()));
                discountInfo.setDiscountAmount(BigDecimal.valueOf(discount.getDiscountAmount()));
                result.add(discountInfo);
            }
            if (0 != discountCodeId && !dicountCodeIdSet.contains(discountCodeId)) {
                DiscountCodeCompositeView discountCodeModel = discountCodeMap.get(discount.getDiscountCodeId());
                if (Objects.nonNull(discountCodeModel)) {
                    if (DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE.equals(discountCodeModel.getType())) {
                        discountInfo.setDiscountRate(BigDecimal.valueOf(discountCodeModel.getAmount()));
                    } else {
                        discountInfo.setDiscountAmount(BigDecimal.valueOf(discountCodeModel.getAmount()));
                    }
                    discountInfo.setDiscountCode(discountCodeModel.getDiscountCode());
                    discountInfo.setDescription(discountCodeModel.getDescription());
                    discountInfo.setAllowedAllThing(discountCodeModel.getAllowedAllThing());
                    discountInfo.setAllowedAllServices(discountCodeModel.getAllowedAllServices());
                    discountInfo.setAllowedAllProducts(discountCodeModel.getAllowedAllProducts());
                    discountInfo.setAllowedAllClients(discountCodeModel.getAllowedAllClients());
                    discountInfo.setProductNames(discountCodeModel.getProductNamesList());
                    discountInfo.setServiceNames(discountCodeModel.getServiceNamesList());
                    dicountCodeIdSet.add(discountCodeId);
                    result.add(discountInfo);
                }
            }
        }
        return result.stream()
                .sorted(Comparator.comparing(OrderInvoiceSummaryDTO.DiscountDTO::getApplySequence))
                .toList();
    }

    private Map<Long, DiscountCodeCompositeView> getDiscountCodeMap(OrderDetailModel orderDetail, Integer businessId) {
        List<Long> discountCodeIdList = orderDetail.getLineDiscountsList().stream()
                .filter(discount -> !Boolean.TRUE.equals(discount.getIsDeleted()) && discount.getDiscountCodeId() != 0)
                .map(OrderLineDiscountModel::getDiscountCodeId)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(discountCodeIdList)) {
            return Map.of();
        }
        GetDiscountCodeListInput getDiscountCodeListInput = GetDiscountCodeListInput.newBuilder()
                .setPagination(PaginationRequest.newBuilder()
                        .setPageNum(1)
                        .setPageSize(10)
                        .build())
                .setBusinessId(businessId.longValue())
                .addAllIds(discountCodeIdList)
                .addAllStatus(List.of(
                        DiscountCodeStatus.DISCOUNT_CODE_STATUS_ACTIVE,
                        DiscountCodeStatus.DISCOUNT_CODE_STATUS_INACTIVE,
                        DiscountCodeStatus.DISCOUNT_CODE_STATUS_ARCHIVED,
                        DiscountCodeStatus.DISCOUNT_CODE_STATUS_DELETED,
                        DiscountCodeStatus.DISCOUNT_CODE_STATUS_EXPIRED))
                .build();
        GetDiscountCodeListOutput discountCodeListOutput =
                discountCodeClient.getDiscountCodeList(getDiscountCodeListInput);
        return discountCodeListOutput.getDiscountCodeCompositeViewsList().stream()
                .collect(Collectors.toMap(DiscountCodeCompositeView::getId, Function.identity()));
    }

    /**
     * 判断 invoice 是否还有可用 package
     *
     * @param invoiceId
     * @return
     */
    public boolean hasAvailablePackage(@Nullable Integer businessId, int invoiceId) {
        return !listAvailablePackageService(businessId, invoiceId).isEmpty();
    }

    /**
     * 查询 order 可用的 package service
     *
     * @param businessId businessId, optional
     * @param orderId    orderId
     * @return 可用的 package service 列表
     */
    public List<MoeGroomingPackageService> listAvailablePackageService(@Nullable Integer businessId, int orderId) {
        var order = getOrderWithItemsById(businessId, orderId);
        if (ObjectUtils.isEmpty(order.getItems())) {
            return List.of();
        }

        // 提取 order 中需要的 serviceId 与数量
        var serviceIdToCount = extractServiceIds(order.getItems());
        if (serviceIdToCount.isEmpty()) {
            return List.of();
        }

        var appt = moeGroomingAppointmentMapper.selectByPrimaryKey(order.getGroomingId());
        if (appt == null) {
            return List.of();
        }

        // customer 所有可用的 package
        var availablePackages = moePackageService.listAvailablePackage(order.getBusinessId(), order.getCustomerId());
        if (availablePackages.isEmpty()) {
            return List.of();
        }

        // 提取 order 中已经应用的 package service 数量
        var serviceIdToAppliedCount = listAppliedPackageService(orderId).stream()
                .collect(Collectors.groupingBy(
                        MoeGroomingInvoiceApplyPackage::getServiceId,
                        Collectors.summingInt(MoeGroomingInvoiceApplyPackage::getQuantity)));

        // 计算 order 中还需要的 service 数量
        var serviceIdToNeedApplyCount = calculateNeedApplyServices(serviceIdToCount, serviceIdToAppliedCount);
        if (serviceIdToNeedApplyCount.isEmpty()) {
            return List.of();
        }

        // 提取 customer 所有可用的 package 中，包含 order 中需要的 service 的 package
        var availablePackageServices =
                listApplicablePackageService(extractPackageIds(availablePackages), serviceIdToNeedApplyCount.keySet());
        if (availablePackageServices.isEmpty()) {
            return List.of();
        }

        // 找出这些package services在其他未完成订单中已经被占用的数量
        var packageServiceIdToOccupiedCount = listOccupiedPackageService(availablePackageServices).stream()
                .collect(Collectors.groupingBy(
                        MoeGroomingInvoiceApplyPackage::getPackageServiceId,
                        Collectors.summingInt(MoeGroomingInvoiceApplyPackage::getQuantity)));

        return calculateAvailablePackageServices(availablePackageServices, packageServiceIdToOccupiedCount);
    }

    private static HashMap<Integer, Long> calculateNeedApplyServices(
            Map<Integer, Long> serviceIdToCount, Map<Integer, Integer> serviceIdToAppliedCount) {
        var serviceIdToNeedApplyCount = new HashMap<Integer, Long>();

        for (var entry : serviceIdToCount.entrySet()) {
            var serviceId = entry.getKey();
            var totalCount = entry.getValue();
            var appliedCount = serviceIdToAppliedCount.getOrDefault(serviceId, 0);
            var needApplyCount = totalCount - appliedCount;
            if (needApplyCount > 0) {
                serviceIdToNeedApplyCount.put(serviceId, needApplyCount);
            }
        }

        return serviceIdToNeedApplyCount;
    }

    private List<MoeGroomingInvoiceApplyPackage> listAppliedPackageService(int orderId) {
        var example = new MoeGroomingInvoiceApplyPackageExample();
        example.createCriteria()
                .andInvoiceIdEqualTo(orderId)
                .andQuantityGreaterThan(0)
                .andStatusEqualTo(CommonConstant.NORMAL);
        return invoiceApplyPackageMapper.selectByExample(example);
    }

    /**
     * @return key: serviceId, value: count
     */
    private static Map<Integer, Long> extractServiceIds(List<MoeGroomingInvoiceItem> items) {
        return items.stream()
                .filter(item -> Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_SERVICE.getType()))
                .collect(Collectors.groupingBy(
                        MoeGroomingInvoiceItem::getServiceId,
                        Collectors.summingLong(MoeGroomingInvoiceItem::getQuantity)));
    }

    private static Set<Integer> extractPackageIds(List<MoeGroomingPackage> packages) {
        return packages.stream().map(MoeGroomingPackage::getId).collect(Collectors.toSet());
    }

    private List<MoeGroomingPackageService> listApplicablePackageService(
            Collection<Integer> packageIds, Collection<Integer> serviceIds) {
        if (packageIds.isEmpty()) {
            return List.of();
        }

        var example = new MoeGroomingPackageServiceExample();
        example.createCriteria().andPackageIdIn(List.copyOf(packageIds)).andRemainingQuantityGreaterThan(0);

        return packageServiceMapper.selectByExample(example).stream()
                .filter(ps -> ps.getServices().stream()
                        .map(PackageInfoDto.Service::getServiceId)
                        .anyMatch(serviceIds::contains))
                .toList();
    }

    private List<MoeGroomingInvoiceApplyPackage> listOccupiedPackageService(
            List<MoeGroomingPackageService> applicablePackageServices) {
        if (applicablePackageServices.isEmpty()) {
            return List.of();
        }

        var packageIds = applicablePackageServices.stream()
                .map(MoeGroomingPackageService::getPackageId)
                .distinct()
                .toList();

        var packageServiceIds = applicablePackageServices.stream()
                .map(MoeGroomingPackageService::getId)
                .distinct()
                .toList();

        var example = new MoeGroomingInvoiceApplyPackageExample();
        example.createCriteria()
                .andPackageIdIn(packageIds)
                .andPackageServiceIdIn(packageServiceIds)
                .andQuantityGreaterThanOrEqualTo(0)
                .andStatusEqualTo(CommonConstant.NORMAL);

        // 特别注意：当 order 关闭时，占用记录也不会删除，所以需要需要额外判断 order 状态

        var applyRecords = invoiceApplyPackageMapper.selectByExample(example);

        var unfinishedOrderIds = listUnfinishedOrder(extractOrderIds(applyRecords)).stream()
                .map(e -> e.getOrder().getId())
                .collect(Collectors.toSet());

        return applyRecords.stream()
                .filter(e -> unfinishedOrderIds.contains(e.getInvoiceId().longValue()))
                .toList();
    }

    private List<OrderDetailModel> listUnfinishedOrder(Set<Long> orderIds) {
        return orderStub
                .getOrderList(GetOrderListRequest.newBuilder()
                        .addAllOrderIds(orderIds)
                        .addAllStatus(List.of(OrderStatus.CREATED_VALUE, OrderStatus.PROCESSING_VALUE))
                        .build())
                .getOrderListList();
    }

    private static Set<Long> extractOrderIds(List<MoeGroomingInvoiceApplyPackage> applyRecords) {
        return applyRecords.stream()
                .map(MoeGroomingInvoiceApplyPackage::getInvoiceId)
                .map(Integer::longValue)
                .collect(Collectors.toSet());
    }

    /**
     * @param packageServices                 所有可用的 package service
     * @param packageServiceIdToOccupiedCount 被占用的 package service 数量，key: packageServiceId, value: occupied quantity
     * @return 可用的 package service 列表
     */
    private static List<MoeGroomingPackageService> calculateAvailablePackageServices(
            List<MoeGroomingPackageService> packageServices, Map<Integer, Integer> packageServiceIdToOccupiedCount) {

        if (packageServiceIdToOccupiedCount.isEmpty()) {
            return packageServices;
        }

        return packageServices.stream()
                .map(ps -> {
                    int occupied = packageServiceIdToOccupiedCount.getOrDefault(ps.getId(), 0);
                    var remainingQuantity = ps.getRemainingQuantity() - occupied;
                    if (remainingQuantity <= 0) {
                        return null;
                    }

                    var copy = GroomingPackageServiceConverter.INSTANCE.entityToEntity(ps);
                    copy.setRemainingQuantity(remainingQuantity);
                    return copy;
                })
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 对比 package 剩余数量及已使用数量，构建可用的 apply package 对象
     *
     * @param invoiceItems                 invoice item 列表
     * @param servicePackageMap            service 可用的 package 列表，key = serviceId，value = 包含当前 service 的 package
     * @param processingServiceQuantityMap package 中某个 service 已被占用的数量，key = packageServiceId（moe_grooming_package_service 主键），value = 占用数量
     * @return
     */
    public List<MoeGroomingInvoiceApplyPackage> joinAvailablePackageService(
            List<MoeGroomingInvoiceItem> invoiceItems,
            Map<Integer, List<GroomingPackageServiceInfoDTO>> servicePackageMap,
            Map<Integer, Integer> processingServiceQuantityMap) {
        List<MoeGroomingInvoiceApplyPackage> resultList = new ArrayList<>();
        invoiceItems.stream()
                .filter(i -> Objects.equals(i.getType(), OrderItemType.ITEM_TYPE_SERVICE.getType()))
                .forEach(item -> {
                    Integer serviceId = item.getServiceId();
                    if (!servicePackageMap.containsKey(serviceId)) {
                        // 无可用 package，跳过当前 item
                        return;
                    }

                    List<GroomingPackageServiceInfoDTO> servicePackageList = servicePackageMap.get(item.getServiceId());
                    Integer applyQuantity = item.getQuantity();
                    for (GroomingPackageServiceInfoDTO servicePackage : servicePackageList) {
                        // 当前可用数量 = package 的 service 剩余可用数量 减去 已经占用的数量
                        int availableQuantity = servicePackage.getRemainingQuantity()
                                - processingServiceQuantityMap.getOrDefault(servicePackage.getPackageServiceId(), 0);
                        // 实际应用数量
                        int quantity = Math.min(applyQuantity, availableQuantity);
                        if (quantity <= 0) {
                            continue;
                        }
                        // 更新 item 待 apply 数量、package 剩余数量
                        applyQuantity -= quantity;
                        servicePackage.setRemainingQuantity(servicePackage.getRemainingQuantity() - quantity);
                        // 构建 apply 对象
                        MoeGroomingInvoiceApplyPackage param = new MoeGroomingInvoiceApplyPackage();
                        BeanUtils.copyProperties(item, param);
                        param.setInvoiceItemId(item.getId());
                        param.setQuantity(quantity);
                        param.setPackageId(servicePackage.getPackageId());
                        param.setPackageServiceId(servicePackage.getPackageServiceId());
                        param.setPackageName(servicePackage.getPackageName());
                        resultList.add(param);
                    }
                });
        return resultList;
    }

    /**
     * 判断是否同类型item都使用了package，如果是，删掉该类型的discount
     *
     * @param invoice
     * @param updateItems
     * @param operatorId
     * @return
     */
    private List<OrderLineDiscountModel> updateDiscountWhenApplyPackage(
            MoeGroomingInvoice invoice, List<MoeGroomingInvoiceItem> updateItems, Integer operatorId) {
        List<OrderLineDiscountModel> lineDiscounts = new ArrayList<>();
        if (CollectionUtils.isEmpty(invoice.getLineDiscounts())) {
            return lineDiscounts;
        }
        // 使用了package的type，且所有的item的quantity都等于purchasedQuantity
        Set<String> types = new HashSet<>();
        for (MoeGroomingInvoiceItem updateItem : updateItems) {
            if (Objects.equals(updateItem.getQuantity(), updateItem.getPurchasedQuantity())) {
                Optional<MoeGroomingInvoiceItem> optional = invoice.getItems().stream()
                        .filter(item -> Objects.equals(item.getType(), updateItem.getType())
                                && !Objects.equals(item.getQuantity(), item.getPurchasedQuantity()))
                        .findFirst();

                if (!optional.isPresent()) {
                    types.add(updateItem.getType());
                }
            }
        }
        for (String type : types) {
            invoice.getLineDiscounts().stream()
                    .filter(discount -> discount.getApplyType().equals(type))
                    .findFirst()
                    .ifPresent(discount -> {
                        OrderLineDiscountModel.Builder discountBuilder = OrderLineDiscountModel.newBuilder()
                                .setId(discount.getId())
                                .setBusinessId(invoice.getBusinessId())
                                .setOrderId(invoice.getId())
                                .setApplyType(type)
                                .setIsDeleted(true)
                                .setDiscountType(discount.getDiscountType())
                                .setDiscountAmount(0d)
                                .setDiscountRate(0)
                                .setApplyBy(operatorId)
                                .setDiscountCodeId(discount.getDiscountCodeId());
                        lineDiscounts.add(discountBuilder.build());
                    });
        }
        return lineDiscounts;
    }

    public void updateOrderWhenCancelAppts(Integer businessId, List<Integer> groomingIds) {
        List<MoeGroomingInvoice> invoices =
                getListByGroomingIds(businessId, groomingIds, OrderSourceType.APPOINTMENT.getSource());
        invoices.forEach(invoice -> {
            // 更新状态
            updateOrderStatus(invoice.getId(), InvoiceStatusEnum.INVOICE_STATUS_REMOVED);
        });
        // 需要同时拉取 ORIGIN 和 EXTRA orders
        List<Integer> invoiceIds = orderHelper.getOrderIds(businessId, groomingIds).stream()
                .map(Long::intValue)
                .toList();
        // 释放 product 库存
        iRetailInvoiceClient.batchReleaseStock(businessId, invoiceIds);
        // 失效 package 使用记录
        moePackageService.invalidPackageUsedHistory(invoiceIds);
    }

    /**
     * cancel 预约后，不再直接更新订单状态。订单状态扭转通过 EventType.APPOINTMENT_CANCELED 事件来处理
     */
    public void updateWhenCancelAppts(Integer businessId, List<Integer> groomingIds) {
        List<Integer> invoiceIds = orderHelper.getOrderIds(businessId, groomingIds).stream()
                .map(Long::intValue)
                .toList();
        // 释放 product 库存
        iRetailInvoiceClient.batchReleaseStock(businessId, invoiceIds);
        // 失效 package 使用记录
        moePackageService.invalidPackageUsedHistory(invoiceIds);
    }

    /**
     * 获取invoiceId DM 前的数据，retail invoice id是在原来的基础上增加了2000万
     *
     * @param invoiceId
     * @return
     */
    public Integer getOldInvoiceId(Integer invoiceId) {
        Integer i = (int) (invoiceId - retailOffset);
        return invoiceId > retailOffset && invoiceId < newOffset ? i : invoiceId;
    }

    /**
     * Report相关 - 查询tips大于0的订单信息
     */
    public GetTipsInvoicesDTO getTipsInvoiceByPage(
            List<Integer> businessIds, List<Integer> groomingIds, Integer pageNum, Integer pageSize) {
        List<Long> groomingIdList = groomingIds.stream().map(Integer::longValue).collect(Collectors.toList());

        GetTipsOrderListResponse response = orderClient.getTipsOrderList(GetTipsOrderListRequest.newBuilder()
                .setBusinessId(businessIds.get(0))
                .addAllBusinessIds(businessIds.stream().map(Integer::longValue).collect(Collectors.toList()))
                .addAllSourceIds(groomingIdList)
                .setPageNum(pageNum)
                .setPageSize(pageSize)
                .build());

        return new GetTipsInvoicesDTO()
                .setCount(response.getCount())
                .setInvoiceList(convertToInvoices(response.getOrdersList()));
    }

    public RefundChannelDTO validateRefundAmount(
            Integer businessId, Integer groomingId, List<PetDetailParams> serviceList) {
        // TODO new order flow
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
        if (!GroomingAppointmentEnum.NOT_PAY.equals(appointment.getIsPaid())) {
            MoeGroomingInvoice existingInvoice =
                    getOrderByGroomingIdAndType(groomingId, InvoiceStatusEnum.TYPE_APPOINTMENT);
            if (existingInvoice == null) {
                throw ExceptionUtil.bizException(Code.CODE_INVOICE_NOT_FOUND);
            }
            Map<String, MoeGroomingInvoiceItem> newItemMap = buildNewService(businessId, serviceList);
            List<MoeGroomingInvoiceItem> updateItems =
                    new ArrayList<>(newItemMap.values().stream().toList());

            for (MoeGroomingInvoiceItem existItem : existingInvoice.getItems()) {
                String key = buildItemKey(existItem);
                MoeGroomingInvoiceItem newItem = newItemMap.get(key);
                if (newItem != null) {
                    newItem.setIsDeleted(false);
                    newItem.setId(existItem.getId());
                    newItem.setLineTaxId(existItem.getLineTaxId());
                } else if (existItem.getType().equals(OrderItemType.ITEM_TYPE_SERVICE.getType())) {
                    // 旧接口编辑预约的时候不处理product items
                    existItem.setIsDeleted(true);
                    updateItems.add(existItem);
                }
            }
            UpdateOrderIncrRequest.Builder requestBuilder = UpdateOrderIncrRequest.newBuilder();
            requestBuilder.setOrderId(existingInvoice.getId());
            requestBuilder.setCheckRefund(true);
            Optional.ofNullable(convertToInvoiceItem(existingInvoice, updateItems))
                    .ifPresent(requestBuilder::addAllLineItems);
            UpdateOrderIncrResponse response = orderClient.updateOrderIncremental(requestBuilder.build());
            return RefundChannelMapper.INSTANCE.toDTO(response.getRefundChannel());
        }
        return null;
    }

    private Map<String, MoeGroomingInvoiceItem> buildNewService(Integer businessId, List<PetDetailParams> serviceList) {
        List<MoeGroomingServiceDTO> services = groomingServiceService.getServicesByServiceIds(
                businessId,
                serviceList.stream().map(PetDetailParams::getServiceId).toList());
        if (CollectionUtils.isEmpty(services)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "no pet service");
        }
        // 查询 tax rate
        List<Integer> taxIds = services.stream()
                .map(MoeGroomingServiceDTO::getTaxId)
                .filter(Objects::nonNull)
                .toList();
        List<MoeBusinessTaxDto> taxList = iBusinessTaxClient.getTaxListByIds(taxIds);
        Map<Integer, MoeBusinessTaxDto> taxMap =
                taxList.stream().collect(Collectors.toMap(MoeBusinessTaxDto::getId, Function.identity()));

        Map<Integer, MoeGroomingServiceDTO> serviceDTOMap =
                services.stream().collect(Collectors.toMap(MoeGroomingServiceDTO::getId, s -> s));
        Map<String, MoeGroomingInvoiceItem> newService = new HashMap<>();
        for (PetDetailParams s : serviceList) {
            String k = buildItemKey(s.getServiceId(), s.getServicePrice(), s.getPetId());
            MoeGroomingInvoiceItem moeGroomingInvoiceItem = newService.get(k);
            if (null != moeGroomingInvoiceItem) {
                moeGroomingInvoiceItem.setQuantity(moeGroomingInvoiceItem.getQuantity() + 1);
            } else {
                moeGroomingInvoiceItem = new MoeGroomingInvoiceItem();
                moeGroomingInvoiceItem.setQuantity(1);
                moeGroomingInvoiceItem.setServiceId(s.getServiceId());
                moeGroomingInvoiceItem.setServiceUnitPrice(s.getServicePrice());
                MoeGroomingServiceDTO serviceDTO = serviceDTOMap.get(s.getServiceId());
                if (null != serviceDTO) {
                    MoeBusinessTaxDto taxDto = taxMap.get(serviceDTO.getTaxId());
                    if (taxDto != null && taxDto.getTaxRate() != null) {
                        moeGroomingInvoiceItem.setTaxRate(BigDecimal.valueOf(taxDto.getTaxRate()));
                    }
                    moeGroomingInvoiceItem.setTaxId(serviceDTO.getTaxId());
                    moeGroomingInvoiceItem.setServiceName(serviceDTO.getName());
                    moeGroomingInvoiceItem.setServiceDescription(serviceDTO.getDescription());
                }
            }
            newService.put(k, moeGroomingInvoiceItem);
        }
        return newService;
    }

    public InvoiceSummaryDTO convertToSummaryDTO(MoeGroomingInvoice invoice) {
        InvoiceSummaryDTO invoiceSummaryDTO = new InvoiceSummaryDTO();
        BeanUtils.copyProperties(invoice, invoiceSummaryDTO);
        if (invoice.getRemainAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 用待支付金额计算convenienceFee，用于C端获取convenienceFee
            invoiceSummaryDTO.setInitProcessingFee(iPaymentService.getConvenienceFee(
                    invoice.getBusinessId(), invoice.getRemainAmount(), PaymentStripeStatus.CARD_PAY));
        }
        invoiceSummaryDTO.setTotalAmountWithFee(invoiceSummaryDTO.getTotalAmount());
        invoiceSummaryDTO.setAmountDue(invoice.getRemainAmount());
        invoiceSummaryDTO.setDiscountedSubTotalNoTipAmount(invoiceSummaryDTO
                .getDiscountedSubTotalAmount()
                .subtract(
                        invoiceSummaryDTO.getTipsAmount() == null
                                ? BigDecimal.ZERO
                                : invoiceSummaryDTO.getTipsAmount()));
        if (Objects.equals(invoice.getType(), OrderSourceType.APPOINTMENT.getSource())) {
            // 查询是否有booking fee，用于C端展示，只有 appointment invoice 才有 booking fee
            MoeBookOnlineDeposit obDeposit =
                    bookOnlineDepositService.getOBDepositByGroomingId(invoice.getBusinessId(), invoice.getGroomingId());
            if (obDeposit != null && obDeposit.getStatus().equals(BookOnlineDepositConst.PAID)) {
                invoiceSummaryDTO.setBookingFeeAmount(obDeposit.getBookingFee());
                invoiceSummaryDTO.setBookingFeePaymentId(obDeposit.getPaymentId());
                invoiceSummaryDTO.setPaidAmount(
                        invoiceSummaryDTO.getPaidAmount().add(invoiceSummaryDTO.getBookingFeeAmount()));
                invoiceSummaryDTO.setTotalAmountWithFee(
                        invoiceSummaryDTO.getTotalAmountWithFee().add(invoiceSummaryDTO.getBookingFeeAmount()));
            }
        }
        invoiceSummaryDTO.setTotalCollected(invoiceSummaryDTO
                .getPaidAmount()
                .subtract(
                        invoiceSummaryDTO.getRefundedAmount() == null
                                ? BigDecimal.ZERO
                                : invoiceSummaryDTO.getRefundedAmount()));
        if (InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(invoice.getStatus())) {
            invoiceSummaryDTO.setStatus("completed");
        } else if (InvoiceStatusEnum.INVOICE_STATUS_PROCESSING.equals(invoice.getStatus())) {
            invoiceSummaryDTO.setStatus("processing");
        } else {
            invoiceSummaryDTO.setStatus("created");
        }

        if (invoice.getItems() != null) {
            invoiceSummaryDTO.setItems(invoice.getItems().stream()
                    .map(t -> {
                        InvoiceSummaryDTO.ItemDTO dto = new InvoiceSummaryDTO.ItemDTO();
                        BeanUtils.copyProperties(t, dto);
                        return dto;
                    })
                    .toList());
        }

        if (invoice.getItems() != null && invoice.getItems().stream().anyMatch(t -> t.getPurchasedQuantity() > 0)) {
            List<MoeGroomingInvoiceApplyPackage> invoicePackages =
                    invoiceApplyPackageMapper.selectByInvoiceId(invoice.getId());
            if (!CollectionUtils.isEmpty(invoicePackages)) {
                Map<Integer, BigDecimal> servicePriceMap = invoice.getItems().stream()
                        .collect(Collectors.toMap(
                                MoeGroomingInvoiceItem::getId, MoeGroomingInvoiceItem::getServiceUnitPrice));

                invoiceSummaryDTO.setAppliedPackageServices(invoicePackages.stream()
                        .map(t -> {
                            InvoiceSummaryDTO.PackageServiceDTO dto = new InvoiceSummaryDTO.PackageServiceDTO();
                            BeanUtils.copyProperties(t, dto);
                            BigDecimal servicePrice = servicePriceMap.get(t.getInvoiceItemId());
                            if (servicePrice != null) {
                                dto.setServicePrice(servicePrice);
                            }
                            return dto;
                        })
                        .toList());
            }
        }
        MoeBusinessCustomerDTO customerInfoDto =
                iCustomerCustomerClient.getCustomerWithDeleted(invoiceSummaryDTO.getCustomerId());
        if (customerInfoDto != null) {
            invoiceSummaryDTO.setCustomerFirstName(customerInfoDto.getFirstName());
            invoiceSummaryDTO.setCustomerLastName(customerInfoDto.getLastName());
            invoiceSummaryDTO.setCustomerEmail(customerInfoDto.getEmail());
        }
        return invoiceSummaryDTO;
    }

    private void removeAllAppliedMemberships(Integer businessId, Integer staffId, OrderDetailModel orderDetail) {
        long orderId = orderDetail.getOrder().getId();
        boolean noMembershipUsage = membershipService
                        .getRecommendBenefitUsage(GetRecommendBenefitUsageRequest.newBuilder()
                                .setOrderId(orderId)
                                .build())
                        .getUsageViewsCount()
                <= 0;
        if (noMembershipUsage) {
            return;
        }

        // remove discounts for memberships
        List<OrderLineDiscountModel> deleteAllUsedDiscounts = orderDetail.getLineDiscountsList().stream()
                .map(discount -> {
                    OrderLineDiscountModel.Builder lineDiscountBuilder =
                            discount.toBuilder().setIsDeleted(true);
                    return lineDiscountBuilder.build();
                })
                .toList();

        // update order
        UpdateOrderIncrRequest.Builder requestBuilder = UpdateOrderIncrRequest.newBuilder()
                .setOrderId(orderId)
                .setOrder(OrderModel.newBuilder()
                        .setId(orderId)
                        .setUpdateBy(staffId)
                        .setBusinessId(businessId)
                        .build())
                .addAllLineDiscounts(deleteAllUsedDiscounts);
        // .addAllLineItems(newItems);
        orderClient.updateOrderIncremental(requestBuilder.build());

        // clear applied records
        membershipService.upsertRecommendBenefitUsage(UpsertRecommendBenefitUsageRequest.newBuilder()
                .setOrderId(orderId)
                .setCustomerId(orderDetail.getOrder().getCustomerId())
                .build());
    }
}
