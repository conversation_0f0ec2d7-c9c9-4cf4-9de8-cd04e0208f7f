package com.moego.server.grooming.web.params;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import java.util.Set;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Data
public class SearchAbandonedClientParam {

    @NotNull
    private Filter filter;

    private Query query;
    private Page page = new Page(1, 10);
    private Sort sort = new Sort(Property.ID, Order.DESC);

    /**
     * 这里为了兼容前端的逻辑，优先使用 pageSize 和 pageNo，如果没有传 pageSize 和 pageNo，就使用 {@link #page}，
     * {@link com.moego.common.params.PageParams} 已经过时，不要继承它
     */
    private Integer pageSize;

    private Integer pageNo;

    /**
     * @param leadType      null or empty means all
     * @param abandonedStep null or empty means all
     * @param abandonStatus null or empty means all
     * @param timeRange     null means all
     */
    @Builder(toBuilder = true)
    public record Filter(
            @Nullable Set<LeadType> leadType,
            @Nullable Set<OBStepEnum> abandonedStep,
            @Nullable Set<AbandonStatus> abandonStatus,
            @Nullable TimeRange timeRange,
            @Schema(description = "最后一次联系时间发生在多少分钟内，包括 mass text 和 mass email 两种") @Nullable
                    Integer lastContactWithInMins,
            @Schema(description = "最后一次联系时间发生在多少分钟前，包括 mass text 和 mass email 两种") @Nullable
                    Integer lastContactBeforeMins,
            @Schema(description = "从 filter 中正选的 abandoned record，如果正选和反选都有，那么正选优先") Set<String> includeBookingFlowIds,
            @Schema(description = "从 filter 中反选的 abandoned record") Set<String> excludeBookingFlowIds,
            @Nullable Set<ServiceItemEnum> careTypes) {}

    @Getter
    @RequiredArgsConstructor
    public enum LeadType {
        /**
         * Not a product definition, only used to filter out not associated with client database
         */
        @JsonProperty("non_client")
        NON_CLIENT("non_client"),
        /**
         * The first time an abandoned record is generated
         */
        @JsonProperty("new_visitor")
        NEW_VISITOR("new_visitor"),
        /**
         * Associated with client database
         */
        @JsonProperty("existing_client")
        EXISTING_CLIENT("existing_client");

        private final String value;
    }

    @Getter
    @RequiredArgsConstructor
    public enum AbandonStatus {
        @JsonProperty("abandoned")
        ABANDONED("abandoned"),
        @JsonProperty("contacted")
        CONTACTED("contacted"),
        @JsonProperty("recovered")
        RECOVERED("recovered");
        private final String value;
        public static final Set<String> NOT_RECOVERED_STATUSES = Set.of(ABANDONED.getValue(), CONTACTED.getValue());
    }

    public record TimeRange(@Nullable Long startTimeSec, @Nullable Long endTimeSec) {}

    public record Query(String keyword) {}

    /**
     * @param pageNumber start from 1
     */
    public record Page(Integer pageNumber, Integer pageSize) {}

    public record Sort(Property property, Order order) {}

    public enum Property {
        @JsonProperty("id")
        ID,
        @JsonProperty("abandonTime")
        ABANDON_TIME,
    }

    public enum Order {
        ASC,
        DESC,
    }
}
