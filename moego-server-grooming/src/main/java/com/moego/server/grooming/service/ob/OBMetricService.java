package com.moego.server.grooming.service.ob;

import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.utils.DateUtil;
import com.moego.lib.common.auth.AuthContext;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import com.moego.server.grooming.service.ob.metrics.IOBMetricsService;
import com.moego.server.grooming.service.ob.metrics.OBMetricsServiceContext;
import com.moego.server.grooming.web.params.OBMetricsParams;
import com.moego.server.grooming.web.vo.ob.OBMetricVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2023/5/22
 */
@Service
@RequiredArgsConstructor
public class OBMetricService {

    private final OBMetricsServiceContext metricsServiceContext;

    private final IBusinessBusinessService businessService;

    private final MoeGroomingBookOnlineService bookOnlineService;

    public List<OBMetricVO> listMetrics(OBMetricsParams metricsParams) {
        if (CollectionUtils.isEmpty(metricsParams.metrics())) {
            return List.of();
        }
        // init ob setting
        bookOnlineService.getSettingInfoByBusinessId(AuthContext.get().getBusinessId());
        // Business date to UTC timestamp
        BusinessDateTimeDTO businessDateTime =
                businessService.getBusinessDateTime(AuthContext.get().getBusinessId());
        long currentPeriodStartTime =
                DateUtil.timestamp(metricsParams.startDate(), businessDateTime.getTimezoneName()) / 1000;
        long currentPeriodEndTime =
                DateUtil.timestamp(metricsParams.endDate(), businessDateTime.getTimezoneName()) / 1000;
        long intervalTime = currentPeriodEndTime - currentPeriodStartTime;
        OBMetricTimeRangeDTO timeRangeDTO = OBMetricTimeRangeDTO.builder()
                .companyId(AuthContext.get().companyId())
                .businessId(AuthContext.get().getBusinessId())
                .startTime(currentPeriodStartTime)
                .endTime(currentPeriodEndTime)
                .build();
        OBMetricTimeRangeDTO lastTimeRangeDTO = OBMetricTimeRangeDTO.builder()
                .companyId(AuthContext.get().companyId())
                .businessId(AuthContext.get().getBusinessId())
                .startTime(currentPeriodStartTime - intervalTime)
                .endTime(currentPeriodEndTime - intervalTime)
                .build();
        return metricsParams.metrics().parallelStream()
                .map(metric -> {
                    IOBMetricsService metricsService = metricsServiceContext.getMetricsService(metric.name());
                    OBMetricVO.OBMetricVOBuilder metricVOBuilder =
                            OBMetricVO.builder().name(metric.name());
                    if (Objects.isNull(metricsService) || CollectionUtils.isEmpty(metric.types())) {
                        return metricVOBuilder.metrics(Map.of()).build();
                    }
                    AtomicReference<Object> currentPeriodSumAtomic = new AtomicReference<>();
                    return metricVOBuilder
                            .metrics(metric.types().stream()
                                    .collect(Collectors.toMap(type -> type, type -> switch (type) {
                                        case SUM -> {
                                            Object currentPeriodSum = currentPeriodSumAtomic.get();
                                            if (Objects.nonNull(currentPeriodSum)) {
                                                yield currentPeriodSum;
                                            }
                                            Object sumMetrics = metricsService.sumMetrics(timeRangeDTO);
                                            currentPeriodSumAtomic.set(sumMetrics);
                                            yield sumMetrics;
                                        }
                                        case RING_RATE -> {
                                            Object currentPeriodSum = currentPeriodSumAtomic.get();
                                            if (Objects.isNull(currentPeriodSum)) {
                                                currentPeriodSum = metricsService.sumMetrics(timeRangeDTO);
                                                currentPeriodSumAtomic.set(currentPeriodSum);
                                            }
                                            Object lastPeriodSum = metricsService.sumMetrics(lastTimeRangeDTO);
                                            if (currentPeriodSum instanceof Integer currentSum
                                                    && lastPeriodSum instanceof Integer lastSum) {
                                                yield Objects.equals(lastSum, 0)
                                                        ? ""
                                                        : BigDecimal.valueOf(currentSum - lastSum)
                                                                .divide(
                                                                        BigDecimal.valueOf(lastSum),
                                                                        2,
                                                                        RoundingMode.HALF_UP);
                                            } else if (currentPeriodSum instanceof BigDecimal currentSum
                                                    && lastPeriodSum instanceof BigDecimal lastSum) {
                                                yield lastSum.compareTo(BigDecimal.ZERO) == 0
                                                        ? ""
                                                        : currentSum
                                                                .subtract(lastSum)
                                                                .divide(lastSum, 2, RoundingMode.HALF_UP);
                                            }
                                            yield "";
                                        }
                                        case PROPORTION -> metricsService.proportionMetrics(timeRangeDTO);
                                    })))
                            .build();
                })
                .toList();
    }
}
