package com.moego.server.grooming.mapperbean;

public class MoeGroomingExchangeRate {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_exchange_rate.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_exchange_rate.timetamp
     *
     * @mbg.generated
     */
    private Integer timestamp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_exchange_rate.base
     *
     * @mbg.generated
     */
    private String base;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_exchange_rate.date
     *
     * @mbg.generated
     */
    private String date;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_exchange_rate.rates
     *
     * @mbg.generated
     */
    private String rates;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_exchange_rate.id
     *
     * @return the value of moe_grooming_exchange_rate.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_exchange_rate.id
     *
     * @param id the value for moe_grooming_exchange_rate.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Integer timestamp) {
        this.timestamp = timestamp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_exchange_rate.base
     *
     * @return the value of moe_grooming_exchange_rate.base
     *
     * @mbg.generated
     */
    public String getBase() {
        return base;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_exchange_rate.base
     *
     * @param base the value for moe_grooming_exchange_rate.base
     *
     * @mbg.generated
     */
    public void setBase(String base) {
        this.base = base == null ? null : base.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_exchange_rate.date
     *
     * @return the value of moe_grooming_exchange_rate.date
     *
     * @mbg.generated
     */
    public String getDate() {
        return date;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_exchange_rate.date
     *
     * @param date the value for moe_grooming_exchange_rate.date
     *
     * @mbg.generated
     */
    public void setDate(String date) {
        this.date = date == null ? null : date.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_exchange_rate.rates
     *
     * @return the value of moe_grooming_exchange_rate.rates
     *
     * @mbg.generated
     */
    public String getRates() {
        return rates;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_exchange_rate.rates
     *
     * @param rates the value for moe_grooming_exchange_rate.rates
     *
     * @mbg.generated
     */
    public void setRates(String rates) {
        this.rates = rates == null ? null : rates.trim();
    }
}
