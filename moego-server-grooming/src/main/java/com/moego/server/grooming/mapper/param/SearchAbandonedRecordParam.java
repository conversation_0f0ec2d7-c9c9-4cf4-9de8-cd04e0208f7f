package com.moego.server.grooming.mapper.param;

import jakarta.annotation.Nullable;
import java.util.Set;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SearchAbandonedRecordParam {

    private Integer businessId;

    @Nullable
    private Set<String> leadType;

    @Nullable
    private Set<String> abandonStep;

    @Nullable
    private Set<Integer> recoveryType;

    @Nullable
    private Long startTimeSec;

    @Nullable
    private Long endTimeSec;

    private boolean includeDeleted = false;

    private Set<String> abandonStatuses;

    private Long lastContactStartTimeSec;

    private Long lastContactEndTimeSec;

    private Set<String> includeBookingFlowIds;

    private Set<String> excludeBookingFlowIds;

    private boolean hasKeyword;

    private Set<String> keywordBookingFlowIds;

    private Set<Integer> keywordCustomerIds;

    private Set<Integer> keywordAddressIds;

    private Boolean notAssociated;

    private Set<String> careTypes;
}
