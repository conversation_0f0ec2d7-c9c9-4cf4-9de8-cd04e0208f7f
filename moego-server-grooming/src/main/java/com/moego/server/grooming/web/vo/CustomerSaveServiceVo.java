package com.moego.server.grooming.web.vo;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CustomerSaveServiceVo {

    @NotNull
    private Integer customerId;

    @NotNull
    private Integer petId;

    @NotNull
    private Integer newServiceId;

    @NotNull
    private Integer lastServiceId;

    private Integer serviceTime;
    private BigDecimal serviceFee;
    /**
     * 0否 1是
     */
    private Byte isSaveDuration;
    /**
     * 0否 1是
     */
    private Byte isSavePrice;
    // 兼容前端定义的别名，实际上还用上面两个
    private Integer duration;
    private BigDecimal price;
}
