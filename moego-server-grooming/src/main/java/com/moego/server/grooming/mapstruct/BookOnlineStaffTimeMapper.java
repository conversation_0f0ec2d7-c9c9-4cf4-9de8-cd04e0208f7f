package com.moego.server.grooming.mapstruct;

import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.dto.BookOnlineStaffTimeDTO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffTime;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Mapper(imports = {JsonUtil.class, StringUtils.class, TypeRef.class})
public interface BookOnlineStaffTimeMapper {
    BookOnlineStaffTimeMapper INSTANCE = Mappers.getMapper(BookOnlineStaffTimeMapper.class);

    @Mapping(
            target = "staffTimes",
            expression =
                    "java(StringUtils.hasText(entity.getStaffTimes()) ? JsonUtil.toBean(entity.getStaffTimes(), new TypeRef<>(){}) : null)")
    @Mapping(
            target = "staffSlots",
            expression =
                    "java(StringUtils.hasText(entity.getStaffSlots()) ? JsonUtil.toBean(entity.getStaffSlots(), new TypeRef<>(){}) : null)")
    BookOnlineStaffTimeDTO entity2DTO(MoeBookOnlineStaffTime entity);
}
