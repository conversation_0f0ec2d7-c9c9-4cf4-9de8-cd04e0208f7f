package com.moego.server.grooming.service.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServiceUpdateDto extends ServiceFilterDTO {

    @NotNull
    private Integer serviceId;

    private Integer categoryId;

    @Length(max = 150)
    private String name;

    private String description;
    private Integer taxId;
    private BigDecimal price;
    private Integer duration;
    private Byte inactive; // 新增inactive字段，0-正常，1-inactive
    private String colorCode;
    private Byte type;
    private List<Integer> staffIdList;
    private Byte showBasePrice;
    private Byte bookOnlineAvailable;
    private Byte isAllStaff;
    private Boolean allowBookingWithOtherCareType;

    private Boolean applyUpcomingAppt;

    private List<Long> bundleServiceIdList;
}
