package com.moego.server.grooming.service;

import static com.moego.server.grooming.service.utils.ReportUtil.calculateDiscount;
import static com.moego.server.grooming.service.utils.ReportUtil.calculateTax;
import static com.moego.server.grooming.service.utils.ReportUtil.calculateTips;
import static java.math.RoundingMode.HALF_UP;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.GsonUtil;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.params.WorkingDailyQueryRangeVo;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerBasicDTO;
import com.moego.server.customer.dto.GroomingCalenderPetInfo;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.LeaderboardRankInfoDTO;
import com.moego.server.grooming.dto.PetBreedInfoDTO;
import com.moego.server.grooming.dto.StaffBlockInfoDTO;
import com.moego.server.grooming.dto.report.LeaderboardStaffReportDTO;
import com.moego.server.grooming.dto.report.ReportWebEmployee;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import com.moego.server.grooming.service.dto.report.CollectedPriceDTO;
import com.moego.server.grooming.service.params.CalCollectedPriceParams;
import com.moego.server.grooming.service.report.ReportCalculateService;
import com.moego.server.grooming.service.report.ReportOrderService;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class GroomingLeaderboardService {

    @Autowired
    private AppointmentMapperProxy appointmentMapper;

    @Autowired
    private ReportCalculateService reportCalculateService;

    @Autowired
    private ReportOrderService reportOrderService;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private ICustomerCustomerClient iCustomerClient;

    @Autowired
    private IPetClient iPetClient;

    public List<LeaderboardStaffReportDTO> getStaffForLeaderboard(
            Integer businessId, String startDate, String endDate, List<Integer> staffIdList) {
        if (businessId == null || StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "getStaffForLeaderboard param is error");
        }
        // 获取带invoice，staff 详情的预约
        List<GroomingReportWebAppointment> appointments =
                reportOrderService.queryWebReportApptEmployee(businessId, startDate, endDate, null);
        if (CollectionUtils.isEmpty(appointments)) {
            return Collections.emptyList();
        }

        staffIdList.addAll(appointments.stream()
                .flatMap(a -> a.getPetDetails().stream())
                .flatMap(detail -> {
                    if (CollectionUtils.isEmpty(detail.getOperationList())) {
                        return Stream.of(detail.getStaffId());
                    }
                    return detail.getOperationList().stream().map(GroomingServiceOperationDTO::getStaffId);
                })
                .distinct()
                .toList());

        // Initiate employee records
        List<ReportWebEmployee> employees = staffIdList.stream()
                .distinct()
                .map(staffId -> ReportWebEmployee.builder()
                        .staffId(staffId)
                        .collectedRevAll(BigDecimal.ZERO)
                        .collectedTips(BigDecimal.ZERO)
                        .finishedApptNum(0)
                        .paidApptNum(0)
                        .totalAppts(0)
                        .servicedPetNum(0)
                        .totalServiceMinute(0L)
                        .totalWorkingMinute(0)
                        .totalBlockMinute(0)
                        .build())
                .collect(Collectors.toList());

        // 计算单项次数
        processCounts(appointments, employees);
        // 计算金额
        reportCalculateService.processMoney(businessId, appointments, employees, true);
        // 计算时长
        processServiceTime(appointments, employees, businessId, startDate, endDate);

        // 处理返回结果
        return employees.stream()
                .map(e -> LeaderboardStaffReportDTO.builder()
                        .staffId(e.getStaffId())
                        .collectedRev(e.getCollectedRevAll())
                        .collectedTips(e.getCollectedTips())
                        .finishedApptNum(e.getFinishedApptNum())
                        .paidApptNum(e.getPaidApptNum())
                        .totalAppts(e.getTotalAppts())
                        .totalApptsAll(e.getTotalApptsAll())
                        .servicedPetNum(e.getServicedPetNum())
                        .totalServiceMinute(e.getTotalServiceMinute())
                        .totalWorkingMinute(e.getTotalWorkingMinute())
                        .totalBlockMinute(e.getTotalBlockMinute())
                        .build())
                .collect(Collectors.toList());
    }

    private void processServiceTime(
            List<GroomingReportWebAppointment> appointments,
            List<ReportWebEmployee> employees,
            Integer businessId,
            String startDate,
            String endDate) {
        WorkingDailyQueryRangeVo params = new WorkingDailyQueryRangeVo();
        params.setStartDate(startDate);
        params.setEndDate(endDate);
        params.setStaffIdList(
                employees.stream().map(ReportWebEmployee::getStaffId).collect(Collectors.toList()));
        ResponseResult<List<StaffWorkingRangeDto>> result = iBusinessStaffClient.queryRange(businessId, 0, params);
        if (Objects.isNull(result.getData()) || CollectionUtils.isEmpty(result.getData())) {
            log.info("iBusinessStaffClient.queryRange return empty, businessId: {}, params: {}", businessId, params);
            return;
        }

        List<StaffWorkingRangeDto> staffWorkingRangeDtoList = result.getData();
        Map<Integer, StaffWorkingRangeDto> staffToWorking = staffWorkingRangeDtoList.stream()
                .collect(Collectors.toMap(StaffWorkingRangeDto::getStaffId, Function.identity()));

        // 计算预约时长
        Map<Integer, Long> staffToServiceTime = getServiceTime(appointments, staffToWorking);
        // 计算无效时长
        Map<Integer, Integer> staffToBlockTime = getBlockTime(businessId, startDate, endDate, staffToWorking);

        employees.forEach(staff -> {
            Integer staffId = staff.getStaffId();
            StaffWorkingRangeDto staffWorkingRangeDto = staffToWorking.get(staffId);
            if (Objects.nonNull(staffWorkingRangeDto)) {
                staff.setTotalWorkingMinute(staffWorkingRangeDto.getTimeRange().values().stream()
                        .flatMap(Collection::stream)
                        .mapToInt(obj -> obj.getEndTime() - obj.getStartTime())
                        .sum());
            } else {
                staff.setTotalWorkingMinute(0);
            }

            staff.setTotalServiceMinute(staffToServiceTime.getOrDefault(staffId, 0L));
            staff.setTotalBlockMinute(staffToBlockTime.getOrDefault(staffId, 0));
        });
    }

    private Map<Integer, Long> getServiceTime(
            List<GroomingReportWebAppointment> appointments, Map<Integer, StaffWorkingRangeDto> staffToWorking) {
        Map<Integer, Long> staffToServiceTime = new HashMap<>();
        appointments.forEach(a -> a.getPetDetails().forEach(p -> {
            if (!CollectionUtils.isEmpty(p.getOperationList())) {
                p.getOperationList().forEach(operation -> {
                    Integer staffId = operation.getStaffId();
                    StaffWorkingRangeDto staffWorkingRangeDto = staffToWorking.get(staffId);
                    // 计算 service time：如果预约在工作时间，则计算，否则在非工作时间，则不计算
                    if (Objects.nonNull(staffWorkingRangeDto)
                            && !CollectionUtils.isEmpty(
                                    staffWorkingRangeDto.getTimeRange().get(a.getAppointmentDate()))) {
                        staffToServiceTime.put(
                                staffId, staffToServiceTime.getOrDefault(staffId, 0L) + operation.getDuration());
                    }
                });
                return;
            }

            Integer staffId = p.getStaffId();
            StaffWorkingRangeDto staffWorkingRangeDto = staffToWorking.get(staffId);

            // 计算 service time：如果预约在工作时间，则计算，否则在非工作时间，则不计算
            if (Objects.nonNull(staffWorkingRangeDto)
                    && !CollectionUtils.isEmpty(
                            staffWorkingRangeDto.getTimeRange().get(a.getAppointmentDate()))) {
                staffToServiceTime.put(
                        staffId, staffToServiceTime.getOrDefault(staffId, 0L) + p.getEndTime() - p.getStartTime());
            }
        }));
        return staffToServiceTime;
    }

    private Map<Integer, Integer> getBlockTime(
            Integer businessId, String startDate, String endDate, Map<Integer, StaffWorkingRangeDto> staffToWorking) {
        List<StaffBlockInfoDTO> blocks = appointmentMapper.selectBlockByDateRange(businessId, startDate, endDate);
        Map<Integer, Integer> staffToBlockTime = new HashMap<>();
        blocks.forEach(block -> {
            Integer staffId = block.getStaffId();

            StaffWorkingRangeDto staffWorkingRangeDto = staffToWorking.get(staffId);

            // 计算 block time：如果 block 在工作时间，则计算，否则在非工作时间，则不计算
            if (Objects.nonNull(staffWorkingRangeDto)
                    && !CollectionUtils.isEmpty(
                            staffWorkingRangeDto.getTimeRange().get(block.getDate()))) {
                staffToBlockTime.put(
                        staffId, staffToBlockTime.getOrDefault(staffId, 0) + block.getEndTime() - block.getStartTime());
            }
        });
        return staffToBlockTime;
    }

    private void processCounts(List<GroomingReportWebAppointment> appointments, List<ReportWebEmployee> employees) {
        Map<Integer, Set<Integer>> staffToAppts = new HashMap<>();
        Map<Integer, Set<Integer>> staffToFinishedAppts = new HashMap<>();
        Map<Integer, Set<Integer>> staffToPaidAppts = new HashMap<>();
        Map<Integer, Set<String>> staffToServicedPets = new HashMap<>();

        appointments.forEach(a -> a.getPetDetails().forEach(p -> {
            if (!CollectionUtils.isEmpty(p.getOperationList())) {
                p.getOperationList().forEach(opration -> {
                    Integer staffId = opration.getStaffId();
                    staffToAppts.computeIfAbsent(staffId, v -> new HashSet<>()).add(a.getId());
                    if (AppointmentStatusEnum.FINISHED.getValue().equals(a.getStatus())) {
                        staffToFinishedAppts
                                .computeIfAbsent(staffId, v -> new HashSet<>())
                                .add(a.getId());
                    }
                    staffToServicedPets
                            .computeIfAbsent(staffId, v -> new HashSet<>())
                            .add(opration.getPetId() + "|" + a.getId());
                });
                return;
            }
            Integer staffId = p.getStaffId();

            staffToAppts.computeIfAbsent(staffId, v -> new HashSet<>()).add(a.getId());

            if (AppointmentStatusEnum.FINISHED.getValue().equals(a.getStatus())) {
                staffToFinishedAppts
                        .computeIfAbsent(staffId, v -> new HashSet<>())
                        .add(a.getId());
            }
            // 统计部分支付/完全支付的订单
            if (!GroomingAppointmentEnum.NOT_PAY.equals(a.getIsPaid())) {
                staffToPaidAppts.computeIfAbsent(staffId, v -> new HashSet<>()).add(a.getId());
            }

            staffToServicedPets.computeIfAbsent(staffId, v -> new HashSet<>()).add(p.getPetId() + "|" + a.getId());
        }));

        employees.forEach(staff -> {
            Integer staffId = staff.getStaffId();
            staff.setTotalApptsAll(appointments.size());
            staff.setFinishedApptNum(staffToFinishedAppts
                    .getOrDefault(staffId, Collections.emptySet())
                    .size());
            staff.setPaidApptNum(staffToPaidAppts
                    .getOrDefault(staffId, Collections.emptySet())
                    .size());
            staff.setTotalAppts(
                    staffToAppts.getOrDefault(staffId, Collections.emptySet()).size());
            staff.setServicedPetNum(staffToServicedPets
                    .getOrDefault(staffId, Collections.emptySet())
                    .size());
        });
    }

    public List<LeaderboardRankInfoDTO> getServiceRankForLeaderboard(
            Integer businessId, String startDate, String endDate) {
        if (businessId == null || StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "getServiceRankForLeaderboard param is error");
        }
        // 获取带 invoice，staff 详情的预约
        List<GroomingReportWebAppointment> appointments =
                reportOrderService.queryWebReportApptEmployee(businessId, startDate, endDate, null);
        if (CollectionUtils.isEmpty(appointments)) {
            return Collections.emptyList();
        }

        Map<Integer, String> idToNameMap = appointments.stream()
                .flatMap(a -> a.getPetDetails().stream())
                .map(detail -> {
                    if (StringUtils.isBlank(detail.getServiceName())) {
                        detail.setServiceName(Strings.EMPTY);
                    }
                    return detail;
                })
                .collect(Collectors.toMap(
                        ReportWebApptPetDetail::getServiceId,
                        ReportWebApptPetDetail::getServiceName,
                        (existing, replacement) -> existing));

        List<LeaderboardRankInfoDTO> services = idToNameMap.keySet().stream()
                .map(sId -> LeaderboardRankInfoDTO.builder()
                        .id(sId)
                        .key(idToNameMap.get(sId))
                        .value(BigDecimal.ZERO)
                        .build())
                .collect(Collectors.toList());

        // 计算金额
        processMoneyByService(businessId, appointments, services);

        return services;
    }

    public List<LeaderboardRankInfoDTO> getClientRankForLeaderboard(
            Integer businessId, String startDate, String endDate) {
        if (businessId == null || StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "getClientRankForLeaderboard param is error");
        }

        // 获取带 invoice，staff 详情的预约
        List<GroomingReportWebAppointment> appointments =
                reportOrderService.queryWebReportApptEmployee(businessId, startDate, endDate, null);
        if (CollectionUtils.isEmpty(appointments)) {
            return Collections.emptyList();
        }

        List<LeaderboardRankInfoDTO> clients = appointments.stream()
                .map(GroomingReportWebAppointment::getCustomerId)
                .distinct()
                .map(sId -> LeaderboardRankInfoDTO.builder()
                        .id(sId)
                        .value(BigDecimal.ZERO)
                        .build())
                .collect(Collectors.toList());

        // 计算金额
        processMoneyByClient(appointments, clients);

        // 处理返回结果
        processClientInfo(clients);
        return clients;
    }

    public List<LeaderboardRankInfoDTO> getPetBreedRankForLeaderboard(
            Integer businessId, String startDate, String endDate) {
        if (businessId == null || StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "getPetBreedRankForLeaderboard param is error");
        }

        List<PetBreedInfoDTO> petBreedInfoDTOS = appointmentMapper.queryPetBreed(businessId, startDate, endDate);

        // 获取 petId 对应的 breed
        List<Integer> petIds =
                petBreedInfoDTOS.stream().map(PetBreedInfoDTO::getPetId).collect(Collectors.toList());
        Map<Integer, String> petIdToBreed = iPetClient.getGroomingCalenderPetInfo(petIds).stream()
                .collect(Collectors.toMap(GroomingCalenderPetInfo::getPetId, GroomingCalenderPetInfo::getPetBreed));

        // 合并相同 breed
        Map<String, Integer> breedToAmount = new HashMap<>();
        petBreedInfoDTOS.forEach(dto -> {
            String breed = petIdToBreed.get(dto.getPetId());
            breedToAmount.put(breed, breedToAmount.getOrDefault(breed, 0) + dto.getAmount());
        });

        return breedToAmount.entrySet().stream()
                .filter(entry -> Objects.nonNull(entry.getKey()) && Objects.nonNull(entry.getValue()))
                .map(entry -> LeaderboardRankInfoDTO.builder()
                        .key(entry.getKey())
                        .value(BigDecimal.valueOf(entry.getValue()))
                        .build())
                .collect(Collectors.toList());
    }

    private void processMoneyByService(
            Integer businessId,
            List<GroomingReportWebAppointment> appointments,
            List<LeaderboardRankInfoDTO> services) {
        // prepare id map
        Map<Integer, LeaderboardRankInfoDTO> idToService =
                services.stream().collect(Collectors.toMap(LeaderboardRankInfoDTO::getId, Function.identity()));

        // 获取refund记录，并赋值到appt
        Set<Integer> invoiceIds = appointments.stream()
                .map(GroomingReportWebAppointment::getInvoiceId)
                .collect(Collectors.toSet());
        Map<Integer, BigDecimal> refundMap = reportOrderService.getRefundMap(invoiceIds);
        if (!CollectionUtils.isEmpty(refundMap)) {
            appointments.forEach(
                    appt -> appt.setRefundedAmount(refundMap.getOrDefault(appt.getInvoiceId(), BigDecimal.ZERO)));
        }

        // 获取服务的tax
        Map<Integer, List<MoeGroomingInvoiceItem>> invoiceItemMap = reportOrderService.getInvoiceItemMap(appointments);

        // 获取订单tip split记录
        Map<Integer, Map<Integer, BigDecimal>> tipSplitMap =
                reportCalculateService.getTipSplitMap(businessId, appointments);

        for (GroomingReportWebAppointment apt : appointments) {
            dealApptMoneyReportByService(
                    apt,
                    idToService,
                    invoiceItemMap.get(apt.getId()),
                    tipSplitMap.getOrDefault(apt.getInvoiceId(), Collections.emptyMap()));
        }
    }

    private void dealApptMoneyReportByService(
            GroomingReportWebAppointment apt,
            Map<Integer, LeaderboardRankInfoDTO> idToService,
            List<MoeGroomingInvoiceItem> invoiceItems,
            Map<Integer, BigDecimal> staffTipMap) {
        List<ReportWebApptPetDetail> pds = apt.getPetDetails();
        if (CollectionUtils.isEmpty(pds)) {
            log.error("No pet detail found for appointment: {}", GsonUtil.toJson(apt, false));
            return;
        }

        // 总service金额、discount金额、tips金额
        BigDecimal totalServiceAmount =
                pds.stream().map(ReportWebApptPetDetail::getServicePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 应收总金额
        BigDecimal totalRevenue = totalServiceAmount
                .add(apt.getTipsAmount())
                .add(apt.getTaxAmount())
                .subtract(apt.getDiscountAmount());
        // 总收入小于等于0，后面的分配都是0，跳过这个Appointment
        if (totalRevenue.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        // 已收、退款金额
        BigDecimal paidAmount = apt.getPaidAmount();
        BigDecimal refundedAmount = apt.getRefundedAmount();

        // 预约是否支付
        Byte aptIsPaid = apt.getIsPaid();

        // 计算tips,discount,tax
        Map<Integer, Map<Integer, BigDecimal>> tipsMap = calculateTips(staffTipMap, apt.getPetDetails());
        //        BigDecimal serviceDiscountAmount =
        //                apt.getServiceDiscountAmount() != null ? apt.getServiceDiscountAmount() :
        // apt.getDiscountAmount();
        Map<Integer, BigDecimal> discountMap = calculateDiscount(apt.getPetDetails(), invoiceItems);
        Map<Integer, BigDecimal> taxMap = calculateTax(apt.getPetDetails(), invoiceItems);

        // 保存需要按服务金额比例分配的金额，确保最后所有相加等于总数
        BigDecimal tempTotalPaidAmount = BigDecimal.ZERO;
        BigDecimal tempTotalRefund = BigDecimal.ZERO;
        for (int i = 0, size = pds.size(); i < size; i++) {
            ReportWebApptPetDetail petDetail = apt.getPetDetails().get(i);
            BigDecimal servicePrice = petDetail.getServicePrice();

            // 应收总tips
            BigDecimal tips = tipsMap.getOrDefault(petDetail.getId(), new HashMap<>()).values().stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 应收总discount
            BigDecimal discount = discountMap.getOrDefault(petDetail.getId(), BigDecimal.ZERO);

            // 应收总tax
            // tax: (servicePrice - discount) * taxRate，约分方法：2位小数，HALF_EVEN(银行家约分法)
            BigDecimal tax = taxMap.getOrDefault(petDetail.getId(), BigDecimal.ZERO);

            // 当前服务的应收金额，包括tips，tax和discount，用于计算未收、实收、refund所占比例
            BigDecimal serviceRevenue = servicePrice.add(tips).add(tax).subtract(discount);

            // 新增 currentPaidAmount:分配到当前服务的已付金额，替代原来的collectedRevenue
            // 新增 currentRefundAmount:分配到当前服务的退款金额
            // 修改 currentCollectedRev = currentPaidAmount - currentRefundAmount，分配到当前服务的实际收入
            BigDecimal currentPaidAmount = BigDecimal.ZERO;
            BigDecimal currentRefundAmount = BigDecimal.ZERO;
            BigDecimal currentCollectedRev = BigDecimal.ZERO;
            if (!GroomingAppointmentEnum.NOT_PAY.equals(aptIsPaid)) {
                // 如果不是未支付的预约
                if (BigDecimal.ZERO.compareTo(totalRevenue) < 0) {
                    if (i != size - 1) {
                        currentPaidAmount = serviceRevenue.multiply(paidAmount).divide(totalRevenue, 2, HALF_UP);
                        tempTotalPaidAmount = tempTotalPaidAmount.add(currentPaidAmount);
                    } else {
                        // 最后一个服务不按比例分配，用总数减去之前的总和以保证各个服务相加等于总数
                        currentPaidAmount = paidAmount.subtract(tempTotalPaidAmount);
                    }

                    if (refundedAmount != null && refundedAmount.compareTo(BigDecimal.ZERO) > 0) {
                        if (i != size - 1) {
                            currentRefundAmount =
                                    serviceRevenue.multiply(refundedAmount).divide(totalRevenue, 2, HALF_UP);
                            tempTotalRefund = tempTotalRefund.add(currentRefundAmount);
                        } else {
                            currentRefundAmount = refundedAmount.subtract(tempTotalRefund);
                        }
                    }
                    currentCollectedRev = currentPaidAmount.subtract(currentRefundAmount);
                }

                /*
                  计算collectedTips ,collectedTax
                  serviceCollectedRev进行分配，优先分配给tax，有剩余再分配给tips，剩下的才是service的
                */
                BigDecimal collectedTax = BigDecimal.ZERO;
                BigDecimal collectedTips = BigDecimal.ZERO;
                if (tax.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal remainRev = currentCollectedRev.subtract(tax);
                    if (remainRev.compareTo(BigDecimal.ZERO) > 0) {
                        // 如果 currentCollectedRev(分到该staff已收到的钱) > tax(分到该staff的tax)
                        collectedTax = tax;
                        // 继续分配collectedTips
                        if (tips.compareTo(BigDecimal.ZERO) > 0) {
                            if (remainRev.compareTo(tips) > 0) {
                                // 如果减去tax后的剩余已支付金额还大于分到该staff的tips
                                collectedTips = tips;
                            } else {
                                collectedTips = remainRev;
                            }
                        }
                    } else {
                        // 否则优先分配已支付的金额给collectedTax
                        collectedTax = currentCollectedRev;
                    }
                } else if (tips.compareTo(BigDecimal.ZERO) > 0) {
                    if (currentCollectedRev.compareTo(tips) > 0) {
                        collectedTips = tips;
                    } else {
                        collectedTips = currentCollectedRev;
                    }
                }

                BigDecimal collectedServiceRev = currentCollectedRev
                        .subtract(collectedTips)
                        .subtract(collectedTax)
                        .add(discount);

                LeaderboardRankInfoDTO rankInfoDTO = idToService.get(petDetail.getServiceId());
                rankInfoDTO.setValue(rankInfoDTO.getValue().add(collectedServiceRev));
            }
        }
    }

    private void processMoneyByClient(
            List<GroomingReportWebAppointment> appointments, List<LeaderboardRankInfoDTO> clients) {
        // prepare id map
        Map<Integer, LeaderboardRankInfoDTO> idToClient =
                clients.stream().collect(Collectors.toMap(LeaderboardRankInfoDTO::getId, Function.identity()));

        // 获取refund记录，并赋值到appt
        Set<Integer> invoiceIds = appointments.stream()
                .map(GroomingReportWebAppointment::getInvoiceId)
                .collect(Collectors.toSet());
        Map<Integer, BigDecimal> refundMap = reportOrderService.getRefundMap(invoiceIds);
        appointments.forEach(
                appt -> appt.setRefundedAmount(refundMap.getOrDefault(appt.getInvoiceId(), BigDecimal.ZERO)));

        for (GroomingReportWebAppointment apt : appointments) {
            dealApptMoneyReportByClient(apt, idToClient);
        }
    }

    private void dealApptMoneyReportByClient(
            GroomingReportWebAppointment apt, Map<Integer, LeaderboardRankInfoDTO> idToClient) {
        List<ReportWebApptPetDetail> pds = apt.getPetDetails();
        if (CollectionUtils.isEmpty(pds)) {
            log.error("No pet detail found for appointment: {}", GsonUtil.toJson(apt, false));
            return;
        }

        // 预约未支付
        Byte aptIsPaid = apt.getIsPaid();
        if (GroomingAppointmentEnum.NOT_PAY.equals(aptIsPaid)) {
            return;
        }

        CollectedPriceDTO collectedPriceDTO =
                reportCalculateService.calculateCollectedPrice(new CalCollectedPriceParams()
                        .setPaidAmount(apt.getPaidAmount())
                        .setRefundAmount(apt.getRefundedAmount())
                        .setExpectedServiceTax(apt.getServiceTaxAmount())
                        .setExpectedProductTax(apt.getProductTaxAmount())
                        .setExpectedTips(apt.getTipsAmount())
                        .setServiceDiscountAmount(apt.getServiceDiscountAmount())
                        .setProductDiscountAmount(apt.getProductDiscountAmount())
                        .setTotalServiceSale(apt.getTotalServiceSale())
                        .setTotalProductSale(apt.getTotalProductSale()));

        LeaderboardRankInfoDTO rankInfoDTO = idToClient.get(apt.getCustomerId());
        rankInfoDTO.setValue(rankInfoDTO.getValue().add(collectedPriceDTO.getCollectedServicePrice()));
    }

    private void processClientInfo(List<LeaderboardRankInfoDTO> rankInfoList) {
        CustomerIdListParams params = new CustomerIdListParams();
        params.setIdList(
                rankInfoList.stream().map(LeaderboardRankInfoDTO::getId).collect(Collectors.toList()));

        List<MoeBusinessCustomerDTO> customerList = iCustomerClient.queryCustomerListWithDeleted(params);
        Map<Integer, MoeBusinessCustomerDTO> idToClientMap =
                customerList.stream().collect(Collectors.toMap(CustomerBasicDTO::getId, Function.identity()));

        rankInfoList.forEach(rank -> {
            MoeBusinessCustomerDTO customer = idToClientMap.get(rank.getId());
            if (Objects.nonNull(customer)) {
                rank.setKey(customer.getFirstName().trim() + " "
                        + customer.getLastName().trim());
            }
        });
    }
}
