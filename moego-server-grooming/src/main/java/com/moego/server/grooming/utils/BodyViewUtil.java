package com.moego.server.grooming.utils;

import com.moego.common.enums.PetTypeEnum;
import com.moego.server.grooming.config.S3Client;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.StringWriter;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import org.apache.batik.anim.dom.SAXSVGDocumentFactory;
import org.apache.batik.anim.dom.SVGOMPathElement;
import org.apache.batik.transcoder.TranscoderException;
import org.apache.batik.transcoder.TranscoderInput;
import org.apache.batik.transcoder.TranscoderOutput;
import org.apache.batik.transcoder.image.ImageTranscoder;
import org.apache.batik.transcoder.image.PNGTranscoder;
import org.apache.batik.util.XMLResourceDescriptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.svg.SVGDocument;

@Component
public class BodyViewUtil {

    public static final String DOG_EMPTY_SVG_PATH = "classpath:svg/grooming-report-dog.svg";
    public static final String CAT_EMPTY_SVG_PATH = "classpath:svg/grooming-report-cat.svg";

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private S3Client s3Client;

    private URI getResourceURI(String path) throws IOException {
        return resourceLoader.getResource(path).getURI();
    }

    public String markedAndUploadBodyView(Integer petTypeId, List<String> markedChoices, boolean isLeft)
            throws IOException, TransformerException, TranscoderException {
        if (CollectionUtils.isEmpty(markedChoices)) {
            return "";
        }

        String svgPath;
        if (Objects.equals(petTypeId, PetTypeEnum.DOG.getType())) {
            svgPath = DOG_EMPTY_SVG_PATH;
        } else if (Objects.equals(petTypeId, PetTypeEnum.CAT.getType())) {
            svgPath = CAT_EMPTY_SVG_PATH;
        } else {
            // 暂不支持其它类型 pet
            return "";
        }
        URI svgURI = getResourceURI(svgPath);

        String parser = XMLResourceDescriptor.getXMLParserClassName();
        SAXSVGDocumentFactory factory = new SAXSVGDocumentFactory(parser);
        Document document = factory.createDocument(svgURI.toString());

        // 标记位置
        NodeList nodeList = document.getDocumentElement().getElementsByTagName("path");
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            String nodeValue = node.getAttributes().getNamedItem("class").getNodeValue();
            if (markedChoices.contains(nodeValue)) {
                node.getAttributes().getNamedItem("fill").setNodeValue("#F96B18"); // 暂时写死橙色，后续可以优化为传入自定义主题色
            }
        }

        // 水平翻转
        if (!isLeft) {
            Element rootElement = ((SVGDocument) document).getRootElement();
            double widthValue = Double.parseDouble(rootElement.getAttribute("width"));
            flipElement(rootElement, widthValue);
        }

        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        StringWriter stringWriter = new StringWriter();
        transformer.transform(new DOMSource(document), new StreamResult(stringWriter));
        byte[] pngData = convertToPNG(stringWriter.toString());

        return s3Client.uploadPublicPNGFile(pngData);
    }

    private static void flipElement(Element element, double translateValue) {
        if (element instanceof SVGOMPathElement pathElement) {
            String transformValue = "translate(" + translateValue + ") scale(-1, 1)";
            pathElement.setAttributeNS(null, "transform", transformValue);
        }

        if (element.hasChildNodes()) {
            for (int i = 0; i < element.getChildNodes().getLength(); i++) {
                Node childNode = element.getChildNodes().item(i);
                if (childNode instanceof Element) {
                    flipElement((Element) childNode, translateValue);
                }
            }
        }
    }

    public byte[] convertToPNG(String svgCode) throws TranscoderException {
        byte[] bytes = svgCode.getBytes(StandardCharsets.UTF_8);
        PNGTranscoder t = new PNGTranscoder();
        TranscoderInput input = new TranscoderInput(new ByteArrayInputStream(bytes));
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        TranscoderOutput output = new TranscoderOutput(baos);
        t.addTranscodingHint(ImageTranscoder.KEY_WIDTH, 330F * 2);
        t.addTranscodingHint(ImageTranscoder.KEY_HEIGHT, 267F * 2);
        t.transcode(input, output);
        return baos.toByteArray();
    }
}
