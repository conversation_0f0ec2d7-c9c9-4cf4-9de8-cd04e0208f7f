package com.moego.server.grooming.convert;

import com.google.protobuf.Value;
import com.moego.common.params.VaccineParams;
import com.moego.idl.models.online_booking.v1.Pet;
import com.moego.lib.common.proto.ProtoUtils;
import com.moego.server.grooming.params.BookOnlinePetParams;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 创建 deposit order 过程中需要用到的 converter.
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface DepositOrderConverter {
    DepositOrderConverter INSTANCE = Mappers.getMapper(DepositOrderConverter.class);

    @Mapping(source = "vaccineList", target = "vaccineListList")
    // Workaround for protobuf map
    @Mapping(target = "mutablePetQuestionAnswers", source = "petQuestionAnswers")
    @Mapping(target = "petQuestionAnswers", ignore = true)
    Pet toModel(BookOnlinePetParams params);

    @Mapping(source = "documentUrls", target = "documentUrlsList")
    Pet.Vaccine toModel(VaccineParams params);

    default Value map(Object o) {
        return ProtoUtils.toValue(o);
    }
}
