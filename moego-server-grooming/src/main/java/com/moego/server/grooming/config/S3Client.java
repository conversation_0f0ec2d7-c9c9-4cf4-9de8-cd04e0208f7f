package com.moego.server.grooming.config;

import com.moego.common.utils.CommonUtil;
import com.moego.lib.aws.S3API;
import com.moego.lib.aws.model.S3Context;
import com.moego.lib.utils.CoreUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.s3.model.ObjectCannedACL;

/**
 * TODO(Freeman): need to be replaced by <PERSON>.
 */
@Component
@Slf4j
public class S3Client {

    @Value("${s3.public.domain}")
    private String publicDomain;

    @Value("${s3.public.bucket}")
    private String publicBucketName;

    @Value("${s3.public.prefix.export-file}")
    private String publicExportFilePrefix;

    private static final String CONTENT_TYPE_EXCEL = "application/vnd.openxmlformats-officedocument.spreadsheetml.xlsx";
    private static final String CONTENT_TYPE_PNG = "image/png";

    @Autowired
    private S3API s3API;

    /**
     * 上传 excel file
     */
    public String uploadExcelFile(byte[] data, String fileName) {
        if (!fileName.endsWith(".xlsx")) {
            fileName = fileName + ".xlsx";
        }

        return doUpload(data, fileName, CONTENT_TYPE_EXCEL);
    }

    public boolean isExisting(String bucket, String key) {
        return this.s3API.isExistingObject(bucket, key);
    }

    public String upload(byte[] data, String bucket, String key, String contentType, String contentDisposition) {
        String acl = ObjectCannedACL.PUBLIC_READ.toString();
        var ctx = S3Context.from(data, contentType, null, null, contentDisposition, acl, null, null, null);
        try {
            var eTag = this.s3API.upload(bucket, key, ctx);
            log.info("upload object '{}' successfully, eTag: {}", key, eTag);
        } catch (Exception e) {
            throw new RuntimeException("upload object failed: " + key);
        }

        return publicDomain + key;
    }

    /**
     * 上传文件处理
     * 设置下载文件名，url增加随机字符串
     */
    private String doUpload(byte[] data, String fileName, String contentType) {
        String key = publicExportFilePrefix + CoreUtils.randomString(44) + "-" + fileName;
        String contentDisposition = "attachment; filename=\"" + fileName + "\"";
        return upload(data, publicBucketName, key, contentType, contentDisposition);
    }

    public String uploadPublicPNGFile(byte[] data) {
        String fileName = CommonUtil.get10Timestamp() + CommonUtil.getUuid() + ".png";
        return doUpload(data, fileName, CONTENT_TYPE_PNG);
    }
}
