package com.moego.server.grooming.mapperbean;

import com.moego.idl.models.online_booking.v1.ExistingClientAccessMode;
import com.moego.idl.models.online_booking.v1.ExistingPetAccessMode;
import com.moego.idl.models.online_booking.v1.NewClientAccessMode;
import com.moego.idl.models.online_booking.v1.NewPetAccessMode;
import java.util.ArrayList;
import java.util.List;

public class MoeBookOnlineQuestionExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    public MoeBookOnlineQuestionExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> newPetAccessModeCriteria;

        protected List<Criterion> existingPetAccessModeCriteria;

        protected List<Criterion> newClientAccessModeCriteria;

        protected List<Criterion> existingClientAccessModeCriteria;

        protected List<Criterion> allCriteria;

        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
            newPetAccessModeCriteria = new ArrayList<>();
            existingPetAccessModeCriteria = new ArrayList<>();
            newClientAccessModeCriteria = new ArrayList<>();
            existingClientAccessModeCriteria = new ArrayList<>();
        }

        public List<Criterion> getNewPetAccessModeCriteria() {
            return newPetAccessModeCriteria;
        }

        protected void addNewPetAccessModeCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            newPetAccessModeCriteria.add(new Criterion(
                    condition,
                    value,
                    "com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler"));
            allCriteria = null;
        }

        protected void addNewPetAccessModeCriterion(
                String condition, NewPetAccessMode value1, NewPetAccessMode value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            newPetAccessModeCriteria.add(new Criterion(
                    condition,
                    value1,
                    value2,
                    "com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewPetAccessModeTypeHandler"));
            allCriteria = null;
        }

        public List<Criterion> getExistingPetAccessModeCriteria() {
            return existingPetAccessModeCriteria;
        }

        protected void addExistingPetAccessModeCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            existingPetAccessModeCriteria.add(
                    new Criterion(
                            condition,
                            value,
                            "com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler"));
            allCriteria = null;
        }

        protected void addExistingPetAccessModeCriterion(
                String condition, ExistingPetAccessMode value1, ExistingPetAccessMode value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            existingPetAccessModeCriteria.add(
                    new Criterion(
                            condition,
                            value1,
                            value2,
                            "com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingPetAccessModeTypeHandler"));
            allCriteria = null;
        }

        public List<Criterion> getNewClientAccessModeCriteria() {
            return newClientAccessModeCriteria;
        }

        protected void addNewClientAccessModeCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            newClientAccessModeCriteria.add(
                    new Criterion(
                            condition,
                            value,
                            "com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler"));
            allCriteria = null;
        }

        protected void addNewClientAccessModeCriterion(
                String condition, NewClientAccessMode value1, NewClientAccessMode value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            newClientAccessModeCriteria.add(
                    new Criterion(
                            condition,
                            value1,
                            value2,
                            "com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionNewClientAccessModeTypeHandler"));
            allCriteria = null;
        }

        public List<Criterion> getExistingClientAccessModeCriteria() {
            return existingClientAccessModeCriteria;
        }

        protected void addExistingClientAccessModeCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            existingClientAccessModeCriteria.add(
                    new Criterion(
                            condition,
                            value,
                            "com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler"));
            allCriteria = null;
        }

        protected void addExistingClientAccessModeCriterion(
                String condition, ExistingClientAccessMode value1, ExistingClientAccessMode value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            existingClientAccessModeCriteria.add(
                    new Criterion(
                            condition,
                            value1,
                            value2,
                            "com.moego.server.grooming.mapper.typehandler.MoeBookOnlineQuestionExistingClientAccessModeTypeHandler"));
            allCriteria = null;
        }

        public boolean isValid() {
            return criteria.size() > 0
                    || newPetAccessModeCriteria.size() > 0
                    || existingPetAccessModeCriteria.size() > 0
                    || newClientAccessModeCriteria.size() > 0
                    || existingClientAccessModeCriteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            if (allCriteria == null) {
                allCriteria = new ArrayList<>();
                allCriteria.addAll(criteria);
                allCriteria.addAll(newPetAccessModeCriteria);
                allCriteria.addAll(existingPetAccessModeCriteria);
                allCriteria.addAll(newClientAccessModeCriteria);
                allCriteria.addAll(existingClientAccessModeCriteria);
            }
            return allCriteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
            allCriteria = null;
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andQuestionIsNull() {
            addCriterion("question is null");
            return (Criteria) this;
        }

        public Criteria andQuestionIsNotNull() {
            addCriterion("question is not null");
            return (Criteria) this;
        }

        public Criteria andQuestionEqualTo(String value) {
            addCriterion("question =", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotEqualTo(String value) {
            addCriterion("question <>", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionGreaterThan(String value) {
            addCriterion("question >", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionGreaterThanOrEqualTo(String value) {
            addCriterion("question >=", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionLessThan(String value) {
            addCriterion("question <", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionLessThanOrEqualTo(String value) {
            addCriterion("question <=", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionLike(String value) {
            addCriterion("question like", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotLike(String value) {
            addCriterion("question not like", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionIn(List<String> values) {
            addCriterion("question in", values, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotIn(List<String> values) {
            addCriterion("question not in", values, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionBetween(String value1, String value2) {
            addCriterion("question between", value1, value2, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotBetween(String value1, String value2) {
            addCriterion("question not between", value1, value2, "question");
            return (Criteria) this;
        }

        public Criteria andPlaceholderIsNull() {
            addCriterion("placeholder is null");
            return (Criteria) this;
        }

        public Criteria andPlaceholderIsNotNull() {
            addCriterion("placeholder is not null");
            return (Criteria) this;
        }

        public Criteria andPlaceholderEqualTo(String value) {
            addCriterion("placeholder =", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderNotEqualTo(String value) {
            addCriterion("placeholder <>", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderGreaterThan(String value) {
            addCriterion("placeholder >", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderGreaterThanOrEqualTo(String value) {
            addCriterion("placeholder >=", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderLessThan(String value) {
            addCriterion("placeholder <", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderLessThanOrEqualTo(String value) {
            addCriterion("placeholder <=", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderLike(String value) {
            addCriterion("placeholder like", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderNotLike(String value) {
            addCriterion("placeholder not like", value, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderIn(List<String> values) {
            addCriterion("placeholder in", values, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderNotIn(List<String> values) {
            addCriterion("placeholder not in", values, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderBetween(String value1, String value2) {
            addCriterion("placeholder between", value1, value2, "placeholder");
            return (Criteria) this;
        }

        public Criteria andPlaceholderNotBetween(String value1, String value2) {
            addCriterion("placeholder not between", value1, value2, "placeholder");
            return (Criteria) this;
        }

        public Criteria andIsShowIsNull() {
            addCriterion("is_show is null");
            return (Criteria) this;
        }

        public Criteria andIsShowIsNotNull() {
            addCriterion("is_show is not null");
            return (Criteria) this;
        }

        public Criteria andIsShowEqualTo(Byte value) {
            addCriterion("is_show =", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowNotEqualTo(Byte value) {
            addCriterion("is_show <>", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowGreaterThan(Byte value) {
            addCriterion("is_show >", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_show >=", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowLessThan(Byte value) {
            addCriterion("is_show <", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowLessThanOrEqualTo(Byte value) {
            addCriterion("is_show <=", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowIn(List<Byte> values) {
            addCriterion("is_show in", values, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowNotIn(List<Byte> values) {
            addCriterion("is_show not in", values, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowBetween(Byte value1, Byte value2) {
            addCriterion("is_show between", value1, value2, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowNotBetween(Byte value1, Byte value2) {
            addCriterion("is_show not between", value1, value2, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsRequiredIsNull() {
            addCriterion("is_required is null");
            return (Criteria) this;
        }

        public Criteria andIsRequiredIsNotNull() {
            addCriterion("is_required is not null");
            return (Criteria) this;
        }

        public Criteria andIsRequiredEqualTo(Byte value) {
            addCriterion("is_required =", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredNotEqualTo(Byte value) {
            addCriterion("is_required <>", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredGreaterThan(Byte value) {
            addCriterion("is_required >", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_required >=", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredLessThan(Byte value) {
            addCriterion("is_required <", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredLessThanOrEqualTo(Byte value) {
            addCriterion("is_required <=", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredIn(List<Byte> values) {
            addCriterion("is_required in", values, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredNotIn(List<Byte> values) {
            addCriterion("is_required not in", values, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredBetween(Byte value1, Byte value2) {
            addCriterion("is_required between", value1, value2, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredNotBetween(Byte value1, Byte value2) {
            addCriterion("is_required not between", value1, value2, "isRequired");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Byte value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Byte value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Byte value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Byte value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Byte value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Byte> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Byte> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Byte value1, Byte value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andIsAllowDeleteIsNull() {
            addCriterion("is_allow_delete is null");
            return (Criteria) this;
        }

        public Criteria andIsAllowDeleteIsNotNull() {
            addCriterion("is_allow_delete is not null");
            return (Criteria) this;
        }

        public Criteria andIsAllowDeleteEqualTo(Byte value) {
            addCriterion("is_allow_delete =", value, "isAllowDelete");
            return (Criteria) this;
        }

        public Criteria andIsAllowDeleteNotEqualTo(Byte value) {
            addCriterion("is_allow_delete <>", value, "isAllowDelete");
            return (Criteria) this;
        }

        public Criteria andIsAllowDeleteGreaterThan(Byte value) {
            addCriterion("is_allow_delete >", value, "isAllowDelete");
            return (Criteria) this;
        }

        public Criteria andIsAllowDeleteGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_allow_delete >=", value, "isAllowDelete");
            return (Criteria) this;
        }

        public Criteria andIsAllowDeleteLessThan(Byte value) {
            addCriterion("is_allow_delete <", value, "isAllowDelete");
            return (Criteria) this;
        }

        public Criteria andIsAllowDeleteLessThanOrEqualTo(Byte value) {
            addCriterion("is_allow_delete <=", value, "isAllowDelete");
            return (Criteria) this;
        }

        public Criteria andIsAllowDeleteIn(List<Byte> values) {
            addCriterion("is_allow_delete in", values, "isAllowDelete");
            return (Criteria) this;
        }

        public Criteria andIsAllowDeleteNotIn(List<Byte> values) {
            addCriterion("is_allow_delete not in", values, "isAllowDelete");
            return (Criteria) this;
        }

        public Criteria andIsAllowDeleteBetween(Byte value1, Byte value2) {
            addCriterion("is_allow_delete between", value1, value2, "isAllowDelete");
            return (Criteria) this;
        }

        public Criteria andIsAllowDeleteNotBetween(Byte value1, Byte value2) {
            addCriterion("is_allow_delete not between", value1, value2, "isAllowDelete");
            return (Criteria) this;
        }

        public Criteria andIsAllowChangeIsNull() {
            addCriterion("is_allow_change is null");
            return (Criteria) this;
        }

        public Criteria andIsAllowChangeIsNotNull() {
            addCriterion("is_allow_change is not null");
            return (Criteria) this;
        }

        public Criteria andIsAllowChangeEqualTo(Byte value) {
            addCriterion("is_allow_change =", value, "isAllowChange");
            return (Criteria) this;
        }

        public Criteria andIsAllowChangeNotEqualTo(Byte value) {
            addCriterion("is_allow_change <>", value, "isAllowChange");
            return (Criteria) this;
        }

        public Criteria andIsAllowChangeGreaterThan(Byte value) {
            addCriterion("is_allow_change >", value, "isAllowChange");
            return (Criteria) this;
        }

        public Criteria andIsAllowChangeGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_allow_change >=", value, "isAllowChange");
            return (Criteria) this;
        }

        public Criteria andIsAllowChangeLessThan(Byte value) {
            addCriterion("is_allow_change <", value, "isAllowChange");
            return (Criteria) this;
        }

        public Criteria andIsAllowChangeLessThanOrEqualTo(Byte value) {
            addCriterion("is_allow_change <=", value, "isAllowChange");
            return (Criteria) this;
        }

        public Criteria andIsAllowChangeIn(List<Byte> values) {
            addCriterion("is_allow_change in", values, "isAllowChange");
            return (Criteria) this;
        }

        public Criteria andIsAllowChangeNotIn(List<Byte> values) {
            addCriterion("is_allow_change not in", values, "isAllowChange");
            return (Criteria) this;
        }

        public Criteria andIsAllowChangeBetween(Byte value1, Byte value2) {
            addCriterion("is_allow_change between", value1, value2, "isAllowChange");
            return (Criteria) this;
        }

        public Criteria andIsAllowChangeNotBetween(Byte value1, Byte value2) {
            addCriterion("is_allow_change not between", value1, value2, "isAllowChange");
            return (Criteria) this;
        }

        public Criteria andIsAllowEditIsNull() {
            addCriterion("is_allow_edit is null");
            return (Criteria) this;
        }

        public Criteria andIsAllowEditIsNotNull() {
            addCriterion("is_allow_edit is not null");
            return (Criteria) this;
        }

        public Criteria andIsAllowEditEqualTo(Byte value) {
            addCriterion("is_allow_edit =", value, "isAllowEdit");
            return (Criteria) this;
        }

        public Criteria andIsAllowEditNotEqualTo(Byte value) {
            addCriterion("is_allow_edit <>", value, "isAllowEdit");
            return (Criteria) this;
        }

        public Criteria andIsAllowEditGreaterThan(Byte value) {
            addCriterion("is_allow_edit >", value, "isAllowEdit");
            return (Criteria) this;
        }

        public Criteria andIsAllowEditGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_allow_edit >=", value, "isAllowEdit");
            return (Criteria) this;
        }

        public Criteria andIsAllowEditLessThan(Byte value) {
            addCriterion("is_allow_edit <", value, "isAllowEdit");
            return (Criteria) this;
        }

        public Criteria andIsAllowEditLessThanOrEqualTo(Byte value) {
            addCriterion("is_allow_edit <=", value, "isAllowEdit");
            return (Criteria) this;
        }

        public Criteria andIsAllowEditIn(List<Byte> values) {
            addCriterion("is_allow_edit in", values, "isAllowEdit");
            return (Criteria) this;
        }

        public Criteria andIsAllowEditNotIn(List<Byte> values) {
            addCriterion("is_allow_edit not in", values, "isAllowEdit");
            return (Criteria) this;
        }

        public Criteria andIsAllowEditBetween(Byte value1, Byte value2) {
            addCriterion("is_allow_edit between", value1, value2, "isAllowEdit");
            return (Criteria) this;
        }

        public Criteria andIsAllowEditNotBetween(Byte value1, Byte value2) {
            addCriterion("is_allow_edit not between", value1, value2, "isAllowEdit");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("sort is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("sort is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("sort =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("sort <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("sort >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("sort <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("sort <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("sort in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("sort not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("sort between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("sort not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andQuestionTypeIsNull() {
            addCriterion("question_type is null");
            return (Criteria) this;
        }

        public Criteria andQuestionTypeIsNotNull() {
            addCriterion("question_type is not null");
            return (Criteria) this;
        }

        public Criteria andQuestionTypeEqualTo(Byte value) {
            addCriterion("question_type =", value, "questionType");
            return (Criteria) this;
        }

        public Criteria andQuestionTypeNotEqualTo(Byte value) {
            addCriterion("question_type <>", value, "questionType");
            return (Criteria) this;
        }

        public Criteria andQuestionTypeGreaterThan(Byte value) {
            addCriterion("question_type >", value, "questionType");
            return (Criteria) this;
        }

        public Criteria andQuestionTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("question_type >=", value, "questionType");
            return (Criteria) this;
        }

        public Criteria andQuestionTypeLessThan(Byte value) {
            addCriterion("question_type <", value, "questionType");
            return (Criteria) this;
        }

        public Criteria andQuestionTypeLessThanOrEqualTo(Byte value) {
            addCriterion("question_type <=", value, "questionType");
            return (Criteria) this;
        }

        public Criteria andQuestionTypeIn(List<Byte> values) {
            addCriterion("question_type in", values, "questionType");
            return (Criteria) this;
        }

        public Criteria andQuestionTypeNotIn(List<Byte> values) {
            addCriterion("question_type not in", values, "questionType");
            return (Criteria) this;
        }

        public Criteria andQuestionTypeBetween(Byte value1, Byte value2) {
            addCriterion("question_type between", value1, value2, "questionType");
            return (Criteria) this;
        }

        public Criteria andQuestionTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("question_type not between", value1, value2, "questionType");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andNewPetAccessModeIsNull() {
            addCriterion("new_pet_access_mode is null");
            return (Criteria) this;
        }

        public Criteria andNewPetAccessModeIsNotNull() {
            addCriterion("new_pet_access_mode is not null");
            return (Criteria) this;
        }

        public Criteria andNewPetAccessModeEqualTo(NewPetAccessMode value) {
            addNewPetAccessModeCriterion("new_pet_access_mode =", value, "newPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewPetAccessModeNotEqualTo(NewPetAccessMode value) {
            addNewPetAccessModeCriterion("new_pet_access_mode <>", value, "newPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewPetAccessModeGreaterThan(NewPetAccessMode value) {
            addNewPetAccessModeCriterion("new_pet_access_mode >", value, "newPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewPetAccessModeGreaterThanOrEqualTo(NewPetAccessMode value) {
            addNewPetAccessModeCriterion("new_pet_access_mode >=", value, "newPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewPetAccessModeLessThan(NewPetAccessMode value) {
            addNewPetAccessModeCriterion("new_pet_access_mode <", value, "newPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewPetAccessModeLessThanOrEqualTo(NewPetAccessMode value) {
            addNewPetAccessModeCriterion("new_pet_access_mode <=", value, "newPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewPetAccessModeIn(List<NewPetAccessMode> values) {
            addNewPetAccessModeCriterion("new_pet_access_mode in", values, "newPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewPetAccessModeNotIn(List<NewPetAccessMode> values) {
            addNewPetAccessModeCriterion("new_pet_access_mode not in", values, "newPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewPetAccessModeBetween(NewPetAccessMode value1, NewPetAccessMode value2) {
            addNewPetAccessModeCriterion("new_pet_access_mode between", value1, value2, "newPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewPetAccessModeNotBetween(NewPetAccessMode value1, NewPetAccessMode value2) {
            addNewPetAccessModeCriterion("new_pet_access_mode not between", value1, value2, "newPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingPetAccessModeIsNull() {
            addCriterion("existing_pet_access_mode is null");
            return (Criteria) this;
        }

        public Criteria andExistingPetAccessModeIsNotNull() {
            addCriterion("existing_pet_access_mode is not null");
            return (Criteria) this;
        }

        public Criteria andExistingPetAccessModeEqualTo(ExistingPetAccessMode value) {
            addExistingPetAccessModeCriterion("existing_pet_access_mode =", value, "existingPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingPetAccessModeNotEqualTo(ExistingPetAccessMode value) {
            addExistingPetAccessModeCriterion("existing_pet_access_mode <>", value, "existingPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingPetAccessModeGreaterThan(ExistingPetAccessMode value) {
            addExistingPetAccessModeCriterion("existing_pet_access_mode >", value, "existingPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingPetAccessModeGreaterThanOrEqualTo(ExistingPetAccessMode value) {
            addExistingPetAccessModeCriterion("existing_pet_access_mode >=", value, "existingPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingPetAccessModeLessThan(ExistingPetAccessMode value) {
            addExistingPetAccessModeCriterion("existing_pet_access_mode <", value, "existingPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingPetAccessModeLessThanOrEqualTo(ExistingPetAccessMode value) {
            addExistingPetAccessModeCriterion("existing_pet_access_mode <=", value, "existingPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingPetAccessModeIn(List<ExistingPetAccessMode> values) {
            addExistingPetAccessModeCriterion("existing_pet_access_mode in", values, "existingPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingPetAccessModeNotIn(List<ExistingPetAccessMode> values) {
            addExistingPetAccessModeCriterion("existing_pet_access_mode not in", values, "existingPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingPetAccessModeBetween(ExistingPetAccessMode value1, ExistingPetAccessMode value2) {
            addExistingPetAccessModeCriterion(
                    "existing_pet_access_mode between", value1, value2, "existingPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingPetAccessModeNotBetween(ExistingPetAccessMode value1, ExistingPetAccessMode value2) {
            addExistingPetAccessModeCriterion(
                    "existing_pet_access_mode not between", value1, value2, "existingPetAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewClientAccessModeIsNull() {
            addCriterion("new_client_access_mode is null");
            return (Criteria) this;
        }

        public Criteria andNewClientAccessModeIsNotNull() {
            addCriterion("new_client_access_mode is not null");
            return (Criteria) this;
        }

        public Criteria andNewClientAccessModeEqualTo(NewClientAccessMode value) {
            addNewClientAccessModeCriterion("new_client_access_mode =", value, "newClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewClientAccessModeNotEqualTo(NewClientAccessMode value) {
            addNewClientAccessModeCriterion("new_client_access_mode <>", value, "newClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewClientAccessModeGreaterThan(NewClientAccessMode value) {
            addNewClientAccessModeCriterion("new_client_access_mode >", value, "newClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewClientAccessModeGreaterThanOrEqualTo(NewClientAccessMode value) {
            addNewClientAccessModeCriterion("new_client_access_mode >=", value, "newClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewClientAccessModeLessThan(NewClientAccessMode value) {
            addNewClientAccessModeCriterion("new_client_access_mode <", value, "newClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewClientAccessModeLessThanOrEqualTo(NewClientAccessMode value) {
            addNewClientAccessModeCriterion("new_client_access_mode <=", value, "newClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewClientAccessModeIn(List<NewClientAccessMode> values) {
            addNewClientAccessModeCriterion("new_client_access_mode in", values, "newClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewClientAccessModeNotIn(List<NewClientAccessMode> values) {
            addNewClientAccessModeCriterion("new_client_access_mode not in", values, "newClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewClientAccessModeBetween(NewClientAccessMode value1, NewClientAccessMode value2) {
            addNewClientAccessModeCriterion("new_client_access_mode between", value1, value2, "newClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andNewClientAccessModeNotBetween(NewClientAccessMode value1, NewClientAccessMode value2) {
            addNewClientAccessModeCriterion(
                    "new_client_access_mode not between", value1, value2, "newClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingClientAccessModeIsNull() {
            addCriterion("existing_client_access_mode is null");
            return (Criteria) this;
        }

        public Criteria andExistingClientAccessModeIsNotNull() {
            addCriterion("existing_client_access_mode is not null");
            return (Criteria) this;
        }

        public Criteria andExistingClientAccessModeEqualTo(ExistingClientAccessMode value) {
            addExistingClientAccessModeCriterion("existing_client_access_mode =", value, "existingClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingClientAccessModeNotEqualTo(ExistingClientAccessMode value) {
            addExistingClientAccessModeCriterion("existing_client_access_mode <>", value, "existingClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingClientAccessModeGreaterThan(ExistingClientAccessMode value) {
            addExistingClientAccessModeCriterion("existing_client_access_mode >", value, "existingClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingClientAccessModeGreaterThanOrEqualTo(ExistingClientAccessMode value) {
            addExistingClientAccessModeCriterion("existing_client_access_mode >=", value, "existingClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingClientAccessModeLessThan(ExistingClientAccessMode value) {
            addExistingClientAccessModeCriterion("existing_client_access_mode <", value, "existingClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingClientAccessModeLessThanOrEqualTo(ExistingClientAccessMode value) {
            addExistingClientAccessModeCriterion("existing_client_access_mode <=", value, "existingClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingClientAccessModeIn(List<ExistingClientAccessMode> values) {
            addExistingClientAccessModeCriterion("existing_client_access_mode in", values, "existingClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingClientAccessModeNotIn(List<ExistingClientAccessMode> values) {
            addExistingClientAccessModeCriterion(
                    "existing_client_access_mode not in", values, "existingClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingClientAccessModeBetween(
                ExistingClientAccessMode value1, ExistingClientAccessMode value2) {
            addExistingClientAccessModeCriterion(
                    "existing_client_access_mode between", value1, value2, "existingClientAccessMode");
            return (Criteria) this;
        }

        public Criteria andExistingClientAccessModeNotBetween(
                ExistingClientAccessMode value1, ExistingClientAccessMode value2) {
            addExistingClientAccessModeCriterion(
                    "existing_client_access_mode not between", value1, value2, "existingClientAccessMode");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_question
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
