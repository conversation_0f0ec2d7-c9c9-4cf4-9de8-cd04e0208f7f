package com.moego.server.grooming.mapstruct;

import com.moego.common.params.VaccineParams;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import com.moego.server.customer.params.CustomerPetAddParams;
import com.moego.server.grooming.dto.AbandonPetDTO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet;
import com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO;
import java.util.List;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Mapper(
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        imports = {JsonUtil.class, List.class},
        builder = @Builder(disableBuilder = true))
public interface AbandonRecordPetMapper {
    AbandonRecordPetMapper INSTANCE = Mappers.getMapper(AbandonRecordPetMapper.class);

    @Mappings({
        @Mapping(target = "vaccineList", expression = "java(vaccine2VO(recordPet.getVaccineList()))"),
        @Mapping(target = "addOnIds", expression = "java(addOnIds2VO(recordPet.getAddonIds()))"),
        @Mapping(target = "questionAnswerList", expression = "java(List.of())"),
    })
    AbandonClientRecordVO.AbandonPetDetailVO entity2VO(MoeBookOnlineAbandonRecordPet recordPet);

    default List<AbandonClientRecordVO.AbandonPetDetailVO.VaccineDetailVO> vaccine2VO(String vaccineList) {
        return StringUtils.hasText(vaccineList)
                ? JsonUtil.toList(vaccineList, AbandonClientRecordVO.AbandonPetDetailVO.VaccineDetailVO.class)
                : List.of();
    }

    default List<Integer> addOnIds2VO(String addOnIds) {
        return StringUtils.hasText(addOnIds) ? JsonUtil.toList(addOnIds, Integer.class) : List.of();
    }

    @Mappings({
        @Mapping(target = "breed", source = "petBreed"),
        @Mapping(target = "vetPhoneNumber", source = "vetPhone"),
        @Mapping(target = "questionAnswerList", expression = "java(List.of())"),
    })
    AbandonClientRecordVO.AbandonPetDetailVO dto2VO(CustomerPetDetailDTO petDetailDTO);

    @Mappings({@Mapping(target = "vaccineDocument", expression = "java(JsonUtil.toJson(recordDto.getDocumentUrls()))")})
    AbandonClientRecordVO.AbandonPetDetailVO.VaccineDetailVO vaccineDTO2VO(VaccineBindingRecordDto recordDto);

    @Mappings({
        @Mapping(target = "vetPhone", source = "vetPhoneNumber"),
        @Mapping(target = "vaccineList", expression = "java(vaccine2Params(recordPet.getVaccineList()))"),
    })
    CustomerPetAddParams entity2Params(MoeBookOnlineAbandonRecordPet recordPet);

    default List<VaccineParams> vaccine2Params(String vaccineList) {
        return StringUtils.hasText(vaccineList) ? JsonUtil.toList(vaccineList, VaccineParams.class) : List.of();
    }

    AbandonPetDTO entityToDTO(MoeBookOnlineAbandonRecordPet entity);
}
