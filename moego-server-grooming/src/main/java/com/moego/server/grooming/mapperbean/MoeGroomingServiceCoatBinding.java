package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_service_coat_binding
 */
public class MoeGroomingServiceCoatBinding {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_coat_binding.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_coat_binding.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   service id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_coat_binding.service_id
     *
     * @mbg.generated
     */
    private Integer serviceId;

    /**
     * Database Column Remarks:
     *   coat id list
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_coat_binding.coat_id_list
     *
     * @mbg.generated
     */
    private String coatIdList;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_coat_binding.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_coat_binding.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_service_coat_binding.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_coat_binding.id
     *
     * @return the value of moe_grooming_service_coat_binding.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_coat_binding.id
     *
     * @param id the value for moe_grooming_service_coat_binding.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_coat_binding.business_id
     *
     * @return the value of moe_grooming_service_coat_binding.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_coat_binding.business_id
     *
     * @param businessId the value for moe_grooming_service_coat_binding.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_coat_binding.service_id
     *
     * @return the value of moe_grooming_service_coat_binding.service_id
     *
     * @mbg.generated
     */
    public Integer getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_coat_binding.service_id
     *
     * @param serviceId the value for moe_grooming_service_coat_binding.service_id
     *
     * @mbg.generated
     */
    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_coat_binding.coat_id_list
     *
     * @return the value of moe_grooming_service_coat_binding.coat_id_list
     *
     * @mbg.generated
     */
    public String getCoatIdList() {
        return coatIdList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_coat_binding.coat_id_list
     *
     * @param coatIdList the value for moe_grooming_service_coat_binding.coat_id_list
     *
     * @mbg.generated
     */
    public void setCoatIdList(String coatIdList) {
        this.coatIdList = coatIdList == null ? null : coatIdList.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_coat_binding.create_time
     *
     * @return the value of moe_grooming_service_coat_binding.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_coat_binding.create_time
     *
     * @param createTime the value for moe_grooming_service_coat_binding.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_coat_binding.update_time
     *
     * @return the value of moe_grooming_service_coat_binding.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_coat_binding.update_time
     *
     * @param updateTime the value for moe_grooming_service_coat_binding.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_service_coat_binding.company_id
     *
     * @return the value of moe_grooming_service_coat_binding.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_service_coat_binding.company_id
     *
     * @param companyId the value for moe_grooming_service_coat_binding.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
