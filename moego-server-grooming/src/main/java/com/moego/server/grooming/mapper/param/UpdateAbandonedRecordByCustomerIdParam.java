package com.moego.server.grooming.mapper.param;

import com.moego.server.grooming.enums.OBStepEnum;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @see com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord
 */
@Data
public class UpdateAbandonedRecordByCustomerIdParam {

    private Long recoveryType;

    private Long recoveryTimeSec;

    private Byte isDeleted;

    private Byte deleteType;

    private String additionalNote;

    private Integer appointmentId;

    private List<OBStepEnum> recoverableSteps;
}
