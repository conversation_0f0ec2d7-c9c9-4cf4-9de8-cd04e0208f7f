package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_gc_appt
 */
public class MoeGcAppt {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_appt.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_appt.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   customer id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_appt.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     * Database Column Remarks:
     *   gc_calendar_id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_appt.gc_calendar_id
     *
     * @mbg.generated
     */
    private Integer gcCalendarId;

    /**
     * Database Column Remarks:
     *   grooming_id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_appt.grooming_id
     *
     * @mbg.generated
     */
    private Integer groomingId;

    /**
     * Database Column Remarks:
     *   google calendar  1import  2 export
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_appt.sync_type
     *
     * @mbg.generated
     */
    private Byte syncType;

    /**
     * Database Column Remarks:
     *   import时的event_id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_appt.import_event_id
     *
     * @mbg.generated
     */
    private String importEventId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_appt.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   最后更新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_appt.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_gc_appt.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_appt.id
     *
     * @return the value of moe_gc_appt.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_appt.id
     *
     * @param id the value for moe_gc_appt.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_appt.business_id
     *
     * @return the value of moe_gc_appt.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_appt.business_id
     *
     * @param businessId the value for moe_gc_appt.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_appt.customer_id
     *
     * @return the value of moe_gc_appt.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_appt.customer_id
     *
     * @param customerId the value for moe_gc_appt.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_appt.gc_calendar_id
     *
     * @return the value of moe_gc_appt.gc_calendar_id
     *
     * @mbg.generated
     */
    public Integer getGcCalendarId() {
        return gcCalendarId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_appt.gc_calendar_id
     *
     * @param gcCalendarId the value for moe_gc_appt.gc_calendar_id
     *
     * @mbg.generated
     */
    public void setGcCalendarId(Integer gcCalendarId) {
        this.gcCalendarId = gcCalendarId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_appt.grooming_id
     *
     * @return the value of moe_gc_appt.grooming_id
     *
     * @mbg.generated
     */
    public Integer getGroomingId() {
        return groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_appt.grooming_id
     *
     * @param groomingId the value for moe_gc_appt.grooming_id
     *
     * @mbg.generated
     */
    public void setGroomingId(Integer groomingId) {
        this.groomingId = groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_appt.sync_type
     *
     * @return the value of moe_gc_appt.sync_type
     *
     * @mbg.generated
     */
    public Byte getSyncType() {
        return syncType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_appt.sync_type
     *
     * @param syncType the value for moe_gc_appt.sync_type
     *
     * @mbg.generated
     */
    public void setSyncType(Byte syncType) {
        this.syncType = syncType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_appt.import_event_id
     *
     * @return the value of moe_gc_appt.import_event_id
     *
     * @mbg.generated
     */
    public String getImportEventId() {
        return importEventId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_appt.import_event_id
     *
     * @param importEventId the value for moe_gc_appt.import_event_id
     *
     * @mbg.generated
     */
    public void setImportEventId(String importEventId) {
        this.importEventId = importEventId == null ? null : importEventId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_appt.create_time
     *
     * @return the value of moe_gc_appt.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_appt.create_time
     *
     * @param createTime the value for moe_gc_appt.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_appt.update_time
     *
     * @return the value of moe_gc_appt.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_appt.update_time
     *
     * @param updateTime the value for moe_gc_appt.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_gc_appt.company_id
     *
     * @return the value of moe_gc_appt.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_gc_appt.company_id
     *
     * @param companyId the value for moe_gc_appt.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
