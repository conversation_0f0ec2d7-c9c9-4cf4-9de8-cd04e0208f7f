package com.moego.server.grooming.service;

import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.order.v1.GroomingDetailRelationModel;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.idl.service.order.v1.GetGroomingDetailRelationRequest;
import com.moego.idl.service.order.v1.GetGroomingDetailRelationResponse;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.lib.utils.model.Pair;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.mapper.po.GroomingStaffIdListPO;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.utils.PetDetailUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class AppointmentServiceDetailService {
    @Autowired
    private IPetClient iPetClient;

    @Autowired
    private LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitClient;

    @Autowired
    MoePetDetailService petDetailService;

    @Autowired
    private GroomingServiceOperationService operationService;

    @Autowired
    EvaluationServiceDetailService evaluationServiceDetailService;

    @Autowired
    GroomingServiceService groomingService;

    @Autowired
    private OrderServiceGrpc.OrderServiceBlockingStub orderClient;

    @Deprecated
    Pair<List<GroomingPetDetailDTO>, List<EvaluationServiceDetail>> getAppointmentServiceDetails(
            List<Integer> groomingIds) {
        List<GroomingPetDetailDTO> petDetails = petDetailService.queryAllPetDetailByGroomingIds(groomingIds);
        List<EvaluationServiceDetail> evaluations = evaluationServiceDetailService.queryByGroomingIds(groomingIds);
        return Pair.of(petDetails, evaluations);
    }

    Pair<List<GroomingPetDetailDTO>, List<EvaluationServiceDetail>> getAppointmentServiceDetails(
            List<Integer> groomingIds, Integer orderId, boolean isOriginOrder) {
        GetGroomingDetailRelationResponse groomingDetailRelResponse =
                orderClient.getGroomingDetailRelation(GetGroomingDetailRelationRequest.newBuilder()
                        .setIsOriginOrder(isOriginOrder)
                        .setOrderId(orderId)
                        .build());
        List<GroomingDetailRelationModel> groomingDetailRelModelsList =
                groomingDetailRelResponse.getGroomingDetailRelationModelsList();
        Set<Long> petDetailIdSet = groomingDetailRelModelsList.stream()
                .map(GroomingDetailRelationModel::getPetDetailId)
                .collect(Collectors.toSet());

        List<GroomingPetDetailDTO> petDetails;
        // 如果是extraOrder的直接查询
        if (!isOriginOrder) {
            petDetails = petDetailService.queryPetDetailDTOByIdList(new ArrayList<>(petDetailIdSet));
        } else {
            // 如果是origin order的需要排除extra order的数据
            petDetails = petDetailService.queryAllPetDetailByGroomingIds(groomingIds);
            petDetails = petDetails.stream()
                    .filter(petDetail -> !petDetailIdSet.contains(Long.valueOf(petDetail.getId())))
                    .toList();
        }

        List<EvaluationServiceDetail> evaluations = evaluationServiceDetailService.queryByGroomingIds(groomingIds);
        return Pair.of(petDetails, evaluations);
    }

    public Map<Integer, CustomerPetDetailDTO> getPetMap(
            List<GroomingPetDetailDTO> petDetails, List<EvaluationServiceDetail> evaluations) {
        List<Integer> petIds = getPetIds(petDetails, evaluations);
        if (CollectionUtils.isEmpty(petIds)) {
            return Map.of();
        }
        return iPetClient.getCustomerPetListByIdList(petIds).stream()
                .collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, p -> p));
    }

    public List<Integer> getPetIds(List<GroomingPetDetailDTO> petDetails, List<EvaluationServiceDetail> evaluations) {
        List<Integer> petIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(petDetails)) {
            petIds.addAll(
                    petDetails.stream().map(GroomingPetDetailDTO::getPetId).toList());
        }
        if (!CollectionUtils.isEmpty(evaluations)) {
            petIds.addAll(evaluations.stream()
                    .map(EvaluationServiceDetail::getPetId)
                    .map(Long::intValue)
                    .toList());
        }
        return petIds.stream().filter(k -> k > 0).distinct().toList();
    }

    @Deprecated
    public List<EvaluationServiceDetailDTO> toEvaluationServiceDetailDTOList(List<EvaluationServiceDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return List.of();
        }
        Map<Long, EvaluationBriefView> serviceMap = evaluationServiceDetailService.getEvaluationServiceMap(
                details.stream().map(EvaluationServiceDetail::getServiceId).toList());
        return details.stream()
                .map(k -> {
                    EvaluationServiceDetailDTO dto = new EvaluationServiceDetailDTO();
                    dto.setId(k.getId());
                    dto.setGroomingId(k.getAppointmentId());
                    dto.setStaffId(k.getStaffId());
                    dto.setPetId(k.getPetId());
                    dto.setServiceId(k.getServiceId());
                    dto.setServiceName(serviceMap.get(k.getServiceId()).getName());
                    return dto;
                })
                .toList();
    }

    /**
     *
     * @param groomingIds
     * @return key: groomingId, value: Pair<petDetails, evaluations>
     */
    public Map<Integer, Pair<List<MoeGroomingPetDetail>, List<EvaluationServiceDetail>>> getByAppointments(
            List<Integer> groomingIds) {
        List<MoeGroomingPetDetail> petDetails = petDetailService.queryPetDetailByAppointmentIds(groomingIds);
        return getByPetDetailListAndAppointments(petDetails, groomingIds);
    }

    /**
     * 根据order id和appointment id列表获取
     *
     * @param orderId order id
     * @param groomingIds grooming id列表
     * @return Map<Integer, Pair<List<MoeGroomingPetDetail>, List<EvaluationServiceDetail>>>
     */
    Map<Integer, Pair<List<MoeGroomingPetDetail>, List<EvaluationServiceDetail>>> getByAppointmentsAndOrderId(
            Integer orderId, boolean isOriginOrder, List<Integer> groomingIds) {
        GetGroomingDetailRelationResponse groomingDetailRelResponse =
                orderClient.getGroomingDetailRelation(GetGroomingDetailRelationRequest.newBuilder()
                        .setIsOriginOrder(isOriginOrder)
                        .setOrderId(orderId)
                        .build());

        List<GroomingDetailRelationModel> groomingDetailRelModelsList =
                groomingDetailRelResponse.getGroomingDetailRelationModelsList();
        Set<Long> petDetailIdSet = groomingDetailRelModelsList.stream()
                .map(GroomingDetailRelationModel::getPetDetailId)
                .collect(Collectors.toSet());
        // 如果是extra order的，直接返回对应pet detail id的数据
        if (!isOriginOrder) {
            List<MoeGroomingPetDetail> petDetails =
                    petDetailService.queryByPetDetailIds(new ArrayList<>(petDetailIdSet));
            return getByPetDetailListAndAppointments(petDetails, groomingIds);
        }
        // 如果是origin order的，，得去掉pet detail id
        List<MoeGroomingPetDetail> petDetails = petDetailService.queryPetDetailByAppointmentIds(groomingIds);
        petDetails = petDetails.stream()
                .filter(petDetail -> !petDetailIdSet.contains(Long.valueOf(petDetail.getId())))
                .toList();
        return getByPetDetailListAndAppointments(petDetails, groomingIds);
    }

    /**
     * 根据pet detail列表和grooming id列表构造map
     *
     * @param petDetails List<MoeGroomingPetDetail>
     * @param groomingIds List<Integer>
     * @return Map<Integer, Pair<List<MoeGroomingPetDetail>, List<EvaluationServiceDetail>>>
     */
    private Map<Integer, Pair<List<MoeGroomingPetDetail>, List<EvaluationServiceDetail>>>
            getByPetDetailListAndAppointments(List<MoeGroomingPetDetail> petDetails, List<Integer> groomingIds) {

        // 很多地方依赖 serviceType, 但是有些数据没有，这里补充一下
        fixServiceType(petDetails);

        List<EvaluationServiceDetail> evaluations = evaluationServiceDetailService.queryByGroomingIds(groomingIds);

        Map<Integer, List<MoeGroomingPetDetail>> appointmentPetDetails =
                petDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getGroomingId));
        Map<Integer, List<EvaluationServiceDetail>> appointmentEvaluations = evaluations.stream()
                .collect(Collectors.groupingBy(k -> k.getAppointmentId().intValue()));

        Map<Integer, Pair<List<MoeGroomingPetDetail>, List<EvaluationServiceDetail>>> result = new HashMap<>();
        for (Integer groomingId : groomingIds) {
            List<MoeGroomingPetDetail> petDetail = appointmentPetDetails.getOrDefault(groomingId, new ArrayList<>());
            List<EvaluationServiceDetail> evaluation =
                    appointmentEvaluations.getOrDefault(groomingId, new ArrayList<>());
            result.put(groomingId, Pair.of(petDetail, evaluation));
        }
        return result;
    }

    public BigDecimal calculateAmount(
            List<MoeGroomingPetDetail> all,
            List<MoeGroomingPetDetail> petDetailToCal,
            List<EvaluationServiceDetail> evaluationToCal) {
        BigDecimal result = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(petDetailToCal)) {
            fixServiceType(petDetailToCal);
            Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = all.stream()
                    .filter(petDetail -> Objects.equals(petDetail.getServiceType(), ServiceType.SERVICE_VALUE))
                    .collect(Collectors.groupingBy(
                            MoeGroomingPetDetail::getPetId,
                            Collectors.toMap(MoeGroomingPetDetail::getServiceId, Function.identity(), (p1, p2) -> p1)));

            result = result.add(petDetailToCal.stream()
                    .map(petDetail -> petDetail
                            .getServicePrice()
                            .multiply(BigDecimal.valueOf(PetDetailUtil.getQuantity(petDetail, petServiceMap))))
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        if (!CollectionUtils.isEmpty(evaluationToCal)) {
            result = result.add(evaluationToCal.stream()
                    .map(EvaluationServiceDetail::getServicePrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        return result;
    }

    // fix: https://moego.atlassian.net/browse/ERP-10301
    public void fixServiceType(List<MoeGroomingPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return;
        }
        List<Integer> toFixServiceIds = petDetails.stream()
                .filter(Objects::nonNull)
                .filter(petDetail -> !PetDetailUtil.isServiceTypeNormal(petDetail.getServiceType()))
                .map(MoeGroomingPetDetail::getServiceId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(toFixServiceIds)) {
            return;
        }
        Map<Integer, MoeGroomingServiceDTO> serviceInfo =
                groomingService.getServicesByServiceIds(null, toFixServiceIds).stream()
                        .collect(Collectors.toMap(MoeGroomingServiceDTO::getId, Function.identity(), (p1, p2) -> p1));
        for (MoeGroomingPetDetail petDetail : petDetails) {
            if (petDetail.getServiceType() == null
                    || Objects.equals(petDetail.getServiceType(), ServiceType.SERVICE_TYPE_UNSPECIFIED_VALUE)) {
                if (serviceInfo.containsKey(petDetail.getServiceId())) {
                    petDetail.setServiceType(
                            serviceInfo.get(petDetail.getServiceId()).getType().intValue());
                }
            }
        }
    }

    public Set<Integer> appointmentsFilterByStaff(List<Integer> appointmentIds, Integer staffId) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return Set.of();
        }
        var details = getByAppointments(appointmentIds);
        List<MoeGroomingPetDetail> allPetDetails = new ArrayList<>();
        List<EvaluationServiceDetail> allEvaluations = new ArrayList<>();
        details.values().forEach(k -> {
            allPetDetails.addAll(k.key());
            allEvaluations.addAll(k.value());
        });
        Set<Integer> apptIDsForStaff = allPetDetails.stream()
                .filter(k -> k.getStaffId().equals(staffId))
                .map(MoeGroomingPetDetail::getGroomingId)
                .collect(Collectors.toSet());
        apptIDsForStaff.addAll(allEvaluations.stream()
                .filter(k -> k.getStaffId() != null && k.getStaffId().intValue() == staffId)
                .map(k -> k.getAppointmentId().intValue())
                .collect(Collectors.toSet()));
        List<GroomingStaffIdListPO> groomingStaffIdListPOS = operationService.queryStaffIdByGroomingIds(appointmentIds);
        groomingStaffIdListPOS.forEach(k -> {
            if (CollectionUtils.isEmpty(k.getStaffIdList())) {
                return;
            }
            if (k.getStaffIdList().contains(staffId)) {
                apptIDsForStaff.add(k.getGroomingId());
            }
        });
        return apptIDsForStaff;
    }

    public List<EvaluationServiceDetailDTO> toEvaluationServiceDetailDTOListV2(List<EvaluationServiceDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return new ArrayList<>();
        }
        return details.stream()
                .map(k -> {
                    EvaluationServiceDetailDTO dto = new EvaluationServiceDetailDTO();
                    dto.setId(k.getId());
                    dto.setGroomingId(k.getAppointmentId());
                    dto.setPetId(k.getPetId());
                    dto.setServiceId(k.getServiceId());
                    dto.setServicePrice(k.getServicePrice());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    public List<EvaluationServiceDetail> toEvaluationServiceDetailList(List<EvaluationServiceDetailDTO> details) {
        if (CollectionUtils.isEmpty(details)) {
            return new ArrayList<>();
        }
        return details.stream()
                .map(k -> {
                    EvaluationServiceDetail po = new EvaluationServiceDetail();
                    po.setId(k.getId());
                    po.setAppointmentId(k.getGroomingId());
                    po.setPetId(k.getPetId());
                    po.setServiceId(k.getServiceId());
                    po.setServicePrice(k.getServicePrice());
                    return po;
                })
                .collect(Collectors.toList());
    }
}
