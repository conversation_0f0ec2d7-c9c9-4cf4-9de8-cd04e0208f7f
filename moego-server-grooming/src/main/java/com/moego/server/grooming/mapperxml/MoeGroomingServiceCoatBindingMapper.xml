<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingServiceCoatBindingMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingServiceCoatBinding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="coat_id_list" jdbcType="CHAR" property="coatIdList" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, service_id, coat_id_list, create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_service_coat_binding
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_service_coat_binding
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceCoatBinding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_service_coat_binding (business_id, service_id, coat_id_list,
      create_time, update_time, company_id
      )
    values (#{businessId,jdbcType=INTEGER}, #{serviceId,jdbcType=INTEGER}, #{coatIdList,jdbcType=CHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceCoatBinding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_service_coat_binding
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="coatIdList != null">
        coat_id_list,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="coatIdList != null">
        #{coatIdList,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceCoatBinding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_coat_binding
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="coatIdList != null">
        coat_id_list = #{coatIdList,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceCoatBinding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_coat_binding
    set business_id = #{businessId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      coat_id_list = #{coatIdList,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertOrUpdate" keyProperty="id" useGeneratedKeys="true">
    insert into moe_grooming_service_coat_binding (
    <if test="businessId != null">
      business_id,
    </if>
    <if test="companyId != null">
      company_id,
    </if>
        service_id, coat_id_list
    )
    values (
    <if test="businessId != null">
      #{businessId,jdbcType=INTEGER},
    </if>
    <if test="companyId != null">
      #{companyId,jdbcType=BIGINT},
    </if>
            #{serviceId,jdbcType=INTEGER},
            #{coatIdList,jdbcType=CHAR}
    ) on duplicate key
    update
      coat_id_list = #{coatIdList,jdbcType=CHAR}
  </insert>

  <delete id="deleteByBusinessIdAndServiceId">
    delete
    from moe_grooming_service_coat_binding
    where service_id = #{serviceId,jdbcType=INTEGER}
      and business_id = #{businessId,jdbcType=INTEGER}
  </delete>

  <select id="selectByBusinessIdAndServiceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming.moe_grooming_service_coat_binding
    where business_id = #{businessId}
      and service_id = #{serviceId}
  </select>

  <select id="selectByBusinessId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming.moe_grooming_service_coat_binding
    where business_id = #{businessId}
  </select>
  <select id="selectByCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming.moe_grooming_service_coat_binding
    where company_id = #{companyId}
  </select>

  <select id="selectByCompanyIdAndCoatId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming.moe_grooming_service_coat_binding
    where company_id = #{companyId}
      and json_contains(coat_id_list, json_array(#{coatId}))
  </select>
</mapper>
