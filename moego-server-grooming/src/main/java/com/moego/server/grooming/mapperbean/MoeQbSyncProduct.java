package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_qb_sync_product
 */
public class MoeQbSyncProduct {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_product.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_product.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   quickbook connect id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_product.connect_id
     *
     * @mbg.generated
     */
    private Integer connectId;

    /**
     * Database Column Remarks:
     *   quickbook realm id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_product.realm_id
     *
     * @mbg.generated
     */
    private String realmId;

    /**
     * Database Column Remarks:
     *   quickbook service id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_product.qb_service_id
     *
     * @mbg.generated
     */
    private String qbServiceId;

    /**
     * Database Column Remarks:
     *   moego product id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_product.product_id
     *
     * @mbg.generated
     */
    private Integer productId;

    /**
     * Database Column Remarks:
     *   moego product name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_product.product_name
     *
     * @mbg.generated
     */
    private String productName;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_product.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_product.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_product.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   moego product description
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_product.product_description
     *
     * @mbg.generated
     */
    private String productDescription;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_product.id
     *
     * @return the value of moe_qb_sync_product.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_product.id
     *
     * @param id the value for moe_qb_sync_product.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_product.business_id
     *
     * @return the value of moe_qb_sync_product.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_product.business_id
     *
     * @param businessId the value for moe_qb_sync_product.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_product.connect_id
     *
     * @return the value of moe_qb_sync_product.connect_id
     *
     * @mbg.generated
     */
    public Integer getConnectId() {
        return connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_product.connect_id
     *
     * @param connectId the value for moe_qb_sync_product.connect_id
     *
     * @mbg.generated
     */
    public void setConnectId(Integer connectId) {
        this.connectId = connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_product.realm_id
     *
     * @return the value of moe_qb_sync_product.realm_id
     *
     * @mbg.generated
     */
    public String getRealmId() {
        return realmId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_product.realm_id
     *
     * @param realmId the value for moe_qb_sync_product.realm_id
     *
     * @mbg.generated
     */
    public void setRealmId(String realmId) {
        this.realmId = realmId == null ? null : realmId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_product.qb_service_id
     *
     * @return the value of moe_qb_sync_product.qb_service_id
     *
     * @mbg.generated
     */
    public String getQbServiceId() {
        return qbServiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_product.qb_service_id
     *
     * @param qbServiceId the value for moe_qb_sync_product.qb_service_id
     *
     * @mbg.generated
     */
    public void setQbServiceId(String qbServiceId) {
        this.qbServiceId = qbServiceId == null ? null : qbServiceId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_product.product_id
     *
     * @return the value of moe_qb_sync_product.product_id
     *
     * @mbg.generated
     */
    public Integer getProductId() {
        return productId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_product.product_id
     *
     * @param productId the value for moe_qb_sync_product.product_id
     *
     * @mbg.generated
     */
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_product.product_name
     *
     * @return the value of moe_qb_sync_product.product_name
     *
     * @mbg.generated
     */
    public String getProductName() {
        return productName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_product.product_name
     *
     * @param productName the value for moe_qb_sync_product.product_name
     *
     * @mbg.generated
     */
    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_product.create_time
     *
     * @return the value of moe_qb_sync_product.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_product.create_time
     *
     * @param createTime the value for moe_qb_sync_product.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_product.update_time
     *
     * @return the value of moe_qb_sync_product.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_product.update_time
     *
     * @param updateTime the value for moe_qb_sync_product.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_product.company_id
     *
     * @return the value of moe_qb_sync_product.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_product.company_id
     *
     * @param companyId the value for moe_qb_sync_product.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_product.product_description
     *
     * @return the value of moe_qb_sync_product.product_description
     *
     * @mbg.generated
     */
    public String getProductDescription() {
        return productDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_product.product_description
     *
     * @param productDescription the value for moe_qb_sync_product.product_description
     *
     * @mbg.generated
     */
    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription == null ? null : productDescription.trim();
    }
}
