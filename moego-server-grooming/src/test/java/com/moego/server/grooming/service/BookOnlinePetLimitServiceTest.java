package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.dto.CompanyIdDTO;
import com.moego.server.customer.client.IPetBreedClient;
import com.moego.server.customer.dto.MoePetBreedDTO;
import com.moego.server.grooming.dto.BookOnlinePetLimitDTO;
import com.moego.server.grooming.mapper.MoeBookOnlinePetLimitBreedBindingMapper;
import com.moego.server.grooming.mapper.MoeBookOnlinePetLimitMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineStaffTimeMapper;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlinePetLimit;
import com.moego.server.grooming.mapperbean.MoeBookOnlinePetLimitBreedBinding;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.web.vo.BookOnlinePetLimitRequest;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BookOnlinePetLimitServiceTest {

    @Mock
    private MoeBookOnlinePetLimitMapper moeBookOnlinePetLimitMapper;

    @Mock
    private IPetBreedClient iPetBreedClient;

    @Mock
    private MoeBookOnlineStaffTimeMapper moeBookOnlineStaffTimeMapper;

    @Mock
    private MoeBookOnlinePetLimitBreedBindingMapper moeBookOnlinePetLimitBreedBindingMapper;

    @Mock
    private MoeBusinessBookOnlineMapper moeBusinessBookOnlineMapper;

    @Mock
    private IBusinessBusinessService iBusinessBusinessService;

    @InjectMocks
    private BookOnlinePetLimitService bookOnlinePetLimitService;

    private MoeBookOnlinePetLimit samplePetLimit;
    private MoeBusinessBookOnline sampleBusinessBookOnline;
    private MoePetBreedDTO samplePetBreed;

    @BeforeEach
    void setUp() {
        samplePetLimit = new MoeBookOnlinePetLimit();
        samplePetLimit.setId(1L);
        samplePetLimit.setBusinessId(100);
        samplePetLimit.setType((byte) 1);
        samplePetLimit.setMaxNumber(5);

        sampleBusinessBookOnline = new MoeBusinessBookOnline();
        sampleBusinessBookOnline.setBusinessId(100);
        sampleBusinessBookOnline.setAvailableTimeType((byte) 1);

        samplePetBreed = new MoePetBreedDTO();
        samplePetBreed.setId(1);
        samplePetBreed.setName("Test Breed");
    }

    @Test
    void getPetLimit_WhenFound_ReturnsPetLimits() {
        // Arrange
        long companyId = 1L;
        int businessId = 100;

        when(moeBusinessBookOnlineMapper.selectByBusinessId(businessId)).thenReturn(sampleBusinessBookOnline);

        // Act
        List<BookOnlinePetLimitDTO> result = bookOnlinePetLimitService.getPetLimit(companyId, businessId);

        // Assert
        assertThat(result).isNotNull();
    }

    @Test
    void savePetLimit_WithValidRequest_ReturnsId() {
        // Arrange
        BookOnlinePetLimitRequest request = new BookOnlinePetLimitRequest();
        request.setType((byte) 1);
        request.setMaxNumber(5);
        request.setFindId(1L);

        when(moeBookOnlinePetLimitMapper.insertSelective(any(MoeBookOnlinePetLimit.class)))
                .thenAnswer(invocation -> {
                    MoeBookOnlinePetLimit petLimit = invocation.getArgument(0);
                    petLimit.setId(1L);
                    return 1;
                });

        // Act
        Long result = bookOnlinePetLimitService.savePetLimit(100, 1L, request);

        // Assert
        assertThat(result).isEqualTo(1L);
    }

    @Test
    void savePetLimit_WithBreedType_SavesBreedBinding() {
        // Arrange
        BookOnlinePetLimitRequest request = new BookOnlinePetLimitRequest();
        request.setType((byte) 2); // LIMIT_BY_BREED
        request.setMaxNumber(5);
        request.setPetTypeId(1);
        request.setAllBreed(false);
        request.setBreedIdList(List.of(1, 2, 3));

        when(moeBookOnlinePetLimitBreedBindingMapper.insertSelective(any(MoeBookOnlinePetLimitBreedBinding.class)))
                .thenAnswer(invocation -> {
                    MoeBookOnlinePetLimitBreedBinding binding = invocation.getArgument(0);
                    binding.setId(1L);
                    return 1;
                });

        when(moeBookOnlinePetLimitMapper.insertSelective(any(MoeBookOnlinePetLimit.class)))
                .thenAnswer(invocation -> {
                    MoeBookOnlinePetLimit petLimit = invocation.getArgument(0);
                    petLimit.setId(2L);
                    return 1;
                });

        // Act
        Long result = bookOnlinePetLimitService.savePetLimit(100, 1L, request);

        // Assert
        assertThat(result).isEqualTo(2L);
    }

    @Test
    void getPetLimitMap_ReturnsValidMap() {
        // Arrange
        int businessId = 100;
        when(iBusinessBusinessService.getCompanyIdByBusinessId(businessId)).thenReturn(new CompanyIdDTO(1L));
        when(moeBusinessBookOnlineMapper.selectByBusinessId(businessId)).thenReturn(sampleBusinessBookOnline);

        // Act
        Map<Long, BookOnlinePetLimitDTO> result = bookOnlinePetLimitService.getPetLimitMap(businessId);

        // Assert
        assertThat(result).isNotNull();
    }
}
