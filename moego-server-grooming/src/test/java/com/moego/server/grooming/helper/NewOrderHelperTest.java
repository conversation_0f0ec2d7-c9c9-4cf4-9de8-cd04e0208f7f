package com.moego.server.grooming.helper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.idl.models.appointment.v1.AppointmentExtraInfoModel;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.ListExtraInfoRequest;
import com.moego.idl.service.appointment.v1.ListExtraInfoResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NewOrderHelperTest {

    @Mock
    private AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;

    @InjectMocks
    private NewOrderHelper newOrderHelper;

    private ListExtraInfoResponse.Builder responseBuilder;
    private AppointmentExtraInfoModel.Builder extraInfoBuilder;

    @BeforeEach
    void setUp() {
        responseBuilder = ListExtraInfoResponse.newBuilder();
        extraInfoBuilder = AppointmentExtraInfoModel.newBuilder();
    }

    @Test
    @DisplayName("当extraInfos为空时_应该返回false")
    void isNewOrder_WhenExtraInfosIsEmpty_ShouldReturnFalse() {
        // Arrange - 准备测试数据
        long appointmentId = 12345L;
        ListExtraInfoResponse emptyResponse = responseBuilder.clearExtraInfo().build();

        when(appointmentStub.listExtraInfo(any(ListExtraInfoRequest.class))).thenReturn(emptyResponse);

        // Act - 执行被测方法
        boolean result = newOrderHelper.isNewOrder(appointmentId);

        // Assert - 验证结果
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("当appointmentId存在且isNewOrder为true时_应该返回true")
    void isNewOrder_WhenAppointmentExistsAndIsNewOrderTrue_ShouldReturnTrue() {
        // Arrange - 准备测试数据
        long appointmentId = 12345L;
        AppointmentExtraInfoModel extraInfo = extraInfoBuilder
                .setAppointmentId(appointmentId)
                .setIsNewOrder(true)
                .build();

        ListExtraInfoResponse response = responseBuilder.addExtraInfo(extraInfo).build();

        when(appointmentStub.listExtraInfo(any(ListExtraInfoRequest.class))).thenReturn(response);

        // Act - 执行被测方法
        boolean result = newOrderHelper.isNewOrder(appointmentId);

        // Assert - 验证结果
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("当appointmentId存在且isNewOrder为false时_应该返回false")
    void isNewOrder_WhenAppointmentExistsAndIsNewOrderFalse_ShouldReturnFalse() {
        // Arrange - 准备测试数据
        long appointmentId = 12345L;
        AppointmentExtraInfoModel extraInfo = extraInfoBuilder
                .setAppointmentId(appointmentId)
                .setIsNewOrder(false)
                .build();

        ListExtraInfoResponse response = responseBuilder.addExtraInfo(extraInfo).build();

        when(appointmentStub.listExtraInfo(any(ListExtraInfoRequest.class))).thenReturn(response);

        // Act - 执行被测方法
        boolean result = newOrderHelper.isNewOrder(appointmentId);

        // Assert - 验证结果
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("当appointmentId不存在于extraInfos中时_应该返回false")
    void isNewOrder_WhenAppointmentIdNotExist_ShouldReturnFalse() {
        // Arrange - 准备测试数据
        long targetAppointmentId = 12345L;
        long differentAppointmentId = 67890L;

        AppointmentExtraInfoModel extraInfo = extraInfoBuilder
                .setAppointmentId(differentAppointmentId)
                .setIsNewOrder(true)
                .build();

        ListExtraInfoResponse response = responseBuilder.addExtraInfo(extraInfo).build();

        when(appointmentStub.listExtraInfo(any(ListExtraInfoRequest.class))).thenReturn(response);

        // Act - 执行被测方法
        boolean result = newOrderHelper.isNewOrder(targetAppointmentId);

        // Assert - 验证结果
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("当有多个extraInfo但只有目标appointmentId匹配时_应该返回对应的isNewOrder值")
    void isNewOrder_WhenMultipleExtraInfosWithTargetMatch_ShouldReturnCorrectValue() {
        // Arrange - 准备测试数据
        long targetAppointmentId = 12345L;

        AppointmentExtraInfoModel extraInfo1 = AppointmentExtraInfoModel.newBuilder()
                .setAppointmentId(11111L)
                .setIsNewOrder(false)
                .build();

        AppointmentExtraInfoModel extraInfo2 = AppointmentExtraInfoModel.newBuilder()
                .setAppointmentId(targetAppointmentId)
                .setIsNewOrder(true)
                .build();

        AppointmentExtraInfoModel extraInfo3 = AppointmentExtraInfoModel.newBuilder()
                .setAppointmentId(33333L)
                .setIsNewOrder(false)
                .build();

        ListExtraInfoResponse response = responseBuilder
                .addExtraInfo(extraInfo1)
                .addExtraInfo(extraInfo2)
                .addExtraInfo(extraInfo3)
                .build();

        when(appointmentStub.listExtraInfo(any(ListExtraInfoRequest.class))).thenReturn(response);

        // Act - 执行被测方法
        boolean result = newOrderHelper.isNewOrder(targetAppointmentId);

        // Assert - 验证结果
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("当有重复appointmentId时_有一个返回 true 另一个返回 false")
    void isNewOrder_WhenDuplicateAppointmentIds_ShouldUseFirstValue() {
        // Arrange - 准备测试数据
        long appointmentId = 12345L;

        AppointmentExtraInfoModel extraInfo1 = AppointmentExtraInfoModel.newBuilder()
                .setAppointmentId(appointmentId)
                .setIsNewOrder(true) // 第一个值为 true 就匹配
                .build();

        AppointmentExtraInfoModel extraInfo2 = AppointmentExtraInfoModel.newBuilder()
                .setAppointmentId(appointmentId)
                .setIsNewOrder(false) // 第二个值，应该被忽略
                .build();

        ListExtraInfoResponse response = responseBuilder
                .addExtraInfo(extraInfo1)
                .addExtraInfo(extraInfo2)
                .build();

        when(appointmentStub.listExtraInfo(any(ListExtraInfoRequest.class))).thenReturn(response);

        // Act - 执行被测方法
        boolean result = newOrderHelper.isNewOrder(appointmentId);

        // Assert - 验证结果
        assertThat(result).isTrue(); // 应该返回第一个值true
    }

    @Test
    @DisplayName("应该返回包含所有预约ID及其新订单状态的映射")
    void shouldReturnMapWithAllAppointmentIdsAndTheirNewOrderStatus() {
        // Arrange - 准备测试数据
        List<Long> appointmentIds = Arrays.asList(1L, 2L, 3L);
        List<AppointmentExtraInfoModel> extraInfoList = Arrays.asList(
                createExtraInfoModel(1L, true), createExtraInfoModel(2L, false), createExtraInfoModel(3L, true));

        when(appointmentStub.listExtraInfo(any(ListExtraInfoRequest.class)))
                .thenReturn(ListExtraInfoResponse.newBuilder()
                        .addAllExtraInfo(extraInfoList)
                        .build());

        // Act - 执行被测试方法
        Map<Long, Boolean> result = newOrderHelper.listNewOrder(appointmentIds);

        // Assert - 验证结果
        assertThat(result)
                .hasSize(3)
                .containsEntry(1L, true)
                .containsEntry(2L, false)
                .containsEntry(3L, true);
    }

    @Test
    @DisplayName("当某些预约ID在响应中缺失时，应该默认返回false")
    void shouldReturnFalseForMissingAppointmentIds() {
        // Arrange
        List<Long> appointmentIds = Arrays.asList(1L, 2L, 3L);
        List<AppointmentExtraInfoModel> extraInfoList = Arrays.asList(
                createExtraInfoModel(1L, true)
                // 注意：只返回ID为1的数据，2和3缺失
                );

        when(appointmentStub.listExtraInfo(any(ListExtraInfoRequest.class)))
                .thenReturn(ListExtraInfoResponse.newBuilder()
                        .addAllExtraInfo(extraInfoList)
                        .build());

        // Act
        Map<Long, Boolean> result = newOrderHelper.listNewOrder(appointmentIds);

        // Assert
        assertThat(result)
                .hasSize(3)
                .containsEntry(1L, true)
                .containsEntry(2L, false) // 默认值
                .containsEntry(3L, false); // 默认值
    }

    @Test
    @DisplayName("当输入空列表时，应该返回空映射")
    void shouldReturnEmptyMapWhenInputIsEmpty() {
        // Arrange
        List<Long> appointmentIds = Collections.emptyList();
        when(appointmentStub.listExtraInfo(any(ListExtraInfoRequest.class)))
                .thenReturn(ListExtraInfoResponse.newBuilder().build());

        // Act
        Map<Long, Boolean> result = newOrderHelper.listNewOrder(appointmentIds);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("当stub返回空的extra info列表时，所有ID应该映射为false")
    void shouldReturnAllFalseWhenStubReturnsEmptyExtraInfo() {
        // Arrange
        List<Long> appointmentIds = Arrays.asList(1L, 2L);
        when(appointmentStub.listExtraInfo(any(ListExtraInfoRequest.class)))
                .thenReturn(ListExtraInfoResponse.newBuilder().build());

        // Act
        Map<Long, Boolean> result = newOrderHelper.listNewOrder(appointmentIds);

        // Assert
        assertThat(result).hasSize(2).containsEntry(1L, false).containsEntry(2L, false);
    }

    // 辅助方法
    private AppointmentExtraInfoModel createExtraInfoModel(Long appointmentId, Boolean isNewOrder) {
        return AppointmentExtraInfoModel.newBuilder()
                .setAppointmentId(appointmentId)
                .setIsNewOrder(isNewOrder)
                .build();
    }
}
