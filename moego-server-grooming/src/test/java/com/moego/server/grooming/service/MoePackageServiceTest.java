package com.moego.server.grooming.service;

import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingPackageServiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageHistory;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageService;
import com.moego.server.grooming.params.PackageUsedParams;
import java.util.ArrayList;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 * @since 2022/9/14
 */
@ExtendWith(MockitoExtension.class)
public class MoePackageServiceTest {

    @InjectMocks
    private MoePackageService moePackageService;

    @Mock
    private MoeGroomingPackageServiceMapper moeGroomingPackageServiceMapper;

    @Mock
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Test
    public void checkUsePackageForGroomingNotExists() {
        List<PackageUsedParams> packageUsedParamsList = new ArrayList<>();
        PackageUsedParams packageUsedParams = new PackageUsedParams();
        packageUsedParams.setInvoiceId(123);
        packageUsedParams.setBusinessId(123);
        packageUsedParams.setCustomerId(123);
        packageUsedParams.setPackageId(123);
        packageUsedParams.setPackageServiceId(123);
        packageUsedParams.setQuantity(2);
        packageUsedParamsList.add(packageUsedParams);

        MoeGroomingPackageService packageService = new MoeGroomingPackageService();
        packageService.setRemainingQuantity(2);
        Mockito.doReturn(packageService)
                .when(moeGroomingPackageServiceMapper)
                .queryPackageServiceByProp(
                        packageUsedParams.getBusinessId(),
                        packageUsedParams.getCustomerId(),
                        packageUsedParams.getPackageId(),
                        packageUsedParams.getPackageServiceId());
        MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();
        moeGroomingAppointment.setAppointmentDate("2022-09-20");
        Mockito.doReturn(moeGroomingAppointment)
                .when(moeGroomingAppointmentMapper)
                .selectByPrimaryKey(packageUsedParams.getGroomingId());

        List<MoeGroomingPackageHistory> historyList =
                moePackageService.checkUsePackageForGrooming(packageUsedParamsList);
        Assertions.assertThat(historyList).as("must exists history").isNotEmpty();
    }

    @Test
    public void checkUsePackageForGroomingExists() {
        List<PackageUsedParams> packageUsedParamsList = new ArrayList<>();
        PackageUsedParams packageUsedParams = new PackageUsedParams();
        packageUsedParams.setInvoiceId(123);
        packageUsedParams.setBusinessId(123);
        packageUsedParams.setCustomerId(123);
        packageUsedParams.setPackageId(123);
        packageUsedParams.setPackageServiceId(123);
        packageUsedParams.setQuantity(2);
        packageUsedParamsList.add(packageUsedParams);

        MoeGroomingPackageService packageService = new MoeGroomingPackageService();
        packageService.setRemainingQuantity(2);
        Mockito.doReturn(packageService)
                .when(moeGroomingPackageServiceMapper)
                .queryPackageServiceByProp(
                        packageUsedParams.getBusinessId(),
                        packageUsedParams.getCustomerId(),
                        packageUsedParams.getPackageId(),
                        packageUsedParams.getPackageServiceId());

        List<MoeGroomingPackageHistory> historyList =
                moePackageService.checkUsePackageForGrooming(packageUsedParamsList);
        Assertions.assertThat(historyList).as("must not exists history").isEmpty();
    }
}
