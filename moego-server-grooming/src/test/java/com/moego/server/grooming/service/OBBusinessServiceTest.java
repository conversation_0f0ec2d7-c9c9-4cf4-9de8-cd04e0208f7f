package com.moego.server.grooming.service;

import com.google.protobuf.Duration;
import com.google.rpc.Code;
import com.google.rpc.Status;
import com.moego.common.constant.CommonConstant;
import com.moego.common.constant.Dictionary;
import com.moego.idl.models.map.v1.RouteMatrixElement;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.grooming.dto.ob.ServiceAreaResultDTO;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.params.ob.ServiceAreaParams;
import com.moego.server.grooming.service.ob.OBBusinessService;
import java.util.ArrayList;
import java.util.List;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 * @since 2022/10/28
 */
@ExtendWith(MockitoExtension.class)
public class OBBusinessServiceTest {

    @InjectMocks
    private OBBusinessService businessService;

    @Mock
    private MoeBusinessBookOnlineMapper businessBookOnlineMapper;

    @Mock
    private GroomingFeaturePricingCheckService pricingCheckService;

    @Mock
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Mock
    private SmartScheduleService smartScheduleService;

    @Mock
    private MoeGroomingBookOnlineService bookOnlineService;

    private static final Integer BUSINESS_ID = 123;
    private static final Integer ADDRESS_ID = 100;
    private static final String LAT = "22.53925";
    private static final String LNG = "113.9431";

    public MoeBusinessBookOnline getEnableSetting() {
        MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
        businessBookOnline.setIsByZipcode(CommonConstant.ENABLE);
        businessBookOnline.setIsByRadius(CommonConstant.DISABLE);
        businessBookOnline.setIsEnable(CommonConstant.ENABLE);
        businessBookOnline.setSettingLat(LAT);
        businessBookOnline.setSettingLng(LNG);
        return businessBookOnline;
    }

    public MoeBusinessBookOnline getEnableRadiusSetting() {
        MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
        businessBookOnline.setIsByZipcode(CommonConstant.DISABLE);
        businessBookOnline.setIsByRadius(CommonConstant.ENABLE);
        businessBookOnline.setIsEnable(CommonConstant.ENABLE);
        return businessBookOnline;
    }

    public MoeBusinessBookOnline getDisableSetting() {
        MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
        businessBookOnline.setIsByZipcode(CommonConstant.DISABLE);
        businessBookOnline.setIsByRadius(CommonConstant.DISABLE);
        businessBookOnline.setIsEnable(CommonConstant.ENABLE);
        return businessBookOnline;
    }

    @Test
    public void testGetCountryByLocation() {
        // Canada
        String[] CA = {"Montreal, QC, Canada", "923 Hancock Avenue, West Hollywood, CA"};
        // United Kingdom
        String[] GB = {"Brewood, UK ", "Park Street, St Albans AL2 2JJ, UK"};
        // America
        String[] US = {"Port Townsend, WA 98368, USA ", "Fontana, CA, USA"};
        // Australia
        String[] AU = {"Ringwood East VIC, Australia", "Littlehampton SA, Australia "};
        // unknown
        String[] unknown = {"West Hollywood, CA, EUA", "Broward County, Florida, EE. UU"};
        // illegal
        String[] illegal = {"1595 Detroit ave 206", "", "USA", ",USA", "Florida USA"};

        for (var location : CA) {
            Assertions.assertThat(OBBusinessService.getCountryByLocation(location))
                    .isEqualTo("CA");
        }
        for (var location : GB) {
            Assertions.assertThat(OBBusinessService.getCountryByLocation(location))
                    .isEqualTo("GB");
        }
        for (var location : US) {
            Assertions.assertThat(OBBusinessService.getCountryByLocation(location))
                    .isEqualTo("US");
        }
        for (var location : AU) {
            Assertions.assertThat(OBBusinessService.getCountryByLocation(location))
                    .isEqualTo("AU");
        }
        for (var location : unknown) {
            Assertions.assertThat(OBBusinessService.getCountryByLocation(location))
                    .isNull();
        }
        for (var location : illegal) {
            Assertions.assertThat(OBBusinessService.getCountryByLocation(location))
                    .isNull();
        }
    }

    @Test
    public void testGetCountryByZipcode() {
        // Canada
        String[] CA = {"K0H9Z0", "K1A0B1", "L9K9Z9", "V1C9Z9"};
        // United Kingdom
        String[] GB = {"M25BQ", "M344AB", "CR02YR", "DN169AA", "W1A4ZZ", "EC1A1HQ", "GIR0AA"};
        // America
        String[] US = {"01234", "99999", "12345"};
        // unknown
        String[] unknown = {"1234567890", "12", "AB", "ABC", "ABCXYZ", "000XYZ", "ABC000"};
        // illegal
        String[] illegal = {"ABC.123", "XYZ-789", "abc123"};

        for (var zipcode : CA) {
            Assertions.assertThat(OBBusinessService.getCountryByZipcode(zipcode))
                    .isEqualTo("CA");
        }
        for (var zipcode : GB) {
            Assertions.assertThat(OBBusinessService.getCountryByZipcode(zipcode))
                    .isEqualTo("GB");
        }
        for (var zipcode : US) {
            Assertions.assertThat(OBBusinessService.getCountryByZipcode(zipcode))
                    .isEqualTo("US");
        }
        for (var zipcode : unknown) {
            Assertions.assertThat(OBBusinessService.getCountryByZipcode(zipcode))
                    .isNull();
        }
        for (var zipcode : illegal) {
            Assertions.assertThat(OBBusinessService.getCountryByZipcode(zipcode))
                    .isNull();
        }
    }

    @Test
    public void getSettingInfoByBusinessId() {
        // mock book online
        Mockito.doReturn(getEnableSetting()).when(businessBookOnlineMapper).selectByBusinessId(BUSINESS_ID);

        MoeBusinessBookOnline result = businessService.getSettingInfoByBusinessId(BUSINESS_ID);
        Assertions.assertThat(result.getIsEnable()).isEqualTo(CommonConstant.ENABLE);
    }

    @Test
    public void buildAvailableResult() {
        List<ServiceAreaParams.ClientAddressParams> paramsList = new ArrayList<>();
        ServiceAreaParams.ClientAddressParams params = new ServiceAreaParams.ClientAddressParams();
        params.setAddressId(ADDRESS_ID);
        paramsList.add(params);

        List<ServiceAreaResultDTO> resultDTOList = businessService.buildAvailableResult(paramsList);
        resultDTOList.forEach(serviceAreaResultDTO -> {
            Assertions.assertThat(serviceAreaResultDTO.getOutOfArea()).isFalse();
            Assertions.assertThat(serviceAreaResultDTO.getAddressId()).isEqualTo(ADDRESS_ID);
        });
    }

    @Test
    public void enableServiceArea() {
        MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
        businessBookOnline.setIsNeedAddress(CommonConstant.ENABLE);

        businessBookOnline.setIsByZipcode(CommonConstant.ENABLE);
        businessBookOnline.setIsByRadius(CommonConstant.DISABLE);
        boolean result = businessService.disableServiceArea(businessBookOnline);
        Assertions.assertThat(result).isFalse();

        businessBookOnline.setIsByZipcode(CommonConstant.DISABLE);
        businessBookOnline.setIsByRadius(CommonConstant.ENABLE);
        result = businessService.disableServiceArea(businessBookOnline);
        Assertions.assertThat(result).isFalse();

        businessBookOnline.setIsByZipcode(CommonConstant.DISABLE);
        businessBookOnline.setIsByRadius(CommonConstant.DISABLE);
        businessBookOnline.setServiceAreas(List.of(1, 2));
        result = businessService.disableServiceArea(businessBookOnline);
        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void disableServiceArea() {
        MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
        businessBookOnline.setIsByZipcode(CommonConstant.DISABLE);
        businessBookOnline.setIsByRadius(CommonConstant.DISABLE);
        businessBookOnline.setServiceAreas(null);
        boolean result = businessService.disableServiceArea(businessBookOnline);
        Assertions.assertThat(result).isTrue();
    }

    @Test
    public void getServiceAreaResultListNotSetZipcodes() {
        Mockito.doReturn(getEnableSetting()).when(bookOnlineService).getSettingInfoByBusinessId(BUSINESS_ID);
        // mock unitOfDistanceType
        MoeBusinessDto moeBusinessDto = new MoeBusinessDto();
        moeBusinessDto.setUnitOfDistanceType(Dictionary.UNITED_DISTANCE_TYPE_1.byteValue());
        Mockito.doReturn(moeBusinessDto).when(iBusinessBusinessClient).getBusinessInfo(Mockito.any(InfoIdParams.class));

        List<ServiceAreaParams.ClientAddressParams> addressParamsList = new ArrayList<>();
        ServiceAreaParams.ClientAddressParams addressParams = new ServiceAreaParams.ClientAddressParams();
        addressParams.setAddressId(ADDRESS_ID);
        addressParams.setLat(LAT);
        addressParams.setLng(LNG);
        addressParamsList.add(addressParams);
        ServiceAreaParams serviceAreaParams = new ServiceAreaParams();
        serviceAreaParams.setBusinessId(BUSINESS_ID);
        serviceAreaParams.setAddressParamsList(addressParamsList);
        List<ServiceAreaResultDTO> serviceAreaResultList = businessService.getServiceAreaResultList(serviceAreaParams);
        serviceAreaResultList.forEach(
                resultDTO -> Assertions.assertThat(resultDTO.getOutOfArea()).isFalse());
    }

    @Test
    public void checkAvailableDistNotHaveZipcodes() {
        ServiceAreaParams.ClientAddressParams params = new ServiceAreaParams.ClientAddressParams();
        boolean result = businessService.checkAvailableDist(
                params, getEnableSetting(), Dictionary.UNITED_DISTANCE_TYPE_1.byteValue(), null);
        Assertions.assertThat(result).isTrue();
    }

    @Test
    public void checkAvailableDistNotHaveSettings() {
        ServiceAreaParams.ClientAddressParams params = new ServiceAreaParams.ClientAddressParams();
        boolean result = businessService.checkAvailableDist(
                params, getEnableRadiusSetting(), Dictionary.UNITED_DISTANCE_TYPE_1.byteValue(), null);
        Assertions.assertThat(result).isTrue();
    }

    @Test
    public void checkAvailableDistFalse() {
        MoeBusinessBookOnline radiusSetting = getEnableRadiusSetting();
        ServiceAreaParams.ClientAddressParams params = new ServiceAreaParams.ClientAddressParams();
        radiusSetting.setSettingLocation("shenzhen");
        radiusSetting.setSettingLat(LAT);
        radiusSetting.setSettingLng(LNG);

        boolean result = businessService.checkAvailableDist(
                params, radiusSetting, Dictionary.UNITED_DISTANCE_TYPE_1.byteValue(), null);
        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void checkZipcodeByLatLngMaxDist() {
        MoeBusinessBookOnline enableSetting = getEnableSetting();
        enableSetting.setMaxAvailableDist(10);
        enableSetting.setMaxAvailableTime(20);

        var distanceMatrixElement = RouteMatrixElement.newBuilder()
                .setStatus(Status.newBuilder()
                        .setCode(Code.OK.getNumber())
                        .setMessage("OK")
                        .build())
                .setDistance(1000)
                .setDuration(Duration.newBuilder().setSeconds(60).build())
                .build();
        Mockito.doReturn(distanceMatrixElement)
                .when(smartScheduleService)
                .callGoogleAPI(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());

        Mockito.doReturn(9).when(bookOnlineService).metersToMiOrKm(Dictionary.UNITED_DISTANCE_TYPE_1.byteValue(), 1000);

        boolean result =
                businessService.checkByRadius(LAT, LNG, enableSetting, Dictionary.UNITED_DISTANCE_TYPE_1.byteValue());
        Assertions.assertThat(result).isTrue();
    }

    @Test
    public void checkZipcodeByLatLngMaxTime() {
        MoeBusinessBookOnline enableSetting = getEnableSetting();
        enableSetting.setMaxAvailableDist(10);
        enableSetting.setMaxAvailableTime(20);

        var matrixElement = RouteMatrixElement.newBuilder()
                .setStatus(Status.newBuilder()
                        .setCode(Code.OK.getNumber())
                        .setMessage("OK")
                        .build())
                .setDistance(1000)
                .setDuration(Duration.newBuilder().setSeconds(60).build())
                .build();
        Mockito.doReturn(matrixElement)
                .when(smartScheduleService)
                .callGoogleAPI(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());

        Mockito.doReturn(11)
                .when(bookOnlineService)
                .metersToMiOrKm(Dictionary.UNITED_DISTANCE_TYPE_1.byteValue(), matrixElement.getDistance());

        boolean result =
                businessService.checkByRadius(LAT, LNG, enableSetting, Dictionary.UNITED_DISTANCE_TYPE_1.byteValue());
        Assertions.assertThat(result).isTrue();
    }

    @Test
    public void checkZipcodeByLatLngNotAvailable() {
        MoeBusinessBookOnline enableSetting = getEnableSetting();
        enableSetting.setMaxAvailableDist(10);
        enableSetting.setMaxAvailableTime(20);

        var matrixElement = RouteMatrixElement.newBuilder()
                .setStatus(Status.newBuilder()
                        .setCode(Code.OK.getNumber())
                        .setMessage("OK")
                        .build())
                .setDistance(1000)
                .setDuration(Duration.newBuilder().setSeconds(60 * 30).build())
                .build();
        Mockito.doReturn(matrixElement)
                .when(smartScheduleService)
                .callGoogleAPI(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());

        Mockito.doReturn(11)
                .when(bookOnlineService)
                .metersToMiOrKm(Dictionary.UNITED_DISTANCE_TYPE_1.byteValue(), matrixElement.getDistance());

        boolean result =
                businessService.checkByRadius(LAT, LNG, enableSetting, Dictionary.UNITED_DISTANCE_TYPE_1.byteValue());
        Assertions.assertThat(result).isFalse();
    }
}
