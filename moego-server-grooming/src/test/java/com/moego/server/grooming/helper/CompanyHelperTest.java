package com.moego.server.grooming.helper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.moego.idl.service.organization.v1.BatchGetCompanyIdResponse;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.lib.common.exception.BizException;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeStaffDto;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CompanyHelperTest {

    @Mock
    private IBusinessStaffClient iBusinessStaffClient;

    @Mock
    private BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    @InjectMocks
    private CompanyHelper companyHelper;

    @Test
    void getStaffInfoMap_withValidStaffIds_returnsExpectedResult() {
        Integer businessId = 1;
        List<Integer> staffIds = List.of(1, 2, 3);
        var staffList = List.of(
                new MoeStaffDto() {
                    {
                        setId(1);
                    }
                },
                new MoeStaffDto() {
                    {
                        setId(2);
                    }
                },
                new MoeStaffDto() {
                    {
                        setId(3);
                    }
                });
        when(iBusinessStaffClient.getStaffList(any())).thenReturn(staffList);

        var result = companyHelper.getStaffInfoMap(businessId, staffIds);

        var expect = Map.of(
                1,
                new MoeStaffDto() {
                    {
                        setId(1);
                    }
                },
                2,
                new MoeStaffDto() {
                    {
                        setId(2);
                    }
                },
                3,
                new MoeStaffDto() {
                    {
                        setId(3);
                    }
                });
        assertThat(result).isEqualTo(expect);
    }

    @Test
    void getStaffInfoMap_withInvalidStaffIds_returnsEmptyResult() {
        Integer businessId = 1;
        List<Integer> staffIds = List.of(-1, 0);

        Map<Integer, MoeStaffDto> result = companyHelper.getStaffInfoMap(businessId, staffIds);

        assertThat(result).isEmpty();
    }

    @Test
    void mustGetCompanyId_ValidCompanyId_ReturnsCompanyId() {
        long businessId = 12345L;
        long expectedCompanyId = 67890L;

        when(businessServiceBlockingStub.batchGetCompanyId(any()))
                .thenReturn(BatchGetCompanyIdResponse.newBuilder()
                        .putBusinessCompanyIdMap(businessId, expectedCompanyId)
                        .build());

        long actualCompanyId = companyHelper.mustGetCompanyId(businessId);

        assertThat(actualCompanyId).isEqualTo(expectedCompanyId);
    }

    @Test
    void mustGetCompanyId_InvalidCompanyId_ThrowsException() {
        long businessId = 12345L;

        var spyService = spy(companyHelper);

        when(businessServiceBlockingStub.batchGetCompanyId(any()))
                .thenReturn(BatchGetCompanyIdResponse.newBuilder().build());

        Exception exception = assertThrows(BizException.class, () -> spyService.mustGetCompanyId(businessId));

        assertThat(exception).isInstanceOf(BizException.class);
    }
}
