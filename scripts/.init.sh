# !/bin/bash
# Purpose: 初始化脚本, 检查依赖版本、安装工具等
# <AUTHOR> <EMAIL>

set -euo pipefail
source scripts/common.sh

version_compare() {
    local v1=(${1//./ }) v2=(${2//./ })
    local max_length=$((${#v1[@]} > ${#v2[@]} ? ${#v1[@]} : ${#v2[@]}))
    
    # 统一数组长度
    for ((i=${#v1[@]}; i<max_length; i++)); do v1[i]=0; done
    for ((i=${#v2[@]}; i<max_length; i++)); do v2[i]=0; done

    for ((i=0; i<max_length; i++)); do
        # 显式声明为整数
        declare -i num1=$((10#${v1[i]}))
        declare -i num2=$((10#${v2[i]}))
        
        if (( num1 < num2 )); then
            return 1
        elif (( num1 > num2 )); then
            return 0
        fi
    done
    return 0
}

bazelisk_version_compare() {
    echo -e "${YELLOW}Checking Bazel version...${NC}"

    # 获取当前版本（新增安全处理）
    local current_version
    current_version=$(bazelisk version 2>/dev/null | grep -Eo '([0-9]+\.){2}[0-9]+' | head -n1)
    
    # 版本号有效性检查
    if [[ ! "$current_version" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        echo -e "${RED}error: Failed to get valid Bazel version${NC}" >&2
        return 1
    fi

    echo "Current Bazel version: $current_version, Required version: >= $MIN_BAZELISK_VERSION"
    
    if ! version_compare "$current_version" "$MIN_BAZELISK_VERSION"; then
        echo -e "${RED}error: Bazel version is too old (current: $current_version, required: >= $MIN_BAZELISK_VERSION)${NC}" >&2
        return 1
    fi

    echo -e "${GREEN}Bazel version check passed${STAR}${NC}"
}

dependencies_version_compare() {
    # go 版本检查
   local go_version=$(go version | grep -Eo 'go[0-9]+\.[0-9]+(\.[0-9]+)?' | sed 's/go//')
    printf "%-50s" "Go version check ($go_version)..."; 
    
    if version_compare "$go_version" "$MIN_GO_VERSION"; then 
		echo -e "${GREEN}${CHECK}${NC}"; 
	else 
		echo -e "${RED}${CROSS}${NC}"; 
		echo "Go version must be greater than or equal to $(MIN_GO_VERSION)"; 
		exit 1; 
	fi

    # lcov 版本检查
    printf "%-50s" "lcov version check..."; 
    if ! command -v lcov >/dev/null 2>&1; then 
		echo -e "${RED}${CROSS}${NC} lcov not installed, please install lcov"; 
		exit 1; 
	else    
		echo -e "${GREEN}${CHECK}${NC}"; 
	fi  

    # buf 版本检查
    printf "%-50s" "buf version check..."; 
    if ! command -v buf >/dev/null 2>&1; then 
		echo -e "${RED}${CROSS}${NC} buf not installed, please install buf"; 
		exit 1; 
	else    
		echo -e "${GREEN}${CHECK}${NC}"; 
	fi  
}

install_tools() {
    echo "Installing dependencies..."
    # 设置Go环境变量避免VCS问题
    export CGO_ENABLED=0
    export GOFLAGS=-buildvcs=false
    
    go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
    go install github.com/incu6us/goimports-reviser/v3@latest
	  go install github.com/bufbuild/buf/cmd/buf@latest
	  go install go.uber.org/mock/mockgen@latest
    go install github.com/bazelbuild/bazelisk@latest
    go install github.com/googleapis/api-linter/cmd/api-linter@v1.69.2 # 更高的版本对 -I 的支持有问题，先锁住
    npm install -g @openapitools/openapi-generator-cli
    npm install -g @bufbuild/protoc-gen-es
    echo -e "${GREEN}Dependencies installed successfully ${CHECK}${NC}"
}

main() {
    # dependencies version check
    dependencies_version_compare

    # install tools
    install_tools

    # bazel 版本检查
    bazelisk_version_compare
    
    buf dep update
    buf generate
}

main $@
