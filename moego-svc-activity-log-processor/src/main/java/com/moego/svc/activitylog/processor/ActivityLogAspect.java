package com.moego.svc.activitylog.processor;

import java.lang.reflect.Method;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.expression.AnnotatedElementKey;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.EvaluationException;
import org.springframework.util.StringUtils;

/**
 * Activity log aspect.
 *
 * <AUTHOR>
 */
@Aspect
public class ActivityLogAspect {
    private static final Logger log = LoggerFactory.getLogger(ActivityLogAspect.class);

    private final ActivityLogExpressionEvaluator evaluator = new ActivityLogExpressionEvaluator();
    private final BeanFactory beanFactory;

    public ActivityLogAspect(BeanFactory beanFactory) {
        this.beanFactory = beanFactory;
    }

    @Pointcut("@annotation(com.moego.svc.activitylog.processor.ActivityLog)")
    public void activityLog() {}

    @Around("activityLog()")
    public Object aroundActivityLog(ProceedingJoinPoint pjp) throws Throwable {
        Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        ActivityLog anno = method.getAnnotation(ActivityLog.class);
        if (anno == null) {
            // should not happen
            return pjp.proceed();
        }
        boolean beforeInvocation = anno.beforeInvocation();
        if (beforeInvocation) {
            sendEvent(pjp, null, method, anno);
            return pjp.proceed();
        }
        Object v = pjp.proceed();
        sendEvent(pjp, v, method, anno);
        return v;
    }

    private void sendEvent(ProceedingJoinPoint pjp, Object result, Method method, ActivityLog anno) {
        try {
            Class<?> targetClass = pjp.getTarget().getClass();
            EvaluationContext ctx = evaluator.createEvaluationContext(
                    method, pjp.getArgs(), pjp.getTarget(), targetClass, result, beanFactory);
            AnnotatedElementKey elementKey = new AnnotatedElementKey(method, targetClass);
            Object resourceId =
                    StringUtils.hasLength(anno.resourceId()) ? evaluator.key(anno.resourceId(), elementKey, ctx) : null;
            Object details =
                    StringUtils.hasLength(anno.details()) ? evaluator.key(anno.details(), elementKey, ctx) : null;
            if (anno.root()) {
                ActivityLogRecorder.recordRoot(anno.action(), anno.resourceType(), resourceId, details);
            } else {
                ActivityLogRecorder.record(anno.action(), anno.resourceType(), resourceId, details);
            }
        } catch (EvaluationException e) {
            // Expression parsing fails and should not affect the business.
            log.warn("Failed to parse activity log expression, will not record activity log.", e);
        } catch (Exception e) {
            // unexpected exception, should not affect the business.
            log.warn("Unknown error occurred when recording activity log.", e);
        }
    }
}
