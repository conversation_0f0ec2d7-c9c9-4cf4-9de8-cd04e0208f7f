GREEN=\033[0;32m
RED=\033[0;31m
NC=\033[0m # No Color
CHECK=✓
CROSS=✗
STAR=✨
SCRIPTS_DIR=./scripts
BAZEL_DIR=./bazel


git_hook:
	@git config core.hooksPath ./.github/hooks

init: git_hook 
	@bash $(SCRIPTS_DIR)/.init.sh
	@$(MAKE) api-test-init CLEAN=true
	@$(MAKE) gazelle
	@echo "${GREEN}Init done!${STAR}${NC}"

lint:
	@bash $(SCRIPTS_DIR)/lint.sh

create:
	@bash $(SCRIPTS_DIR)/.create_app.sh $(module) $(service)

# CODEOWNERS 相关命令
codeowners:
	@bash $(SCRIPTS_DIR)/generate_codeowners.sh

################### bazel ###################
# BAZEL_COMMAND = bazelisk --bazelrc=$(BAZEL_DIR)/$(shell test $$(uname) = "Darwin" && echo "mac.bazelrc" || echo ".bazelrc")
BAZEL_COMMAND = bazelisk --bazelrc=$(BAZEL_DIR)/.bazelrc
clean: 
	$(BAZEL_COMMAND) clean --expunge --async
	@ if [[ "$(cache)" == "1" ]]; then rm -rf $(BAZEL_DIR)/cache/bazel; fi
	@echo "${GREEN}clean bazel done${STAR}${NC}"

build: gazelle 
	@$(BAZEL_COMMAND) build $(if $(dir),$(dir),//backend/app/...)

build_proto:
	@$(BAZEL_COMMAND) build $(if $(dir),$(dir),//backend/proto:moego)

run:
	@echo "${GREEN}Running ${app}...${NC}"
	@CONFIG_FILE="./config/testing/config.yaml"; \
	if [[ "$(local)" == "1" ]]; then \
		CONFIG_FILE="./config/local/config.yaml"; \
		echo "${GREEN}Using local configuration${NC}"; \
	fi; \
	cp -f $(shell $(BAZEL_COMMAND) info bazel-bin)/backend/app/${app}/${app}_/${app} backend/app/${app}/server; \
	cd backend/app/${app} && ./server -config $$CONFIG_FILE

test: gazelle
	@bash $(SCRIPTS_DIR)/.test_coverage.sh $(if $(report),$(report),0) $(dir)

# 获取发生了变更的 app, dir 为可选参数, 默认值为 backend, 可选值为 backend 或 frontend
diff: 
	@bash $(SCRIPTS_DIR)/.get_changed_apps.sh $(dir)

gazelle:
	@if git diff --merge-base origin/main --stat | grep -q '^ \+go.mod' ; then \
		echo "${GREEN}go mod tidy${STAR}${NC}" ; \
		go mod tidy ; \
		${BAZEL_COMMAND} mod tidy ; \
		${BAZEL_COMMAND} run //:gazelle-update-repos ; \
	fi 
	@${BAZEL_COMMAND} run //:gazelle
	@echo "${GREEN}bazel gazelle done${STAR}${NC}"

proto:
	@rm -rf tmp
	@find backend/proto -name "*.go" -type f -delete
	@buf generate --include-imports
	@cp -r tmp/go/backend/proto/. ./backend/proto/.
	@rm -rf tmp/go
	@echo "${GREEN}generate go files done${STAR}${NC}"

# 生成外部引用的 proto 文件，用于 IDE 本地索引
deps:
	@yq -r '.deps[]' buf.yaml | xargs -I % buf export % -o backend/proto/deps
	@echo "${GREEN}generate proto dependency done${STAR}${NC}"

# 生成proto的二进制文件
pbin:
	buf build -o moego.bin --as-file-descriptor-set --path $(PROTO_DIR)

.PHONY: init tools gazelle build proto codeowners codeowners-all ci-image-build ci-image-test ci-image-push ci-image

################### CI Docker Image ###################

# 构建CI镜像
ci-image-build:
	@echo "${GREEN}Building CI builder image...${NC}"
	@docker build -f .github/docker/ci-builder.Dockerfile -t moego-ci-builder:latest .
	@echo "${GREEN}CI builder image built successfully!${STAR}${NC}"

# 测试CI镜像
ci-image-test:
	@echo "${GREEN}Testing CI builder image...${NC}"
	@docker run --rm moego-ci-builder:latest /bin/bash -c "\
		echo '=== Testing installed tools ===' && \
		go version && \
		node --version && \
		npm --version && \
		yarn --version && \
		bazel version && \
		buf --version && \
		yq --version && \
		aws --version && \
		docker --version && \
		api-linter --version && \
		goimports-reviser --help | head -1 && \
		echo '=== All tools working correctly ===' \
	"
	@echo "${GREEN}CI builder image test passed!${STAR}${NC}"

# 推送CI镜像到GitHub Container Registry
ci-image-push:
	@echo "${GREEN}Pushing CI builder image to GitHub Container Registry...${NC}"
	@docker tag moego-ci-builder:latest ghcr.io/moegolibrary/moego-ci-builder:latest
	@docker push ghcr.io/moegolibrary/moego-ci-builder:latest
	@echo "${GREEN}CI builder image pushed successfully!${STAR}${NC}"

# 完整的CI镜像构建和测试流程
ci-image: ci-image-build ci-image-test
	@echo "${GREEN}CI builder image ready for use!${STAR}${NC}"

################### test ###################

# 初始化 api 集成测试需要的代码
api-test-init:
	@echo "init api test code"
	@bash backend/test/api_integration/script/api-test-init.sh CLEAN=$(CLEAN) GREY_NAME=$(GREY_NAME) DEF_BRANCH=$(DEF_BRANCH)

# 运行 api 集成测试
# e.g. make api-test
api-test:
	@if [ "$(INIT)" = "true" ]; then $(MAKE) api-test-init CLEAN=$(CLEAN) GREY_NAME=$(GREY_NAME) DEF_BRANCH=$(DEF_BRANCH); fi

	@echo "run api test"
	@bash backend/test/api_integration/script/api-test.sh ENV=$(ENV) GREY_NAME=$(GREY_NAME) TEST=$(TEST) LOG=$(LOG) LOG_RESPONSE=$(LOG_RESPONSE) TAGS=$(TAGS)
