package com.moego.lib.common.auth.http;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootTest(
        classes = HttpAuthIntegrationTests.Config.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        properties = {"moego.grpc.enabled=false", "moego.http.server.observability.metrics.enabled=false"})
public class HttpAuthIntegrationTests {

    @LocalServerPort
    int port;

    @Autowired
    TestRestTemplate restTemplate;

    @Test
    void testAuth() {
        String url = "http://localhost:" + port + "/hello";

        String res = restTemplate.getForObject(url, String.class);
        assertThat(res)
                .isEqualTo(
                        "{\"code\":401,\"message\":\"Unauthorized error\",\"data\":\"Unauthorized error\",\"causedBy\":null,\"success\":false}");

        RequestEntity request = RequestEntity.get(url)
                .header(AuthContext.HK_SESSION_ID, "111")
                .header(AuthContext.HK_ACCOUNT_ID, "222")
                .build();
        ResponseEntity<String> re = restTemplate.exchange(request, String.class);
        assertThat(re.getBody()).isEqualTo("hello");
        assertThat(re.getStatusCodeValue()).isEqualTo(200);
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @RestController
    static class Config {

        @GetMapping("/hello")
        @Auth(AuthType.ACCOUNT)
        public String hello() {
            return "hello";
        }
    }
}
