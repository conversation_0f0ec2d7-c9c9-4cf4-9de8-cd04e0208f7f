package com.moego.lib.common.grpc;

import static org.assertj.core.api.Assertions.assertThat;

import io.grpc.Channel;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import org.junit.jupiter.api.Test;

/**
 * {@link GrpcUtil} tester.
 */
class GrpcUtilTest {

    /**
     * {@link GrpcUtil#shutdownChannel(Channel)}
     */
    @Test
    void testShutdownChannel() {
        ManagedChannel channel = ManagedChannelBuilder.forAddress("localhost", 8080)
                .usePlaintext()
                .build();
        assertThat(channel.isShutdown()).isFalse();
        GrpcUtil.shutdownChannel(channel);
        assertThat(channel.isShutdown()).isTrue();
        assertThat(channel.isTerminated()).isTrue();
    }
}
