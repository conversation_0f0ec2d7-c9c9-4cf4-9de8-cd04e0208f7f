package com.moego.lib.common.proto;

import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.NullValue;
import com.google.protobuf.Struct;
import com.google.protobuf.Value;
import com.google.protobuf.util.JsonFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;
import org.junit.jupiter.api.Test;

/**
 * {@link ProtoUtils} tester.
 */
class ProtoUtilsTest {

    @Test
    void toValue_whenSimpleStruct() {
        // null
        Value value = ProtoUtils.toValue(null);
        assertThat(value.hasNullValue()).isTrue();
        assertThat(value.getNullValue()).isEqualTo(NullValue.NULL_VALUE);

        // string
        value = ProtoUtils.toValue("hello");
        assertThat(value.getStringValue()).isEqualTo("hello");

        // boolean
        value = ProtoUtils.toValue(true);
        assertThat(value.getBoolValue()).isTrue();

        // number
        value = ProtoUtils.toValue(1);
        assertThat(value.getNumberValue()).isEqualTo(1);
        value = ProtoUtils.toValue(0.0);
        assertThat(value.getNumberValue()).isEqualTo(0);

        // list & array
        value = ProtoUtils.toValue(Arrays.asList("a", "b", "c"));
        assertThat(value.getListValue().getValuesList()).hasSize(3);
        value = ProtoUtils.toValue(new String[] {"hello", "world"});
        assertThat(value.getListValue().getValuesList()).hasSize(2);

        // struct
        value = ProtoUtils.toValue(new Object());
        assertThat(value.getStructValue().getFieldsMap()).isEmpty();

        value = ProtoUtils.toValue(new User().setAge(20).setName("hello"));
        assertThat(value.getStructValue().getFieldsMap()).hasSize(2);

        Map<String, String> map = new HashMap<>();
        map.put("a", "b");
        value = ProtoUtils.toValue(map);
        assertThat(value.getStructValue().getFieldsMap()).hasSize(1);
    }

    @Test
    void toValue_whenComplexStruct() throws InvalidProtocolBufferException {
        ComplexObject complexObject = buildComplexObject();
        Value value = ProtoUtils.toValue(complexObject);
        Struct sv = value.getStructValue();

        assertThat(sv.getFieldsMap()).hasSize(5);
        assertThat(sv.getFieldsMap().get("name").getStringValue()).isEqualTo("hello");
        assertThat(sv.getFieldsMap().get("friends").getListValue().getValuesList())
                .hasSize(2);
        assertThat(sv.getFieldsMap().get("hobbies").getStructValue().getFieldsMap())
                .hasSize(1);
        assertThat(sv.getFieldsMap().get("friendsMap").getStructValue().getFieldsMap())
                .hasSize(2);
        assertThat(sv.getFieldsMap().get("complex").getListValue().getValuesList())
                .hasSize(1);

        // take a look at the json format
        System.out.println(JsonFormat.printer().print(sv));
    }

    private static ComplexObject buildComplexObject() {
        ComplexObject complexObject = new ComplexObject();
        Map<String, String> map = new HashMap<>();
        map.put("a", "b");
        List<User> friends = Arrays.asList(
                new User().setAge(20).setName("hello"), new User().setAge(30).setName("world"));
        Map<String, User> friendsMap = friends.stream().collect(Collectors.toMap(User::getName, Function.identity()));
        List<Map<String, Map<String, String>>> complex = new ArrayList<>();
        Map<String, Map<String, String>> complexMap = new HashMap<>();
        complexMap.put("a", map);
        complexMap.put("b", map);
        complex.add(complexMap);
        complexObject
                .setName("hello")
                .setFriends(friends)
                .setHobbies(map)
                .setFriendsMap(friendsMap)
                .setComplex(complex);
        return complexObject;
    }

    /**
     * {@link ProtoUtils#mapToStruct(Map)}
     */
    @Test
    void mapToStruct() {
        var m = Map.of(
                "a",
                "b",
                "b",
                2,
                "c",
                true,
                "d",
                new User().setAge(20).setName("hello"),
                "e",
                List.of("a", 1, Map.of()),
                "f",
                Map.of("a", "b", "c", 1));
        Struct struct = ProtoUtils.mapToStruct(m);
        assertThat(struct.getFieldsMap()).hasSize(6);
        assertThat(struct.getFieldsMap().get("a").getStringValue()).isEqualTo("b");
        assertThat(struct.getFieldsMap().get("b").getNumberValue()).isEqualTo(2);
        assertThat(struct.getFieldsMap().get("c").getBoolValue()).isTrue();
        assertThat(struct.getFieldsMap().get("d").getStructValue().getFieldsMap())
                .hasSize(2);
        assertThat(struct.getFieldsMap().get("e").getListValue().getValuesList())
                .hasSize(3);
        assertThat(struct.getFieldsMap().get("f").getStructValue().getFieldsMap())
                .hasSize(2);
    }

    @Test
    void mapToStructNullValue() {
        var m = new HashMap<String, Object>();
        m.put("key", null);
        m.put("user", new User().setName(null).setAge(18));

        Struct struct = ProtoUtils.mapToStruct(m);
        assertThat(struct.getFieldsMap()).hasSize(2);
        assertThat(struct.getFieldsMap().get("key").getKindCase()).isEqualTo(Value.KindCase.NULL_VALUE);

        var userValue = struct.getFieldsMap().get("user");
        assertThat(userValue.getKindCase()).isEqualTo(Value.KindCase.STRUCT_VALUE);
        assertThat(userValue.getStructValue().getFieldsMap()).hasSize(2);
        assertThat(userValue.getStructValue().getFieldsMap().get("name").getKindCase())
                .isEqualTo(Value.KindCase.NULL_VALUE);
        assertThat(userValue.getStructValue().getFieldsMap().get("age").getNumberValue())
                .isEqualTo(18);
    }

    /**
     * {@link ProtoUtils#structToMap(Struct)}
     */
    @Test
    void structToMap() {
        var m = Map.of(
                "a",
                "b",
                "b",
                2,
                "c",
                true,
                "d",
                new User().setAge(20).setName("hello"),
                "e",
                List.of("a", 1, Map.of()),
                "f",
                Map.of("a", "b", "c", 1));
        Struct struct = ProtoUtils.mapToStruct(m);
        Map<String, Object> map = ProtoUtils.structToMap(struct);
        assertThat(map).hasSize(6);
        assertThat(map.get("a")).isEqualTo("b");
        assertThat(map.get("b")).isEqualTo(2.0);
        assertThat(map.get("c")).isEqualTo(true);
        assertThat(map.get("d")).isInstanceOf(Map.class);
        assertThat(map.get("e")).isInstanceOf(List.class);
        assertThat(map.get("f")).isInstanceOf(Map.class);
    }

    @Test
    void structToMapNullValue() {
        Struct userStruct = Struct.newBuilder()
                .putFields(
                        "name",
                        Value.newBuilder().setNullValue(NullValue.NULL_VALUE).build())
                .putFields("age", Value.newBuilder().setNumberValue(18).build())
                .build();

        Struct struct = Struct.newBuilder()
                .putFields(
                        "key",
                        Value.newBuilder().setNullValue(NullValue.NULL_VALUE).build())
                .putFields("user", Value.newBuilder().setStructValue(userStruct).build())
                .build();

        Map<String, Object> map = ProtoUtils.structToMap(struct);
        assertThat(map).hasSize(2);
        assertThat(map.containsKey("key")).isTrue();
        assertThat(map.get("key")).isNull();

        assertThat(map.containsKey("user")).isTrue();
        Map<String, Object> user = (Map<String, Object>) map.get("user");
        assertThat(user).hasSize(2);
        assertThat(user.containsKey("name")).isTrue();
        assertThat(user.get("name")).isNull();
        assertThat(user.get("age")).isEqualTo((double) 18);
    }

    @Data
    @Accessors(chain = true)
    static class User {

        private String name;
        private int age;
    }

    @Data
    @Accessors(chain = true)
    static class ComplexObject {

        private String name;
        Map<String, String> hobbies;
        List<User> friends;
        Map<String, User> friendsMap;
        List<Map<String, Map<String, String>>> complex;
    }
}
