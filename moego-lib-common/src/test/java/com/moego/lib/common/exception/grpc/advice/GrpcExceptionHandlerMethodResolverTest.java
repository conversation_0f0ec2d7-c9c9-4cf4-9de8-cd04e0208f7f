package com.moego.lib.common.exception.grpc.advice;

import static org.assertj.core.api.Assertions.assertThat;

import java.lang.reflect.Method;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ReflectionUtils;

/**
 * {@link GrpcExceptionHandlerMethodResolver} tester.
 */
class GrpcExceptionHandlerMethodResolverTest {

    /**
     * {@link GrpcExceptionHandlerMethodResolver#extractExtendedThrowable}
     */
    @Test
    void testExtractExtendedThrowable_whenNestedException() {
        GrpcExceptionHandlerMethodResolver resolver =
                new GrpcExceptionHandlerMethodResolver(new GrpcAdviceDiscoverer());

        Method m1 = ReflectionUtils.findMethod(Foo.class, "method01");
        Method m2 = ReflectionUtils.findMethod(Foo.class, "method02");

        assertThat(m1).isNotNull();
        assertThat(m2).isNotNull();

        Map<Class<? extends Throwable>, Method> mappedMethods = Map.ofEntries(
                Map.entry(IllegalStateException.class, m1), Map.entry(IllegalArgumentException.class, m2));

        ReflectionTestUtils.setField(resolver, "mappedMethods", mappedMethods);

        Method method = resolver.extractExtendedThrowable(new IllegalStateException());
        assertThat(method).isEqualTo(m1);

        method = resolver.extractExtendedThrowable(new IllegalArgumentException());
        assertThat(method).isEqualTo(m2);

        method = resolver.extractExtendedThrowable(new RuntimeException(new IllegalStateException()));
        assertThat(method).isEqualTo(m1);

        method = resolver.extractExtendedThrowable(new RuntimeException(new IllegalArgumentException()));
        assertThat(method).isEqualTo(m2);

        method = resolver.extractExtendedThrowable(
                new RuntimeException(new IllegalArgumentException(new IllegalStateException())));
        assertThat(method).isEqualTo(m2);

        method = resolver.extractExtendedThrowable(new RuntimeException());
        assertThat(method).isNull();
    }

    static class Foo {

        void method01() {}

        void method02() {}
    }
}
