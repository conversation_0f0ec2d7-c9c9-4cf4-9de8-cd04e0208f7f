package com.moego.lib.common.autoconfigure.feature;

import static com.freemanan.cr.core.anno.Verb.ADD;
import static com.moego.lib.common.Dependency.REDIS_STARTER;
import static org.assertj.core.api.Assertions.assertThat;

import com.freemanan.cr.core.anno.Action;
import com.freemanan.cr.core.anno.ClasspathReplacer;
import com.moego.lib.common.grpc.health.DataSourceHealthDetector;
import com.moego.lib.common.grpc.health.HealthChecker;
import com.moego.lib.common.grpc.health.RedisHealthDetector;
import com.moego.lib.common.http.health.InitController;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;

/**
 * {@link Health} tester.
 */
@ClasspathReplacer({@Action(verb = ADD, value = REDIS_STARTER)})
public class HealthWebAppNoJdbcStarterTest {

    private final WebApplicationContextRunner runner =
            new WebApplicationContextRunner().withUserConfiguration(Health.class);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(InitController.class);
            assertThat(context).hasSingleBean(HealthChecker.class);
            assertThat(context).doesNotHaveBean(DataSourceHealthDetector.class);
            assertThat(context).hasSingleBean(RedisHealthDetector.class);
        });
    }
}
