package com.moego.lib.common.http.feign;

import lombok.Data;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootTest(
        classes = ControllerEmptyBeanIT.Cfg.class,
        properties = {"moego.grpc.enabled=false"},
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class ControllerEmptyBeanIT {

    @Autowired
    TestRestTemplate rest;

    @LocalServerPort
    int port;

    @Test
    void testReturnEmptyBeanInController() {
        ResponseEntity<EmptyBean> entity = rest.getForEntity("http://localhost:" + port + "/get", EmptyBean.class);
        Assertions.assertThat(entity.getStatusCode()).isEqualTo(HttpStatus.OK);
    }

    @Data
    static class EmptyBean {}

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @RestController
    static class Cfg {

        @GetMapping("/get")
        public EmptyBean get() {
            return new EmptyBean();
        }
    }
}
