package com.moego.lib.common.exception.http;

import com.moego.lib.common.exception.BizException;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 * @since 2022/10/12
 */
@UtilityClass
public class HttpExceptionUtil {

    public static CommonResponse toCommonResponse(BizException e) {
        CommonResponse response = new CommonResponse();
        response.setCode(e.getCode());
        response.setMessage(e.getMessage());
        response.setData(e.getData());
        if (e.getCausedBy() != null) {
            response.setCausedBy(e.getCausedBy());
        } else if (e.getCause() != null) {
            response.setCausedBy(e.getCause().toString());
        }
        return response;
    }
}
