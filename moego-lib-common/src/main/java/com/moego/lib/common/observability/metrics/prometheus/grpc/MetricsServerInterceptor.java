package com.moego.lib.common.observability.metrics.prometheus.grpc;

import io.grpc.ForwardingServerCall;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import io.grpc.ServerCall;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import io.grpc.Status;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;

/**
 * Server interceptor for Prometheus metrics.
 *
 * <AUTHOR>
 * @since 2022/8/18
 */
public class MetricsServerInterceptor implements ServerInterceptor {

    // Interface time statistics
    private static final Histogram serverHandlingSeconds = Histogram.build()
            .name("grpc_server_handling_seconds")
            .help(
                    "Cumulative counters for the observation buckets (Histogram of response latency (seconds) of gRPC that had been application-level handled by the server.)")
            .labelNames("grpc_service", "grpc_method", "grpc_type")
            .buckets(0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10)
            .register();

    // gRPC request counter
    private static final Counter serverHandledTotal = Counter.build()
            .name("grpc_server_handled_total")
            .help("Total number of RPCs completed on the server, regardless of success or failure.")
            .labelNames("grpc_service", "grpc_method", "grpc_type", "grpc_code")
            .register();

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(
            ServerCall<ReqT, RespT> call, Metadata headers, ServerCallHandler<ReqT, RespT> next) {
        MethodDescriptor<ReqT, RespT> methodDescriptor = call.getMethodDescriptor();

        String gRPCMethod = methodDescriptor.getBareMethodName();
        String gRPCService = methodDescriptor.getServiceName();
        String gRPCType = methodDescriptor.getType().name();
        long start = System.currentTimeMillis();

        return next.startCall(
                new ForwardingServerCall.SimpleForwardingServerCall<>(call) {
                    @Override
                    public void close(Status status, Metadata trailers) {
                        double latencySeconds = (System.currentTimeMillis() - start) / 1000.0;
                        String code = status.getCode().toString();

                        serverHandlingSeconds
                                .labels(gRPCService, gRPCMethod, gRPCType)
                                .observe(latencySeconds);
                        serverHandledTotal
                                .labels(gRPCService, gRPCMethod, gRPCType, code)
                                .inc();
                        super.close(status, trailers);
                    }
                },
                headers);
    }
}
