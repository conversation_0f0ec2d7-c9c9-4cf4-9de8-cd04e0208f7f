package com.moego.lib.common.mybatis.typehandler;

import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.util.JsonUtil;
import java.lang.reflect.Type;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.core.ResolvableType;

/**
 * 将 MySQL json/varchar 类型转换为 Java 对象或者 Protobuf message。
 *
 * <AUTHOR>
 * @since 2025/1/26
 */
public abstract class BaseMySQLJsonTypeHandler<T> extends BaseTypeHandler<T> {

    private final Type type;

    protected BaseMySQLJsonTypeHandler() {
        this.type = ResolvableType.forClass(getClass())
                .as(BaseMySQLJsonTypeHandler.class)
                .getGeneric(0)
                .getType();
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JsonUtil.toJson(parameter));
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return fromJson(rs.getString(columnName), rs.wasNull());
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return fromJson(rs.getString(columnIndex), rs.wasNull());
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return fromJson(cs.getString(columnIndex), cs.wasNull());
    }

    private T fromJson(String value, boolean wasNull) {
        if (value == null && wasNull) {
            return null;
        }
        return JsonUtil.toBean(value, new TypeRef<>() {
            @Override
            public Type getType() {
                return type;
            }
        });
    }
}
