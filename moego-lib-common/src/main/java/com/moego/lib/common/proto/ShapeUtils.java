/*
 * @since 2022-06-22 22:20:04
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.proto;

import com.moego.idl.utils.v1.Id;
import com.moego.idl.utils.v1.OwnId;

/**
 * @deprecated by <PERSON> since 2023/11/3, do not use this class anymore.
 */
@Deprecated(forRemoval = true)
public class ShapeUtils {

    public static OwnId ownId(long id, long ownerId) {
        return OwnId.newBuilder().setId(id).setOwnerId(ownerId).build();
    }

    public static Id id(long id) {
        return Id.newBuilder().setId(id).build();
    }
}
