package com.moego.lib.common.http.util;

import lombok.experimental.UtilityClass;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2022/10/10
 */
@UtilityClass
public class HttpUtil {

    public static final String UNKNOWN_INTERNAL_ORIGIN = "UNKNOWN";

    /**
     * Categorize the status code into 5 categories
     *
     * @param code http status code
     * @return http code category
     */
    public static String categorizeCode(int code) {
        if (code < 200) {
            return "1xx";
        }
        if (code < 300) {
            return "2xx";
        }
        if (code < 400) {
            return "3xx";
        }
        if (code < 500) {
            return "4xx";
        }
        if (code < 600) {
            return "5xx";
        }
        return "4xx";
    }

    /**
     * Get service from authority.
     *
     * <ul>
     *     <li>svc -> svc</li>
     *     <li>svc:8080 -> svc</li>
     *     <li>svc.ns:8080 -> svc</li>
     * </ul>
     *
     * @param authority authority
     * @return service name
     */
    public static String getService(String authority) {
        authority = removeSchema(authority);
        int idx = authority.indexOf('.');
        if (idx > 0) {
            // exist namespace
            return authority.substring(0, idx);
        }
        // no namespace
        int i = authority.indexOf(':');
        if (i > 0) {
            // exist port
            return authority.substring(0, i);
        }
        // no port
        return authority;
    }

    /**
     * Remove schema from authority.
     *
     * @param authority authority
     * @return authority without schema
     */
    public static String removeSchema(String authority) {
        int schemaIdx = authority.indexOf("://");
        if (schemaIdx > 0) {
            authority = authority.substring(schemaIdx + 3);
        }
        return authority;
    }

    /**
     * Get host and ip from authority.
     *
     * @param authority authority
     * @return host ip
     */
    public static String getHostIp(String authority) {
        return getDomain(authority) + ":" + getPort(authority);
    }

    /**
     * Get port from authority.
     *
     * <ul>
     *     <li>svc -> 80</li>
     *     <li>svc:8080 -> 8080</li>
     *     <li>svc.ns:8080 -> 8080</li>
     * </ul>
     *
     * @param authority authority
     * @return port number
     */
    public static int getPort(String authority) {
        authority = removeSchema(authority);
        int i = authority.indexOf(':');
        if (i > 0) {
            // exist port
            return Integer.parseInt(authority.substring(i + 1));
        }
        // no port
        return 80;
    }

    /**
     * Get host from authority.
     *
     * <ul>
     *     <li>svc -> svc</li>
     *     <li>svc:8080 -> svc</li>
     *     <li>svc.ns:8080 -> svc.ns</li>
     * </ul>
     *
     * @param authority authority
     * @return host name
     */
    public static String getDomain(String authority) {
        authority = removeSchema(authority);
        int i = authority.indexOf(':');
        if (i > 0) {
            // exist port
            return authority.substring(0, i);
        }
        // no port
        return authority;
    }

    public static String getInternalOrigin(String internalOrigin) {
        if (StringUtils.hasText(internalOrigin)) {
            return internalOrigin;
        }
        return UNKNOWN_INTERNAL_ORIGIN;
    }
}
