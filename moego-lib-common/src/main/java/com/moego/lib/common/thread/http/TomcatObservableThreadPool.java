package com.moego.lib.common.thread.http;

import com.moego.lib.common.thread.ObservableThreadPool;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;

/**
 * Adapt tomcat thread pool {@link ThreadPoolExecutor}.
 *
 * <AUTHOR>
 * @since 2022/10/10
 */
public class TomcatObservableThreadPool implements ObservableThreadPool<ThreadPoolExecutor> {

    private final ThreadPoolExecutor pool;

    public TomcatObservableThreadPool(ThreadPoolExecutor threadPoolExecutor) {
        this.pool = threadPoolExecutor;
    }

    @Override
    public int getCoreSize() {
        return pool.getCorePoolSize();
    }

    @Override
    public int getMaxSize() {
        return pool.getMaximumPoolSize();
    }

    @Override
    public int getCurrentPoolSize() {
        return pool.getPoolSize();
    }

    @Override
    public int getActiveCount() {
        return pool.getActiveCount();
    }

    @Override
    public int getQueueSize() {
        return pool.getQueue().size();
    }

    @Override
    public ThreadPoolExecutor getThreadPool() {
        return pool;
    }
}
