package com.moego.lib.common.autoconfigure.datasource.mybatis;

import jakarta.annotation.Nullable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;
import org.apache.ibatis.session.SqlSession;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
public class MyBatisCache {

    private static final ConcurrentHashMap<Key, SqlSession> sqlSessions = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<Key, Object> mappers = new ConcurrentHashMap<>();

    public static Object getMapper(Class<?> mapperInterface, String datasourceName, Supplier<Object> supplier) {
        return mappers.computeIfAbsent(new Key(mapperInterface, datasourceName), k -> supplier.get());
    }

    @Nullable
    public static Object getMapper(Class<?> mapperInterface, String datasourceName) {
        return mappers.get(new Key(mapperInterface, datasourceName));
    }

    public static SqlSession getSqlSession(
            Class<?> mapperInterface, String datasourceName, Supplier<SqlSession> supplier) {
        return sqlSessions.computeIfAbsent(new Key(mapperInterface, datasourceName), k -> supplier.get());
    }

    /**
     * Invoked when the application is shutting down.
     */
    public static void clear() {
        mappers.clear();
        // NOTE: do not close the SqlSession here
        // see org.mybatis.spring.SqlSessionTemplate.close
        sqlSessions.clear();
    }

    private record Key(Class<?> mapperInterface, String datasourceName) {}
}
