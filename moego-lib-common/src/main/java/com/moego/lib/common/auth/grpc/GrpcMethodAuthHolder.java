package com.moego.lib.common.auth.grpc;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.grpc.AbstractAnnotationHolder;
import io.grpc.BindableService;
import io.grpc.ServerMethodDefinition;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Holds all the {@link Auth}s for gRPC service's methods.
 *
 * <AUTHOR>
 */
public class GrpcMethodAuthHolder extends AbstractAnnotationHolder<Auth> {

    private static final Logger log = LoggerFactory.getLogger(GrpcMethodAuthHolder.class);

    public GrpcMethodAuthHolder(List<BindableService> services) {
        super(services);
    }

    @Override
    public void putAnnotation(Method method, ServerMethodDefinition<?, ?> methodDefinition) {
        Optional.ofNullable(method.getAnnotation(Auth.class))
                .ifPresent(annotation ->
                        map.put(methodDefinition.getMethodDescriptor().getFullMethodName(), annotation));
    }

    @Override
    public void log() {
        if (!map.isEmpty() && log.isInfoEnabled()) {
            log.info("Grpc method auth holder initialized with {} methods", map.size());
        }
    }

    @Override
    public Auth getAnnotation(String methodName) {
        return map.get(methodName);
    }
}
