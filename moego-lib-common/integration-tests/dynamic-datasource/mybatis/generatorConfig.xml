<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC
        "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="Postgres">
        <property name="autoDelimitKeywords" value='true'/>
        <property name="beginningDelimiter" value='"'/>
        <property name="endingDelimiter" value='"'/>

        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"/>
        <plugin type="com.moego.lib.common.mybatisplugins.DynamicDataSourcePlugin"/>
        <plugin type="com.moego.lib.common.mybatisplugins.DisableGeneratedMapperMethodsPlugin"/>
        <plugin type="com.moego.lib.common.mybatisplugins.DeprecatedColumnsPlugin"/>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <jdbcConnection
                driverClass="org.postgresql.Driver"
                connectionURL="******************************************************************"
                userId="moego_developer_240310_eff7a0dc"
                password="G0MxI7NM_jX_f7Ky73vnrwej97xg1tly"/>

        <javaTypeResolver>
            <!-- https://mybatis.org/generator/configreference/javaTypeResolver.html#supported-properties -->
            <property name="forceBigDecimals" value="true"/>
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>

        <javaModelGenerator
                targetPackage="example.entity.postgres"
                targetProject="moego-lib-common/integration-tests/dynamic-datasource/mybatis/src/main/java"/>

        <javaClientGenerator
                targetPackage="example.mapper.postgres"
                targetProject="moego-lib-common/integration-tests/dynamic-datasource/mybatis/src/main/java">
            <property name="rootInterface" value="org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper"/>
        </javaClientGenerator>

        <table tableName="booking_request">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="attr"
                            javaType="example.models.BookingRequestModel.Attr"
                            typeHandler="example.mapper.typehandler.BookingRequestAttrTypeHandler"/>
            <columnOverride column="is_prepaid" javaType="java.lang.Boolean">
                <property name="deprecated" value="true"/>
                <property name="deprecatedSince" value="2025-01-22"/>
                <property name="deprecatedForRemoval" value="false"/>
                <property name="deprecatedDescription" value="use payment_status instead"/>
            </columnOverride>
            <columnOverride column="payment_status" javaType="java.lang.Integer"/>
        </table>
    </context>

    <context id="MySQL">
        <property name="autoDelimitKeywords" value='true'/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"/>
        <plugin type="com.moego.lib.common.mybatisplugins.DisableGeneratedMapperMethodsPlugin"/>
        <plugin type="com.moego.lib.common.mybatisplugins.DynamicDataSourcePlugin"/>
        <plugin type="com.moego.lib.common.mybatisplugins.DeprecatedColumnsPlugin"/>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <jdbcConnection
                driverClass="com.mysql.cj.jdbc.Driver"
                connectionURL="***************************************************************"
                userId="moego_developer_240310_eff7a0dc"
                password="G0MxI7NM_jX_f7Ky73vnrwej97xg1tly"
        >
        </jdbcConnection>

        <javaTypeResolver>
            <!-- https://mybatis.org/generator/configreference/javaTypeResolver.html#supported-properties -->
            <property name="forceBigDecimals" value="true"/>
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>

        <javaModelGenerator
                targetPackage="example.entity.mysql"
                targetProject="moego-lib-common/integration-tests/dynamic-datasource/mybatis/src/main/java"/>

        <javaClientGenerator
                targetPackage="example.mapper.mysql"
                targetProject="moego-lib-common/integration-tests/dynamic-datasource/mybatis/src/main/java">
            <property name="rootInterface" value="org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper"/>
        </javaClientGenerator>

        <table tableName="moe_grooming_appointment">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
            <columnOverride column="status"
                            javaType="example.models.AppointmentModel.Status"
                            typeHandler="example.mapper.typehandler.AppointmentStatusTypeHandler"/>
        </table>
        <table tableName="moe_grooming_package_service">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
            <columnOverride column="services"
                            javaType="java.util.List&lt;example.entity.po.PackageService&gt;"
                            typeHandler="example.mapper.typehandler.PackageServiceTypeHandler"/>
            <ignoreColumn column="service_id"/>
            <ignoreColumn column="service_unit_price"/>
        </table>
    </context>
</generatorConfiguration>
