package com.moego.lib.messaging.pulsar;

import static com.moego.lib.common.util.GreyUtil.GERY_NAME;

import com.moego.lib.common.grey.VersionContext;
import com.moego.lib.common.thread.ThreadContextHolder;
import com.moego.lib.common.util.GreyUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.messaging.MessagingOperations;
import com.moego.lib.messaging.TopicDecider;
import java.io.Closeable;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import org.apache.pulsar.client.api.MessageId;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.ProducerAccessMode;
import org.apache.pulsar.client.api.PulsarClient;
import org.apache.pulsar.client.api.PulsarClientException;
import org.apache.pulsar.client.api.Schema;
import org.apache.pulsar.client.api.TypedMessageBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.util.function.SingletonSupplier;

/**
 * Pulsar implementation for {@link MessagingOperations}.
 *
 * <AUTHOR>
 * @since 2022/11/30
 */
public class PulsarMessagingOperations implements MessagingOperations, Closeable {

    private static final Logger log = LoggerFactory.getLogger(PulsarMessagingOperations.class);

    private static final SingletonSupplier<String> branch = SingletonSupplier.of(GreyUtil::getCurrentBranch);

    private final PulsarClient pulsarClient;
    private final TopicDecider topicDecider;
    private final ProducerCache producerCache;

    public PulsarMessagingOperations(PulsarClient pulsarClient, TopicDecider topicDecider) {
        this.pulsarClient = pulsarClient;
        this.topicDecider = topicDecider;
        this.producerCache = new ProducerCache();
    }

    @Override
    public void send(String topic, Object message) {
        send(topic, message, Map.of());
    }

    @Override
    public void send(String topic, Object message, Map<String, String> headers) {
        String body = JsonUtil.toJson(message);
        try {
            MessageId id = getMessageBuilder(topic, headers).value(body).send();
            if (log.isDebugEnabled()) {
                log.debug("Message sent to topic: {}, message id: {}, body: {}", topic, id, body);
            }
        } catch (PulsarClientException e) {
            throw new RuntimeException("Failed to send message to topic: " + topic, e);
        }
    }

    @Override
    public CompletableFuture<?> asyncSend(String topic, Object message) {
        return asyncSend(topic, message, Map.of());
    }

    @Override
    public CompletableFuture<?> asyncSend(String topic, Object message, Map<String, String> headers) {
        String body = JsonUtil.toJson(message);
        return getMessageBuilder(topic, headers).value(body).sendAsync().thenApply(id -> {
            if (log.isDebugEnabled()) {
                log.debug("Message async sent to topic: {}, message id: {}, body: {}", topic, id, body);
            }
            return id;
        });
    }

    @Override
    public void sendDelayed(String topic, Object message, long delay) {
        sendDelayed(topic, message, delay, Map.of());
    }

    @Override
    public void sendDelayed(String topic, Object message, long delay, Map<String, String> headers) {
        String body = JsonUtil.toJson(message);
        try {
            MessageId id = getMessageBuilder(topic, headers)
                    .value(body)
                    .deliverAfter(delay, TimeUnit.MILLISECONDS)
                    .send();
            if (log.isDebugEnabled()) {
                log.debug(
                        "Message sent delayed to topic: {}, message id: {}, delay: {}, body: {}",
                        topic,
                        id,
                        delay,
                        body);
            }
        } catch (PulsarClientException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public CompletableFuture<?> asyncSendDelayed(String topic, Object message, long delay) {
        return asyncSendDelayed(topic, message, delay, Map.of());
    }

    @Override
    public CompletableFuture<?> asyncSendDelayed(
            String topic, Object message, long delay, Map<String, String> headers) {
        String body = JsonUtil.toJson(message);
        return getMessageBuilder(topic, headers)
                .value(body)
                .deliverAfter(delay, TimeUnit.MILLISECONDS)
                .sendAsync()
                .thenApply(id -> {
                    if (log.isDebugEnabled()) {
                        log.debug(
                                "Message async sent delayed to topic: {}, message id: {}, delay: {}, body: {}",
                                topic,
                                id,
                                delay,
                                body);
                    }
                    return id;
                });
    }

    @Override
    public void close() throws IOException {
        pulsarClient.close();
    }

    private TypedMessageBuilder<String> getMessageBuilder(String topic, Map<String, String> headers) {
        var builder = getOrCreateProducer(determineRealTopic(topic)).newMessage();
        headers.forEach(builder::property);
        if (!headers.containsKey(GERY_NAME)) {
            var greyName = getGreyName();
            if (StringUtils.hasText(greyName)) {
                builder.property(GERY_NAME, greyName);
            }
        }
        return builder;
    }

    private String getGreyName() {

        // 优先从 ThreadContext 中获取 grey name，如果没有则使用当前 branch

        // NOTE: 这里会出现被意外消费的情况，比如 greyName a 只配置了灰度服务 grooming -> feature-a, 但是消费端 svc-activity-log 有分支 feature-a
        // 这时候 grooming(feature-a) 发送的消息会被 svc-activity-log(feature-a) 消费，因为分支相同。
        // 这里如果要做的完备需要判断 greyName 是在 grey-gateway 手动配置还是自动生成，需要多添加一个 header。
        // 考虑到这种 case 会比较少并且在测试环境，允许这种情况出现。

        return Optional.ofNullable(ThreadContextHolder.getContext(VersionContext.class))
                .map(VersionContext::getHeaders)
                .map(h -> h.get(GERY_NAME))
                .filter(StringUtils::hasText)
                .orElseGet(branch);
    }

    private String determineRealTopic(String topic) {
        return topicDecider.determine(topic);
    }

    private Producer<String> getOrCreateProducer(String topic) {
        return producerCache.getOrSupply(topic, () -> newProducer(topic));
    }

    private Producer<String> newProducer(String topic) {
        // Pulsar enable batching by default, which is not suitable for our use case,
        // if we shut down the consumer during the batch messages are not fully acked,
        // the acked messages belong to the batch will be re-consumed !
        // So we disable batching here.
        // see https://pulsar.apache.org/docs/next/concepts-messaging/#batching
        try {
            return pulsarClient
                    .newProducer(Schema.STRING)
                    .topic(topic)
                    .accessMode(ProducerAccessMode.Shared)
                    .enableBatching(false)
                    .create();
        } catch (PulsarClientException e) {
            throw new RuntimeException(e);
        }
    }
}
