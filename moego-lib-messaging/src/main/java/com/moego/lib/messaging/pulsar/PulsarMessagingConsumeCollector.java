package com.moego.lib.messaging.pulsar;

import static com.moego.lib.messaging.pulsar.PulsarUtil.consumer;

import com.moego.lib.messaging.EventListener;
import com.moego.lib.messaging.GracefulShutter;
import com.moego.lib.messaging.MessagingConsume;
import com.moego.lib.messaging.MessagingConsumeCollector;
import com.moego.lib.messaging.SubscribeType;
import com.moego.lib.messaging.TopicDecider;
import com.moego.lib.messaging.autoconfigure.MessagingProperties;
import com.moego.lib.messaging.util.ClassUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.client.api.PulsarClient;
import org.apache.pulsar.client.api.SubscriptionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.env.Environment;
import org.springframework.util.Assert;

/**
 * Pulsar implementation of {@link MessagingConsumeCollector}.
 *
 * <p> All of the subscription types are {@link SubscriptionType#Shared}, otherwise, delayed messages are not supported.
 *
 * <p> Different {@link SubscribeType} implementation:
 * <p> {@link SubscribeType#ONCE_EACH_SERVICE}: Instances of one service use same subscription name. Format: {@code <topic>-<app>}
 * <p> {@link SubscribeType#BROADCAST}: Instances of one service use different subscription names. Format: {@code <topic>-<app>-<timestamp>}
 * <p> {@link SubscribeType#ONCE}: Instances of all services use same subscription name. Format: {@code <topic>}
 *
 * <AUTHOR>
 * @see org.apache.pulsar.client.api.TypedMessageBuilder#deliverAfter(long, TimeUnit)
 */
public class PulsarMessagingConsumeCollector implements MessagingConsumeCollector, EnvironmentAware {

    private static final Logger log = LoggerFactory.getLogger(PulsarMessagingConsumeCollector.class);

    private String applicationName;

    private final List<EventListener<Object>> listeners;
    private final PulsarClient pulsarClient;
    private final TopicDecider topicDecider;
    private final ObjectProvider<GracefulShutter> gracefulShutter;
    private final MessagingProperties messagingProperties;

    public PulsarMessagingConsumeCollector(
            List<EventListener<Object>> listeners,
            PulsarClient pulsarClient,
            TopicDecider topicDecider,
            ObjectProvider<GracefulShutter> gracefulShutter,
            MessagingProperties messagingProperties) {
        this.listeners = listeners;
        this.pulsarClient = pulsarClient;
        this.topicDecider = topicDecider;
        this.gracefulShutter = gracefulShutter;
        this.messagingProperties = messagingProperties;
    }

    @Override
    public List<MessagingConsume> collect() {
        Map<EventListener<Object>, PulsarConsumeModel> listenerToModel = getListenerModelMap();

        checkDuplicateTopics(listenerToModel);

        return initConsumes(listenerToModel);
    }

    private List<MessagingConsume> initConsumes(Map<EventListener<Object>, PulsarConsumeModel> listenerToModel) {
        List<MessagingConsume> consumes = new ArrayList<>();
        for (var entry : listenerToModel.entrySet()) {
            EventListener<Object> listener = entry.getKey();
            PulsarConsumeModel model = listenerToModel.get(listener);
            consumes.addAll(messagingConsumes(listener, model));
            log.info(
                    "[Messaging] Listener '{}' start listening event '{}', topics: {}, threadCount: {}, subscribeType: {}",
                    listener.getClass().getSimpleName(),
                    ClassUtil.getEventTypeForListener(listener).getSimpleName(),
                    model.topics(),
                    model.threadCount(),
                    model.subscribeType());
        }
        return consumes;
    }

    private static void checkDuplicateTopics(Map<EventListener<Object>, PulsarConsumeModel> listenerToModel) {
        Map<String, EventListener<Object>> topicsMap = new HashMap<>();
        for (var entry : listenerToModel.entrySet()) {
            EventListener<Object> listener = entry.getKey();
            PulsarConsumeModel model = entry.getValue();
            String topicsStr = model.topics().toString();
            if (topicsMap.containsKey(topicsStr)) {
                throw new IllegalStateException(String.format(
                        "Duplicate topics found in listeners [%s, %s]. You need to refactor your code !",
                        topicsMap.get(topicsStr).getClass().getSimpleName(),
                        listener.getClass().getSimpleName()));
            }
            topicsMap.put(topicsStr, listener);
        }
    }

    private Map<EventListener<Object>, PulsarConsumeModel> getListenerModelMap() {
        return listeners.stream().collect(Collectors.toMap(Function.identity(), listener -> {
            com.moego.lib.messaging.Consumer anno =
                    AnnotationUtils.findAnnotation(listener.getClass(), com.moego.lib.messaging.Consumer.class);
            Assert.notNull(
                    anno,
                    "Annotation @Consumer is required for listener: "
                            + listener.getClass().getName());
            return PulsarConsumerModelUtil.merge(anno, messagingProperties, applicationName, topicDecider);
        }));
    }

    private List<PulsarMessagingConsume> messagingConsumes(EventListener<Object> listener, PulsarConsumeModel model) {
        Consumer<String> consumer = consumer(pulsarClient, model);
        int threadCount = model.threadCount();
        List<PulsarMessagingConsume> result = new ArrayList<>(threadCount);
        for (int i = 0; i < threadCount; i++) {
            result.add(
                    new PulsarMessagingConsume(consumer, listener, gracefulShutter, messagingProperties.isAutoAck()));
        }
        return result;
    }

    @Override
    public void setEnvironment(Environment environment) {
        // we use spring.application.name as application name
        // if not set, we can still use main class name as application name
        this.applicationName = environment.getProperty("spring.application.name", getMainClassName());
    }

    private static String getMainClassName() {
        StackTraceElement[] trace = Thread.currentThread().getStackTrace();
        if (trace.length > 0) {
            return trace[trace.length - 1].getClassName();
        }
        throw new IllegalStateException("Can't get main class name");
    }
}
