package com.moego.lib.messaging;

import java.util.List;

/**
 * {@link MessagingConsumeCollector} use to collect all {@link MessagingConsume} instances.
 *
 * <p> We need to separate the init and start phase of {@link MessagingConsume}, because we can't consume any messages until the application is ready, which means pass the health check.
 *
 * <AUTHOR>
 * @since 2022/11/30
 */
public interface MessagingConsumeCollector {
    /**
     * Collect all {@link MessagingConsume} instances.
     *
     * @return {@link MessagingConsume} instances
     */
    List<MessagingConsume> collect();
}
