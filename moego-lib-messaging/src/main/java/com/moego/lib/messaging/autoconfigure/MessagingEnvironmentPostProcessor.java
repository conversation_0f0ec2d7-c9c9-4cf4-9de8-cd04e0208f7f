package com.moego.lib.messaging.autoconfigure;

import java.util.Map;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;

/**
 * <AUTHOR>
 */
public class MessagingEnvironmentPostProcessor implements EnvironmentPostProcessor {

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        // Pulsar client prints too much logs......
        MapPropertySource ps =
                new MapPropertySource("moego-lib-messaging", Map.of("logging.level.org.apache.pulsar.client", "WARN"));
        environment.getPropertySources().addLast(ps);
    }
}
