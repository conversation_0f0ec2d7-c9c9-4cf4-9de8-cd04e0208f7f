package com.moego.lib.messaging.it;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.messaging.Consumer;
import com.moego.lib.messaging.EventListener;
import com.moego.lib.messaging.MessagingOperations;
import com.moego.lib.messaging.Msg;
import com.moego.lib.messaging.SubscribeType;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import lombok.Data;
import lombok.experimental.Accessors;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Integration tests for messaging with Pulsar.
 *
 * <p> Steps to run this test:
 *
 * <p> 1. Run Pulsar standalone:
 * <p> {@code docker run -it --rm -p 6650:6650 -p 8080:8080 apachepulsar/pulsar:2.11.0 bin/pulsar standalone}
 *
 * <p> 2. Using admin create tenants and namespaces: (can't success with docker on M1, wired :-)
 * <p> {@code curl -OL https://archive.apache.org/dist/pulsar/pulsar-2.11.0/apache-pulsar-2.11.0-bin.tar.gz}
 * <p> {@code tar -zxvf apache-pulsar-2.11.0-bin.tar.gz && rm -rf apache-pulsar-2.11.0-bin.tar.gz && cd apache-pulsar-2.11.0}
 * <p> {@code bin/pulsar-admin tenants create test2 && bin/pulsar-admin tenants create staging && bin/pulsar-admin tenants create prod}
 * <p> {@code bin/pulsar-admin namespaces create test2/service && bin/pulsar-admin namespaces create staging/service && bin/pulsar-admin namespaces create prod/service}
 *
 * <p> 3. Run the test:
 * <p> {@code ./gradlew :moego-lib-common:test --tests MessagingWithPulsarIntegrationTests -Dit.pulsar=true}
 *
 * <AUTHOR>
 */
@EnabledIfSystemProperty(named = "it.pulsar", matches = "true")
class MessagingWithPulsarIntegrationTests {

    private static final String address = "localhost:6650";

    static AtomicReference<MyEvent> myEvent = new AtomicReference<>();
    static AtomicInteger eventCounter = new AtomicInteger(0);
    static AtomicInteger disposableEventCounter = new AtomicInteger(0);
    static AtomicInteger broadcastEventCounter = new AtomicInteger(0);
    static AtomicInteger delayedMessageCounter = new AtomicInteger(0);

    @Test
    void testEvent() throws InterruptedException {
        ConfigurableApplicationContext svc_1_1 = new SpringApplicationBuilder(EventConfig.class)
                .web(WebApplicationType.NONE)
                .properties("spring.application.name=messaging-pulsar-it-1")
                .properties("moego.grpc.enabled=false")
                .properties("moego.http.enabled=false")
                .properties("moego.messaging.pulsar.service-url=" + address)
                .properties("moego.messaging.pulsar.tenant=test2")
                .run();

        ConfigurableApplicationContext svc_1_2 = new SpringApplicationBuilder(EventConfig.class)
                .web(WebApplicationType.NONE)
                .properties("spring.application.name=messaging-pulsar-it-1")
                .properties("moego.grpc.enabled=false")
                .properties("moego.http.enabled=false")
                .properties("moego.messaging.pulsar.service-url=" + address)
                .properties("moego.messaging.pulsar.tenant=test2")
                .run();

        ConfigurableApplicationContext svc_2_1 = new SpringApplicationBuilder(EventConfig.class)
                .web(WebApplicationType.NONE)
                .properties("spring.application.name=messaging-pulsar-it-2")
                .properties("moego.grpc.enabled=false")
                .properties("moego.http.enabled=false")
                .properties("moego.messaging.pulsar.service-url=" + address)
                .properties("moego.messaging.pulsar.tenant=test2")
                .run();

        ConfigurableApplicationContext svc_2_2 = new SpringApplicationBuilder(EventConfig.class)
                .web(WebApplicationType.NONE)
                .properties("spring.application.name=messaging-pulsar-it-2")
                .properties("moego.grpc.enabled=false")
                .properties("moego.http.enabled=false")
                .properties("moego.messaging.pulsar.service-url=" + address)
                .properties("moego.messaging.pulsar.tenant=test2")
                .run();

        MessagingOperations messagingOperations = svc_1_1.getBean(MessagingOperations.class);

        // 1. test normal event
        messagingOperations.send(MyEvent.TOPIC, new MyEvent().setMessage("Hello, world!"));
        messagingOperations.send(MyEvent.TOPIC, new MyEvent().setMessage("Hello, world!"));

        Thread.sleep(1500);

        assertThat(myEvent.get()).isNotNull();
        assertThat(myEvent.get().getMessage()).isEqualTo("Hello, world!");
        assertThat(eventCounter.get()).isEqualTo(4); // 2 services * 2 events

        // 2. test disposable event
        messagingOperations.send(MyDisposableEvent.TOPIC, new MyDisposableEvent().setMessage("Hello, world!"));
        messagingOperations.send(MyDisposableEvent.TOPIC, new MyDisposableEvent().setMessage("Hello, world!"));

        Thread.sleep(1500);

        assertThat(disposableEventCounter.get()).isEqualTo(2);

        // 3. test broadcast event
        messagingOperations.send(MyBroadcastEvent.TOPIC, new MyBroadcastEvent().setMessage("Hello, world!"));
        messagingOperations.send(MyBroadcastEvent.TOPIC, new MyBroadcastEvent().setMessage("Hello, world!"));

        Thread.sleep(1500);

        assertThat(broadcastEventCounter.get()).isEqualTo(8); // 4 instances * 2 events

        // 4. test event with delay
        messagingOperations.sendDelayed(
                MyDelayedEvent.TOPIC, new MyDelayedEvent().setTimestamp(System.currentTimeMillis()), 2000L);

        Thread.sleep(1500);

        // haven't been consumed yet
        assertThat(delayedMessageCounter.get()).isEqualTo(0);

        Thread.sleep(1500);

        // should have been consumed
        assertThat(delayedMessageCounter.get()).isNotEqualTo(0);
        assertThat(delayedMessageCounter.get()).isEqualTo(2); // 2 services

        svc_2_2.close();
        svc_2_1.close();
        svc_1_2.close();
        svc_1_1.close();
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @Import({
        EventConfig.MyEventListener.class,
        EventConfig.MyDisposableEventListener.class,
        EventConfig.MyBroadcastEventListener.class,
        EventConfig.MyDelayedEventListener.class,
    })
    static class EventConfig {

        @Consumer(topics = MyEvent.TOPIC, subscribeType = SubscribeType.ONCE_EACH_SERVICE)
        static class MyEventListener implements EventListener<MyEvent> {

            @Override
            public void onEvent(Msg<MyEvent> message) {
                myEvent.set(message.getBody());
                eventCounter.incrementAndGet();
                message.ack();
            }
        }

        @Consumer(topics = MyDisposableEvent.TOPIC, subscribeType = SubscribeType.ONCE)
        static class MyDisposableEventListener implements EventListener<MyDisposableEvent> {

            @Override
            public void onEvent(Msg<MyDisposableEvent> message) {
                disposableEventCounter.incrementAndGet();
                message.ack();
            }
        }

        @Consumer(topics = MyBroadcastEvent.TOPIC, subscribeType = SubscribeType.BROADCAST)
        static class MyBroadcastEventListener implements EventListener<MyBroadcastEvent> {

            @Override
            public void onEvent(Msg<MyBroadcastEvent> message) {
                broadcastEventCounter.incrementAndGet();
                message.ack();
            }
        }

        @Consumer(topics = MyDelayedEvent.TOPIC, subscribeType = SubscribeType.ONCE_EACH_SERVICE)
        static class MyDelayedEventListener implements EventListener<MyDelayedEvent> {

            @Override
            public void onEvent(Msg<MyDelayedEvent> message) {
                delayedMessageCounter.incrementAndGet();
                message.ack();
            }
        }
    }

    @Data
    @Accessors(chain = true)
    static class MyEvent {

        public static final String TOPIC = "service:MyEvent";

        private String message;
    }

    @Data
    @Accessors(chain = true)
    static class MyDisposableEvent {

        public static final String TOPIC = "service:MyDisposableEvent";

        private String message;
    }

    @Data
    @Accessors(chain = true)
    static class MyBroadcastEvent {

        public static final String TOPIC = "service:MyBroadcastEvent";

        private String message;
    }

    @Data
    @Accessors(chain = true)
    static class MyDelayedEvent {

        public static final String TOPIC = "service:MyDelayedEvent";

        private long timestamp;
    }
}
