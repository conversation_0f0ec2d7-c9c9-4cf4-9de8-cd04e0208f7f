package com.moego.lib.messaging.it;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.messaging.Consumer;
import com.moego.lib.messaging.EventListener;
import com.moego.lib.messaging.MessagingOperations;
import com.moego.lib.messaging.Msg;
import com.moego.lib.messaging.SubscribeType;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Data;
import lombok.experimental.Accessors;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Integration tests for messaging with Pulsar.
 *
 * <p> Steps to run this test:
 *
 * <p> 1. Run Pulsar standalone:
 * <p> {@code docker run -it --rm -p 6650:6650 -p 8080:8080 apachepulsar/pulsar:2.11.0 bin/pulsar standalone}
 *
 * <p> 2. Using admin create tenants and namespaces: (can't success with docker on M1, wired :-)
 * <p> {@code curl -OL https://archive.apache.org/dist/pulsar/pulsar-2.11.0/apache-pulsar-2.11.0-bin.tar.gz}
 * <p> {@code tar -zxvf apache-pulsar-2.11.0-bin.tar.gz && rm -rf apache-pulsar-2.11.0-bin.tar.gz && cd apache-pulsar-2.11.0}
 * <p> {@code bin/pulsar-admin tenants create test2 && bin/pulsar-admin tenants create staging && bin/pulsar-admin tenants create prod}
 * <p> {@code bin/pulsar-admin namespaces create test2/service && bin/pulsar-admin namespaces create staging/service && bin/pulsar-admin namespaces create prod/service}
 *
 * <p> 3. Run the test:
 * <p> {@code ./gradlew :moego-lib-common:test --tests MessagingWithPulsarGroupIntegrationTests -Dit.pulsar=true}
 *
 * <AUTHOR>
 */
@EnabledIfSystemProperty(named = "it.pulsar", matches = "true")
class MessagingWithPulsarGroupIntegrationTests {

    private static final String address = "localhost:6650";
    private static final String group = "feature-test";

    static AtomicInteger broadcastEventCounter = new AtomicInteger(0);

    @Test
    void shouldNotReceiveMsg_whenDifferentGroup() throws InterruptedException {
        ConfigurableApplicationContext svc_1_1 = new SpringApplicationBuilder(EventConfig.class)
                .web(WebApplicationType.NONE)
                .properties("spring.application.name=messaging-pulsar-it-1")
                .properties("moego.grpc.enabled=false")
                .properties("moego.http.enabled=false")
                .properties("moego.messaging.pulsar.service-url=" + address)
                .properties("moego.messaging.test-group=" + group)
                .properties("moego.messaging.pulsar.tenant=test2")
                .run();
        ConfigurableApplicationContext svc_1_2 = new SpringApplicationBuilder(EventConfig.class)
                .web(WebApplicationType.NONE)
                .properties("spring.application.name=messaging-pulsar-it-1")
                .properties("moego.grpc.enabled=false")
                .properties("moego.http.enabled=false")
                .properties("moego.messaging.pulsar.service-url=" + address)
                .properties("moego.messaging.test-group=" + group)
                .properties("moego.messaging.pulsar.tenant=test2")
                .run();
        ConfigurableApplicationContext svc_2_1 = new SpringApplicationBuilder(EventConfig.class)
                .web(WebApplicationType.NONE)
                .properties("spring.application.name=messaging-pulsar-it-2")
                .properties("moego.grpc.enabled=false")
                .properties("moego.http.enabled=false")
                .properties("moego.messaging.pulsar.service-url=" + address)
                .properties("moego.messaging.pulsar.tenant=test2")
                .run();

        MessagingOperations messagingOperations = svc_1_1.getBean(MessagingOperations.class);
        messagingOperations.send(GroupTestEvent.TOPIC, new GroupTestEvent().setMessage("hello"));

        Thread.sleep(1500);
        assertThat(broadcastEventCounter.get()).isEqualTo(2);

        svc_2_1.close();
        svc_1_2.close();
        svc_1_1.close();
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @Import(EventConfig.GroupTestEventListener.class)
    static class EventConfig {

        @Consumer(topics = GroupTestEvent.TOPIC, subscribeType = SubscribeType.BROADCAST)
        static class GroupTestEventListener implements EventListener<GroupTestEvent> {

            @Override
            public void onEvent(Msg<GroupTestEvent> message) {
                broadcastEventCounter.incrementAndGet();
                message.ack();
            }
        }
    }

    @Data
    @Accessors(chain = true)
    static class GroupTestEvent {

        public static final String TOPIC = "service:GroupTestEvent";

        private String message;
    }
}
