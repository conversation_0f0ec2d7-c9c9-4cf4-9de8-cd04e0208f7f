package com.moego.lib.encryption;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.moego.lib.utils.CoreUtils;
import com.moego.lib.utils.DateTimeUtils;
import com.moego.lib.utils.model.Pair;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

public class JsonWebTokenTest {

    @Test
    public void testJWT() {
        System.out.println("JWT supported algorithms: " + JsonWebToken.listAlgorithms());

        var key = JsonWebToken.generateKey("HS256");
        System.out.println("key: " + JsonWebToken.toString(key));

        long iat = DateTimeUtils.now() / 1000 * 1000;
        long nbf = iat;
        long exp = iat + (60 * 60 * 1000);
        var token = JsonWebToken.encode(key, "frank", "login", "customer", iat, nbf, exp, "0001", "Moe", "Go");
        System.out.println("token: " + token);

        var jwt = JsonWebToken.decode(key, token);
        System.out.println("jwt: " + jwt);
        assertEquals("HS256", jwt.headers().algorithm());
        assertEquals("JWT", jwt.headers().type());
        assertNull(jwt.headers().kid());
        assertEquals("frank", jwt.claims().issuer());
        assertEquals("login", jwt.claims().subject());

        var t = jwt.claims().expiration();
        assertEquals(exp, t);
        var expiration = jwt.claims().get(JsonWebToken.JWK_EXPIRATION);
        assertTrue(expiration instanceof Number);
        assertEquals(exp / 1000, ((Number) expiration).longValue());

        var r1 = JsonWebToken.verify(key, token, "frank", "login", "customer", iat, nbf, exp, "0001", "Moe", "Go");
        assertEquals(JsonWebToken.VerifyResult.OK, r1.key());
        var r2 = JsonWebToken.verify(key, token, "frank", "login", "customer", iat, nbf, exp + 7, "0001", "Moe", "Go");
        assertEquals(JsonWebToken.VerifyResult.OK, r2.key());
        var r3 = JsonWebToken.verify(key, token, "frank", "login", "customer", iat, nbf, exp, "0001");
        assertEquals(JsonWebToken.VerifyResult.OK, r3.key());
        var r4 = JsonWebToken.verify(key, token, "frank", "login", "customer", iat, nbf, exp, null);
        assertEquals(JsonWebToken.VerifyResult.OK, r4.key());
        var r5 = JsonWebToken.verify(key, token, "frank", "login", "customer", iat, null, exp, null);
        assertEquals(JsonWebToken.VerifyResult.OK, r5.key());
        var r6 = JsonWebToken.verify(key, token, "frank", "login", null, null, null, exp, null);
        assertEquals(JsonWebToken.VerifyResult.OK, r6.key());
        var r7 = JsonWebToken.verify(key, token, null, null, null, null, null, exp, null);
        assertEquals(JsonWebToken.VerifyResult.OK, r7.key());
        var r8 = JsonWebToken.verify(key, token, null, null, null, null, null, null, null);
        assertEquals(JsonWebToken.VerifyResult.OK, r8.key());
        var r9 = JsonWebToken.verify(key, token, null, null, null, null, null, null, null, "Moe", "Go");
        assertEquals(JsonWebToken.VerifyResult.OK, r9.key());

        var r10 = JsonWebToken.verify(key, token, null, null, null, null, null, null, null, "Moe", "xx");
        assertEquals(Pair.of(JsonWebToken.VerifyResult.INVALID_CLAIM, "Moe"), r10);
        var r11 = JsonWebToken.verify(key, token, "frank", "login", "customer", iat, nbf, exp - 10, "0001");
        assertEquals(Pair.of(JsonWebToken.VerifyResult.INVALID_CLAIM, JsonWebToken.JWK_EXPIRATION), r11);
        var r12 = JsonWebToken.verify(key, token, "frank", "login", "customer", iat, nbf - 10, exp, "0001");
        assertEquals(Pair.of(JsonWebToken.VerifyResult.INVALID_CLAIM, JsonWebToken.JWK_NOT_BEFORE), r12);
        var r13 = JsonWebToken.verify(key, token, "frank", "login", "customer", iat - 10, nbf, exp, "0001");
        assertEquals(Pair.of(JsonWebToken.VerifyResult.INVALID_CLAIM, JsonWebToken.JWK_ISSUED_AT), r13);
        var r14 = JsonWebToken.verify(key, token, "frank", "login", "customer", iat, nbf, null, "0002");
        assertEquals(Pair.of(JsonWebToken.VerifyResult.INVALID_CLAIM, JsonWebToken.JWK_JWT_ID), r14);
        var r15 = JsonWebToken.verify(key, token, "frank", "login", "user", null, null, null, null);
        assertEquals(Pair.of(JsonWebToken.VerifyResult.INVALID_CLAIM, JsonWebToken.JWK_AUDIENCE), r15);
        var r16 = JsonWebToken.verify(key, token, "frank", "logout", null, null, null, null, null);
        assertEquals(Pair.of(JsonWebToken.VerifyResult.INVALID_CLAIM, JsonWebToken.JWK_SUBJECT), r16);
        var r17 = JsonWebToken.verify(key, token, "emma", "login", null, null, null, null, null);
        assertEquals(Pair.of(JsonWebToken.VerifyResult.INVALID_CLAIM, JsonWebToken.JWK_ISSUER), r17);
        var r18 = JsonWebToken.verify(key, token, null, null, null, null, null, null, null, "Hello", "MoeGo");
        assertEquals(Pair.of(JsonWebToken.VerifyResult.MISSING_CLAIM, "Hello"), r18);
        var r19 = JsonWebToken.verify(key, token.substring(0, token.length() - 1), null);
        assertEquals(JsonWebToken.VerifyResult.INVALID_SIGN, r19.key());
        var r20 = JsonWebToken.verify(JsonWebToken.generateKey("HS256"), token, null);
        assertEquals(JsonWebToken.VerifyResult.INVALID_SIGN, r20.key());
        var r21 = JsonWebToken.verify(JsonWebToken.generateKey("HS512"), token, null);
        assertEquals(JsonWebToken.VerifyResult.INVALID_SIGN, r21.key());
        var r22 = JsonWebToken.verify(key, token.substring(1), null);
        assertEquals(JsonWebToken.VerifyResult.INVALID_TOKEN, r22.key());

        System.out.println("--> test JWT encrypt ok !");
    }

    @Test
    public void testKey() throws NoSuchAlgorithmException, InvalidKeySpecException {
        var keyHS256 = JsonWebToken.generateKey("HS256");
        var keyHS256Text = JsonWebToken.toString(keyHS256);
        var HS256Key = JsonWebToken.buildKey("HS256", keyHS256Text);
        assertVerify("HS256", keyHS256, keyHS256);
        assertVerify("HS256", keyHS256, HS256Key);
        assertVerify("HS256", HS256Key, HS256Key);

        var keyHS384 = JsonWebToken.generateKey("HS384");
        var keyHS384Text = JsonWebToken.toString(keyHS384);
        var HS384Key = JsonWebToken.buildKey("HS384", keyHS384Text);
        assertVerify("HS384", keyHS384, keyHS384);
        assertVerify("HS384", keyHS384, HS384Key);
        assertVerify("HS384", HS384Key, HS384Key);

        var keyHS512 = JsonWebToken.generateKey("HS512");
        var keyHS512Text = JsonWebToken.toString(keyHS512);
        var HS512Key = JsonWebToken.buildKey("HS512", keyHS512Text);
        assertVerify("HS512", keyHS512, keyHS512);
        assertVerify("HS512", keyHS512, HS512Key);
        assertVerify("HS512", HS512Key, HS512Key);

        var pairRS256 = JsonWebToken.generateKeyPair("RS256");
        var pairRS256Text = JsonWebToken.toString(pairRS256);
        var RS256Pair = JsonWebToken.buildKeyPair("RS256", pairRS256Text.key(), pairRS256Text.value());
        assertVerify("RS256", pairRS256.getPrivate(), pairRS256.getPublic());
        assertVerify("RS256", pairRS256.getPrivate(), RS256Pair.getPublic());
        assertVerify("RS256", RS256Pair.getPrivate(), RS256Pair.getPublic());

        var pairRS384 = JsonWebToken.generateKeyPair("RS384");
        var pairRS384Text = JsonWebToken.toString(pairRS384);
        var RS384Pair = JsonWebToken.buildKeyPair("RS384", pairRS384Text.key(), pairRS384Text.value());
        assertVerify("RS384", pairRS384.getPrivate(), pairRS384.getPublic());
        assertVerify("RS384", pairRS384.getPrivate(), RS384Pair.getPublic());
        assertVerify("RS384", RS384Pair.getPrivate(), RS384Pair.getPublic());

        var pairRS512 = JsonWebToken.generateKeyPair("RS512");
        var pairRS512Text = JsonWebToken.toString(pairRS512);
        var RS512Pair = JsonWebToken.buildKeyPair("RS512", pairRS512Text.key(), pairRS512Text.value());
        assertVerify("RS512", pairRS512.getPrivate(), pairRS512.getPublic());
        assertVerify("RS512", pairRS512.getPrivate(), RS512Pair.getPublic());
        assertVerify("RS512", RS512Pair.getPrivate(), RS512Pair.getPublic());

        var pairES256 = JsonWebToken.generateKeyPair("ES256");
        var pairES256Text = JsonWebToken.toString(pairES256);
        var ES256Pair = JsonWebToken.buildKeyPair("ES256", pairES256Text.key(), pairES256Text.value());
        assertVerify("ES256", pairES256.getPrivate(), pairES256.getPublic());
        assertVerify("ES256", pairES256.getPrivate(), ES256Pair.getPublic());
        assertVerify("ES256", ES256Pair.getPrivate(), ES256Pair.getPublic());

        var pairES384 = JsonWebToken.generateKeyPair("ES384");
        var pairES384Text = JsonWebToken.toString(pairES384);
        var ES384Pair = JsonWebToken.buildKeyPair("ES384", pairES384Text.key(), pairES384Text.value());
        assertVerify("ES384", pairES384.getPrivate(), pairES384.getPublic());
        assertVerify("ES384", pairES384.getPrivate(), ES384Pair.getPublic());
        assertVerify("ES384", ES384Pair.getPrivate(), ES384Pair.getPublic());

        var pairES512 = JsonWebToken.generateKeyPair("ES512");
        var pairES512Text = JsonWebToken.toString(pairES512);
        var ES512Pair = JsonWebToken.buildKeyPair("ES512", pairES512Text.key(), pairES512Text.value());
        assertVerify("ES512", pairES512.getPrivate(), pairES512.getPublic());
        assertVerify("ES512", pairES512.getPrivate(), ES512Pair.getPublic());
        assertVerify("ES512", ES512Pair.getPrivate(), ES512Pair.getPublic());

        var pairPS256 = JsonWebToken.generateKeyPair("PS256");
        var pairPS256Text = JsonWebToken.toString(pairPS256);
        var PS256Pair = JsonWebToken.buildKeyPair("PS256", pairPS256Text.key(), pairPS256Text.value());
        assertVerify("PS256", pairPS256.getPrivate(), pairPS256.getPublic());
        assertVerify("PS256", pairPS256.getPrivate(), PS256Pair.getPublic());
        assertVerify("PS256", PS256Pair.getPrivate(), PS256Pair.getPublic());

        var pairPS384 = JsonWebToken.generateKeyPair("PS384");
        var pairPS384Text = JsonWebToken.toString(pairPS384);
        var PS384Pair = JsonWebToken.buildKeyPair("PS384", pairPS384Text.key(), pairPS384Text.value());
        assertVerify("PS384", pairPS384.getPrivate(), pairPS384.getPublic());
        assertVerify("PS384", pairPS384.getPrivate(), PS384Pair.getPublic());
        assertVerify("PS384", PS384Pair.getPrivate(), PS384Pair.getPublic());

        var pairPS512 = JsonWebToken.generateKeyPair("PS512");
        var pairPS512Text = JsonWebToken.toString(pairPS512);
        var PS512Pair = JsonWebToken.buildKeyPair("PS512", pairPS512Text.key(), pairPS512Text.value());
        assertVerify("PS512", pairPS512.getPrivate(), pairPS512.getPublic());
        assertVerify("PS512", pairPS512.getPrivate(), PS512Pair.getPublic());
        assertVerify("PS512", PS512Pair.getPrivate(), PS512Pair.getPublic());

        System.out.println("--> test JWT verify ok !");
    }

    @Test
    public void testAlgorithms() {
        var keyHS256 = JsonWebToken.generateKey("HS256");
        System.out.println("HS256 key: " + JsonWebToken.toString(keyHS256));
        assertAlgorithm("HS256", keyHS256, keyHS256);

        var keyHS384 = JsonWebToken.generateKey("HS384");
        System.out.println("HS384 key: " + JsonWebToken.toString(keyHS384));
        assertAlgorithm("HS384", keyHS384, keyHS384);

        var keyHS512 = JsonWebToken.generateKey("HS512");
        System.out.println("HS512 key: " + JsonWebToken.toString(keyHS512));
        assertAlgorithm("HS512", keyHS512, keyHS512);

        var keyPairES256 = JsonWebToken.generateKeyPair("ES256");
        System.out.println("ES256 pubKey: " + JsonWebToken.toString(keyPairES256.getPublic()));
        System.out.println("ES256 priKey: " + JsonWebToken.toString(keyPairES256.getPrivate()));
        assertAlgorithm("ES256", keyPairES256.getPrivate(), keyPairES256.getPublic());

        var keyPairES384 = JsonWebToken.generateKeyPair("ES384");
        System.out.println("ES384 pubKey: " + JsonWebToken.toString(keyPairES384.getPublic()));
        System.out.println("ES384 priKey: " + JsonWebToken.toString(keyPairES384.getPrivate()));
        assertAlgorithm("ES384", keyPairES384.getPrivate(), keyPairES384.getPublic());

        var keyPairES512 = JsonWebToken.generateKeyPair("ES512");
        System.out.println("ES512 pubKey: " + JsonWebToken.toString(keyPairES512.getPublic()));
        System.out.println("ES512 priKey: " + JsonWebToken.toString(keyPairES512.getPrivate()));
        assertAlgorithm("ES512", keyPairES512.getPrivate(), keyPairES512.getPublic());

        var keyPairRS256 = JsonWebToken.generateKeyPair("RS256");
        System.out.println("RS256 pubKey: " + JsonWebToken.toString(keyPairRS256.getPublic()));
        System.out.println("RS256 priKey: " + JsonWebToken.toString(keyPairRS256.getPrivate()));
        assertAlgorithm("RS256", keyPairRS256.getPrivate(), keyPairRS256.getPublic());

        var keyPairRS384 = JsonWebToken.generateKeyPair("RS384");
        System.out.println("RS384 pubKey: " + JsonWebToken.toString(keyPairRS384.getPublic()));
        System.out.println("RS384 priKey: " + JsonWebToken.toString(keyPairRS384.getPrivate()));
        assertAlgorithm("RS384", keyPairRS384.getPrivate(), keyPairRS384.getPublic());

        var keyPairRS512 = JsonWebToken.generateKeyPair("RS512");
        System.out.println("RS512 pubKey: " + JsonWebToken.toString(keyPairRS512.getPublic()));
        System.out.println("RS512 priKey: " + JsonWebToken.toString(keyPairRS512.getPrivate()));
        assertAlgorithm("RS512", keyPairRS512.getPrivate(), keyPairRS512.getPublic());

        var keyPairPS256 = JsonWebToken.generateKeyPair("PS256");
        System.out.println("PS256 pubKey: " + JsonWebToken.toString(keyPairPS256.getPublic()));
        System.out.println("PS256 priKey: " + JsonWebToken.toString(keyPairPS256.getPrivate()));
        assertAlgorithm("PS256", keyPairPS256.getPrivate(), keyPairPS256.getPublic());

        var keyPairPS384 = JsonWebToken.generateKeyPair("PS384");
        System.out.println("PS384 pubKey: " + JsonWebToken.toString(keyPairPS384.getPublic()));
        System.out.println("PS384 priKey: " + JsonWebToken.toString(keyPairPS384.getPrivate()));
        assertAlgorithm("PS384", keyPairPS384.getPrivate(), keyPairPS384.getPublic());

        var keyPairPS512 = JsonWebToken.generateKeyPair("PS512");
        System.out.println("PS512 pubKey: " + JsonWebToken.toString(keyPairPS512.getPublic()));
        System.out.println("PS512 priKey: " + JsonWebToken.toString(keyPairPS512.getPrivate()));
        assertAlgorithm("PS512", keyPairPS512.getPrivate(), keyPairPS512.getPublic());

        System.out.println("--> test JWT algorithms ok !");
    }

    @Disabled
    @Test
    public void showBenchmarks() {
        int n = 1000;
        List<String> times = new ArrayList<>();

        var keyHS256 = JsonWebToken.generateKey("HS256");
        System.out.println("HS256 key: " + JsonWebToken.toString(keyHS256));
        times.add(benchmarks("HS256", keyHS256, keyHS256, n));

        var keyHS384 = JsonWebToken.generateKey("HS384");
        System.out.println("HS384 key: " + JsonWebToken.toString(keyHS384));
        times.add(benchmarks("HS384", keyHS384, keyHS384, n));

        var keyHS512 = JsonWebToken.generateKey("HS512");
        System.out.println("HS512 key: " + JsonWebToken.toString(keyHS512));
        times.add(benchmarks("HS512", keyHS512, keyHS512, n));

        var pairES256 = JsonWebToken.generateKeyPair("ES256");
        System.out.println("ES256 pubKey: " + JsonWebToken.toString(pairES256.getPublic()));
        System.out.println("ES256 priKey: " + JsonWebToken.toString(pairES256.getPrivate()));
        times.add(benchmarks("ES256", pairES256.getPrivate(), pairES256.getPublic(), n));

        var pairES384 = JsonWebToken.generateKeyPair("ES384");
        System.out.println("ES384 pubKey: " + JsonWebToken.toString(pairES384.getPublic()));
        System.out.println("ES384 priKey: " + JsonWebToken.toString(pairES384.getPrivate()));
        times.add(benchmarks("ES384", pairES384.getPrivate(), pairES384.getPublic(), n));

        var pairES512 = JsonWebToken.generateKeyPair("ES512");
        System.out.println("ES512 pubKey: " + JsonWebToken.toString(pairES512.getPublic()));
        System.out.println("ES512 priKey: " + JsonWebToken.toString(pairES512.getPrivate()));
        times.add(benchmarks("ES512", pairES512.getPrivate(), pairES512.getPublic(), n));

        var pairRS256 = JsonWebToken.generateKeyPair("RS256");
        System.out.println("RS256 pubKey: " + JsonWebToken.toString(pairRS256.getPublic()));
        System.out.println("RS256 priKey: " + JsonWebToken.toString(pairRS256.getPrivate()));
        times.add(benchmarks("RS256", pairRS256.getPrivate(), pairRS256.getPublic(), n));

        var pairRS384 = JsonWebToken.generateKeyPair("RS384");
        System.out.println("ES256 pubKey: " + JsonWebToken.toString(pairRS384.getPublic()));
        System.out.println("ES256 priKey: " + JsonWebToken.toString(pairRS384.getPrivate()));
        times.add(benchmarks("RS384", pairRS384.getPrivate(), pairRS384.getPublic(), n));

        var pairRS512 = JsonWebToken.generateKeyPair("RS512");
        System.out.println("RS512 pubKey: " + JsonWebToken.toString(pairRS512.getPublic()));
        System.out.println("RS512 priKey: " + JsonWebToken.toString(pairRS512.getPrivate()));
        times.add(benchmarks("RS512", pairRS512.getPrivate(), pairRS512.getPublic(), n));

        var pairPS256 = JsonWebToken.generateKeyPair("PS256");
        System.out.println("PS256 pubKey: " + JsonWebToken.toString(pairPS256.getPublic()));
        System.out.println("PS256 priKey: " + JsonWebToken.toString(pairPS256.getPrivate()));
        times.add(benchmarks("PS256", pairPS256.getPrivate(), pairPS256.getPublic(), n));

        var pairPS384 = JsonWebToken.generateKeyPair("PS384");
        System.out.println("PS384 pubKey: " + JsonWebToken.toString(pairPS384.getPublic()));
        System.out.println("PS384 priKey: " + JsonWebToken.toString(pairPS384.getPrivate()));
        times.add(benchmarks("PS384", pairPS384.getPrivate(), pairPS384.getPublic(), n));

        var pairPS512 = JsonWebToken.generateKeyPair("PS512");
        System.out.println("PS512 pubKey: " + JsonWebToken.toString(pairPS512.getPublic()));
        System.out.println("PS512 priKey: " + JsonWebToken.toString(pairPS512.getPrivate()));
        times.add(benchmarks("PS512", pairPS512.getPrivate(), pairPS512.getPublic(), n));

        times.forEach(System.out::println);
    }

    private static String benchmarks(String alg, Key encodeKey, Key decodeKey, int n) {
        long iat = DateTimeUtils.now() / 1000 * 1000;
        long exp = iat + (60 * 60 * 1000);
        Map<String, Object> headers = Map.of(JsonWebToken.JWK_KEY_ID, CoreUtils.getId(100, 999), "version", "1.0.0");
        var claims = buildClaims(iat, iat, exp);
        var token = JsonWebToken.encode(encodeKey, alg, headers, claims);
        System.out.println("token: " + token.length() + " " + token);
        var result = JsonWebToken.verify(decodeKey, token, claims);
        assertEquals(JsonWebToken.VerifyResult.OK, result.key());

        var t1 = DateTimeUtils.now();
        for (int i = 0; i < n; ++i) {
            JsonWebToken.encode(encodeKey, alg, headers, claims);
        }
        var t2 = DateTimeUtils.now();
        for (int i = 0; i < n; ++i) {
            JsonWebToken.decode(decodeKey, token);
        }
        var t3 = DateTimeUtils.now();

        return "alg: " + alg + ", token: " + token.length() + ", time: " + List.of(t2 - t1, t3 - t2);
    }

    private static void assertVerify(String alg, Key encodeKey, Key verifyKey) {
        System.out.println(alg + " encodeKey: " + JsonWebToken.toString(encodeKey));
        System.out.println(alg + " verifyKey: " + JsonWebToken.toString(verifyKey));

        long iat = DateTimeUtils.now() / 1000 * 1000;
        long exp = iat + (60 * 60 * 1000);
        Map<String, Object> headers = Map.of(JsonWebToken.JWK_KEY_ID, CoreUtils.getId(100, 999), "version", "1.0.0");
        var claims = buildClaims(iat, iat, exp);
        var token = JsonWebToken.encode(encodeKey, alg, headers, claims);
        System.out.println("token: " + token.length() + " " + token);

        claims.put(JsonWebToken.JWK_EXPIRATION, exp + CoreUtils.random(10));
        var result = JsonWebToken.verify(verifyKey, token, claims);
        assertEquals(JsonWebToken.VerifyResult.OK, result.key());
    }

    private static void assertAlgorithm(String alg, Key encodeKey, Key decodeKey) {
        long iat = DateTimeUtils.now() / 1000 * 1000;
        long exp = iat + (60 * 60 * 1000);
        Map<String, Object> headers = Map.of(JsonWebToken.JWK_KEY_ID, CoreUtils.getId(100, 999), "version", "1.0.0");
        var claims = buildClaims(iat, iat, exp);
        var token = JsonWebToken.encode(encodeKey, alg, headers, claims);
        System.out.println("token: " + token.length() + " " + token);

        var jwt = JsonWebToken.decode(decodeKey, token);
        System.out.println("jwt: " + jwt);
        assertEquals(alg, jwt.headers().algorithm());
        assertEquals("JWT", jwt.headers().type());
        assertEquals(
                headers.get(JsonWebToken.JWK_KEY_ID).toString(), jwt.headers().kid());
        assertEquals(headers.get("version"), jwt.headers().get("version"));
        assertEquals(claims.get(JsonWebToken.JWK_ISSUER), jwt.claims().issuer());
        assertEquals(claims.get(JsonWebToken.JWK_SUBJECT), jwt.claims().subject());
        assertEquals(claims.get(JsonWebToken.JWK_AUDIENCE), jwt.claims().audience());
        assertEquals(claims.get(JsonWebToken.JWK_JWT_ID), jwt.claims().jwtId());
        assertEquals(iat, jwt.claims().issuedAt());
        assertEquals(iat, jwt.claims().notBefore());
        assertEquals(exp, jwt.claims().expiration());
        assertEquals(claims.get("MoeGo"), jwt.claims().get("MoeGo"));

        var expiration = jwt.claims().get(JsonWebToken.JWK_EXPIRATION);
        assertTrue(expiration instanceof Number);
        assertEquals(exp / 1000, ((Number) expiration).longValue());
    }

    private static Map<String, Object> buildClaims(long iat, long nbf, long exp) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(JsonWebToken.JWK_ISSUER, "frank");
        claims.put(JsonWebToken.JWK_SUBJECT, "login");
        claims.put(JsonWebToken.JWK_AUDIENCE, "customer");
        claims.put(JsonWebToken.JWK_ISSUED_AT, iat);
        claims.put(JsonWebToken.JWK_NOT_BEFORE, Instant.ofEpochMilli(nbf));
        claims.put(JsonWebToken.JWK_EXPIRATION, Instant.ofEpochMilli(exp).atZone(ZoneId.systemDefault()));
        claims.put(JsonWebToken.JWK_JWT_ID, "0001");
        claims.put("MoeGo", "Pet");
        return claims;
    }
}
