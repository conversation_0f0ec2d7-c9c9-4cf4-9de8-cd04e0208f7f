package com.moego.common.params;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.moego.common.enums.PropertyEnum;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/22
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
public record QueryParams(
        @Schema(description = "Query keyword") String keyword, @Hidden List<@NotNull PropertyEnum> properties) {}
