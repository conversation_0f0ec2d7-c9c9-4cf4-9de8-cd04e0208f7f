package com.moego.common.exception;

import com.moego.common.enums.ResponseCodeEnum;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020-06-18 22:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@SuppressFBWarnings("RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE")
public class CommonException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 2L;

    public static boolean isCommonException(Throwable ex) {
        if (ex == null) {
            return false;
        }
        if (ex instanceof CommonException) {
            return true;
        }
        return isCommonException(ex.getCause());
    }

    private Integer code;
    private String message;
    private Object data;
    private String causedBy;

    @Override
    public String getMessage() {
        return String.format("%d:%s:%s", code, message, data == null ? "null" : data.toString());
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Data
    @NoArgsConstructor
    public static class CommonResponse implements Serializable {

        private Integer code;
        private String message;
        private Object data;
        private String causedBy;
        // 兼容旧逻辑：失败是标记success字段为false
        private Boolean success = false;
    }

    public CommonResponse toResponse() {
        CommonResponse response = new CommonResponse();
        response.setCode(code);
        response.setMessage(message);
        response.setData(data);
        if (this.causedBy != null) {
            response.setCausedBy(this.causedBy);
        } else if (this.getCause() != null) {
            response.setCausedBy(this.getCause().toString());
        } else {
            response.setCausedBy(super.toString());
        }
        return response;
    }

    /**
     * @deprecated please specify the error type
     */
    @Deprecated
    public CommonException() {
        this(ResponseCodeEnum.SERVER_ERROR);
    }

    /**
     * @param data
     * @deprecated please specify the error type
     */
    @Deprecated
    public CommonException(String data) {
        this(ResponseCodeEnum.SERVER_ERROR, data);
    }

    public CommonException(ResponseCodeEnum responseCodeEnum) {
        super(responseCodeEnum.toString());
        this.code = responseCodeEnum.getCode();
        this.message = responseCodeEnum.getMessage();
        this.data = responseCodeEnum.getMessage();
    }

    public CommonException(ResponseCodeEnum responseCodeEnum, Object data) {
        this.code = responseCodeEnum.getCode();
        this.message = responseCodeEnum.getMessage();
        this.data = data;
    }

    public CommonException(ResponseCodeEnum responseCodeEnum, String message, Throwable cause) {
        super(responseCodeEnum.toString(), cause);
        this.code = responseCodeEnum.getCode();
        this.message = responseCodeEnum.getMessage();
        this.data = message;
    }
}
