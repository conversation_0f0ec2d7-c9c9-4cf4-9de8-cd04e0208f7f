package com.moego.common.mybatis;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.moego.common.dto.PageDTO;
import com.moego.common.params.PageParams;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-27 20:05
 * @deprecated Please use {@link com.moego.common.utils.PageUtil} instead.
 */
@Deprecated
public class MybatisUtils {

    public static <T> PageDTO<T> queryPage(PageParams pageForm, Callback<List<T>> callback) {
        Page page = PageHelper.startPage(pageForm.getPageNo(), pageForm.getPageSize(), pageForm.getOrderBy());
        callback.call();
        PageDTO<T> pageDTO = PageDTO.create(page.getResult(), page.getTotal(), page.getPageNum(), page.getPageSize());
        return pageDTO;
    }

    public static <T> PageDTO<T> queryPage(int pageNo, int pageSize, String orderBy, Callback<List<T>> callback) {
        Page page = PageHelper.startPage(pageNo, pageSize, orderBy);
        callback.call();
        PageDTO<T> pageDTO = PageDTO.create(page.getResult(), page.getTotal(), page.getPageNum(), page.getPageSize());
        return pageDTO;
    }

    public static <T> PageDTO<T> queryPage(int pageNo, int pageSize, Callback<List<T>> callback) {
        Page page = PageHelper.startPage(pageNo, pageSize);
        callback.call();
        PageDTO<T> pageDTO = PageDTO.create(page.getResult(), page.getTotal(), page.getPageNum(), page.getPageSize());
        return pageDTO;
    }
}
