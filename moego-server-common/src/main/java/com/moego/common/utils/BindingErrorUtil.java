package com.moego.common.utils;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import lombok.Data;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

/**
 * <AUTHOR>
 */
@Data
public class BindingErrorUtil {

    public static final String ERROR_TYPE_NOT_NULL = "NotNull";

    public static void handleResult(BindingResult bindingResult) {
        if (bindingResult != null && bindingResult.hasErrors()) {
            FieldError fieldError = bindingResult.getFieldError();
            if (fieldError != null) {
                if (ERROR_TYPE_NOT_NULL.equals(fieldError.getCode())) {
                    throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, ":" + fieldError.getField() + " is null");
                }
                String errorInfo = fieldError.getDefaultMessage();
                if (!CommonUtil.stringIsBlank(errorInfo)) {
                    throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, ":" + errorInfo);
                }
            }
        }
    }
}
