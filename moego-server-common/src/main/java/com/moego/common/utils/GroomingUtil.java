package com.moego.common.utils;

public class GroomingUtil {

    /**
     * 获取是否支付的描述
     * @param isPaid
     * @return
     */
    public static String getAppPaidDesc(Byte isPaid) {
        switch (isPaid) {
            case 1:
                return "Fully paid";
            case 2:
                return "Unpaid";
            case 3:
                return "Partial paid";
            default:
                return "Unknown";
        }
    }
}
