package com.moego.common.utils;

import com.moego.common.dto.SortDto;
import java.util.ArrayList;
import java.util.List;

public class SortUtils {

    public static List<SortDto> sort(List<Integer> idList) {
        List<SortDto> sortDtos = new ArrayList<SortDto>();

        if (idList == null || idList.size() == 0) {
            return sortDtos;
        }

        int sortValue = idList.size();
        for (Integer id : idList) {
            SortDto sortDto = new SortDto();
            sortDto.setId(id);
            sortDto.setSort(sortValue--);
            sortDtos.add(sortDto);
        }
        return sortDtos;
    }
}
