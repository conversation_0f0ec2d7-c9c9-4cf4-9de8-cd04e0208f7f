package com.moego.common.utils;

import com.moego.common.params.FilterParams;
import java.util.Objects;
import org.springframework.util.CollectionUtils;

/**
 * A tool class for FilterParams to determine whether the filter is a filter condition or a filter condition group
 *
 * <AUTHOR>
 * @since 2023/4/4
 */
public class FilterUtils {

    private FilterUtils() {}

    /**
     * Determine whether it is a filter condition.
     * filter condition contains property, operator and value
     *
     * @param filter filter params
     * @return true if it is a filter condition
     */
    public static boolean isFilter(FilterParams filter) {
        return (Objects.nonNull(filter)
                && Objects.nonNull(filter.property())
                && Objects.nonNull(filter.operator())
                && (Objects.nonNull(filter.value()) || !CollectionUtils.isEmpty(filter.values())));
    }

    /**
     * Determine whether it is a filter condition group.
     * filter condition group contains type and filters
     *
     * @param filter filter params
     * @return true if it is a filter condition group
     */
    public static boolean isFilterGroup(FilterParams filter) {
        return Objects.nonNull(filter) && Objects.nonNull(filter.type()) && !CollectionUtils.isEmpty(filter.filters());
    }

    /**
     * Determine whether the filter depth is greater than 2
     *
     * @param filter filter params
     * @return true if the filter depth is greater than 2
     */
    public static boolean checkFilterDepth(FilterParams filter, int depth) {
        if (depth > 2) {
            return true;
        }
        if (isFilterGroup(filter)) {
            return filter.filters().stream().anyMatch(f -> checkFilterDepth(f, depth + 1));
        } else if (isFilter(filter)) {
            return false;
        } else {
            return false;
        }
    }
}
