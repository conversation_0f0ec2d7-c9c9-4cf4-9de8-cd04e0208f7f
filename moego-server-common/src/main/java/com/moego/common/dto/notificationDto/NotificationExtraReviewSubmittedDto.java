package com.moego.common.dto.notificationDto;

import com.moego.common.enums.ReviewBoosterConst;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class NotificationExtraReviewSubmittedDto {

    private Integer customerId;
    private String customerFirstName;
    private String customerLastName;
    private Integer score;
    private Byte source = ReviewBoosterConst.REVIEW_SOURCE_SMS; // 没有值时默认是 SMS，历史通知都是 SMS
}
