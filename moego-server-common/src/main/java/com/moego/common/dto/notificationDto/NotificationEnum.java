package com.moego.common.dto.notificationDto;

/**
 * <AUTHOR>
 */
public interface NotificationEnum {
    // common template 各个通知的可用发送模板
    String TEMPLATE_TAG_CUSTOMER_FULL_NAME = "{customerFullName}";
    String TEMPLATE_TAG_AGREEMENT_TITLE = "{agreementTitle}";
    String TEMPLATE_TAG_TOTAL_PAID_AMOUNT = "{totalPaidAmount}";
    String TEMPLATE_TAG_REVIEW_SCORE = "{reviewScore}";
    String TEMPLATE_TAG_INTAKE_FORM_NAME = "{intakeFormName}";
    String TEMPLATE_TAG_DATE_WITH_TIME = "{Date}{Time}";
    String TEMPLATE_TAG_STAFF_FIRST_NAME = "{staffFirstName}";
    String TEMPLATE_TAG_MESSAGE_CONTENT = "{messageContent}";
    String TEMPLATE_MESSAGE_PUSH_TEMPLATE = "{customerFullName}: {messageContent}";
    String TEMPLATE_CALENDAR_PUSH_REMAINING_TIME = "{remainingTime}";
    String TEMPLATE_CALENDAR_PUSH_DATE_TIME_HOUR = "{appointmentTime:Hour}";
    String TEMPLATE_CALENDAR_PUSH_PET_NAME_BREEDS = "{petNameBreeds}";
    String TEMPLATE_DATE = "{Date}";
    String TEMPLATE_LAST4 = "{LAST4}";
    String TEMPLATE_TASK_DATE_TIME = "{taskDateTime}";
    String TEMPLATE_TASK_ASSIGN_COUNT = "{assignCount}";
    /**
     * message new mobile推送标题
     */
    String MESSAGE_NEW_TITLE = "New message";

    String TYPE_ACTIVITY_APPT_ASSIGNED = "APPT_ASSIGNED";
    /**
     * 只有owner会受到，
     * 当预约创建，但是owner不是指派人的时候，owner受到的是appt_create通知
     * 当预约创建，但是owner是指派人的时候，owner受到的是APPT_ASSIGNED通知
     * [Appointment created for a staff](https://www.notion.so/Appointment-created-for-a-staff-fcf8999ee329468dacd78f3801d3f0d9)
     */
    String TYPE_ACTIVITY_APPT_CREATED = "APPT_CREATED";
    /**
     * [Appointment rescheduled for a staff](https://www.notion.so/Appointment-rescheduled-for-a-staff-a9bd2da3c6fe409a8b657d1fe3752094)
     */
    String TYPE_ACTIVITY_APPT_RESCHEDULED = "APPT_RESCHEDULED";
    /**
     * [Appointment cancelled for a staff](https://www.notion.so/Appointment-cancelled-for-a-staff-57e525190e7448b0949172fde1aaf5f2)
     */
    String TYPE_ACTIVITY_APPT_CANCELLED = "APPT_CANCELLED";
    /**
     * [Appointment cancelled by client](https://www.notion.so/Appointment-cancelled-by-client-3f0e2311b66c42cdac9aa253ed07d35c)
     */
    String TYPE_ACTIVITY_APPT_CANCELLED_BY_CLIENT = "APPT_CANCELLED_BY_CLIENT";
    /**
     * [Appointment confirmed by client](https://www.notion.so/Appointment-confirmed-by-client-c5934d1ae72c47bc91b0c9219a1b38a1)
     */
    String TYPE_ACTIVITY_APPT_CONFIRMED_BY_CLIENT = "APPT_CONFIRMED_BY_CLIENT";
    /**
     * [New booking requested by client](https://www.notion.so/New-booking-requested-by-client-1f692521791843c7a7e3934404990f4e)
     */
    String TYPE_ACTIVITY_ONLINE_BOOKING_REQUEST = "OB_REQUEST";

    String TYPE_ACTIVITY_ONLINE_BOOKING_REQUEST_RESCHEDULE = "OB_REQUEST_RESCHEDULE";

    String TYPE_ACTIVITY_ONLINE_BOOKING_REQUEST_CANCEL = "OB_REQUEST_CANCEL";

    // waitlist available
    String TYPE_ACTIVITY_WAITLIST_AVAILABLE = "WAITLIST_AVAILABLE";
    // new waitlist signed up
    String TYPE_ACTIVITY_WAITLIST_SIGNED_UP = "WAITLIST_SIGNED_UP";

    /**
     * New Abandoned bookings 通知类型
     *
     * @see <a href="https://moego.atlassian.net/browse/ERP-6132">ERP-6132</a>
     * @see <a href="https://moego.atlassian.net/browse/ERP-6446">ERP-6446</a>
     */
    String TYPE_ACTIVITY_NEW_ABANDONED_BOOKINGS = "OB_ABANDONED";

    /**
     * Assigned task to staff 通知类型
     */
    String TYPE_ACTIVITY_ASSIGNED_TASK = "ASSIGNED_TASK";

    /**
     * [New intake form submitted by client](https://www.notion.so/New-intake-form-submitted-by-client-6165660ffbdd46c3b98b9f371cc9b82b)
     */
    String TYPE_ACTIVITY_NEW_INTAKE_FORM = "NEW_INTAKE_FORM";
    /**
     * [Invoice paid by client](https://www.notion.so/Invoice-paid-by-client-f724859c902f4eb9bfeb33d7a299f284)
     */
    String TYPE_ACTIVITY_INVOICE_PAID = "INVOICE_PAID";

    String TYPE_ACTIVITY_PAYMENT_DISPUTE = "PAYMENT_DISPUTE";
    /**
     * [Agreement signed by client](https://www.notion.so/Agreement-signed-by-client-c3befe9b124d4f2cb88c5d06616d05d0)
     */
    String TYPE_ACTIVITY_AGREEMENT_SIGNED = "AGREEMENT_SIGNED";
    /**
     * [Review submitted by client](https://www.notion.so/Review-submitted-by-client-98e3ef4df48141549ca0d4153e83010c)
     */
    String TYPE_ACTIVITY_REVIEW_SUBMITTED = "REVIEW_SUBMITTED";
    /**
     * [Calendar reminder (low)](https://www.notion.so/Calendar-reminder-low-6e846f63f6b047b3ade50338d42d30e6)
     */
    String TYPE_ACTIVITY_APPT_UPCOMING_REMINDER = "APPT_UPCOMING_REMINDER";
    /**
     * [Message received from client](https://www.notion.so/Message-received-from-client-85c1bc2f46304e1aadf6d422baf404f6)
     */
    String TYPE_ACTIVITY_MESSAGE_NEW = "MESSAGE_NEW";

    /**
     * 当 booking_range_type 为 custom date range 时，需要在 end date 前 7 天发送通知，告诉用户 renew 设置。
     *
     * @see <a href="https://moego.atlassian.net/browse/ERP-6284">ERP-6284</a>
     */
    String TYPE_ACTIVITY_RENEW_ONLINE_BOOKING_END_DATE = "RENEW_ONLINE_BOOKING_END_DATE";

    String TYPE_SYSTEM = "SYSTEM";

    String TYPE_CLOCK_IN_OUT = "CLOCK_IN_OUT";

    String TYPE_ACTIVITY_APPT_CREATED_TITLE = "Appointment has been created";

    String TYPE_MOBILE_APPOINTMENT_REMINDER = "CALENDAR_REMINDER";

    String TYPE_ACTIVITY_CAPITAL_REQUEST_APPROVED = "FINANCE_CAPITAL_OFFER_APPROVED";
    String TYPE_ACTIVITY_BUY_MEMBERSHIP_VIA_BRANDED_APP = "BUY_MEMBERSHIP_VIA_BRANDED_APP";

    // c 端操作 notification，命名 pattern 为 CLIENT_UPDATE_XXX

    String TYPE_ACTIVITY_CLIENT_UPDATE_APPOINTMENT = "CLIENT_UPDATE_APPOINTMENT";
    String TYPE_ACTIVITY_CLIENT_UPDATE_CUSTOMER = "CLIENT_UPDATE_CUSTOMER";
    String TYPE_ACTIVITY_CLIENT_UPDATE_PET = "CLIENT_UPDATE_PET";

    String TYPE_SYSTEM_SUB_PAYMENT_FAIL = "SUB_PAYMENT_FAIL";
    String TYPE_SYSTEM_A2P_REGISTER_FAIL = "A2P_FAIL";
    String TYPE_SYSTEM_A2P_REGISTER_REQUIRED = "A2P_REQUIRED";
    String TYPE_SYSTEM_TWILIO_FINE = "TWILIO_FINE";
    String TYPE_SYSTEM_SUB_PAYMENT_SUCCESS = "SUB_PAYMENT_SUCCESS";
    String TYPE_SUSTEM_SMS_AMOUNT_LOW = "SMS_AMOUNT_LOW";
    String TYPE_SYSTEM_STRIPE_RESTRICTED = "STRIPE_RESTRICTED";
    String TYPE_SYSTEM_PAYMENT_DISPUTED = "PAYMENT_DISPUTED";
    String TYPE_SYSTEM_GOOGLE_RESERVE_GEO_UNMATCHED = "GOOGLE_RESERVE_GEO_UNMATCHED";
    String TYPE_SYSTEM_GOOGLE_RESERVE_GEO_MATCHED = "GOOGLE_RESERVE_GEO_MATCHED";
    String TYPE_SYSTEM_PLATFORM_MARKETING_CAMPAIGN = "PLATFORM_MARKETING_CAMPAIGN";

    String TYPE_PENDING_REVIEW_PET_VACCINE_REQUEST = "PET_VACCINE_REQUEST";

    Byte APP_PUSH_TOKEN_STATUS_NORMAL = 1;
    Byte APP_PUSH_TOKEN_STATUS_TIME_EXPIRED = 2;
    Byte APP_PUSH_TOKEN_STATUS_LOGOUT_EXPIRED = 3;
    Byte STATUS_DISMISS = 3;
}
