package com.moego.common.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BusinessPreferenceDto {

    // 0 或 1 开启 关闭
    public static final Integer AUTO_REPLY_STATUS_OPEN = 1;
    public static final Integer AUTO_REPLY_STATUS_CLOSE = 0;
    private String country;
    private String currencySymbol;
    private String currencyCode;
    private Integer timeFormatType;
    private String timeFormat;
    private Integer unitOfWeightType;
    private String unitOfWeight;
    private Integer unitOfDistanceType;
    private String unitOfDistance;
    private String timezoneName;
    private Integer timezoneSeconds;
    private Integer dateFormatType;
    private String dateFormat;
    private Integer calendarFormatType;
    private String calendarFormat;
    private Integer numberFormatType;
    private String numberFormat;
    private boolean isNeedSendCode;
    private Integer autoReplyStatus;
    private Byte messageSendBy;
    private Byte notificationSoundEnable;
}
