package com.moego.common.dto;

import com.moego.common.enums.FeatureConst;
import lombok.Data;

@Data
public class FeatureQuotaDto {

    private String code;
    private Boolean enable;
    private Integer quota;
    private Byte allowType;

    public void updateByEnable(Boolean enableValue) {
        if (enableValue == null || !enableValue) {
            // close feature
            this.setEnable(false);
            this.setQuota(FeatureConst.MIN_QUOTA);
        } else {
            // open feature
            this.setEnable(true);
            this.setQuota(FeatureConst.MAX_QUOTA);
        }
    }

    public boolean checkEnable() {
        if (enable) {
            if (FeatureConst.MAX_QUOTA.equals(quota)) {
                return true;
            } else return quota > FeatureConst.MIN_QUOTA;
        }
        return false;
    }
}
