package com.moego.common.enums;

public interface TwilioConst {
    String DEFAULT_CALL_BACK_TEXT = "good bye";
    Byte CALL_HANDLE_TYPE_NOTHING = 0;
    Byte CALL_HANDLE_TYPE_FORWARDING = 1;
    Byte CALL_REPLY_TYPE_CLOSE = 0;
    Byte CALL_REPLY_TYPE_OPEN = 1;
    Byte HANDEL_STATUS_IGNORE = 0;
    Byte HANDEL_STATUS_BEGIN_HANDLE = 1;
    Byte HANDEL_STATUS_HANDEL_SUCCESS = 2;
    Byte HANDEL_STATUS_HANDEL_FAILED = 3;
    Integer TWILIO_CALL_FORWARDING_MAX_TIME_LIMIT = 10 * 60;

    String REPLY_TEMPLATE_BUSINESS_NAME = "{storeName}";

    String TWILIO_VOICE_CALL_STATUS_RINGING = "ringing";
    // The call is currently ringing.
    String TWILIO_VOICE_CALL_STATUS_QUEUED = "queued";
    // The call is ready and waiting in line before going out.
    String TWILIO_VOICE_CALL_STATUS_COMPLETED = "completed";
    // The call was answered and has ended normally.
    String TWILIO_VOICE_CALL_STATUS_IN_PROGRESS = "in-progress";
    // The call was answered and is actively in progress.
    String TWILIO_VOICE_CALL_STATUS_IN_BUSY = "busy";
    // The caller received a busy signal.
    String TWILIO_VOICE_CALL_STATUS_FAILED = "failed";
    // The call could not be completed as dialed, most likely because the phone number was non-existent.
    String TWILIO_VOICE_CALL_STATUS_NO_ANSWER = "no-answer";
    // The call ended without being answered.
    String TWILIO_VOICE_CALL_STATUS_CANCELED = "canceled";
    // The call was canceled via the REST API while queued or ringing.
}
