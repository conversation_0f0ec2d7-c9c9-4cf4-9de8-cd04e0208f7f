package com.moego.common.enums.appointment;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CancelTypeEnum {
    CANCEL_BY_BUSINESS((byte) 0),
    CANCEL_BY_CLIENT((byte) 1),
    CANCEL_BY_PET_REMOVE((byte) 2),
    CANCEL_BY_CLIENT_PORTAL((byte) 3);

    private final Byte value;

    public static CancelTypeEnum fromValue(Byte value) {
        for (CancelTypeEnum cancelTypeEnum : CancelTypeEnum.values()) {
            if (cancelTypeEnum.getValue().equals(value)) {
                return cancelTypeEnum;
            }
        }
        // default value
        return CANCEL_BY_BUSINESS;
    }
}
