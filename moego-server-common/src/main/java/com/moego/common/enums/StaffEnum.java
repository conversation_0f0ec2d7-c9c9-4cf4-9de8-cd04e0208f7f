package com.moego.common.enums;

/**
 * <AUTHOR>
 */
public interface StaffEnum {
    Byte INACTIVE_TRUE = 1;
    Byte INACTIVE_FALSE = 0;
    Byte EMPLOYEE_CATEGORY_OWNER = 1;
    Byte EMPLOYEE_CATEGORY_STAFF = 0;
    Byte EMPLOYEE_CATEGORY_MASTER_OWNER = 2;
    Byte EMPLOYEE_CATEGORY_ENTERPRISE_STAFF = 3;
    Byte SHOW_ON_CALENDAR_TRUE = 1;
    Byte SHOW_ON_CALENDAR_FALSE = 0;

    Byte SHOW_CALENDAR_STAFF_ALL_TRUE = 1;
    Byte SHOW_CALENDAR_STAFF_ALL_FALSE = 0;

    Byte STATUS_TEMPORARY = 4; // staff 已迁移，老服务里删除 staff 是更新为 2，非下面的 0，所以这里取 3，用于区分
    Byte STATUS_MIGRATED = 3; // staff 已迁移，老服务里删除 staff 是更新为 2，非下面的 0，所以这里取 3，用于区分
    Byte STATUS_DELETED = 2; // staff 已删除
    Byte STATUS_NORMAL = 1; // 正常

    @Deprecated
    Byte STATUS_DELETE = 0; // 商户已删除

    Byte ALLOW_LOGIN_TRUE = 1; // 允许登录
    Byte ALLOW_LOGIN_FALSE = 0; // 不允许登录

    Byte INVITE_CODE_USED = 1; // 邀请码已被用
    Byte INVITE_CODE_CLOSED = 2; // 邀请码已关闭
    Byte INVITE_CODE_NOT_FOUND = 3; // 邀请码找不到
    Byte INVITE_CODE_IS_VALID_TRUE = 1; // 邀请码验证通过
    Byte INVITE_CODE_IS_VALID_FALSE = 0; // 邀请码验证不通过

    Byte NOTIFICATION_OPEN = 1; // 设置的开关打开
    Byte NOTIFICATION_CLOSE = 0; // 设置的开关关闭

    Byte NOTIFICATION_TYPE_ACROSS_WORKING_LOCATIONS = 2; // 接收 working locations 所有 staff 的通知
    Byte NOTIFICATION_TYPE_RELATED_TO_THEM = 1; // 仅接收 related to them 的通知,非迁移用户的通知只有开和关两种状态,1表示打开
    Byte NOTIFICATION_TYPE_DO_NOT_NOTIFY = 0; // 不接收通知

    Byte PAY_BY_COMMISSION_BASED = 0; // pay by类型是commission based
    Byte PAY_BY_HOURLY_BASED = 1; // pay by类型是hourly based
}
