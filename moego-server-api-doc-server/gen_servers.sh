#!/usr/bin/env bash
# @since 2024-07-16 19:16:30
# <AUTHOR> <EMAIL>

rm -rf "./src/main/java/com/moego/doc/servers/"

for file in ../moego-server-api/src/main/java/com/moego/server/*/api/*.java; do
  module="$(echo "$file" | awk -F'/' '{print $(NF-2)}')"
  class="$(echo "$file" | awk -F'/' '{print $NF}' | awk -F'.' '{print $1}')"
  mkdir -p "./src/main/java/com/moego/doc/servers/${module}/"
  cat <<EOF>"./src/main/java/com/moego/doc/servers/${module}/${class}Server.java"
package com.moego.doc.servers.${module};

import com.moego.server.${module}.api.${class}Base;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class ${class}Server extends ${class}Base {}
EOF

done
