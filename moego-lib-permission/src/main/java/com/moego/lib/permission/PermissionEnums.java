package com.moego.lib.permission;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum PermissionEnums {
    ACCESS_PAYMENT_SETTING("accessPaymentSettings"),
    PROCESS_PAYMENT("processPayment"),
    REMOVE_PROCESSING_FEE_BY_CLIENT("removeProcessingFeeByClient"),
    CREATE_APPOINTMENT("createAppointment"),
    EDIT_APPOINTMENT("editAppointment"),
    CANCEL_APPOINTMENT("cancelAppointment"),
    EDIT_TICKET_COMMENT_AND_GROOMING_REPORT("editTicketCommentAndGroomingReport"),
    CREATE_AND_EDIT_BLOCK("createAndEditBlock"),
    ACCESS_CLIENT("accessClient"),
    DELETE_CLIENT("deleteClient"),
    DELETE_PET("deletePet"),
    UPDATE_CLIENT_AND_PET_STATUS("updateClientAndPetStatus"),
    IMPORT_CLIENT("importClient"),
    EXPORT_CLIENT("exportClient"),
    MERGE_CLIENT("mergeClient"),
    ACCESS_CLIENT_PACKAGE_LIST("accessClientPackageList"),
    EDIT_CLIENT_PACKAGE("editClientPackage"),
    CREATE_OR_EDIT_INCIDENT_REPORT("createOrEditIncidentReport"),
    ACCESS_CLIENT_EMAIL_AND_PHONE_NUMBER("accessClientEmailAndPhoneNumber"),
    ADD_NEW_CLIENT("addNewClient"),
    ACCESS_BOOKING_REQUEST_AND_WAITING_LIST("accessBookingRequestAndWaitingList"),
    ACCESS_OVERVIEW_DATA_FOR_BOOKING_REQUEST_AND_WAITING_LIST("accessOverViewDataForBookingRequestAndWaitingList"),
    ACCESS_ABANDON_BOOKING_REQUEST("accessAbandonBookingRequest"),
    ACCESS_OVERVIEW_DATA_FOR_ABANDON_BOOKING_REQUEST("accessOverViewDataForAbandonBookingRequest"),
    ONLINE_BOOKING_SETTINGS("onlineBookingSettings"),
    MESSAGE_CLIENT("messageClient"),
    DELETE_CHAT_HISTORY("deleteChatHistory"),
    BLOCK_CLIENT_MESSAGE("blockClientMessage"),
    ACCESS_STAFF_MEMBERS("accessStaffMembers"),
    ADD_NEW_STAFF("addNewStaff"),
    EDIT_ROLE_PERMISSIONS("editRolePermissions"),
    EDIT_STAFF_PROFILE("editStaffProfile"),
    EDIT_STAFF_NOTIFICATION("editStaffNotification"),
    EDIT_STAFF_PAY_RATE("editStaffPayRate"),
    ACCESS_STAFF_SHIFT("accessStaffShift"),
    ACCESS_GENERAL_PAYROLL_SETTING("accessGeneralPayrollSetting"),
    CLOCK_IN_OR_OUT("clockinOrOut"),
    EDIT_CLOCK_IN_OR_OUT_RECORDS("editClockinOrOutRecords"),
    ACCESS_REPORT("accessReport"),
    ACCESS_RETAIL("accessRetail"),
    ACCESS_PACKAGE("accessPackage"),
    ACCESS_REVIEW_BOOSTER("accessReviewBooster"),
    ACCESS_MARKETING_CAMPAIGN("accessMarketingCampaign"),
    ACCESS_AUTO_REMINDERS("accessAutoReminders"),
    ACCESS_GENERAL_SETTINGS("accessGeneralSettings"),
    ACCESS_AGREEMENT_SETTINGS("accessAgreementSettings"),
    ACCESS_AUTO_MESSAGE_SETTINGS("accessAutoMessageSettings"),
    ACCESS_GROOMING_REPORT_SETTINGS("accessGroomingReportSettings"),
    ACCESS_MOBILE_GROOMING_SETTINGS("accessMobileGroomingSettings"),
    ACCESS_INTAKE_FORM_SETTINGS("accessIntakeFormSettings"),
    ACCESS_QUICKBOOK_SYNC("accessQuickBookSync"),
    ACCESS_GOOGLE_CALENDAR_SYNC("accessGoogleCalendarSync"),
    ACCESS_DISCOUNT("accessDiscount"),
    ADD_OR_REMOVE_DISCOUNT_AT_CHECK_OUT("addOrRemoveDiscountAtCheckOut"),
    ACCESS_SERVICE_SETTINGS("accessServiceSettings"),
    SELL_PACKAGES("sellPackages"),
    SELL_RETAIL_PRODUCTS("sellRetailProducts"),
    ACCESS_LODGING_SETTING("accessLodgingSettings"),
    ACCESS_STAFF_LOCATION("accessStaffLocation"),
    ACCESS_MEMBERSHIP("accessMembership"),
    SELL_MEMBERSHIP("sellMembership"),
    ACCESS_CLIENT_MEMBERSHIPS("accessClientMemberships"),
    OPERATE_MEMBERSHIP("operateMembership"),
    AUTO_TIPPING("autoTipping"),
    ACCESS_CLIENT_PET_SETTING("accessClientPetSettings"),
    ONLINE_BOOKING_SWITCH("onlineBookingSwitch"),
    ENTERPRISE_ACCESS_TENANT("accessTenant"),
    MANAGE_MEMBERSHIP("manageMembership"),
    COMPLETE_INVOICE("completeInvoice"),
    EXTRA_CHARGE_INVOICE("extraChargeInvoice"),
    ADD_EXTRA_TIPS("addExtraTips"),
    ADD_EXTRA_ITEMS("addExtraItems"),
    MANAGE_INVOICE_STAFF("manageInvoiceStaff"),
    MANAGE_INVOICE_TIP_SPLIT("manageInvoiceTipSplit"),
    CAN_PROCESS_REFUND("canProcessRefund"),
    ACCESS_PAYROLL_REPORT("accessPayrollReport"),
    ACCESS_TASKS("accessTasks"),
    ASSIGN_STAFF_TO_TASK("assignStaffToTask"),
    MANAGE_PET_WEIGHT_RANGE("managePetWeightRange"),
    MANAGE_PET_TYPE_AND_BREED("managePetTypeAndBreed"),
    ACCESS_CASH_DRAWER("accessCashDrawer"),
    EDIT_CARE_TYPE_NAME("editCareTypeName"),
    Unknown("unknown");

    private final String permissionName;

    public static PermissionEnums fromString(String permissionName) {
        for (PermissionEnums permissionEnums : PermissionEnums.values()) {
            if (permissionEnums.getPermissionName().equals(permissionName)) {
                return permissionEnums;
            }
        }
        return Unknown;
    }
}
