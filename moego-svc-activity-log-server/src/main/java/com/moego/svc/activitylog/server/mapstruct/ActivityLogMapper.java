package com.moego.svc.activitylog.server.mapstruct;

import com.moego.svc.activitylog.event.ActivityLogEvent;
import com.moego.svc.activitylog.server.entity.ActivityLog;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public abstract class ActivityLogMapper {

    public static final ActivityLogMapper INSTANCE = Mappers.getMapper(ActivityLogMapper.class);

    /**
     * Convert {@link ActivityLogEvent} to {@link ActivityLog}.
     *
     * @param event {@link ActivityLogEvent}
     * @return {@link ActivityLog}
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "operatorName", ignore = true)
    @Mapping(target = "resourceName", ignore = true)
    @Mapping(target = "ownerId", ignore = true)
    @Mapping(target = "ownerName", ignore = true)
    @Mapping(target = "isRoot", expression = "java(event.isRoot())")
    @Mapping(target = "resourceType", ignore = true)
    public abstract ActivityLog toEntity(ActivityLogEvent event);

    @AfterMapping
    protected void toEntityAfterMapping(ActivityLogEvent source, @MappingTarget ActivityLog target) {
        if (source.getResourceTypeV2() != null) {
            target.setResourceType(source.getResourceTypeV2().toString());
        } else if (source.getResourceType() != null) {
            target.setResourceType(source.getResourceType().toString());
        }
    }
}
