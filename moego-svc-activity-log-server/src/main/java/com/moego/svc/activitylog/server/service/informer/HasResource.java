package com.moego.svc.activitylog.server.service.informer;

/**
 * @param <R> resource type
 * <AUTHOR>
 */
public interface HasResource<R> {

    /**
     * Get resource type.
     *
     * @return resource type
     */
    String resourceType();

    /**
     * Get resource.
     *
     * @param resourceId resource id
     * @return resource
     */
    R resource(String resourceId);

    /**
     * Get human-readable resource name.
     *
     * @param resource resource
     * @return resource name
     */
    String resourceName(R resource);
}
