package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.message.api.IMessageService;
import com.moego.server.message.dto.MessageDetailDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#MESSAGE}.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MessageInformer extends AbstractStaffOperatorCustomerOwnerInformer<MessageDetailDTO> {

    private final IMessageService messageApi;

    @Override
    public String resourceType() {
        return ResourceType.MESSAGE.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public String getOwnerId(MessageDetailDTO messageDetailDTO) {
        return Optional.ofNullable(messageDetailDTO.getCustomerId())
                .map(String::valueOf)
                .orElse(null);
    }

    @Override
    public MessageDetailDTO resource(String resourceId) {
        return messageApi.getMessageById(Integer.valueOf(resourceId));
    }

    @Override
    public String resourceName(MessageDetailDTO messageDetailDTO) {
        return messageDetailDTO.getMessageText();
    }
}
