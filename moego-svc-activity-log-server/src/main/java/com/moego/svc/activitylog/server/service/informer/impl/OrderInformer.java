package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc.OrderServiceBlockingStub;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#ORDER}.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderInformer extends AbstractStaffOperatorCustomerOwnerInformer<OrderModel> {

    private final OrderServiceBlockingStub orderApi;
    private final AppointmentInformer appointmentInformer;

    @Override
    public String getOwnerId(OrderModel order) {
        return String.valueOf(order.getCustomerId());
    }

    @Override
    public String resourceType() {
        return ResourceType.ORDER.toString();
    }

    @Override
    public String resourceName(OrderModel orderModel) {
        var sourceType = orderModel.getSourceType();
        // 如果是 appt 相关的订单，显示 appt 的资源名
        if ("appointment".equals(sourceType) || "noshow".equals(sourceType)) {
            var appointmentId = orderModel.getSourceId();

            var appointment = appointmentInformer.resource(String.valueOf(appointmentId));
            if (appointment != null) {
                return appointmentInformer.resourceName(appointment);
            }
        }
        return orderModel.getTitle();
    }

    @Override
    public OrderModel resource(String resourceId) {
        var request =
                GetOrderRequest.newBuilder().setId(Long.parseLong(resourceId)).build();
        return orderApi.getOrder(request);
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
