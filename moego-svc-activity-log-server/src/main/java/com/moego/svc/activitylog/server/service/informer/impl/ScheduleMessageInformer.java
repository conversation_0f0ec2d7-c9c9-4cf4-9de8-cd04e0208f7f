package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

@Component
public class ScheduleMessageInformer extends AbstractStaffOperatorInformer<Object> {

    @Override
    public String resourceType() {
        return ResourceType.SCHEDULE_MESSAGE.toString();
    }
}
