package com.moego.svc.activitylog.server.config;

import com.jayway.jsonpath.JsonPath;
import com.moego.svc.activitylog.server.service.mapper.MapperName;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "moego.field-mapping-rules")
public class FieldMappingRuleProperties implements InitializingBean {

    private List<Rule> general = new ArrayList<>();
    private List<ExtraRule> extra = new ArrayList<>();

    private final Map<String, List<Rule>> extraMap = new HashMap<>();

    public List<Rule> getMappingRules(String className) {
        return extraMap.getOrDefault(className, general);
    }

    @Override
    public void afterPropertiesSet() {
        for (var extraRule : extra) {
            List<Rule> rules = extraRule.getRules();
            if (!extraRule.isExcludeGeneral()) {
                // merge general rules and extra rules
                rules = new ArrayList<>(general);
                rules.addAll(extraRule.getRules());
            }
            // these class share the same rules
            for (String className : extraRule.getClassNames()) {
                this.extraMap.put(className, rules);
            }
        }
    }

    @Data
    public static class ExtraRule {
        private List<String> classNames;
        private List<Rule> rules;
        private boolean excludeGeneral;
    }

    @Data
    public static class Rule {
        private JsonPath pathPattern;
        private MapperName mapperName;
        private boolean isList;

        public Rule(String pathPattern, MapperName mapperName, boolean isList) {
            this.pathPattern = JsonPath.compile(pathPattern);
            this.mapperName = mapperName;
            this.isList = isList;
        }
    }
}
