package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.service.order.v1.GetOrderListRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class OrderIdMapper implements Mapper<OrderDetailModel> {

    private final OrderServiceGrpc.OrderServiceBlockingStub orderApi;

    @Override
    public Map<String, String> map(Set<String> orderIds) {
        Map<String, String> result = new HashMap<>(orderIds.size());

        var longOrderIdList =
                orderIds.stream().map(Long::parseLong).filter(id -> id > 0).toList();

        GetOrderListRequest request =
                GetOrderListRequest.newBuilder().addAllOrderIds(longOrderIdList).build();

        try {
            var orders = orderApi.getOrderList(request);

            for (var order : orders.getOrderListList()) {
                var id = String.valueOf(order.getOrder().getId());
                var name = getName(order);
                result.put(id, name);
            }
        } catch (Exception e) {
            log.error("Failed to get order name for order id: {}", longOrderIdList, e);
        }
        return result;
    }

    @Override
    public String getName(OrderDetailModel order) {
        return order.getOrder().getTitle();
    }
}
