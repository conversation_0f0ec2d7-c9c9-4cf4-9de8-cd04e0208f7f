package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.idl.models.auto_message.v1.AutoMessageConfigModel;
import com.moego.idl.service.auto_message.v1.AutoMessageConfigServiceGrpc;
import com.moego.idl.service.auto_message.v1.GetAutoMessageConfigDetailRequest;
import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class AutoMessageIdMapper implements Mapper<AutoMessageConfigModel> {
    private final AutoMessageConfigServiceGrpc.AutoMessageConfigServiceBlockingStub autoMessageConfigClient;

    /**
     * Map auto message ids to names.
     * @param ids auto message ids
     * @return map of auto message id -> use case name
     */
    @Override
    public Map<String, String> map(Set<String> ids) {
        Map<String, String> result = new HashMap<>(ids.size());
        // auto message activity log 目前没有需要批量处理的逻辑，所以这里不做批量处理
        for (String id : ids) {
            try {
                long autoMsgId = Long.parseLong(id);
                if (autoMsgId <= 0) {
                    continue;
                }
                AutoMessageConfigModel autoMessage = autoMessageConfigClient
                        .getAutoMessageConfigDetail(GetAutoMessageConfigDetailRequest.newBuilder()
                                .setId(autoMsgId)
                                .build())
                        .getAutoMessage();
                result.put(id, getName(autoMessage));
            } catch (Exception e) {
                log.error("Failed to get auto message use case name for auto message id: {}", id, e);
            }
        }
        return result;
    }

    @Override
    public String getName(AutoMessageConfigModel autoMsg) {
        return autoMsg.getUseCase().toString();
    }
}
