package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#GROOMING_NOTE}.
 *
 * <AUTHOR>
 */
@Component
public class GroomingNoteInformer extends AbstractStaffOperatorInformer<Object> {

    @Override
    public String resourceType() {
        return ResourceType.GROOMING_NOTE.toString();
    }
}
