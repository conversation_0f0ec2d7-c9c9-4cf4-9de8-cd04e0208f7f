syntax = "proto3";

package moego.client.payment.v1;

import "moego/models/online_booking/v1/payments_defs.proto";
import "moego/models/online_booking/v1/selected_defs.proto";
import "moego/models/order/v1/service_charge_model.proto";
import "moego/models/payment/v1/payment_method_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/payment/v1;paymentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.payment.v1";

// get prepay amount request
message GetPrepayAmountRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet selected service list
  repeated moego.models.online_booking.v1.SelectedPetServiceDef selected_pet_services = 2;
  // discount code
  optional string discount_code = 3 [(validate.rules).string = {max_len: 20}];
}

// get prepay amount response
message GetPrepayAmountResponse {
  // prepay amount
  moego.models.online_booking.v1.PrepayDef prepay = 1;
  // service charge list
  repeated moego.models.order.v1.ServiceChargeOnlineBookingView service_charges = 2;
}

// create payment intent request
message CreatePaymentIntentRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// create payment intent  response
message CreatePaymentIntentResponse {
  // payment intent id
  string payment_intent_id = 1;
  // payment intent client secret
  string secret_key = 2;
}

// prepay request
message PrepayRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // prepay amount
  moego.models.online_booking.v1.PrepayDef prepay = 2;
  // stripe payment method
  moego.models.payment.v1.StripePaymentMethod stripe_payment_method = 3;
  // payment intent id
  string payment_intent_id = 4;
  // save credit card
  bool is_save_card = 5;
}

// prepay response
message PrepayResponse {
  // total payment
  double total_payment = 1;
}

// prepayment / deposit service
service PrepayService {
  // get prepay amount
  rpc GetPrepayAmount(GetPrepayAmountRequest) returns (GetPrepayAmountResponse);
  // create payment intent
  rpc CreatePaymentIntent(CreatePaymentIntentRequest) returns (CreatePaymentIntentResponse);
  // prepay
  rpc Prepay(PrepayRequest) returns (PrepayResponse);
}
