syntax = "proto3";

package moego.client.grooming.v1;

import "moego/models/grooming/v1/review_booster_record_models.proto";
import "moego/models/review_booster/v1/review_booster_config_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/grooming/v1;groomingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.grooming.v1";

// create review booster request
message CreateReviewBoosterRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64.gt = 0];
  // review booster score
  int32 score = 2 [(validate.rules).int32 = {
    gte: 1
    lte: 5
  }];
  // review content
  optional string review_content = 3 [(validate.rules).string = {max_len: 1000}];
}

// create review booster response
message CreateReviewBoosterResponse {}

// get review booster list request
message GetReviewBoosterListRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64.gt = 0];
}

// get review booster list response
message GetReviewBoosterListResponse {
  // review booster record list
  repeated moego.models.grooming.v1.ReviewBoosterRecordModelClientView records = 1;
}

// get review booster config params
message GetReviewBoosterConfigParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
}

// get review booster config result
message GetReviewBoosterConfigResult {
  // review booster config
  models.review_booster.v1.ReviewBoosterConfigClientView config = 1;
}

// review booster service
service ReviewBoosterService {
  // create review booster record
  rpc CreateReviewBooster(CreateReviewBoosterRequest) returns (CreateReviewBoosterResponse);
  // get review booster record
  rpc GetReviewBoosterList(GetReviewBoosterListRequest) returns (GetReviewBoosterListResponse);
  // get review booster config for specific business
  rpc GetReviewBoosterConfig(GetReviewBoosterConfigParams) returns (GetReviewBoosterConfigResult);
}
