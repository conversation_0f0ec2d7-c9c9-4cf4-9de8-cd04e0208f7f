package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbSyncProduct;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeQbSyncProductMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_product
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_product
     *
     * @mbg.generated
     */
    int insert(MoeQbSyncProduct record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_product
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbSyncProduct record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_product
     *
     * @mbg.generated
     */
    MoeQbSyncProduct selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_product
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbSyncProduct record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_product
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeQbSyncProduct record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_product
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbSyncProduct record);

    List<MoeQbSyncProduct> selectByBusinessIdRealmIdProductIds(
            @Param("businessId") Integer businessId,
            @Param("realmId") String realmId,
            @Param("productIdList") List<Integer> productIdList);

    int insertOrUpdate(MoeQbSyncProduct record);
}
