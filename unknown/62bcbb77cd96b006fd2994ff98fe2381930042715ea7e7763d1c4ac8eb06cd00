package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationBuyMembershipViaBrandedAppParams extends NotificationParams {
    private String title = "Membership purchased";
    private String type = NotificationEnum.TYPE_ACTIVITY_BUY_MEMBERSHIP_VIA_BRANDED_APP;
    private ExtraDTO webPushDto;

    @Data
    public static class ExtraDTO {
        private Long customerId;
        private String customerFirstName;
        private String customerLastName;
        private Long subscriptionId;
        private String membershipName;
        // in milliseconds
        private Long purchasedTime;
    }
}
