package com.moego.lib.risk.control.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2023/9/14
 */
@Data
@ConfigurationProperties(prefix = RiskControlProperties.PROPERTIES_PREFIX)
public class RiskControlProperties {

    public static final String PROPERTIES_PREFIX = "moego.risk-control";

    private String name = "moego-svc-risk-control";

    private int port = 9090;
}
