// @since 2024-01-25 10:28:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "google/type/date.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/appointment_models.proto";
import "moego/models/appointment/v1/overview_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// get overview list request
message GetOverviewListRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // date
  string date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // key word
  optional string keyword = 3 [(validate.rules).string = {max_len: 50}];
  // service item type
  repeated models.offering.v1.ServiceItemType service_item_types = 4 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // date type
  models.appointment.v1.OverviewDateType date_type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // company id
  int64 company_id = 6 [(validate.rules).int64 = {gt: 0}];
}

// overview status entry
message OverviewStatusEntry {
  // status
  models.appointment.v1.OverviewStatus status = 1;
  // count
  int64 count = 2;
  // appointment overviews
  repeated moego.models.appointment.v1.AppointmentOverview appointment_overviews = 3;
}

// get overview list response
message GetOverviewListResponse {
  // entries
  repeated OverviewStatusEntry entries = 1;
}

// get overview list request
message ListOverviewAppointmentRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // date
  google.type.Date date = 3 [(validate.rules).message = {required: true}];
  // date type
  models.appointment.v1.OverviewDateType date_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // overview status
  models.appointment.v1.OverviewStatus overview_status = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // service item type, empty for not filtering
  repeated models.offering.v1.ServiceItemType service_item_types = 6 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // appointment status, empty for not filtering
  repeated models.appointment.v1.AppointmentStatus appointment_statuses = 7 [(validate.rules).repeated = {
    max_items: 6
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // customer ids, empty for not filtering
  repeated int64 customer_ids = 8 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// list overview appointment response
message ListOverviewAppointmentResponse {
  // appointment overviews
  repeated moego.models.appointment.v1.AppointmentOverview appointments = 1;
}

// the overview service
service OverviewService {
  // get overview list
  rpc GetOverviewList(GetOverviewListRequest) returns (GetOverviewListResponse);
  // list overview appointment
  rpc ListOverviewAppointment(ListOverviewAppointmentRequest) returns (ListOverviewAppointmentResponse);
}
