syntax = "proto3";

package moego.service.appointment.v1;

import "google/type/date.proto";
import "moego/models/appointment/v1/playgroup_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// list pet playgroup request
message ListPetPlaygroupRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // start date
  google.type.Date start_date = 3 [(validate.rules).message.required = true];
  // end date
  google.type.Date end_date = 4 [(validate.rules).message.required = true];
  // playgroup ids
  repeated int64 playgroup_ids = 5 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// list pet playgroup response
message ListPetPlaygroupResponse {
  // pet playgroup models
  repeated moego.models.appointment.v1.PetPlaygroupModel pet_playgroups = 1;
}

// reschedule pet playgroup request
message ReschedulePetPlaygroupRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64.gt = 0];
  // pet playgroup id
  int64 pet_playgroup_id = 4 [(validate.rules).int64.gt = 0];
  // target playgroup id
  int64 playgroup_id = 5 [(validate.rules).int64.gt = 0];
  // target date
  google.type.Date date = 6 [(validate.rules).message.required = true];
  // target index
  int32 index = 7 [(validate.rules).int32.gt = 0];
}

// reschedule pet playgroup response
message ReschedulePetPlaygroupResponse {}

// pet playgroup service
service PetPlaygroupService {
  // list pet playgroup
  rpc ListPetPlaygroup(ListPetPlaygroupRequest) returns (ListPetPlaygroupResponse) {}
  // reschedule pet playgroup
  rpc ReschedulePetPlaygroup(ReschedulePetPlaygroupRequest) returns (ReschedulePetPlaygroupResponse) {}
}
