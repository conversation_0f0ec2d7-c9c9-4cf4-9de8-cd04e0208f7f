syntax = "proto3";

package moego.service.message.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
//import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/message/v1/message_enums.proto";
import "moego/models/message/v1/schedule_message_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/message/v1;messagesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.message.v1";

// Create a schedule message for SMS type request
message CreateScheduleMessageRequest {
  // receipt company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // receipt business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // receipt customer id
  int64 customer_id = 3 [(validate.rules).int64.gt = 0];
  // send staff id
  int64 staff_id = 4 [(validate.rules).int64.gt = 0];
  // message content, such as custom content, auto message template content
  string content = 5 [(validate.rules).string = {max_len: *********}];
  // receipt customer's contact id, the auto message type defaults to primary contact phone number
  optional int64 receipt_contact_id = 6 [(validate.rules).int64 = {gt: 0}];
  // appointment id associated with auto message type
  optional int64 appointment_id = 7 [(validate.rules).int64.gt = 0];
  // auto message template type
  optional models.message.v1.AutoMessageType type = 8 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // send out at, preset sending time
  google.protobuf.Timestamp send_out_at = 9;
  // method, default is SMS
  models.message.v1.Method method = 10 [(validate.rules).enum = {defined_only: true}];
}

// Create a schedule message for SMS type response
message CreateScheduleMessageResponse {
  // schedule message id
  int64 schedule_message_id = 1 [(validate.rules).int64.gt = 0];
}

// Update a schedule message for SMS type request
message UpdateScheduleMessageRequest {
  // schedule message id
  int64 schedule_message_id = 1 [(validate.rules).int64.gt = 0];
  // send staff id
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];
  // custom content, the message manually entered by the user in the input box
  optional string content = 3 [(validate.rules).string = {max_len: *********}];
  // receipt customer's contact id
  optional int64 receipt_contact_id = 4 [(validate.rules).int64 = {gt: 0}];
  // send out at, preset sending time
  optional google.protobuf.Timestamp send_out_at = 5;
}

// Update a schedule message for SMS type response
message UpdateScheduleMessageResponse {}

// Delete a schedule message request
message DeleteScheduleMessageRequest {
  // schedule message id
  int64 schedule_message_id = 1 [(validate.rules).int64.gt = 0];
  // send staff id
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];
}

// Delete a schedule message response
message DeleteScheduleMessageResponse {}

// Delete all schedule messages associated with the appointment request
message DeleteAppointmentScheduleMessageRequest {
  // appointment id associated with auto message type
  int64 appointment_id = 1 [(validate.rules).int64.gt = 0];
  // send staff id
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];
}

// Delete all schedule messages associated with the appointment request
message DeleteAppointmentScheduleMessageResponse {
  // deleted count
  int64 deleted_count = 1;
}

// Get a schedule message request
message GetScheduleMessageRequest {
  // schedule message id
  int64 schedule_message_id = 1 [(validate.rules).int64.gt = 0];
}

// Get a schedule message response
message GetScheduleMessageResponse {
  // schedule message
  models.message.v1.ScheduleMessageModel schedule_message = 1;
}

// Get scheduled messages request
message GetScheduleMessagesRequest {
  // receipt company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // receipt business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // receipt customer ids
  repeated int64 customer_ids = 3 [(validate.rules).repeated = {
    min_items: 0
    items: {
      int64: {gt: 0}
    }
  }];
  // status filter
  repeated models.message.v1.ScheduleMessageStatus statuses = 4 [(validate.rules).repeated = {
    min_items: 0
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // appointment id associated with auto message type
  optional int64 appointment_id = 7 [(validate.rules).int64.gt = 0];
  // method filter
  repeated models.message.v1.Method methods = 8 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// Get scheduled messages response
message GetScheduleMessagesResponse {
  // schedule message list
  repeated models.message.v1.ScheduleMessageModel schedule_messages = 1;
}

// Send scheduled message request
message SendScheduleMessageRequest {
  // schedule message detail
  models.message.v1.ScheduleMessageModel schedule_message = 1 [(validate.rules).message = {required: true}];
  // send staff id
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];
}

// Send scheduled message response
message SendScheduleMessageResponse {}

// Used for SMS type scheduled task management
service ScheduleMessageService {
  // Create a schedule message
  rpc CreateScheduleMessage(CreateScheduleMessageRequest) returns (CreateScheduleMessageResponse);

  // Incremental update a schedule message
  rpc UpdateScheduledMessage(UpdateScheduleMessageRequest) returns (UpdateScheduleMessageResponse);

  // Delete a schedule message
  rpc DeleteScheduleMessage(DeleteScheduleMessageRequest) returns (DeleteScheduleMessageResponse);

  // Delete all schedule messages associated with the appointment
  rpc DeleteAppointmentScheduleMessages(DeleteAppointmentScheduleMessageRequest) returns (DeleteAppointmentScheduleMessageResponse);

  // Get a specific schedule message detail
  rpc GetScheduleMessage(GetScheduleMessageRequest) returns (GetScheduleMessageResponse);

  // Get the schedule message list of business or specific customers
  rpc GetScheduleMessages(GetScheduleMessagesRequest) returns (GetScheduleMessagesResponse);

  // Run the schedule message task
  rpc RunScheduleMessageTask(google.protobuf.Empty) returns (google.protobuf.Empty);

  // Send the schedule message now
  rpc SendScheduleMessage(SendScheduleMessageRequest) returns (SendScheduleMessageResponse);
}
