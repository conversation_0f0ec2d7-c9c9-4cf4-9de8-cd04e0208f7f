// @since 2024-06-18 10:56:24
// <AUTHOR> <z<PERSON><PERSON>@moego.pet>

syntax = "proto3";

package moego.service.organization.v1;

import "moego/models/organization/v1/tax_defs.proto";
import "moego/models/organization/v1/tax_models.proto";
import "moego/models/organization/v1/tenant.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.organization.v1";

// add tax rule request
message CreateTaxRuleRequest {
  // tax rule
  models.organization.v1.TaxRuleDef tax_rule = 1 [(validate.rules).message = {required: true}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 3 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // tenant id
  models.organization.v1.Tenant tenant = 4 [(validate.rules).message = {required: true}];
}

// add tax rule response
message CreateTaxRuleResponse {
  // tax rule id
  int64 id = 1;
}

// update tax rule request
message UpdateTaxRuleV2Request {
  // tax rule id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // tax rule
  models.organization.v1.TaxRuleDef tax_rule = 2 [(validate.rules).message = {required: true}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 4 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // tenant id
  models.organization.v1.Tenant tenant = 5 [(validate.rules).message = {required: true}];
}

// update tax rule response
message UpdateTaxRuleV2Response {
  // update result
  bool success = 1;
}

// delete tax rule request
message DeleteTaxRuleV2Request {
  // tax rule id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the operator id, allow internal modifier or external modifier
  oneof operator_identifier {
    option (validate.required) = true;
    // staff id from token
    int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
    // the internal operator id
    string internal_operator_id = 3 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
  // tenant id
  models.organization.v1.Tenant tenant = 5 [(validate.rules).message = {required: true}];
}

// delete tax rule response
message DeleteTaxRuleV2Response {
  // delete result
  bool success = 1;
}

// list tax rule request
message ListTaxRuleRequest {
  // tenant id
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];
}

// list tax rule response
message ListTaxRuleResponse {
  // tax rule list
  repeated models.organization.v1.TaxRuleModel rules = 1;
}

// get tax rule request
message GetTaxRuleRequest {
  // tax rule id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // Tenant ID. If it cannot be provided, the caller is responsible for verifying the data ownership.
  optional models.organization.v1.Tenant tenant = 2;
}

// get tax rule response
message GetTaxRuleResponse {
  // tax rule
  models.organization.v1.TaxRuleModel rule = 1;
}

// batch get tax rule request
message BatchGetTaxRuleRequest {
  // tax rule id
  repeated int64 ids = 1;
}

// batch get tax rule response
message BatchGetTaxRuleResponse {
  // tax rules
  repeated models.organization.v1.TaxRuleModel rules = 1;
}

// the tax service
service TaxRuleService {
  // add tax rule
  rpc CreateTaxRule(CreateTaxRuleRequest) returns (CreateTaxRuleResponse);
  // update tax rule
  rpc UpdateTaxRuleV2(UpdateTaxRuleV2Request) returns (UpdateTaxRuleV2Response);
  // delete tax rule
  rpc DeleteTaxRuleV2(DeleteTaxRuleV2Request) returns (DeleteTaxRuleV2Response);
  // list tax rule
  rpc ListTaxRule(ListTaxRuleRequest) returns (ListTaxRuleResponse);
  // get tax rule
  rpc GetTaxRule(GetTaxRuleRequest) returns (GetTaxRuleResponse);
  // get tax rule
  rpc BatchGetTaxRule(BatchGetTaxRuleRequest) returns (BatchGetTaxRuleResponse);
}
