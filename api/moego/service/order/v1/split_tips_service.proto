syntax = "proto3";

package moego.service.order.v1;

import "google/type/decimal.proto";
import "moego/models/order/v1/split_tips_enum.proto";
import "moego/models/order/v1/split_tips_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v1";

// get split tips request
message GetSplitTipsInput {
  // business id
  int64 business_id = 1;
  // order id
  int64 order_id = 2;
}

// get split tips response
message GetSplitTipsOutput {
  // if exist record for the order
  bool exist = 1;
  // split tips config record
  optional moego.models.order.v1.SplitTipsRecord split_tips_record = 2;
}

// get split tips list request
message GetSplitTipsListInput {
  // business id
  int64 business_id = 1;
  // order id list
  repeated int64 order_id = 2;
  // business id list
  repeated int64 business_ids = 3;
}

// get split tips list response
message GetSplitTipsListOutput {
  // split tips config record list
  repeated moego.models.order.v1.SplitTipsRecord split_tips_record = 1;
}

// customized tip config
message CustomizedTipConfigInput {
  // staff id
  int64 staff_id = 1;
  // tips amount
  optional double amount = 2 [(validate.rules).double = {gte: 0}];
  // tips percentage
  optional int32 percentage = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 100
  }];
}

// save split tips request
message SaveSplitTipsInput {
  // business id
  int64 business_id = 1;
  // order id
  int64 order_id = 2;
  // tip split method
  optional moego.models.order.v1.SplitTipsMethod split_method = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // customized type: amount/percentage
  optional moego.models.order.v1.CustomizedTipType customized_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // customized tip config list
  repeated CustomizedTipConfigInput customized_config = 5;
  // apply by
  optional int64 apply_by = 6;
  // delete
  optional bool is_deleted = 7;
  // tips to business.
  optional google.type.Decimal business_tip_amount = 8;
}

// save split tips response
message SaveSplitTipsOutput {
  // if exist record for the order
  bool success = 1;
}

// split tips internal api
service SplitTipsService {
  // get split tips info
  rpc GetSplitTipsRecord(GetSplitTipsInput) returns (GetSplitTipsOutput);
  // get split tips info list
  rpc GetSplitTipsListRecord(GetSplitTipsListInput) returns (GetSplitTipsListOutput);
  // save split tips
  rpc SaveSplitTipsRecord(SaveSplitTipsInput) returns (SaveSplitTipsOutput);
}
