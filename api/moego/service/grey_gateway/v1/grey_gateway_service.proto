// @since 2022/5/31 3:49 PM
// <AUTHOR>

syntax = "proto3";

package moego.service.grey_gateway.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/wrappers.proto";
import "moego/models/grey_gateway/v1/grey_gateway_models.proto";
import "moego/utils/v1/id_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/grey_gateway/v1;greygatewaysvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.grey_gateway.v1";

// GetServiceBranchMapResponse
message GetServiceBranchMapResponse {
  // branch list
  message BranchList {
    // branches
    repeated string name = 1;
  }
  // moego-service-mesage -> ["feature-a", "bugfix-b"]
  map<string, BranchList> svc_branches_map = 1;
}

// GetServiceBranchMapRequest
message GetServiceBranchMapRequest {
  // k8s namespace
  string namespace = 1 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 30
  }];
}

// list grey item
message GetGreyItemListRequest {
  // k8s namespace
  string namespace = 1 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 30
  }];
}

// insert
message InsertGreyItemRequest {
  // item version, the format is "MMddxxx" (e.g. 0501001)
  string name = 2 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 30
  }];
  // kubernetes namespace
  string namespace = 3 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 30
  }];
  // description
  google.protobuf.StringValue description = 5;
  // jira tickets
  repeated string jira_tickets = 10 [(validate.rules).repeated = {
    ignore_empty: true
    min_items: 0
  }];
  // service branch map
  map<string, string> svc_branch_map = 15 [(validate.rules).map = {
    ignore_empty: true
    min_pairs: 1
  }];
}

// update
message UpdateGreyItemRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // item version, the format is "MMddxxx" (e.g. 0501001)
  string name = 2 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 30
  }];
  // description (nullable)
  google.protobuf.StringValue description = 5;
  // jira tickets
  repeated string jira_tickets = 10 [(validate.rules).repeated = {
    ignore_empty: true
    min_items: 0
  }];
  // service branch map
  map<string, string> svc_branch_map = 15 [(validate.rules).map = {
    ignore_empty: true
    min_pairs: 0
  }];
}

// grey item list response
message GetGreyItemListResponse {
  // grey item list
  repeated moego.models.grey_gateway.v1.GreyItemModel items = 1;
}

// GetGreyItemByName request
message GetGreyItemByNameRequest {
  // grey item name
  string name = 1 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 30
  }];
}

// GreyGateway service
service GreyGatewayService {
  // Get services branches mapping
  rpc GetServiceBranchMap(GetServiceBranchMapRequest) returns (GetServiceBranchMapResponse);

  // list grey items
  rpc GetGreyItemList(GetGreyItemListRequest) returns (GetGreyItemListResponse);

  // get grey item
  rpc GetGreyItem(moego.utils.v1.Id) returns (moego.models.grey_gateway.v1.GreyItemModel);

  // get grey item by name
  rpc GetGreyItemByName(GetGreyItemByNameRequest) returns (moego.models.grey_gateway.v1.GreyItemModel);

  // insert grey item
  rpc InsertGreyItem(InsertGreyItemRequest) returns (moego.models.grey_gateway.v1.GreyItemModel);

  // update grey item
  rpc UpdateGreyItem(UpdateGreyItemRequest) returns (moego.models.grey_gateway.v1.GreyItemModel);

  // delete grey item
  rpc DeleteGreyItem(moego.utils.v1.Id) returns (google.protobuf.Empty);
}
