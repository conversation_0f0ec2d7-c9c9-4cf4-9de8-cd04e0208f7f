syntax = "proto3";

package moego.service.enterprise.v1;

import "moego/models/enterprise/v1/staff_defs.proto";
import "moego/models/enterprise/v1/staff_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.enterprise.v1";

// list staff access tenant
message ListStaffsRequest {
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 1;
  // enterprise id
  int64 enterprise_id = 2 [(validate.rules).int64 = {gt: 0}];
  // order by
  optional moego.utils.v2.OrderBy order_by = 3;
  // filter
  message Filter {
    // ids
    repeated int64 ids = 1;
    // keyword
    optional string keyword = 2;
    // role id
    repeated int64 role_ids = 3 [(validate.rules).repeated = {
      max_items: 1000
      unique: true
    }];
  }
  // filter
  optional Filter filter = 4;
}

// list staff access tenant response
message ListStaffsResponse {
  // staff access
  repeated moego.models.enterprise.v1.StaffModel staffs = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// create enterprise staff
message CreateStaffRequest {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // enterprise id
  int64 enterprise_id = 2 [(validate.rules).int64.gt = 0];
  // staff profile
  models.enterprise.v1.CreateStaffProfile profile = 3;
}

// create enterprise staff response
message CreateStaffResponse {
  // staff
  moego.models.enterprise.v1.StaffModel staff = 1;
}

// update  staff
message UpdateStaffRequest {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // staff profile
  optional models.enterprise.v1.UpdateStaffProfile profile = 2;
}

// update  staff response
message UpdateStaffResponse {
  // staff
  moego.models.enterprise.v1.StaffModel staff = 1;
}

// delete enterprise staff
message DeleteStaffRequest {
  // staff id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // enterprise id
  int64 enterprise_id = 2;
}

// delete enterprise staff response
message DeleteStaffResponse {}

// staff access service
service StaffService {
  // list staff access tenant
  rpc ListStaffs(ListStaffsRequest) returns (ListStaffsResponse);
  // create  staff
  rpc CreateStaff(CreateStaffRequest) returns (CreateStaffResponse);
  // update  staff
  rpc UpdateStaff(UpdateStaffRequest) returns (UpdateStaffResponse);
  // delete  staff
  rpc DeleteStaff(DeleteStaffRequest) returns (DeleteStaffResponse);
}
