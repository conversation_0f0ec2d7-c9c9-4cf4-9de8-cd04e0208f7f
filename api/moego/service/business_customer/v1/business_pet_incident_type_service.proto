syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_incident_type_defs.proto";
import "moego/models/business_customer/v1/business_pet_incident_type_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get pet incident type request
message GetPetIncidentTypeRequest {
  // pet incident type id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
}

// get pet incident type response
message GetPetIncidentTypeResponse {
  // pet incident type
  moego.models.business_customer.v1.BusinessPetIncidentTypeModel incident_type = 1;
}

// list pet incident type request
message ListPetIncidentTypeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // include deleted pet incident types
  optional bool is_include_deleted = 2;
}

// list pet incident type response
message ListPetIncidentTypeResponse {
  // pet incident type list
  repeated moego.models.business_customer.v1.BusinessPetIncidentTypeModel incident_types = 1;
}

// create pet incident type request
message CreatePetIncidentTypeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];

  // pet incident type
  moego.models.business_customer.v1.BusinessPetIncidentTypeCreateDef incident_type = 2 [(validate.rules).message.required = true];
}

// create pet incident type response
message CreatePetIncidentTypeResponse {
  // pet incident type
  moego.models.business_customer.v1.BusinessPetIncidentTypeModel incident_type = 1;
}

// update pet incident type request
message UpdatePetIncidentTypeRequest {
  // pet incident type id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];

  // pet incident type
  moego.models.business_customer.v1.BusinessPetIncidentTypeUpdateDef incident_type = 3 [(validate.rules).message.required = true];
}

// update pet incident type response
message UpdatePetIncidentTypeResponse {}

// sort pet incident type request
message SortPetIncidentTypeRequest {
  // pet incident type id list, should contain all pet incident type ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
}

// sort pet incident type response
message SortPetIncidentTypeResponse {}

// delete pet incident type request
message DeletePetIncidentTypeRequest {
  // pet incident type id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
}

// delete pet incident type response
message DeletePetIncidentTypeResponse {}

// Service for pet incident type settings
service BusinessPetIncidentTypeService {
  // Get a pet incident type.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet incident type does not exist, or does not belong to the given company or business.
  rpc GetPetIncidentType(GetPetIncidentTypeRequest) returns (GetPetIncidentTypeResponse);

  // List pet incident types.
  // If the company does not exists, or does not define any pet incident types, an empty list will be returned rather than an error.
  rpc ListPetIncidentType(ListPetIncidentTypeRequest) returns (ListPetIncidentTypeResponse);

  // Create a pet incident type.
  // The name of the new pet incident type must be unique among all pet incident types of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another pet incident type of the company or business.
  rpc CreatePetIncidentType(CreatePetIncidentTypeRequest) returns (CreatePetIncidentTypeResponse);

  // Update a pet incident type.
  // If the name of the pet incident type is changed, it must be unique among all pet incident types of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another pet incident type of the company or business, or the pet
  //                      incident type does not exist, or has been deleted, or does not belong to the company or business.
  rpc UpdatePetIncidentType(UpdatePetIncidentTypeRequest) returns (UpdatePetIncidentTypeResponse);

  // Sort pet incident types of the company or business.
  // Pet incident types will be sorted according to the order of `ids`. If there are pet incident types of the company or business
  // whose ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not
  // belong to the company or business, it will be ignored.
  rpc SortPetIncidentType(SortPetIncidentTypeRequest) returns (SortPetIncidentTypeResponse);

  // Delete a pet incident type.
  // If the pet incident type is already deleted, it will return success without throwing any error.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet incident type does not exist, or does not belong to the given company.
  rpc DeletePetIncidentType(DeletePetIncidentTypeRequest) returns (DeletePetIncidentTypeResponse);
}
