syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_tag_defs.proto";
import "moego/models/business_customer/v1/business_customer_tag_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get customer tag request
message GetCustomerTagRequest {
  // tag id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// get customer tag response
message GetCustomerTagResponse {
  // tag
  moego.models.business_customer.v1.BusinessCustomerTagModel tag = 1;
}

// list customer tag request
message ListCustomerTagRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// list customer tag response
message ListCustomerTagResponse {
  // customer tag list
  repeated moego.models.business_customer.v1.BusinessCustomerTagModel tags = 1;
}

// list customer tag template request
message ListCustomerTagTemplateRequest {}

// list customer tag template response
message ListCustomerTagTemplateResponse {
  // customer tag list
  repeated moego.models.business_customer.v1.BusinessCustomerTagModel tags = 1;
}

// create customer tag request
message CreateCustomerTagRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];

  // tag
  moego.models.business_customer.v1.BusinessCustomerTagCreateDef tag = 3 [(validate.rules).message.required = true];
}

// create customer tag response
message CreateCustomerTagResponse {
  // tag
  moego.models.business_customer.v1.BusinessCustomerTagModel tag = 1;
}

// update customer tag request
message UpdateCustomerTagRequest {
  // tag id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];

  // tag
  moego.models.business_customer.v1.BusinessCustomerTagUpdateDef tag = 4 [(validate.rules).message.required = true];
}

// update customer tag response
message UpdateCustomerTagResponse {}

// sort customer tag request
message SortCustomerTagRequest {
  // tag id list, should contain all tag ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];

  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// sort customer tag response
message SortCustomerTagResponse {}

// delete customer tag request
message DeleteCustomerTagRequest {
  // tag id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// delete customer tag response
message DeleteCustomerTagResponse {}

// list binding customer tag request
message ListBindingCustomerTagRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // customer id
  int64 customer_id = 3 [(validate.rules).int64.gt = 0];
}

// list binding customer tag response
message ListBindingCustomerTagResponse {
  // customer tag list
  repeated moego.models.business_customer.v1.BusinessCustomerTagModel tags = 1;
}

// batch list binding customer tag request
message BatchListBindingCustomerTagRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // customer id list
  repeated int64 customer_ids = 3 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch list binding customer tag response
message BatchListBindingCustomerTagResponse {
  // customer tag bindings
  repeated moego.models.business_customer.v1.BusinessCustomerTagBindingModel bindings = 1;

  // customer tags
  repeated moego.models.business_customer.v1.BusinessCustomerTagModel tags = 2;
}

// Service for customer tag settings
service BusinessCustomerTagService {
  // Get a customer tag.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The tag does not exist, or does not belong to the given company or business.
  rpc GetCustomerTag(GetCustomerTagRequest) returns (GetCustomerTagResponse);

  // List customer tags.
  // If `customer_id` is set, only customer tags associated with the customer will be returned.
  // If the company does not exists, or does not define any customer tags, an empty list will be returned rather than an error.
  rpc ListCustomerTag(ListCustomerTagRequest) returns (ListCustomerTagResponse);

  // List customer tag templates.
  rpc ListCustomerTagTemplate(ListCustomerTagTemplateRequest) returns (ListCustomerTagTemplateResponse);

  // Create a customer tag.
  // The name of the new tag must be unique among all tags of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another tag of the company or business.
  rpc CreateCustomerTag(CreateCustomerTagRequest) returns (CreateCustomerTagResponse);

  // Update a customer tag.
  // If the name of the tag is changed, it must be unique among all tag of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another tag of the company or business, or the
  //                      tag does not exist, or has been deleted, or does not belong to the company or business.
  rpc UpdateCustomerTag(UpdateCustomerTagRequest) returns (UpdateCustomerTagResponse);

  // Sort tags of the company or business.
  // tags will be sorted according to the order of `ids`. If there are tags of the company or business
  // whose ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not
  // belong to the company or business, it will be ignored.
  rpc SortCustomerTag(SortCustomerTagRequest) returns (SortCustomerTagResponse);

  // Delete a customer tag.
  // If the tag is already deleted, it will return success without throwing any error.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The tag does not exist, or does not belong to the given company.
  rpc DeleteCustomerTag(DeleteCustomerTagRequest) returns (DeleteCustomerTagResponse);

  // List customer tags binding to a customer.
  rpc ListBindingCustomerTag(ListBindingCustomerTagRequest) returns (ListBindingCustomerTagResponse);

  // List customer tags binding to several customers.
  rpc BatchListBindingCustomerTag(BatchListBindingCustomerTagRequest) returns (BatchListBindingCustomerTagResponse);
}
