syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_vaccine_defs.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get pet vaccine request
message GetPetVaccineRequest {
  // pet vaccine id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// get pet vaccine response
message GetPetVaccineResponse {
  // pet vaccine
  moego.models.business_customer.v1.BusinessPetVaccineModel vaccine = 1;
}

// list pet vaccine template request
message ListPetVaccineTemplateRequest {}

// list pet vaccine template response
message ListPetVaccineTemplateResponse {
  // pet vaccine list
  repeated moego.models.business_customer.v1.BusinessPetVaccineModel vaccines = 1;
}

// list pet vaccine request
message ListPetVaccineRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// list pet vaccine response
message ListPetVaccineResponse {
  // pet vaccine list
  repeated moego.models.business_customer.v1.BusinessPetVaccineModel vaccines = 1;
}

// create pet vaccine request
message CreatePetVaccineRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // pet vaccine
  moego.models.business_customer.v1.BusinessPetVaccineCreateDef vaccine = 3 [(validate.rules).message.required = true];
}

// create pet vaccine response
message CreatePetVaccineResponse {
  // pet vaccine
  moego.models.business_customer.v1.BusinessPetVaccineModel vaccine = 1;
}

// update pet vaccine request
message UpdatePetVaccineRequest {
  // pet vaccine id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
  // pet vaccine
  moego.models.business_customer.v1.BusinessPetVaccineUpdateDef vaccine = 4 [(validate.rules).message.required = true];
}

// update pet vaccine response
message UpdatePetVaccineResponse {}

// sort pet vaccine request
message SortPetVaccineRequest {
  // pet vaccine id list, should contain all pet vaccine ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];

  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// sort pet vaccine response
message SortPetVaccineResponse {}

// delete pet vaccine request
message DeletePetVaccineRequest {
  // pet vaccine id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// delete pet vaccine response
message DeletePetVaccineResponse {}

// Service for pet vaccine settings
service BusinessPetVaccineService {
  // Get a pet vaccine.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet vaccine does not exist, or does not belong to the given company or business.
  rpc GetPetVaccine(GetPetVaccineRequest) returns (GetPetVaccineResponse);

  // List pet vaccine template.
  // A list of vaccines defined by MoeGo will be returned.
  rpc ListPetVaccineTemplate(ListPetVaccineTemplateRequest) returns (ListPetVaccineTemplateResponse);

  // List pet vaccines.
  // If the company does not exists, or does not define any pet vaccines, an empty list will be returned rather than an error.
  rpc ListPetVaccine(ListPetVaccineRequest) returns (ListPetVaccineResponse);

  // Create a pet vaccine.
  // The name of the new pet vaccine must be unique among all pet vaccines of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another pet vaccine of the company or business.
  rpc CreatePetVaccine(CreatePetVaccineRequest) returns (CreatePetVaccineResponse);

  // Update a pet vaccine.
  // If the name of the pet vaccine is changed, it must be unique among all pet coat types of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another pet vaccine of the company or business, or the pet vaccine
  //                      does not exist, or has been deleted, or does not belong to the company or business.
  rpc UpdatePetVaccine(UpdatePetVaccineRequest) returns (UpdatePetVaccineResponse);

  // Sort pet vaccines of the company or business.
  // Pet vaccines will be sorted according to the order of `ids`. If there are vaccines of the company or business whose
  // ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not belong
  // to the company or business, it will be ignored.
  rpc SortPetVaccine(SortPetVaccineRequest) returns (SortPetVaccineResponse);

  // Delete pet vaccine.
  // If the pet vaccine is already deleted, will return success without throwing any error.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet vaccine does not exist, or does not belong to the given company.
  rpc DeletePetVaccine(DeletePetVaccineRequest) returns (DeletePetVaccineResponse);
}
