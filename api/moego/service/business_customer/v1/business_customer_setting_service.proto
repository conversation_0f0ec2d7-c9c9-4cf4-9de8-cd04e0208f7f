// @since 2024-08-05 15:34:20
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_setting_defs.proto";
import "moego/models/business_customer/v1/business_customer_setting_models.proto";
import "moego/models/organization/v1/tenant.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get customer setting request
message GetCustomerSettingRequest {
  // tenant (company)
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];
}

// get customer setting response
message GetCustomerSettingResponse {
  // setting
  moego.models.business_customer.v1.BusinessCustomerSettingModel setting = 1;
}

// update customer creation setting request
message UpdateCustomerCreationSettingRequest {
  // tenant (company)
  models.organization.v1.Tenant tenant = 1 [(validate.rules).message = {required: true}];

  // creation setting
  moego.models.business_customer.v1.BusinessCustomerCreationSettingUpdateDef creation_setting = 2 [(validate.rules).message = {required: true}];
}

// update customer creation setting response
message UpdateCustomerCreationSettingResponse {}

// business customer setting service
service BusinessCustomerSettingService {
  // get all customer settings
  rpc GetCustomerSetting(GetCustomerSettingRequest) returns (GetCustomerSettingResponse);

  // update customer creation setting
  rpc UpdateCustomerCreationSetting(UpdateCustomerCreationSettingRequest) returns (UpdateCustomerCreationSettingResponse);

  // update customer xxx setting
}
