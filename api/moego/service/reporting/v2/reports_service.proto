syntax = "proto3";

package moego.service.reporting.v2;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/type/calendar_period.proto";
import "google/type/interval.proto";
import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/diagram_model.proto";
import "moego/models/reporting/v2/report_def.proto";
import "moego/models/reporting/v2/report_meta_def.proto";
import "moego/models/reporting/v2/report_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reporting/v2;reportingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.reporting.v2";

// ReportService is the service for reports
service ReportService {
  // Query report pages returns a list of report pages
  rpc QueryReportPages(QueryReportPagesParams) returns (QueryReportPagesResult);
  // Mark report favorite marks/removes a report as favorite, return the reports with new sequence
  rpc MarkReportFavorite(MarkReportFavoriteParams) returns (MarkReportFavoriteResult);
  // Save report customized config
  rpc SaveReportCustomizeConfig(SaveReportCustomizeConfigParams) returns (google.protobuf.Empty);
  // Query metadata of reports
  rpc QueryReportMetas(QueryReportMetasParams) returns (QueryReportsMetasResult);
  // Fetch report data
  rpc FetchReportData(FetchReportDataParams) returns (FetchReportDataResult);
  // Export report data
  rpc ExportReportData(ExportReportDataParams) returns (ExportReportDataResult);

  // Common page api
  rpc QueryPages(QueryPageMetaRequest) returns (QueryPageMetaResponse);
  // Common meta api
  rpc QueryMetas(QueryMetasRequest) returns (QueryMetasResponse);
  // Common fetch data api
  rpc FetchData(FetchDataRequest) returns (FetchDataResponse);
  // Common export data api
  rpc ExportData(ExportDataRequest) returns (ExportDataResponse);
}

// QueryReportPagesParams
message QueryReportPagesParams {
  // The tabs to query reports page
  repeated moego.models.reporting.v2.ReportPage.Tab tabs = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // token info: company id and staff id
  moego.models.reporting.v2.TokenInfo token_info = 2;
  // 区分不同的 reporting 场景
  moego.models.reporting.v2.ReportingScene reporting_type = 3;
}

// QueryReportPagesResult
message QueryReportPagesResult {
  // The list of report pages
  repeated moego.models.reporting.v2.ReportPage pages = 1;
  // Report data last synced time
  google.protobuf.Timestamp last_synced_time = 2;
}

// MarkReportFavoriteParams
message MarkReportFavoriteParams {
  // The favorite action enum
  enum Action {
    // Unspecified favorite actions
    FAVORITE_ACTION_UNSPECIFIED = 0;
    // A favorite action to add a report to favorites
    ADD = 1;
    // A favorite action to remove a report from favorites
    REMOVE = 2;
  }

  // The report to mark as favorite
  string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // The action to take
  Action action = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // token info: company id and staff id
  moego.models.reporting.v2.TokenInfo token_info = 3;
  // 区分不同的 reporting 场景
  moego.models.reporting.v2.ReportingScene reporting_type = 4;
}

// MarkReportFavoriteResponse
message MarkReportFavoriteResult {
  // Mark result
  bool result = 1;
}

// SaveReportCustomizeConfigRequest
message SaveReportCustomizeConfigParams {
  // The report to save customized configs
  string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // Customize configs to save
  moego.models.reporting.v2.TableCustomizedConfig customized_config = 2;
  // token info: company id and staff id
  moego.models.reporting.v2.TokenInfo token_info = 3;
  // 区分不同的 reporting 场景
  moego.models.reporting.v2.ReportingScene reporting_type = 4;
}

// QueryReportsMetaRequest
message QueryReportMetasParams {
  // The report to query meta data, if empty, return all reports' meta data
  repeated string diagram_ids = 1 [(validate.rules).repeated.items.string = {
    min_len: 1
    max_len: 100
  }];
  // token info: company id and staff id
  moego.models.reporting.v2.TokenInfo token_info = 2;
  // 区分不同的 reporting 场景
  moego.models.reporting.v2.ReportingScene reporting_type = 3;
  // The tenant id
  repeated uint64 tenants_ids = 4 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
}

// QueryReportsMetaRequest
message QueryReportsMetasResult {
  // The list of report metadata
  repeated moego.models.reporting.v2.TableMeta report_metas = 1;
}

// Describe a request to fetch report data
message FetchReportDataParams {
  // diagram id
  string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // The business id
  repeated uint64 business_ids = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // current period
  google.type.Interval current_period = 3 [(validate.rules).message = {required: true}];
  // previous period
  optional google.type.Interval previous_period = 4;
  // Filters
  repeated moego.models.reporting.v2.FilterRequest filters = 5;
  // The group by field key
  repeated string group_by_field_keys = 6;
  // The pagination request
  moego.utils.v2.PaginationRequest pagination = 7;
  // The order by config
  repeated moego.utils.v2.OrderBy order_bys = 8;
  // token info: company id and staff id
  moego.models.reporting.v2.TokenInfo token_info = 9;
  // The group by period, could be day, week, month, year etc., default day.
  optional google.type.CalendarPeriod group_by_period = 10;
  // The filter groups
  repeated moego.models.reporting.v2.FilterRequestGroup filter_groups = 11;
  // 区分不同的 reporting 场景
  moego.models.reporting.v2.ReportingScene reporting_type = 12;
  // The tenant id
  repeated uint64 tenants_ids = 13 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
}

// Describe a response to fetch report data
message FetchReportDataResult {
  // The report data
  moego.models.reporting.v2.TableData table_data = 1;
  // The pagination response
  moego.utils.v2.PaginationResponse pagination = 2;
  // Report data last synced time
  google.protobuf.Timestamp last_synced_time = 3;
}

// ExportReportDataParams
message ExportReportDataParams {
  // diagram id
  string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // Filters
  repeated moego.models.reporting.v2.FilterRequest filters = 2;
  // token info: company id and staff id
  moego.models.reporting.v2.TokenInfo token_info = 3;
  // business ids
  repeated uint64 business_ids = 4 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // current period
  google.type.Interval current_period = 5 [(validate.rules).message = {required: true}];
  // previous period
  optional google.type.Interval previous_period = 6;
  // group by field key
  repeated string group_by_field_keys = 7;
  // order by params
  repeated moego.utils.v2.OrderBy order_bys = 8;
  // The group by period, could be day, week, month, year etc., default day.
  optional google.type.CalendarPeriod group_by_period = 9;
  // 区分不同的 reporting 场景
  moego.models.reporting.v2.ReportingScene reporting_type = 10;
  // tenant ids
  repeated uint64 tenants_ids = 11 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // filter groups
  repeated moego.models.reporting.v2.FilterRequestGroup filter_groups = 12;
  // is all tenants
  optional bool all_tenants = 13;
}

// ExportReportDataResult
message ExportReportDataResult {
  // file id
  int64 file_id = 1;
}

// Query page meta request definition
message QueryPageMetaRequest {
  // tabs to query
  repeated moego.models.reporting.v2.InsightsTab tabs = 1;
  // reporting scene
  moego.models.reporting.v2.ReportingScene scene = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Query page meta response definition
message QueryPageMetaResponse {
  // pages
  repeated moego.models.reporting.v2.PageMetaDef pages = 1;
  // last synced time
  google.protobuf.Timestamp last_synced_time = 2;
}

// QueryMetasRequest
message QueryMetasRequest {
  // diagram_ids to query
  repeated string diagram_ids = 1 [(validate.rules).repeated.items.string = {
    min_len: 1
    max_len: 100
  }];
  // reporting scene for current query
  moego.models.reporting.v2.ReportingScene scene = 2;
  // scope filter
  moego.models.reporting.v2.ScopeFilter scope = 3;
}

// QueryMetasResponse
message QueryMetasResponse {
  // meta result
  repeated moego.models.reporting.v2.DiagramMeta metas = 1;
}

// FetchDataRequest
message FetchDataRequest {
  // diagram id
  string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // query scope: business id or all businesses
  moego.models.reporting.v2.ScopeFilter scope = 2;
  // query time
  optional moego.models.reporting.v2.TimeFilter time_range = 3;
  // pagination params
  optional moego.utils.v2.PaginationRequest pagination = 4;
  // order by params
  repeated moego.utils.v2.OrderBy order_bys = 5;
  // filter params
  repeated moego.models.reporting.v2.FilterRequest filters = 6;
  // dimension filter
  optional moego.models.reporting.v2.DimensionFilter dimension = 7;
  // reporting scene for current query
  moego.models.reporting.v2.ReportingScene scene = 8;
  // metrics field keys params
  repeated string metric_keys = 9;
  // dynamic column mode, use final dimension to generate columns
  bool dynamic_column_mode = 10;
}

// FetchDataResponse
message FetchDataResponse {
  // result
  moego.models.reporting.v2.FetchDataDef data = 1;
  // report data last synced time
  optional google.protobuf.Timestamp last_synced_time = 2;
}

// ExportDataRequest
message ExportDataRequest {
  // diagram id
  string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // query scope: business id or all businesses
  moego.models.reporting.v2.ScopeFilter scope = 2;
  // query time
  optional moego.models.reporting.v2.TimeFilter time_range = 3;
  // order by params
  repeated moego.utils.v2.OrderBy order_bys = 4;
  // filter params
  repeated moego.models.reporting.v2.FilterRequest filters = 5;
  // dimension filter
  optional moego.models.reporting.v2.DimensionFilter dimension = 6;
  // reporting scene for current query
  moego.models.reporting.v2.ReportingScene scene = 7;
}

// ExportDataResponse
message ExportDataResponse {
  // file id
  int64 file_id = 1;
}
