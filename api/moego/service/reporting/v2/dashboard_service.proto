syntax = "proto3";

package moego.service.reporting.v2;

import "google/protobuf/timestamp.proto";
import "google/type/interval.proto";
import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/dashboard_model.proto";
import "moego/models/reporting/v2/diagram_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reporting/v2;reportingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.reporting.v2";

// DashboardService is the service for reporting dashboards
service DashboardService {
  // QueryDashboardPages returns pages of dashboard
  rpc QueryDashboardPages(QueryDashboardPagesParams) returns (QueryDashboardPagesResult);
  // FetchDashboardData fetches dashboard data
  rpc FetchDashboardData(FetchDashboardDataParams) returns (FetchDashboardDataResult);
}

// Describe pages of dashboard
message QueryDashboardPagesParams {
  // The tabs of query dashboard page
  repeated moego.models.reporting.v2.DashboardPage.Tab tabs = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // 区分不同的 reporting 场景
  moego.models.reporting.v2.ReportingScene reporting_type = 2;
  // The tenant id
  repeated uint64 tenants_ids = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
}

// Describe pages of dashboard
message QueryDashboardPagesResult {
  // The list of dashboard pages
  repeated moego.models.reporting.v2.DashboardPage dashboard_pages = 1;
  // The customized config
  moego.models.reporting.v2.TableCustomizedConfig customized_config = 2;
}

// Describe a request to fetch dashboard data
message FetchDashboardDataParams {
  // diagram ids
  repeated string diagram_ids = 1 [(validate.rules).repeated.items.string = {
    min_len: 1
    max_len: 100
  }];
  // The business id
  repeated uint64 business_ids = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // current interval
  google.type.Interval current_period = 3 [(validate.rules).message = {required: true}];
  // previous interval
  optional google.type.Interval previous_period = 4;
  // 区分不同的 reporting 场景
  moego.models.reporting.v2.ReportingScene reporting_type = 5;
  // The tenant id
  repeated uint64 tenants_ids = 6 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // group by field key
  repeated string group_by_field_keys = 7;
  // Filters
  repeated moego.models.reporting.v2.FilterRequest filters = 8;
}

// Describe a response to fetch dashboard diagram data
message FetchDashboardDataResult {
  // The dashboard diagram data
  repeated moego.models.reporting.v2.DiagramData diagram_data = 1;
  // Report data last synced time
  google.protobuf.Timestamp last_synced_time = 2;
}
