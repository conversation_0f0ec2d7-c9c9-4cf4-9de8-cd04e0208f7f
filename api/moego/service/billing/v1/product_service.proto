// @since 2024-06-06
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.billing.v1;

import "google/type/money.proto";
import "moego/models/billing/v1/product_models.proto";
import "moego/utils/v1/time_period.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/billing/v1;billingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.billing.v1";

// get product by slug request
message GetProductParams {
  // product relation id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get product response
message GetProductResponse {
  // product object
  models.billing.v1.ProductModel product = 1;
}

// create product request
message CreateProductRequest {
  // product name
  string name = 1 [(validate.rules).string = {max_len: 255}];
  // product description
  string description = 2 [(validate.rules).string = {max_len: 1500}];
}

// create product response
message CreateProductResponse {
  // product object
  models.billing.v1.ProductModel product = 1;
}

// update product request
message UpdateProductRequest {
  // product relation id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // product name
  string name = 2 [(validate.rules).string = {max_len: 255}];
  // product description
  string description = 3 [(validate.rules).string = {max_len: 1500}];
}

// update product response
message UpdateProductResponse {
  // product object
  models.billing.v1.ProductModel product = 1;
}

// create price request
message CreatePriceRequest {
  // product relation id
  int64 product_id = 1 [(validate.rules).int64 = {gt: 0}];
  // price amount with units
  google.type.Money unit_amount = 2;
  // one off price if null
  utils.v1.TimePeriod recurring = 3;
}

// create price response
message CreatePriceResponse {
  // price object
  models.billing.v1.PriceModel price = 1;
}

// billing product service
service ProductService {
  // get product by id
  rpc GetProduct(GetProductParams) returns (GetProductResponse);
  // create product
  rpc CreateProduct(CreateProductRequest) returns (CreateProductResponse);
  // update product
  rpc UpdateProduct(UpdateProductRequest) returns (UpdateProductResponse);
  // create price
  rpc CreatePrice(CreatePriceRequest) returns (CreatePriceResponse);
}
