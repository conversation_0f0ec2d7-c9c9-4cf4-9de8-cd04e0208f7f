syntax = "proto3";

package moego.service.smart_scheduler.v1;

import "moego/models/smart_scheduler/v1/smart_schedule_setting_defs.proto";
import "moego/models/smart_scheduler/v1/smart_schedule_setting_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/smart_scheduler/v1;smartschedulersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.smart_scheduler.v1";

// get smart schedule setting request
message GetSmartScheduleSettingRequest {
  // company id
  int64 token_company_id = 1 [(validate.rules).int64.gt = 0];
}

// get smart schedule setting response
message GetSmartScheduleSettingResponse {
  // smart schedule setting
  models.smart_scheduler.v1.SmartScheduleSettingModel smart_schedule_setting = 1;
  // business setting override list
  repeated models.smart_scheduler.v1.BusinessSettingOverrideModel business_override_list = 2;
}

// update smart schedule setting request
message UpdateSmartScheduleSettingRequest {
  // company id
  int64 token_company_id = 1 [(validate.rules).int64.gt = 0];
  // update smart schedule setting
  models.smart_scheduler.v1.UpdateSmartScheduleSettingDef smart_schedule_setting = 2;
}

// update smart schedule setting response
message UpdateSmartScheduleSettingResponse {}

// smart schedule setting service
service SmartScheduleSettingService {
  // get smart schedule setting
  rpc GetSmartScheduleSetting(GetSmartScheduleSettingRequest) returns (GetSmartScheduleSettingResponse);
  // update smart schedule setting
  rpc UpdateSmartScheduleSetting(UpdateSmartScheduleSettingRequest) returns (UpdateSmartScheduleSettingResponse);
}
