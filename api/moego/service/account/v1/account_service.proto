syntax = "proto3";

package moego.service.account.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/account/v1/account_defs.proto";
import "moego/models/account/v1/account_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1;accountsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.account.v1";

// check identifier request
message CheckIdentifierRequest {
  // check identifier in the given namespace, default is MOEGO(id=0)
  optional models.account.v1.NamespaceDef namespace = 1;

  // account identifier
  oneof identifier {
    option (validate.required) = true;

    // email
    string email = 2 [(validate.rules).string = {
      min_len: 3
      max_len: 100
    }];

    // phone number
    string phone_number = 3 [(validate.rules).string = {pattern: "^\\+[1-9]\\d{1,18}$"}];
  }
}

// check identifier response
message CheckIdentifierResponse {
  // if identifier is used
  bool used = 1;
}

// validate password request
message ValidatePasswordRequest {
  // account id
  int64 id = 1;

  // password
  string password = 2 [(validate.rules).string = {max_len: 100}];
}

// validate password response
message ValidatePasswordResponse {
  // if password is correct
  bool correct = 1;
}

// get account request
message GetAccountRequest {
  // account identifier
  oneof identifier {
    option (validate.required) = true;
    // account id
    int64 id = 1;

    // email
    string email = 2 [(validate.rules).string = {
      min_len: 3
      max_len: 100
    }];

    // phone number
    string phone_number = 3 [(validate.rules).string = {pattern: "^\\+[1-9]\\d{1,18}$"}];
  }

  // namespace, optional
  // If set, the identifier will be searched in the given namespace.
  // If not set, id will be searched in all namespaces while email and phone number will be searched in MOEGO namespace.
  optional models.account.v1.NamespaceDef namespace = 4;
}

// create account request
message CreateAccountRequest {
  // email
  string email = 1 [(validate.rules).string = {
    ignore_empty: true
    max_len: 100
    email: true
  }];

  // phone number
  string phone_number = 2 [(validate.rules).string = {
    ignore_empty: true
    pattern: "^\\+[1-9]\\d{1,18}$"
  }];

  // first name
  string first_name = 3 [(validate.rules).string = {max_len: 50}];

  // last name
  string last_name = 4 [(validate.rules).string = {max_len: 50}];

  // password
  string password = 5 [(validate.rules).string = {max_len: 100}];

  // avatar path
  string avatar_path = 6 [(validate.rules).string = {
    ignore_empty: true
    max_len: 256
    uri: true
  }];

  // source
  string source = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];

  // namespace, default is MOEGO(id=0)
  optional models.account.v1.NamespaceDef namespace = 8;
}

// update account request
message UpdateAccountRequest {
  // account id
  int64 id = 1;

  // email
  optional string email = 2 [(validate.rules).string = {
    max_len: 100
    email: true
  }];

  // phone number
  optional string phone_number = 3 [(validate.rules).string = {pattern: "^\\+[1-9]\\d{1,18}$"}];

  // first name
  optional string first_name = 4 [(validate.rules).string = {max_len: 50}];

  // last name
  optional string last_name = 5 [(validate.rules).string = {max_len: 50}];

  // password
  optional string password = 6 [(validate.rules).string = {max_len: 100}];

  // avatar path
  optional string avatar_path = 7 [(validate.rules).string = {
    max_len: 256
    uri: true
  }];

  // operator, optional
  // If this update request is made by users themselves, this field should not be set.
  // If this update request is made by administrators, this field should be set to the administrator's email.
  optional string operator = 8 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
}

// batch get account request
message BatchGetAccountRequest {
  // account id
  repeated int64 ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
  }];
}

// batch get account response
message BatchGetAccountResponse {
  // account list
  repeated moego.models.account.v1.AccountModel accounts = 1;
}

// freeze account request
message FreezeAccountRequest {
  // account id
  int64 id = 1;
  // operator, optional
  // If this update request is made by users themselves, this field should not be set.
  // If this update request is made by administrators, this field should be set to the administrator's email.
  optional string operator = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
}

// unfreeze account request
message UnfreezeAccountRequest {
  // account id
  int64 id = 1;
  // operator, optional
  // If this update request is made by users themselves, this field should not be set.
  // If this update request is made by administrators, this field should be set to the administrator's email.
  optional string operator = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
}

// delete account request
message DeleteAccountRequest {
  // account id
  int64 id = 1;
  // operator, optional
  // If this update request is made by users themselves, this field should not be set.
  // If this update request is made by administrators, this field should be set to the administrator's email.
  optional string operator = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
}

// recover account request
message RecoverAccountRequest {
  // account id
  int64 id = 1;
  // operator, optional
  // If this update request is made by users themselves, this field should not be set.
  // If this update request is made by administrators, this field should be set to the administrator's email.
  optional string operator = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
}

// recover account response
message RecoverAccountResponse {}

// get security last update time request
message GetSecurityLastUpdateTimeRequest {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get security last update time response
message GetSecurityLastUpdateTimeResponse {
  // password last update time
  google.protobuf.Timestamp password_last_update_time = 1;
}

// account service
service AccountService {
  // Check if the account identifier has been used.
  // The account identifier can be email or phone number.
  // An identifier is usable (`used` = false) if it is not associated with any accounts.
  // An identifier is occupied (`used` = true) if it is associated with an account which has not been deleted (can be active or frozen).
  // If an account is deleted, its identifier will be usable again (`used` = false).
  rpc CheckIdentifier(CheckIdentifierRequest) returns (CheckIdentifierResponse);

  // Validate if the password is correct for certain account.
  // If the account does not exist, or does not set a password, or has been deleted,
  // any password inputted will return `correct` = false.
  rpc ValidatePassword(ValidatePasswordRequest) returns (ValidatePasswordResponse);

  // Get an account by one of id, email or phone number.
  // A deleted account can be queried by id but not by email or phone number,
  // because an email or a phone number can be associated with multiple deleted accounts
  // while an id can uniquely identifies an account.
  //
  // Error codes:
  // - CODE_ACCOUNT_NOT_EXIST: The account does not exist.
  rpc GetAccount(GetAccountRequest) returns (moego.models.account.v1.AccountModel);

  // Get accounts by id list.
  // Deleted accounts can be queried.
  // If the account of certain id does not exist, the result list will not contain this account.
  // Thus, the size of result list may smaller than the size of request id list.
  // A maximum of 1000 ids can be requested at a time.
  rpc BatchGetAccount(BatchGetAccountRequest) returns (BatchGetAccountResponse);

  // Create a new account.
  //
  // Error codes:
  // - CODE_EMAIL_CONFLICT: the email has been used by another existing account.
  // - CODE_PHONE_NUMBER_CONFLICT: the phone number has been used by another existing account.
  rpc CreateAccount(CreateAccountRequest) returns (moego.models.account.v1.AccountModel);

  // Update an existing account.
  // An deleted account can also be updated.
  //
  // Error codes:
  // - CODE_ACCOUNT_NOT_EXIST: The account does not exist.
  // - CODE_EMAIL_CONFLICT: the email has been used by another existing account.
  // - CODE_PHONE_NUMBER_CONFLICT: the phone number has been used by another existing account.
  rpc UpdateAccount(UpdateAccountRequest) returns (moego.models.account.v1.AccountModel);

  // Freeze an account by id.
  // A frozen account may not be able to create sessions.
  //
  // Error codes:
  // - CODE_ACCOUNT_NOT_EXIST: The account does not exist or has been deleted.
  rpc FreezeAccount(FreezeAccountRequest) returns (google.protobuf.Empty);

  // Unfreeze an account by id.
  //
  // Error codes:
  // - CODE_ACCOUNT_NOT_EXIST: The account does not exist or has been deleted.
  rpc UnfreezeAccount(UnfreezeAccountRequest) returns (google.protobuf.Empty);

  // Delete an account by id.
  //
  // Error codes:
  // - CODE_ACCOUNT_NOT_EXIST: The account does not exist or has been deleted.
  rpc DeleteAccount(DeleteAccountRequest) returns (google.protobuf.Empty);

  // Try to recover an account by id.
  // An account can be recovered only if it has been deleted by DeleteAccount method.
  // The recovery may fail if the email or phone number of the account has been used by other active accounts.
  //
  // Error codes:
  // - CODE_ACCOUNT_NOT_EXIST: The account does not exist.
  // - CODE_ACCOUNT_ALREADY_RECOVERED: The account has not been deleted.
  // - CODE_EMAIL_CONFLICT: the email has been used by another existing account.
  // - CODE_PHONE_NUMBER_CONFLICT: the phone number has been used by another existing account.
  rpc RecoverAccount(RecoverAccountRequest) returns (RecoverAccountResponse);

  // Get security module last update time.
  // Now support password last update time.
  //
  // Error codes:
  // - CODE_ACCOUNT_NOT_EXIST: The account does not exist.
  rpc GetSecurityLastUpdateTime(GetSecurityLastUpdateTimeRequest) returns (GetSecurityLastUpdateTimeResponse);
}
