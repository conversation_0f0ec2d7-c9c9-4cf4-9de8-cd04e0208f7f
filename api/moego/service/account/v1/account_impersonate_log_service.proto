syntax = "proto3";

package moego.service.account.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/account/v1/account_impersonate_log_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1;accountsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.account.v1";

// account service
service AccountImpersonateLogService {
  // list account impersonate logs
  rpc ListAccountImpersonateLogs(ListAccountImpersonateLogsRequest) returns (ListAccountImpersonateLogsResponse);
}

// list account impersonate logs
message ListAccountImpersonateLogsRequest {
  // impersonator
  optional string impersonator = 1 [(validate.rules).string = {max_len: 100}];

  // target account id
  optional int64 target_account_id = 2 [(validate.rules).int64 = {gt: 0}];

  // sources
  repeated string sources = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 100
      }
    }
  }];

  // start time
  optional google.protobuf.Timestamp start_time = 4;

  // end time
  optional google.protobuf.Timestamp end_time = 5;

  // pagination
  moego.utils.v2.PaginationRequest pagination = 10;
}

// list account impersonate logs response
message ListAccountImpersonateLogsResponse {
  // logs
  repeated models.account.v1.AccountImpersonateLogModel logs = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}
