syntax = "proto3";

package moego.admin.activity_log.v1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/activity_log/v1/activity_log_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/activity_log/v1;activitylogapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.activity_log.v1";

// Search activity logs with pagination request
message SearchActivityLogPageRequest {
  // operator id
  repeated string operator_id = 1 [(validate.rules).repeated = {ignore_empty: true}];
  // resource type
  repeated string resource_type = 2 [(validate.rules).repeated = {
    items: {
      string: {
        min_len: 0
        max_len: 100
      }
    }
  }];
  // resource id
  repeated string resource_id = 3 [(validate.rules).repeated = {ignore_empty: true}];
  // action
  repeated string action = 4 [(validate.rules).repeated = {
    items: {
      string: {
        min_len: 0
        max_len: 100
      }
    }
  }];
  // owner id
  repeated string owner_id = 5 [(validate.rules).repeated = {ignore_empty: true}];
  // timestamp (inclusive)
  optional google.protobuf.Timestamp start_time = 6 [(validate.rules).timestamp = {
    gt: {seconds: 0}
  }];
  // timestamp (inclusive)
  optional google.protobuf.Timestamp end_time = 7 [(validate.rules).timestamp = {
    gt: {seconds: 0}
  }];
  // business id
  optional int64 business_id = 8 [(validate.rules).int64 = {gte: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 9;
}

// Search activity logs with pagination response
message SearchActivityLogPageResponse {
  // activity log list
  repeated moego.models.activity_log.v1.ActivityLogModelSimpleView activity_logs = 1;
  // business map, key is business id
  map<int64, google.protobuf.Struct> deprecated_business_map = 2 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;
}

// Get activity log details request
message GetActivityLogDetailsRequest {
  // activity log id
  string id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
}

// Get activity log details response
message GetActivityLogDetailsResponse {
  // activity log model
  moego.models.activity_log.v1.ActivityLogModel activity_log = 1;
  // affected activity logs
  repeated moego.models.activity_log.v1.ActivityLogModel effects = 2;
}

// Search operators with pagination request
message SearchOperatorPageRequest {
  // operator name
  optional string operator_name = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 100
  }];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gte: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3;
}

// Search operators with pagination response
message SearchOperatorPageResponse {
  // operator list
  repeated moego.models.activity_log.v1.Operator operators = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;
}

// Search resources with pagination request
message SearchResourceTypePageRequest {
  // resource name
  optional string resource_type = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 100
  }];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gte: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3;
}

// Search resources with pagination response
message SearchResourceTypePageResponse {
  // resource type list
  repeated string resource_types = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;
}

// Search actions with pagination request
message SearchActionPageRequest {
  // action
  optional string action = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 100
  }];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gte: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3;
}

// Search actions with pagination response
message SearchActionPageResponse {
  // action list
  repeated string actions = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;
}

// Search owners with pagination request
message SearchOwnerPageRequest {
  // owner name
  optional string owner_name = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 100
  }];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gte: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3;
}

// Search owners with pagination response
message SearchOwnerPageResponse {
  // owner list
  repeated moego.models.activity_log.v1.Owner owners = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;
}

// Activity log service
service ActivityLogService {
  // Search activity logs with pagination
  rpc SearchActivityLogPage(SearchActivityLogPageRequest) returns (SearchActivityLogPageResponse);

  // Get activity log details
  rpc GetActivityLogDetails(GetActivityLogDetailsRequest) returns (GetActivityLogDetailsResponse);

  // Search operators with pagination
  rpc SearchOperatorPage(SearchOperatorPageRequest) returns (SearchOperatorPageResponse);

  // Search resources with pagination
  rpc SearchResourceTypePage(SearchResourceTypePageRequest) returns (SearchResourceTypePageResponse);

  // Search actions with pagination
  rpc SearchActionPage(SearchActionPageRequest) returns (SearchActionPageResponse);

  // Search owners with pagination
  rpc SearchOwnerPage(SearchOwnerPageRequest) returns (SearchOwnerPageResponse);
}
