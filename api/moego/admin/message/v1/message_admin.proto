// @since 2023-07-06 13:56:41
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.message.v1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/file/v2/file_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/message/v1;messageapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.message.v1";

// DescribeThreadsParams is the params for DescribeThreads
message DescribeThreadsParams {
  // business id
  optional int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // customer id
  optional int64 customer_id = 2 [(validate.rules).int64 = {gt: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15 [(validate.rules).message = {required: true}];
}

// DescribeThreadsResult is the result for DescribeThreads
message DescribeThreadsResult {
  // deprecated threads
  repeated google.protobuf.Struct threads = 1 [deprecated = true];
  // deprecated business map, key is business id
  map<int64, google.protobuf.Struct> business_map = 2 [deprecated = true];
  // deprecated customer map, key is customer id
  map<int64, google.protobuf.Struct> customer_map = 3 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15 [(validate.rules).message = {required: true}];
}

// DescribeMessagesParams is the params for DescribeMessages
message DescribeMessagesParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // target type
  optional int32 target_type = 2 [(validate.rules).int32 = {gt: 0}];
  // target id
  optional int64 target_id = 3 [(validate.rules).int64 = {gt: 0}];
  // customer id
  optional int64 customer_id = 8 [(validate.rules).int64 = {gt: 0}];

  // type
  optional int32 type = 4 [(validate.rules).int32 = {gt: 0}];
  // method
  optional int32 method = 5 [(validate.rules).int32 = {gt: 0}];
  // source
  optional int32 source = 6 [(validate.rules).int32 = {gt: 0}];
  // message type
  optional int32 message_type = 7 [(validate.rules).int32 = {gt: 0}];

  // pagination
  moego.utils.v2.PaginationRequest pagination = 15 [(validate.rules).message = {required: true}];
}

// DescribeMessagesResult is the result for DescribeMessages
message DescribeMessagesResult {
  // deprecated messages
  repeated google.protobuf.Struct messages = 1 [deprecated = true];
  // deprecated staff map, key is staff id
  map<int64, google.protobuf.Struct> staff_map = 2 [deprecated = true];
  // deprecated customer map, key is customer id
  map<int64, google.protobuf.Struct> customer_map = 3 [deprecated = true];
  // deprecated business map, key is business id
  map<int64, google.protobuf.Struct> business_map = 4 [deprecated = true];
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15 [(validate.rules).message = {required: true}];
}

// export sms usage of businesses in the company params
message ExportSmsUsageParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // start time
  optional google.protobuf.Timestamp start_time = 6 [(validate.rules).timestamp = {
    gt: {seconds: 0}
  }];
  // end time
  optional google.protobuf.Timestamp end_time = 7 [(validate.rules).timestamp = {
    gt: {seconds: 0}
  }];
}

// export sms usage of businesses in the company result
message ExportSmsUsageResult {
  // file id
  int64 file_id = 1;
}

//query sms usage export files params
message QuerySmsUsageExportFilesParams {
  // file id
  optional int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // default not include
  optional bool include_deleted = 2;
  // company id
  optional int64 company_id = 3 [(validate.rules).int64 = {gte: 0}];
  // order by
  optional moego.utils.v2.OrderBy order_by = 14;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// describe files message export result
message QuerySmsUsageExportFilesResult {
  // files
  repeated moego.models.file.v2.FileModel files = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// the message service
service MessageService {
  // describe threads
  rpc DescribeThreads(DescribeThreadsParams) returns (DescribeThreadsResult);
  // describe messages
  rpc DescribeMessages(DescribeMessagesParams) returns (DescribeMessagesResult);
  // export sms usage of businesses in the company
  rpc ExportSmsUsage(ExportSmsUsageParams) returns (ExportSmsUsageResult);
  // query sms usage export files
  rpc QuerySmsUsageExportFiles(QuerySmsUsageExportFilesParams) returns (QuerySmsUsageExportFilesResult);
}
