// @since 2023-05-27 21:29:19
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.admin_permission.v1;

import "moego/models/admin_permission/v1/role_binding_models.proto";
import "moego/models/admin_permission/v1/role_models.proto";
import "moego/models/admin_permission/v1/role_permission_models.proto";
import "moego/utils/v1/struct.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/admin_permission/v1;adminpermissionapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.admin_permission.v1";

// update account role binding
message UpdateRoleBindingsParams {
  // account id
  string account_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // role ids
  repeated int64 role_ids = 2 [(validate.rules).repeated = {
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// update account role binding response
message UpdateRoleBindingsResult {}

// describe role bindings request
message DescribeRoleBindingsParams {
  // filter by account id
  optional moego.utils.v1.StringListValue account_ids = 1;
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// describe role bindings
message DescribeRoleBindingsResult {
  // account role bindings
  repeated moego.models.admin_permission.v1.RoleBindingsModel role_bindings = 1;
  // roles map
  map<int64, moego.models.admin_permission.v1.RoleModel> roles = 2;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// describe permissions
message DescribeAccountPermissionsParams {
  // account id
  string account_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // filter by permission points
  repeated string permissions = 2 [(validate.rules).repeated = {
    max_items: 1000
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 100
      }
    }
  }];
}

// describe account permissions
message DescribeAccountPermissionsResult {
  // permission points
  repeated moego.models.admin_permission.v1.RolePermissionModel permissions = 1;
  // roles map
  map<int64, moego.models.admin_permission.v1.RoleModel> roles = 2;
}

// the role_binding service
service RoleBindingService {
  // fully update account role bindings
  rpc UpdateRoleBindings(UpdateRoleBindingsParams) returns (UpdateRoleBindingsResult);
  // describe role bindings list
  rpc DescribeRoleBindings(DescribeRoleBindingsParams) returns (DescribeRoleBindingsResult);
  // filter specified permissions
  rpc DescribeAccountPermissions(DescribeAccountPermissionsParams) returns (DescribeAccountPermissionsResult);
}
