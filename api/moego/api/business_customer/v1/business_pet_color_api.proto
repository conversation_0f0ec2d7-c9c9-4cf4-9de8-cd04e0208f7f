syntax = "proto3";

package moego.api.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// pet color service
service BusinessPetColorService {
  // list pet color
  rpc ListPetColor(ListPetColorParams) returns (ListPetColorResult);

  // create pet color
  rpc CreatePetColor(CreatePetColorParams) returns (CreatePetColorResult);

  // delete pet color
  rpc DeletePetColor(DeletePetColorParams) returns (DeletePetColorResult);

  // get pet binding list
  rpc ListPetBinding(ListPetBindingParams) returns (ListPetBindingResult);

  // binding pet and color
  rpc BindingColor(BindingColorParams) returns (BindingColorResult);
}

// list pet color params
message ListPetColorParams {}

// list pet color result
message ListPetColorResult {
  // color
  message Color {
    // id
    int64 color_id = 1;
    // name
    string color_name = 2;
    // status
    int32 status = 3;
    // create time
    google.protobuf.Timestamp create_time = 4;
    // update time
    google.protobuf.Timestamp update_time = 5;
  }
  // list color
  repeated Color colors = 1;
}

// create pet color params
message CreatePetColorParams {
  // pet color name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
}

// create pet color result
message CreatePetColorResult {
  // result
  bool result = 1;
  // color
  message Color {
    // id
    int64 color_id = 1;
    // name
    string color_name = 2;
    // status
    int32 status = 3;
    // create time
    google.protobuf.Timestamp create_time = 4;
    // update time
    google.protobuf.Timestamp update_time = 5;
  }

  // create color
  Color color = 2;
}

// delete pet color params
message DeletePetColorParams {
  // pet color id
  int64 color_id = 1 [(validate.rules).int64.gt = 0];
}

// delete pet color result
message DeletePetColorResult {
  // result
  bool result = 1;
}

// list pet binding list params
message ListPetBindingParams {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];
}

// list pet binding list result
message ListPetBindingResult {
  // pet binding list
  message PetColorBinding {
    // pet id
    int64 pet_id = 1;
    // color
    int64 color_id = 2;
    // color name
    string color_name = 3;
    // status
    int32 status = 4;
  }
  // binding list
  repeated PetColorBinding pet_colors_bindings = 1;
}

// binding color
message BindingColorParams {
  // status
  enum Status {
    // unspecified
    STATUS_UNSPECIFIED = 0;
    // ..
    NORMAL = 1;
    // ..
    DELETED = 2;
  }
  // color id
  int64 color_id = 1;
  // pet id
  int64 pet_id = 2;
  // status
  Status status = 3;
}

// response
message BindingColorResult {
  // result
  bool result = 1;
}
