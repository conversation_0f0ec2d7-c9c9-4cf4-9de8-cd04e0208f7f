syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_incident_report_defs.proto";
import "moego/models/business_customer/v1/business_pet_incident_report_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// list pet incident report params
message ListPetIncidentReportParams {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];
  // business id ( if set, will return incident reports of the pet in the business )
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// list pet incident report result
message ListPetIncidentReportResult {
  // pet incident report list
  repeated moego.models.business_customer.v1.BusinessPetIncidentReportModel incident_reports = 1;
}

// create pet incident report params
message CreatePetIncidentReportParams {
  // pet incident report
  moego.models.business_customer.v1.BusinessPetIncidentReportCreateDef incident_report = 1 [(validate.rules).message.required = true];
}

// create pet incident report result
message CreatePetIncidentReportResult {
  // pet incident report
  moego.models.business_customer.v1.BusinessPetIncidentReportModel incident_report = 1;
}

// update pet incident report params
message UpdatePetIncidentReportParams {
  // pet incident report id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // pet incident report
  moego.models.business_customer.v1.BusinessPetIncidentReportUpdateDef incident_report = 2 [(validate.rules).message.required = true];
}

// update pet incident report result
message UpdatePetIncidentReportResult {}

// delete pet incident report params
message DeletePetIncidentReportParams {
  // pet incident report id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// delete pet incident report result
message DeletePetIncidentReportResult {}

// API for pet incident report settings
service BusinessPetIncidentReportService {
  // List pet incident reports of current company
  rpc ListPetIncidentReport(ListPetIncidentReportParams) returns (ListPetIncidentReportResult);

  // Create a pet incident report
  rpc CreatePetIncidentReport(CreatePetIncidentReportParams) returns (CreatePetIncidentReportResult);

  // Update a pet incident report
  rpc UpdatePetIncidentReport(UpdatePetIncidentReportParams) returns (UpdatePetIncidentReportResult);

  // Delete a pet incident report
  rpc DeletePetIncidentReport(DeletePetIncidentReportParams) returns (DeletePetIncidentReportResult);
}
