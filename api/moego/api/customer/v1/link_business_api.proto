syntax = "proto3";

package moego.api.customer.v1;

import "google/protobuf/empty.proto";
import "moego/models/business/v1/business_models.proto";
import "moego/models/message/v1/business_twilio_model.proto";
import "moego/models/online_booking/v1/business_ob_config_models.proto";
import "moego/models/online_booking/v1/business_ob_gallery_models.proto";
import "moego/models/online_booking/v1/business_ob_profile_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/customer/v1;customerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.customer.v1";

// client portal link business response
message ClientPortalLinkBusinessResponse {
  // business info
  moego.models.business.v1.BusinessModel business_info = 1;
  // online booking profile
  moego.models.online_booking.v1.BusinessOBClientPortalView ob_profile = 2;
  // online booking gallery first image
  moego.models.online_booking.v1.BusinessOBGalleryClientPortalView ob_gallery_first_image = 3;
  // online booking config
  moego.models.online_booking.v1.BusinessOBConfigClientPortalView ob_config = 4;
  // business twilio number
  moego.models.message.v1.BusinessTwilioNumberView business_twilio = 5;
}

// client portal link business list response
message ClientPortalLinkBusinessListResponse {
  // link business list
  repeated ClientPortalLinkBusinessResponse link_business = 1;
}

// link customer request
message LinkCustomerRequest {
  // customer id
  int64 customer_id = 1 [(validate.rules).int64 = {
    ignore_empty: true
    gt: 0
  }];
  // customer code
  string customer_code = 2 [(validate.rules).string = {
    ignore_empty: true
    max_len: 10
  }];
}

// step 1: enter invoice page (client.moego.pet/invoice/xxx)
// step 2: get:
//          session: account
//          page: customer (account_id)
//          account == null && customer.account_id == null -> register (disable input phone) -> bind
//          account == null && customer.account_id != null -> login
//          account != null && customer.account_id == null
//                        phone number is same -> bind & redirect (LinkBusiness.LinkCustomer)
//                        phone number is diff -> register (disable input phone) -> bind
//          account != null && customer.account_id != null
//                        account id is same -> redirect profile
//                        account id is diff -> login
// step 3: click button is login or not

// api: LinkBusinessService.LinkCustomer(account_id (auth customer_id), customer_id)
//      1. get business customer list
//      2. link customer
//      2. get customer pet
//      3. create and link customer pet
//      3. create address
// pet metadata list
// customer_account (account_id)
// business_customer (customer_id, business_id, phone_number, account_id)
// customer_pet (customer_pet_id, business_id, customer_id, pet_id)
// client_pet (pet_id, account_id)
// client_address
// BusinessService.CreateBusiness
// CreateBusinessService.CreateBusiness
//
//  link service
service LinkBusinessService {
  // link new customer and pet
  rpc LinkCustomerAndPet(LinkCustomerRequest) returns (google.protobuf.Empty);
  // get link business
  rpc GetLinkBusiness(google.protobuf.Empty) returns (ClientPortalLinkBusinessListResponse);
}
