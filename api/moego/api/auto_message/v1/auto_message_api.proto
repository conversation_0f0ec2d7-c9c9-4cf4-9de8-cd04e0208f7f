syntax = "proto3";

package moego.api.auto_message.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "moego/models/auto_message/v1/auto_message_defs.proto";
import "moego/models/auto_message/v1/auto_message_models.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/message/v1/message_enums.proto";
import "moego/models/message/v1/message_template_enums.proto";
import "moego/utils/v1/time_period.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/auto_message/v1;automessageapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.auto_message.v1";

// transfer msg config by business params
message MessageTransferByBusinessParams {
  // business ids to transfer
  repeated int64 business_ids = 1 [(validate.rules).repeated.items.int64 = {gt: 0}];
  // token to verify the request
  string token = 2 [(validate.rules).string = {max_len: 256}];
}

// transfer msg config by business result
message MessageTransferByBusinessResult {}

// get ob auto message list params
message GetOBAutoMessageListParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get ob auto message list result
message GetOBAutoMessageListResult {
  // auto message list
  repeated models.auto_message.v1.OBAutoMessageListView auto_messages = 1;
}

// get appointment auto message list params
message GetAppointmentAutoMessageListParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get appointment auto message list result
message GetAppointmentAutoMessageListResult {
  // auto message list
  repeated models.auto_message.v1.AppointmentAutoMessageListView auto_messages = 1;
}

// get appointment auto message detail params
message GetAppointmentAutoMessageDetailParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get appointment auto message detail result
message GetAppointmentAutoMessageDetailResult {
  // auto message detail
  models.auto_message.v1.AppointmentAutoMessageDetailView auto_message = 1;
}

// update appointment auto message params
message UpdateAppointmentAutoMessageParams {
  // auto message id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // is enabled
  optional bool is_enabled = 2;
  // client receive
  optional models.message.v1.MessageTypeList client_receive = 3;
  // templates for service types
  optional models.auto_message.v1.ServiceTypeTemplateDefList template = 4;
}

// update appointment auto message result
message UpdateAppointmentAutoMessageResult {
  // auto message
  models.auto_message.v1.AppointmentAutoMessageDetailView auto_message = 1;
}

// get payment auto message list params
message GetPayAutoMessageListParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get payment auto message list result
message GetPayAutoMessageListResult {
  // auto message list
  repeated models.auto_message.v1.PayAutoMessageListView auto_messages = 1;
}

// get payment auto message detail params
message GetPayAutoMessageDetailParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get payment auto message detail result
message GetPayAutoMessageDetailResult {
  // auto message detail
  models.auto_message.v1.PayAutoMessageDetailView auto_message = 1;
}

// update payment auto message params
message UpdatePayAutoMessageParams {
  // auto message id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // is enabled
  optional bool is_enabled = 2;
  //email subject
  optional string email_subject = 3 [(validate.rules).string = {max_len: 256}];
  // email body
  optional string email_body = 4 [(validate.rules).string = {max_len: 131072}];
  // sms body
  optional string sms_body = 5 [(validate.rules).string = {max_len: 65536}];
  // app body
  optional string app_body = 6 [(validate.rules).string = {max_len: 65536}];
}

// update payment auto message result
message UpdatePayAutoMessageResult {
  // auto message
  models.auto_message.v1.PayAutoMessageDetailView auto_message = 1;
}

// get appointment reminder list params
message GetAppointmentReminderListParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // list of message template use case enum. Ignored when empty.
  repeated moego.models.message.v1.MessageTemplateUseCase use_cases = 2 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// get appointment reminder list result
message GetAppointmentReminderListResult {
  // reminder message list
  repeated models.auto_message.v1.AppointmentReminderListView reminders = 1;
}

// get appointment reminder detail params
message GetAppointmentReminderDetailParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get appointment reminder detail result
message GetAppointmentReminderDetailResult {
  // reminder message detail
  models.auto_message.v1.AppointmentReminderDetailView reminder = 1;
}

// update appointment reminder params
message UpdateAppointmentReminderParams {
  // reminder message id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // is enabled
  optional bool is_enabled = 2;
  // days before
  optional int32 days_before = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 28
  }];
  // minutes at
  optional int32 minutes_at = 4 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // template for appointment reminder
  optional models.auto_message.v1.ServiceTypeTemplateDefList template = 5;
}

// update appointment reminder result
message UpdateAppointmentReminderResult {
  // reminder message
  models.auto_message.v1.AppointmentReminderDetailView reminder = 1;
}

// get default reminder list params
message GetReminderListParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get default reminder list result
message GetReminderListResult {
  // reminder message list
  repeated models.auto_message.v1.ReminderListView reminders = 1;
}

// get default reminder detail params
message GetReminderDetailParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get default reminder detail result
message GetReminderDetailResult {
  // reminder message detail
  models.auto_message.v1.ReminderDetailView reminder = 1;
}

// update default reminder params
message UpdateReminderParams {
  // auto message id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // is enabled
  optional bool is_enabled = 2;
  // days before
  optional int32 days_before = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 28
  }];
  // hours after
  optional int32 hours_after = 4 [(validate.rules).int32 = {
    gte: 0
    lt: 24
  }];
  // minutes at
  optional int32 minutes_at = 5 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  //email subject
  optional string email_subject = 6 [(validate.rules).string = {max_len: 256}];
  // email body
  optional string email_body = 7 [(validate.rules).string = {max_len: 131072}];
  // sms body
  optional string sms_body = 8 [(validate.rules).string = {max_len: 65536}];
  // app body
  optional string app_body = 9 [(validate.rules).string = {max_len: 65536}];
}

// update default reminder result
message UpdateReminderResult {
  // auto message
  models.auto_message.v1.ReminderDetailView reminder = 1;
}

// list appointment reminder task params
message ListAppointmentReminderTaskParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2 [(validate.rules).message = {required: true}];
}

// list appointment reminder task result
message ListAppointmentReminderTaskResult {
  // appointment reminder task list
  repeated AppointmentReminderTaskView appointment_reminder_tasks = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;

  // appointment reminder task view
  message AppointmentReminderTaskView {
    // task id
    int64 id = 1;
    // customer name
    string customer_name = 2;
    // appointment date, in yyyy-MM-dd format
    string appointment_start_date = 3;
    // end date
    string appointment_end_date = 4;
    // appointment start time, the number of minutes of the day
    int32 appointment_start_time = 5;
    // appointment end time, the number of minutes of the day
    int32 appointment_end_time = 6;
    // status: scheduled, failed, sent
    string status = 7;
    // method
    repeated moego.models.message.v1.MessageType methods = 8;
    // use case
    moego.models.message.v1.MessageTemplateUseCase use_case = 9;
    // scheduled time
    google.protobuf.Timestamp scheduled_time = 10;
    // appointment id
    int64 appointment_id = 11;
    // customer id
    int64 customer_id = 12;
  }
}

// list pet birthday reminder task params
message ListPetBirthdayReminderTaskParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2 [(validate.rules).message = {required: true}];
}

// list pet birthday reminder task result
message ListPetBirthdayReminderTaskResult {
  // pet birthday reminder task list
  repeated PetBirthdayReminderTaskView pet_birthday_reminder_tasks = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;

  // pet birthday reminder task view
  message PetBirthdayReminderTaskView {
    // task id
    int64 id = 1;
    // customer name
    string customer_name = 2;
    // pet name
    string pet_name = 3;
    // pet type
    moego.models.customer.v1.PetType pet_type = 4;
    // pet avatar
    string pet_avatar = 5;
    // pet breed
    string pet_breed = 6;
    // pet birthday
    google.type.Date pet_birthday = 7;
    // scheduled time
    google.protobuf.Timestamp scheduled_time = 10;
    // customer id
    int64 customer_id = 11;
    // pet id
    int64 pet_id = 12;
    // use case
    moego.models.message.v1.MessageTemplateUseCase use_case = 13;
  }
}

// list rebook reminder task params
message ListRebookReminderTaskParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2 [(validate.rules).message = {required: true}];
}

// list rebook reminder task result
message ListRebookReminderTaskResult {
  // rebook reminder task list
  repeated RebookReminderTaskView rebook_reminder_tasks = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;

  // rebook reminder task view
  message RebookReminderTaskView {
    // task id
    int64 id = 1;
    // customer name
    string customer_name = 2;
    // pet name
    repeated string pet_names = 3;
    // customer avatar path
    string customer_avatar = 4;
    // expected date
    google.type.Date expected_date = 6;
    // last appointment date time
    google.protobuf.Timestamp last_appointment_date_time = 7;
    // service frequency
    moego.utils.v1.TimePeriod preferred_grooming_frequency = 8;
    // customer id
    int64 customer_id = 9;
    // use case
    moego.models.message.v1.MessageTemplateUseCase use_case = 10;
  }
}

// auto message service
service AutoMessageService {
  // transfer auto message config by business. for auto message service initialization
  rpc MessageTransferByBusiness(MessageTransferByBusinessParams) returns (MessageTransferByBusinessResult);
  // get ob auto message list
  rpc GetOBAutoMessageList(GetOBAutoMessageListParams) returns (GetOBAutoMessageListResult);
  // get appointment auto message list
  rpc GetAppointmentAutoMessageList(GetAppointmentAutoMessageListParams) returns (GetAppointmentAutoMessageListResult);
  // get appointment auto message detail
  rpc GetAppointmentAutoMessageDetail(GetAppointmentAutoMessageDetailParams) returns (GetAppointmentAutoMessageDetailResult);
  // update appointment auto message
  rpc UpdateAppointmentAutoMessage(UpdateAppointmentAutoMessageParams) returns (UpdateAppointmentAutoMessageResult);
  // get payment auto message list
  rpc GetPayAutoMessageList(GetPayAutoMessageListParams) returns (GetPayAutoMessageListResult);
  // get payment auto message detail
  rpc GetPayAutoMessageDetail(GetPayAutoMessageDetailParams) returns (GetPayAutoMessageDetailResult);
  // update payment auto message
  rpc UpdatePayAutoMessage(UpdatePayAutoMessageParams) returns (UpdatePayAutoMessageResult);
  // get appointment reminder list
  rpc GetAppointmentReminderList(GetAppointmentReminderListParams) returns (GetAppointmentReminderListResult);
  // get appointment reminder detail
  rpc GetAppointmentReminderDetail(GetAppointmentReminderDetailParams) returns (GetAppointmentReminderDetailResult);
  // update appointment reminder
  rpc UpdateAppointmentReminder(UpdateAppointmentReminderParams) returns (UpdateAppointmentReminderResult);
  // get default reminder list
  rpc GetReminderList(GetReminderListParams) returns (GetReminderListResult);
  // get default reminder detail
  rpc GetReminderDetail(GetReminderDetailParams) returns (GetReminderDetailResult);
  // update default reminder
  rpc UpdateReminder(UpdateReminderParams) returns (UpdateReminderResult);
  // list appointment reminder task
  rpc ListAppointmentReminderTask(ListAppointmentReminderTaskParams) returns (ListAppointmentReminderTaskResult);
  // list pet birthday reminder task
  rpc ListPetBirthdayReminderTask(ListPetBirthdayReminderTaskParams) returns (ListPetBirthdayReminderTaskResult);
  // list rebook reminder task
  rpc ListRebookReminderTask(ListRebookReminderTaskParams) returns (ListRebookReminderTaskResult);
}
