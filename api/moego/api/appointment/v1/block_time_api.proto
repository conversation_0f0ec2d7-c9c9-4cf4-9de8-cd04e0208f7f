// @since 2024-01-15 15:02:36
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.appointment.v1;

import "moego/models/appointment/v1/appointment_defs.proto";
import "moego/models/appointment/v1/appointment_note_defs.proto";
import "moego/models/appointment/v1/pet_detail_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// Create a block time params
message CreateBlockTimeParams {
  // Block appointment params
  models.appointment.v1.BlockAppointmentCreateDef block_appointment = 1 [(validate.rules).message = {required: true}];

  // Selected staff and block date and time
  models.appointment.v1.BlockTimeDef block_time = 2 [(validate.rules).message = {required: true}];

  // Description
  optional models.appointment.v1.AppointmentNoteCreateDef note = 3 [(validate.rules).message = {skip: true}];
}

// Create a block time result
message CreateBlockTimeResult {
  // Appointment id
  int64 appointment_id = 1;
}

// Update a block time params
message UpdateBlockTimeParams {
  // Block appointment params
  models.appointment.v1.BlockAppointmentUpdateDef block_appointment = 1 [(validate.rules).message = {required: true}];

  // Selected staff and block date and time
  models.appointment.v1.BlockTimeDef block_time = 2 [(validate.rules).message = {required: true}];

  // Description
  optional models.appointment.v1.AppointmentNoteUpdateDef note = 3 [(validate.rules).message = {skip: true}];
}

// Update a block time result
message UpdateBlockTimeResult {}

// the block time service
service BlockTimeService {
  // Create a block time
  rpc CreateBlockTime(CreateBlockTimeParams) returns (CreateBlockTimeResult);

  // Update a block time
  rpc UpdateBlockTime(UpdateBlockTimeParams) returns (UpdateBlockTimeResult);
}
