syntax = "proto3";

package moego.api.organization.v1;

import "google/type/date.proto";
import "moego/models/organization/v1/staff_availability_def.proto";
import "moego/models/organization/v1/staff_availability_models.proto";
import "moego/models/organization/v1/staff_defs.proto";
import "moego/models/organization/v1/staff_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/organization/v1;organizationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.organization.v1";

// request for create a new staff
message CreateStaffParams {
  // staff profile def
  models.organization.v1.CreateStaffDef staff_profile = 1;
  // working business def
  optional models.organization.v1.StaffWorkingLocationDef working_location = 2;
  // access control def
  optional models.organization.v1.StaffAccessControlDef access_control = 3;
  // notification def
  optional models.organization.v1.StaffNotificationDef notification_setting = 4;
  // payroll setting def
  optional models.organization.v1.StaffPayrollSettingDef payroll_setting = 5;
  // send invite link params
  optional models.organization.v1.SendInviteLinkParamsDef invite_link = 6;
  // staff login time
  optional models.organization.v1.StaffLoginTimeDef login_time = 7;
}

// response for create a new staff
message CreateStaffResult {
  // generated staff id
  int64 id = 1;
}

// request for get staff detail
message GetStaffFullDetailParams {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// response for get staff detail
message GetStaffFullDetailResult {
  // staff id
  int64 id = 1;
  // staff info
  moego.models.organization.v1.StaffBasicView staff_profile = 2;
  // working location
  moego.models.organization.v1.StaffWorkingLocationDef working_location = 3;
  // access control
  moego.models.organization.v1.StaffAccessControlDef access_control = 4;
  // notification setting
  moego.models.organization.v1.StaffNotificationDef notification_setting = 5;
  // payroll setting
  moego.models.organization.v1.StaffPayrollSettingDef payroll_setting = 6;
  // staff email
  moego.models.organization.v1.StaffEmailDef staff_email = 7;
  // staff login time
  moego.models.organization.v1.StaffLoginTimeDef login_time = 8;
}

// request for update staff
message UpdateStaffParams {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // staff profile def
  optional models.organization.v1.UpdateStaffDef staff_profile = 2;
  // working business def
  optional models.organization.v1.StaffWorkingLocationDef working_location = 3;
  // access control def
  optional models.organization.v1.StaffAccessControlDef access_control = 4;
  // notification def
  optional models.organization.v1.StaffNotificationDef notification_setting = 5;
  // payroll setting def
  optional models.organization.v1.StaffPayrollSettingDef payroll_setting = 6;
  // send invite link params
  optional models.organization.v1.SendInviteLinkParamsDef invite_link = 7;
  // staff login time
  optional models.organization.v1.StaffLoginTimeDef login_time = 8;
}

// response for update staff
message UpdateStaffResult {
  // updated result
  bool success = 1;
}

// request for delete staff
message DeleteStaffParams {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// response for delete staff
message DeleteStaffResult {
  // deleted result
  bool success = 1;
}

// request for query staff list by pagination
message QueryStaffListByPaginationParams {
  // business ids
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
  // preserved 2-13 for future usage, for example: query by keyword, status, role, etc.
  // order by params
  repeated moego.utils.v2.OrderBy order_bys = 14;
  // pagination params
  moego.utils.v2.PaginationRequest pagination = 15;
}

// response for query staff list by pagination
message QueryStaffListByPaginationResult {
  // staff list
  repeated models.organization.v1.StaffInfoDef staffs = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// request for get staff list by business id
message GetAllWorkingLocationStaffsParams {}

// response for get working location staff list by business id
message GetAllWorkingLocationStaffsResult {
  // staff list
  repeated models.organization.v1.LocationStaffsDef location_staffs = 1;
  // total location count
  int64 total_location_count = 2;
  // total staff count
  int64 total_staff_count = 3;
}

// request for get staff list by business ids
message GetStaffsByWorkingLocationIdsParams {
  // business ids, if empty, will get all working location staffs
  repeated int64 business_ids = 3 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// response for get working staff list by business ids
message GetStaffsByWorkingLocationIdsResult {
  // staff list
  repeated models.organization.v1.LocationStaffsDef location_staffs = 1;
  // total location count
  int64 total_location_count = 2;
  // total staff count
  int64 total_staff_count = 3;
}

// request for get clock in out staff list
message GetClockInOutStaffsParams {
  // clock in/out date
  string date = 1 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // staff ids
  repeated int64 staff_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// response for get clock in out staff list
message GetClockInOutStaffsResult {
  // staff list
  repeated models.organization.v1.ClockInOutStaffDef clock_in_out_staffs = 1;
}

// request for get enterprise staff list by working location ids
message GetEnterpriseStaffsByWorkingLocationIdsParams {
  // business ids, if empty, will get all working location enterprise staffs
  repeated int64 business_ids = 3 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// response for get working staff list by business ids
message GetEnterpriseStaffsByWorkingLocationIdsResult {
  // staff list
  repeated models.organization.v1.LocationStaffsDef location_staffs = 1;
}

// get staff login time params
message GetStaffLoginTimeParams {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];
}

// get staff login time result
message GetStaffLoginTimeResult {
  // staff login time
  models.organization.v1.StaffLoginTimeModel login_time = 1;
}

// update staff login time params
message UpdateStaffLoginTimeParams {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];
  // staff login time
  models.organization.v1.StaffLoginTimeDef login_time = 2;
}

// update staff login time result
message UpdateStaffLoginTimeResult {
  // is success
  bool success = 1;
}

// get recommended staff login time in company
message GetRecommendedStaffLoginTimeParams {}

// get recommended staff login time result
message GetRecommendedStaffLoginTimeResult {
  // recommended staff login time
  models.organization.v1.StaffLoginTimeDef login_time = 1;
}

// The params of list staff group by role
message ListStaffGroupByRoleParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];

  // staff working date
  google.type.Date date = 2 [(validate.rules).message = {required: true}];
}

// The result of list staff group by role
message ListStaffGroupByRoleResult {
  // staff group by role
  repeated RoleStaffGroup role_staff_groups = 1;

  // Working staff ids
  repeated int64 working_staff_ids = 2;

  // Role staff group
  message RoleStaffGroup {
    // Role name
    string role_name = 1;
    // Staffs
    repeated models.organization.v1.StaffBasicView staffs = 2;
  }
}

// GetBusinessStaffAvailabilityTypeRequest staff availability 配置
message GetBusinessStaffAvailabilityTypeParams {
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// UpdateBusinessStaffAvailabilityTypeRequest update staff availability 配置
message UpdateBusinessStaffAvailabilityTypeParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // availability type
  moego.models.organization.v1.AvailabilityType availability_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// UpdateBusinessStaffAvailabilityTypeResponse update staff availability 配置
message UpdateBusinessStaffAvailabilityTypeResult {}

// GetBusinessStaffAvailabilityTypeResponse 返回staff 类型的结果
message GetBusinessStaffAvailabilityTypeResult {
  // availability type
  moego.models.organization.v1.AvailabilityType availability_type = 1;
}

// GetStaffCalenderViewRequest
message GetStaffCalenderViewParams {
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // start date
  string start_date = 4;
  // availability type
  optional moego.models.organization.v1.AvailabilityType availability_type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// CalenderStaff
message CalenderStaff {
  // staff id
  int64 staff_id = 1;
  // is available
  bool is_available = 2;
  // schedule type
  moego.models.organization.v1.ScheduleType schedule_type = 3;
  // slot daily setting
  map<string, moego.models.organization.v1.SlotAvailabilityDay> slot_availability_day_map = 4;
  // slot start sunday
  string slot_start_sunday = 6;

  // schedule type
  moego.models.organization.v1.ScheduleType time_schedule_type = 7;
  // time daily setting
  map<string, moego.models.organization.v1.TimeAvailabilityDay> time_availability_day_map = 8;
  // time start sunday
  string time_start_sunday = 9;
}

// GetStaffCalenderViewResponse
message GetStaffCalenderViewResult {
  // staff available list
  repeated CalenderStaff staff_availability_list = 1;
}

// GetStaffAvailabilityRequest 获取staff 的 availability
message GetStaffAvailabilityParams {
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id list, staff will init when staff_id no exist
  repeated int64 staff_id_list = 3 [(validate.rules).repeated = {unique: true}];
  // availability type
  optional moego.models.organization.v1.AvailabilityType availability_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// get staff availability result
message GetStaffAvailabilityResult {
  // staff availability info
  repeated StaffAvailabilityInfo staff_list = 3 [deprecated = true];
  // staff available list
  repeated moego.models.organization.v1.StaffAvailability staff_availability_list = 1;
}

// StaffAvailabilityInfo
message StaffAvailabilityInfo {
  // staff id
  int64 staff_id = 1;
  // is available
  bool is_available = 2;
  // start date
  string start_date = 3;
  // schedule type
  moego.models.organization.v1.ScheduleType schedule_type = 4;
  // weeks availability, key: "firstWeek", "secondWeek", "thirdWeek", "forthWeek"
  map<string, WeekAvailability> weeks = 5;
}

// WeekAvailability
message WeekAvailability {
  // by slot, key: "sunday", "monday", ..., "saturday"
  map<string, moego.models.organization.v1.SlotAvailabilityDay> days = 1;
}

// UpdateStaffAvailabilityParams
message UpdateStaffAvailabilityParams {
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff availability info
  repeated StaffAvailabilityInfo staff_list = 3 [deprecated = true];
  // staff available list
  repeated moego.models.organization.v1.StaffAvailabilityDef staff_availability_list = 1 [(validate.rules).repeated = {max_items: 100}];
}

// UpdateStaffAvailabilityOverrideParams
message UpdateStaffAvailabilityOverrideParams {
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
  // slot override days
  repeated moego.models.organization.v1.SlotAvailabilityDay override_days = 4 [(validate.rules).repeated = {max_items: 100}];
  // slot override days
  repeated moego.models.organization.v1.SlotAvailabilityDayDef slot_override_days = 5 [(validate.rules).repeated = {max_items: 100}];
  // time override days
  repeated moego.models.organization.v1.TimeAvailabilityDayDef time_override_days = 6 [(validate.rules).repeated = {max_items: 100}];
}

// UpdateStaffAvailabilityResponse
message UpdateStaffAvailabilityResult {}

// GetStaffAvailabilityOverrideParams
message GetStaffAvailabilityOverrideParams {
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff ids
  repeated int64 staff_id_list = 3 [(validate.rules).repeated = {unique: true}];
  // availability type
  optional moego.models.organization.v1.AvailabilityType availability_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// SlotAvailabilityDayOngoingHistoryList
message SlotAvailabilityDayOngoingHistoryList {
  // slot availability day
  repeated moego.models.organization.v1.SlotAvailabilityDay ongoing = 1;
  // staff availability info history
  repeated moego.models.organization.v1.SlotAvailabilityDay history = 2;
}

// TimeAvailabilityDayOngoingHistoryList
message TimeAvailabilityDayOngoingHistoryList {
  // time availability day
  repeated moego.models.organization.v1.TimeAvailabilityDay ongoing = 1;
  // staff availability info history
  repeated moego.models.organization.v1.TimeAvailabilityDay history = 2;
}

// GetStaffAvailabilityOverrideResult
message GetStaffAvailabilityOverrideResult {
  // staff availability info
  map<int64, SlotAvailabilityDayOngoingHistoryList> staff_map = 1;
  // time availability info
  map<int64, TimeAvailabilityDayOngoingHistoryList> time_staff_map = 2;
}

// DeleteStaffAvailabilityOverrideParams
message DeleteStaffAvailabilityOverrideParams {
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
  // staff availability override day list
  repeated string override_days = 4 [(validate.rules).repeated = {
    min_items: 1
    unique: true
  }];
  // availability type
  optional moego.models.organization.v1.AvailabilityType availability_type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// DeleteStaffAvailabilityOverrideResult
message DeleteStaffAvailabilityOverrideResult {}

// init staff availability params
message InitStaffAvailabilityParams {
  // company id
  optional int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // staff id list
  repeated int64 staff_ids = 3 [(validate.rules).repeated = {max_items: 100}];
  // start business id
  optional int64 start_business_id = 4 [(validate.rules).int64.gt = 0];
  // end business id
  optional int64 end_business_id = 5 [(validate.rules).int64.gt = 0];
}

// init staff availability result
message InitStaffAvailabilityResult {}

// staff service
service StaffService {
  // create a new staff
  rpc CreateStaff(CreateStaffParams) returns (CreateStaffResult);
  // get staff detail
  rpc GetStaffFullDetail(GetStaffFullDetailParams) returns (GetStaffFullDetailResult);
  // update staff
  rpc UpdateStaff(UpdateStaffParams) returns (UpdateStaffResult);
  // delete staff
  rpc DeleteStaff(DeleteStaffParams) returns (DeleteStaffResult);
  // query staff list by pagination
  rpc QueryStaffListByPagination(QueryStaffListByPaginationParams) returns (QueryStaffListByPaginationResult);
  // get all working location staffs
  rpc GetAllWorkingLocationStaffs(GetAllWorkingLocationStaffsParams) returns (GetAllWorkingLocationStaffsResult);
  // get staffs by working locations
  rpc GetStaffsByWorkingLocationIds(GetStaffsByWorkingLocationIdsParams) returns (GetStaffsByWorkingLocationIdsResult);
  // get clock in out staffs of current staff
  rpc GetClockInOutStaffs(GetClockInOutStaffsParams) returns (GetClockInOutStaffsResult);
  // get enterprise staffs by working locations
  rpc GetEnterpriseStaffsByWorkingLocationIds(GetEnterpriseStaffsByWorkingLocationIdsParams) returns (GetEnterpriseStaffsByWorkingLocationIdsResult);
  // get staff login time
  rpc GetStaffLoginTime(GetStaffLoginTimeParams) returns (GetStaffLoginTimeResult);
  // update staff login time
  rpc UpdateStaffLoginTime(UpdateStaffLoginTimeParams) returns (UpdateStaffLoginTimeResult);
  // get recommended staff login time
  rpc GetRecommendedStaffLoginTime(GetRecommendedStaffLoginTimeParams) returns (GetRecommendedStaffLoginTimeResult);

  // List staff group by role
  rpc ListStaffGroupByRole(ListStaffGroupByRoleParams) returns (ListStaffGroupByRoleResult);

  // get business级别的 staff slot availability type 返回by time or by slot
  rpc GetBusinessStaffAvailabilityType(GetBusinessStaffAvailabilityTypeParams) returns (GetBusinessStaffAvailabilityTypeResult) {}
  // update business级别的 staff slot availability type
  rpc UpdateBusinessStaffAvailabilityType(UpdateBusinessStaffAvailabilityTypeParams) returns (UpdateBusinessStaffAvailabilityTypeResult) {}

  // get staff slot availability
  rpc GetStaffAvailability(GetStaffAvailabilityParams) returns (GetStaffAvailabilityResult) {}
  // update staff slot availability
  rpc UpdateStaffAvailability(UpdateStaffAvailabilityParams) returns (UpdateStaffAvailabilityResult) {}

  // update staff override config
  rpc UpdateStaffAvailabilityOverride(UpdateStaffAvailabilityOverrideParams) returns (UpdateStaffAvailabilityResult) {}
  // get staff override config
  rpc GetStaffAvailabilityOverride(GetStaffAvailabilityOverrideParams) returns (GetStaffAvailabilityOverrideResult) {}
  // delete staff slot availability override
  rpc DeleteStaffAvailabilityOverride(DeleteStaffAvailabilityOverrideParams) returns (DeleteStaffAvailabilityOverrideResult) {}

  // get staff calender view
  rpc GetStaffCalenderView(GetStaffCalenderViewParams) returns (GetStaffCalenderViewResult);

  // init staff availability
  rpc InitStaffAvailability(InitStaffAvailabilityParams) returns (InitStaffAvailabilityResult);
}
