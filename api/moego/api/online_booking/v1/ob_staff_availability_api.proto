syntax = "proto3";

package moego.api.online_booking.v1;

import "moego/models/organization/v1/staff_availability_def.proto";
import "moego/models/organization/v1/staff_availability_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.online_booking.v1";

// GetStaffAvailabilityParams
message GetStaffAvailabilityParams {
  // staff id list, staff will init when staff_id no exist
  repeated int64 staff_id_list = 1 [(validate.rules).repeated = {unique: true}];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // availability type
  optional moego.models.organization.v1.AvailabilityType availability_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// StaffAvailabilityResult
message GetStaffAvailabilityResult {
  // staff available list
  repeated moego.models.organization.v1.StaffAvailability staff_availability_list = 1;
  // staff availability info
  repeated StaffAvailabilityInfo staff_list = 2;
}

// StaffAvailabilityInfo
message StaffAvailabilityInfo {
  // staff id
  int64 staff_id = 1;
  // is available
  bool is_available = 2;
  // start date
  string start_date = 3;
  // schedule type
  moego.models.organization.v1.ScheduleType schedule_type = 4;
  // weeks availability, key: "firstWeek", "secondWeek", "thirdWeek", "forthWeek"
  map<string, WeekAvailability> weeks = 5;
}

// WeekAvailability
message WeekAvailability {
  // by slot, key: "sunday", "monday", ..., "saturday"
  map<string, moego.models.organization.v1.SlotAvailabilityDay> days = 1;
  // by time, key: "sunday", "monday", ..., "saturday"
  map<string, moego.models.organization.v1.TimeAvailabilityDayDef> by_time_days = 2;
}

// UpdateStaffAvailabilityParams
message UpdateStaffAvailabilityParams {
  // staff available list
  repeated moego.models.organization.v1.StaffAvailabilityDef staff_availability_list = 1 [(validate.rules).repeated = {min_items: 1}];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// UpdateStaffAvailabilityResult
message UpdateStaffAvailabilityResult {}

// staff available service
service OBStaffAvailabilityService {
  // get staff available
  rpc GetStaffAvailability(GetStaffAvailabilityParams) returns (GetStaffAvailabilityResult);
  // update staff available
  rpc UpdateStaffAvailability(UpdateStaffAvailabilityParams) returns (UpdateStaffAvailabilityResult);
}
