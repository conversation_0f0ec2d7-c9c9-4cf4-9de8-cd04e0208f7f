// @since 2023-10-20 17:20:50
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.order.v1;

import "moego/models/marketing/v1/discount_code_models.proto";
import "moego/models/order/v1/order_detail_models.proto";
import "moego/models/order/v1/order_line_item_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/order/v1;orderapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.order.v1";

// get available discount code list request
message GetAvailableDiscountListRequest {
  // pagination request
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message.required = true];
  // invoice id
  int64 invoice_id = 2 [(validate.rules).int64 = {gt: 0}];
  // code name
  optional string code_name = 3 [(validate.rules).string = {max_len: 20}];
}

// get available discount code list response
message GetAvailableDiscountListResponse {
  // pagination response
  moego.utils.v2.PaginationResponse pagination = 1;
  // discount code list
  repeated moego.models.marketing.v1.DiscountCodeCompositeView discount_code_composite_views = 2;
}

// ListApplicableLineItemsParams
message ListApplicableLineItemsParams {
  // discount id
  int64 discount_id = 1;
  // order id
  int64 order_id = 2;
}

// ListApplicableLineItemsResult
message ListApplicableLineItemsResult {
  // Order Line Item Model
  repeated models.order.v1.OrderLineItemModel order_line_items = 1;
  // 关联的宠物的基本信息.
  repeated models.order.v1.OrderDetailView.PetBrief pet_briefs = 2;
}

// order discount_code service
service OrderDiscountCodeService {
  // get available discount code list
  rpc GetAvailableDiscountList(GetAvailableDiscountListRequest) returns (GetAvailableDiscountListResponse);
  // list applicable line items
  rpc ListApplicableLineItems(ListApplicableLineItemsParams) returns (ListApplicableLineItemsResult);
}
