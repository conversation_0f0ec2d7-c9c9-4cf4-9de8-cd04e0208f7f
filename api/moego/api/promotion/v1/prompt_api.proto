syntax = "proto3";

package moego.api.promotion.v1;

import "moego/api/appointment/v1/appointment_view.proto";
import "moego/models/marketing/v1/discount_code_models.proto";
import "moego/models/membership/v1/membership_models.proto";
import "moego/models/promotion/v1/coupon.proto";
import "moego/models/promotion/v1/prompt.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/promotion/v1;promotionapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.promotion.v1";

// promotion service
service PromotionService {
  // list promotions
  rpc ListPromotions(ListPromotionsRequest) returns (ListPromotionsResponse);

  // SearchCoupons
  rpc SearchCoupons(SearchCouponsRequest) returns (SearchCouponsResponse);
}

// list promotions request
message ListPromotionsRequest {
  // page name
  string page = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // filter, a custom filter specified by the page
  // e.g. {"ob_name":"CrazyPet"}
  map<string, string> filter = 2 [(validate.rules).map = {
    max_pairs: 20
    keys: {
      string: {
        max_len: 200
        in: ["ob_name"]
      }
    }
  }];
}

// list promotions response
message ListPromotionsResponse {
  // promotions
  repeated moego.models.promotion.v1.SlotModel promotions = 1;
}

// search coupons request
message SearchCouponsRequest {
  // search condition
  moego.models.promotion.v1.CouponSearchCondition search_condition = 1;
}

// search coupons response
message SearchCouponsResponse {
  // available coupons
  repeated moego.models.promotion.v1.Coupon available_coupons = 1;
  // unavailable coupons
  repeated moego.models.promotion.v1.Coupon unavailable_coupons = 2;
  // membership subjects
  repeated moego.models.membership.v1.MembershipModel membership_subjects = 3;
  // package subjects
  repeated moego.api.appointment.v1.CustomerPackageView package_subjects = 4;
  // discount subjects
  repeated moego.models.marketing.v1.DiscountCodeCompositeView discount_subjects = 5;
}
