syntax = "proto3";

package moego.client.message.v2;

import "moego/client/message/v2/message_view.proto";
import "moego/models/message/v2/message_enums.proto";
import "moego/models/message/v2/message_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/message/v2;messageapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.message.v2";

// 提供给 Customer 的消息服务
service CustomerMessageService {
  // 向会话发送消息
  rpc SendMessage(SendMessageParams) returns (SendMessageResult);
  // 获取对话列表
  rpc ListChat(ListChatParams) returns (ListChatResult);
  // 获取会话的历史消息
  rpc ListChatHistoricalMessage(ListChatHistoricalMessageParams) returns (ListChatMessageResult);
  // 获取会话的新消息
  rpc ListChatNewMessage(ListChatNewMessageParams) returns (ListChatMessageResult);

  // 获取所有会话的未读消息数量
  rpc GetAllChatUnreadMessageCount(GetAllChatUnreadMessageCountParams) returns (GetAllChatUnreadMessageCountResult);
}

// 发送消息的请求
message SendMessageParams {
  // UUID v7，用于幂等逻辑。
  // 不设置时会自动生成一个。
  optional string uuid = 1 [(validate.rules).string = {
    len: 36
    ignore_empty: true
  }];
  // 消息的目标对话 ID
  // 允许等于 0，因为要让 Customer 能主动发起新的会话
  uint64 chat_id = 2;
  // 消息内容的类型
  models.message.v2.ContentType content_type = 3 [(validate.rules).enum = {defined_only: true}];
  // 消息内容
  string content = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 1024
  }];
  // Company ID
  uint64 company_id = 5 [(validate.rules).uint64 = {gt: 0}];
  // Business ID
  uint64 business_id = 6 [(validate.rules).uint64 = {gt: 0}];
  // 消息元信息，不同的 ContentType 的元信息不同
  models.message.v2.MessageModel.Metadata metadata = 10;
}

// 发送消息的响应
message SendMessageResult {
  // 完整消息内容
  MessageView message = 1;
}

// 获取对话列表的请求
message ListChatParams {
  // 分页请求
  moego.utils.v2.PaginationRequest pagination = 1;
  // Company ID
  uint64 company_id = 5 [(validate.rules).uint64 = {gt: 0}];
  // Business ID
  uint64 business_id = 6 [(validate.rules).uint64 = {gt: 0}];
}

// 获取对话列表的响应
message ListChatResult {
  // 对话列表
  repeated ChatView chats = 1;
  // 分页响应
  moego.utils.v2.PaginationResponse pagination = 2;
}

// 获取指定对话的历史消息的请求
message ListChatHistoricalMessageParams {
  // 不传 next 时，等效于获取最新的 page_size 条消息
  // 传 next 时，获取更早的 page_size 条消息
  optional uint64 next = 1 [(validate.rules).uint64 = {
    gte: 0
    ignore_empty: true
  }];
  // 单次最多获取消息的数量
  uint32 page_size = 2 [(validate.rules).uint32 = {
    lte: 100
    gt: 0
  }];
  // 对话 ID
  uint64 chat_id = 3 [(validate.rules).uint64 = {gt: 0}];
  // Company ID
  uint64 company_id = 4 [(validate.rules).uint64 = {gt: 0}];
  // Business ID
  uint64 business_id = 5 [(validate.rules).uint64 = {gt: 0}];
}

// 获取指定对话的新消息的响应
message ListChatNewMessageParams {
  // 获取新消息时必须带上 next。
  // 第一次调用时，应先通过 ListChatHistoricalMessage 的 Response 获取 next。
  uint64 next = 1 [(validate.rules).uint64 = {gt: 0}];
  // 单次最多获取消息的数量
  uint32 page_size = 2 [(validate.rules).uint32 = {
    lte: 100
    gt: 0
  }];
  // 对话 ID
  uint64 chat_id = 3 [(validate.rules).uint64 = {gt: 0}];
  // Company ID
  uint64 company_id = 4 [(validate.rules).uint64 = {gt: 0}];
  // Business ID
  uint64 business_id = 5 [(validate.rules).uint64 = {gt: 0}];
}

// 获取指定对话消息列表的响应
message ListChatMessageResult {
  // 消息列表
  repeated MessageView messages = 1;
}

// 获取所有会话的未读消息数量的请求
message GetAllChatUnreadMessageCountParams {
  // Company ID
  uint64 company_id = 4 [(validate.rules).uint64 = {gt: 0}];
  // Business ID
  uint64 business_id = 5 [(validate.rules).uint64 = {gt: 0}];
}

// 获取所有会话的未读消息数量的响应
message GetAllChatUnreadMessageCountResult {
  // 总的未读数量
  uint64 unread_message_count = 1;
}
