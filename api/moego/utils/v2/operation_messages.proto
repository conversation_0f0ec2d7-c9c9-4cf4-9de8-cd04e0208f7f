// @since 2023-07-27 10:03:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.utils.v2;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2;utilsV2";
option java_multiple_files = true;
option java_package = "com.moego.idl.utils.v2";

// add operation info for record
message OperationRequest {
  // moego account id
  optional int64 account_id = 1;
  // the operator id
  oneof operator_id {
    // company staff id
    int64 staff_id = 2;
    // company customer id
    int64 business_customer_id = 3;
    // mis internal account id (email)
    // or system components
    string admin_account_id = 4;
  }
  // the operator name
  optional string operator_name = 7 [(validate.rules).string = {max_len: 50}];
  // the session id
  optional int64 session_id = 5;
  // the time
  optional google.protobuf.Timestamp time = 6;
}
