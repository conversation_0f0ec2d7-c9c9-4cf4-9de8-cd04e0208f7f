syntax = "proto3";

package moego.enterprise.subscription.v1;

import "moego/models/payment/v1/payment_method_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/subscription/v1;subscriptionapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.subscription.v1";

// ListCardsParams
message ListCardsParams {
  // Enterprise ID
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// ListCardsResponse
message ListCardsResponse {
  // Cards
  repeated models.payment.v1.Card cards = 1;
}

// GetEnterpriseUnitParams
message GetEnterpriseUnitParams {}

// GetEnterpriseUnitResponse
message GetEnterpriseUnitResponse {
  // total location num in subscription
  int64 total_location_num = 1 [(validate.rules).int64 = {gte: 0}];
  // total van num in subscription
  int64 total_van_num = 2 [(validate.rules).int64 = {gte: 0}];
  // used location num
  int64 used_location_num = 3 [(validate.rules).int64 = {gte: 0}];
  // used van num
  int64 used_van_num = 4 [(validate.rules).int64 = {gte: 0}];
}

// SubscriptionService
service SubscriptionService {
  // ListCards
  rpc ListCards(ListCardsParams) returns (ListCardsResponse) {}
  // GetEnterpriseUnit
  rpc GetEnterpriseUnit(GetEnterpriseUnitParams) returns (GetEnterpriseUnitResponse) {}
}
