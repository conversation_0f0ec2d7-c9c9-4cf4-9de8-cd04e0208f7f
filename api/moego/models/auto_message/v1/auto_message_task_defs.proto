// @since 2024-06-05 11:24:51
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.auto_message.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/auto_message/v1/auto_message_task_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/auto_message/v1;automessagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.auto_message.v1";

// auto message task insert or update definition
message AutoMessageTaskUpsertDef {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // auto message config id
  int64 auto_msg_config_id = 3 [(validate.rules).int64 = {gt: 0}];
  // object id
  int64 object_id = 4 [(validate.rules).int64 = {gt: 0}];
  // object type
  AutoMessageTaskObjectType object_type = 5 [(validate.rules).enum = {defined_only: true}];
  // receiver id
  int64 receiver_id = 6 [(validate.rules).int64 = {gt: 0}];
  // receiver type
  AutoMessageTaskReceiverType receiver_type = 7 [(validate.rules).enum = {defined_only: true}];
  // send time
  google.protobuf.Timestamp send_time = 8 [(validate.rules).timestamp = {
    gt: {seconds: 0}
  }];
  // status
  AutoMessageTaskStatus status = 9 [(validate.rules).enum = {defined_only: true}];
}
