syntax = "proto3";

package moego.models.agreement.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1;agreementpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.agreement.v1";

// policy requirement for signed an agreement
enum SignedPolicy {
  // unspecified
  SIGNED_POLICY_UNSPECIFIED = 0;
  // sign for first
  SIGNED_POLICY_FOR_FIRST = 1;
  // sign for each
  SIGNED_POLICY_FOR_EACH = 2;
  // allow not to sign
  SIGNED_POLICY_OPTIONAL = 3;
}

// signing status of agreement
enum SignedStatus {
  // unspecified
  SIGNED_STATUS_UNSPECIFIED = 0;
  // unsigned
  SIGNED_STATUS_UNSIGNED = 1;
  // signed
  SIGNED_STATUS_SIGNED = 2;
}

// signed an agreement type
enum SignedType {
  // unspecified
  SIGNED_TYPE_UNSPECIFIED = 0;
  // customer signature
  SIGNED_TYPE_BY_CUSTOMER_SIGNED = 1;
  // business upload file
  SIGNED_TYPE_BY_BUSINESS_UPLOAD = 2;
}

// service type
enum ServiceType {
  // unspecified
  SERVICE_TYPE_UNSPECIFIED = 0;
  // online booking
  SERVICE_TYPE_ONLINE_BOOKING = 1;
  // intake form
  SERVICE_TYPE_INTAKE_FORM = 2;
  // grooming
  SERVICE_TYPE_GROOMING = 4;
  // boarding
  SERVICE_TYPE_BOARDING = 8;
  // daycare
  SERVICE_TYPE_DAYCARE = 16;
  // training
  SERVICE_TYPE_TRAINING = 32;
  // platform care
  SERVICE_TYPE_PLATFORM_CARE = 64;
  // platform sales
  SERVICE_TYPE_PLATFORM_SALES = 128;
}

// agreement source type
enum SourceType {
  // unspecified
  SOURCE_TYPE_UNSPECIFIED = 0;
  // from web url
  SOURCE_TYPE_URL = 1;
  // from mobile app
  SOURCE_TYPE_MOBILE = 2;
  // from online booking
  SOURCE_TYPE_ONLINE_BOOKING = 3;
  // from intake form
  SOURCE_TYPE_INTAKE_FORM = 4;
}

// agreement source
enum Source {
  // unspecified
  SOURCE_UNSPECIFIED = 0;
  // from company
  SOURCE_COMPANY = 1;
  // from enterprise
  SOURCE_ENTERPRISE = 2;
}
