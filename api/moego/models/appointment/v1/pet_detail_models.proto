syntax = "proto3";

package moego.models.appointment.v1;

import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// the appointment pet detail model, Includes details of different types of appointment services
message PetDetailModel {
  // pet detail id
  int64 id = 1;
  // grooming id
  int64 grooming_id = 2;
  // pet id
  int64 pet_id = 3;
  // staff id
  int64 staff_id = 4;
  // service id
  int64 service_id = 5;
  // service type
  models.offering.v1.ServiceType service_type = 6;
  // service time, in minutes
  int32 service_time = 7;
  // service price
  double service_price = 8;
  // start time, in minutes
  int32 start_time = 9;
  // end time, in minutes
  int32 end_time = 10;
  // status
  PetDetailStatus status = 11;
  // update time
  int64 update_time = 12;
  // scope type price
  models.offering.v1.ServiceScopeType scope_type_price = 13;
  // scope type time
  models.offering.v1.ServiceScopeType scope_type_time = 14;
  // star staff id
  int64 star_staff_id = 15;
  // package service id
  int64 package_service_id = 16;
  //  // service name
  //  string service_name = 17;
  //  // service description
  //  string service_description = 18;
  //  // tax id
  //  int64 tax_id = 19;
  //  // tax rate
  //  double tax_rate = 20;
  // enable operation
  bool enable_operation = 21;
  // work mode, 0-parallel, 1-sequence
  models.appointment.v1.WorkMode work_mode = 22;
  // service color code
  string service_color_code = 23;
  // service start date, in yyyy-MM-dd format, for boarding or daycare service
  string start_date = 24;
  // service end date, in yyyy-MM-dd format, for boarding or daycare service
  string end_date = 25;
  // service item type, different from service type, it includes grooming, boarding, daycare or other services.
  models.offering.v1.ServiceItemType service_item_type = 26;
  // lodging id, only for boarding service item type
  int64 lodging_id = 27;
  // price unit, 1 - per session, 2 - per night, 3 - per hour, 4 - per day
  int32 price_unit = 28;
  // add-on specific dates, yyyy-MM-dd
  string specific_dates = 29;
  // add-on associated service id
  int64 associated_service_id = 30;
  // pet detail date type
  optional models.appointment.v1.PetDetailDateType date_type = 31;
  // price override type
  optional models.offering.v1.ServiceOverrideType price_override_type = 32;
  // duration override value
  optional models.offering.v1.ServiceOverrideType duration_override_type = 33;
  // quantity per day
  int32 quantity_per_day = 34;
  // total price
  double total_price = 35;
  // quantity
  int32 quantity = 36;
  // order line item id
  int64 order_line_item_id = 37;
}

// the appointment pet detail model client view
message PetDetailModelClientView {
  // pet id
  int64 pet_id = 1;
  // staff id
  int64 staff_id = 2;
  // service id
  int64 service_id = 3;
  // service time, in minutes
  int32 service_time = 5;
  // service price
  double service_price = 6;
  // service type
  models.offering.v1.ServiceType service_type = 7;
  // pet detail date type
  optional models.appointment.v1.PetDetailDateType date_type = 8;
  // add-on specific dates, yyyy-MM-dd
  repeated string specific_dates = 9;
  // quantity per day
  int32 quantity_per_day = 10;
  // add-on associated service id
  int64 associated_service_id = 11;
}

// the appointment pet detail model, Includes details of different types of appointment services
message PetDetailPopView {
  // pet detail id
  int64 id = 1;
  // appointment id
  int64 appointment_id = 2;
  // pet id
  int64 pet_id = 3;
  // staff id
  int64 staff_id = 4;
  // service id
  int64 service_id = 5;
  // service type
  models.offering.v1.ServiceType service_type = 6;
  // service time, in minutes
  int32 service_time = 7;
  // service price
  double service_price = 8;
  // start time, in minutes
  int32 start_time = 9;
  // end time, in minutes
  int32 end_time = 10;
  // status
  PetDetailStatus status = 11;
  // update time
  int64 update_time = 12;
  // scope type price
  models.offering.v1.ServiceScopeType scope_type_price = 13;
  // scope type time
  models.offering.v1.ServiceScopeType scope_type_time = 14;
  // star staff id
  int64 star_staff_id = 15;
  // package service id
  int64 package_service_id = 16;
  // enable operation
  bool enable_operation = 21;
  // work mode, 0-parallel, 1-sequence
  models.appointment.v1.WorkMode work_mode = 22;
  // service color code
  string service_color_code = 23;
  // service start date, in yyyy-MM-dd format, for boarding or daycare service
  string start_date = 24;
  // service end date, in yyyy-MM-dd format, for boarding or daycare service
  string end_date = 25;
  // service item type, different from service type, it includes grooming, boarding, daycare or other services.
  models.offering.v1.ServiceItemType service_item_type = 26;
  // lodging id, only for boarding service item type
  int64 lodging_id = 27;
}

// the appointment staff pet detail model, Includes grooming pet detail & operation & evaluation
message StaffPetDetail {
  // pet_detail_id or evaluation_id(differentiation by service_item_type field)
  int64 id = 1;
  // appointment id
  int64 appointment_id = 2;
  // pet id
  int64 pet_id = 3;
  // staff id
  int64 staff_id = 4;
  // service id
  int64 service_id = 5;
  // service time, in minutes
  int32 service_time = 7;
  // start time, in minutes
  int32 start_time = 9;
  // end time, in minutes
  int32 end_time = 10;
  // customer id
  int64 customer_id = 11;
  // pet start_date
  string start_date = 12;
  // appointment date
  string appointment_date = 13;
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 14;
  // appt is block
  bool is_block = 15;
  // service price
  double service_price = 16;
}
