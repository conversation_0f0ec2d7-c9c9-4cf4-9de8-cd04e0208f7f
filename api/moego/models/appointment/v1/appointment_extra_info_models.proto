syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// the appointment extra info model
message AppointmentExtraInfoModel {
  // id
  int64 id = 1;
  // appointment id
  int64 appointment_id = 2;
  // Is new order
  bool is_new_order = 3;
}
