// @since 2024-01-15 16:36:40
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

import "moego/models/appointment/v1/lodging_enums.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// lodging ticket
message LodgingTicket {
  // id
  int64 ticket_id = 1;
  // ticket start date
  string start_date = 2;
  // ticket end date
  string end_date = 3;
  // ticket start time
  int32 start_time = 4;
  // ticket end time
  int32 end_time = 5;
  // lodging id
  int64 lodging_id = 6;
  // customer id
  int64 customer_id = 7;
  // lodging usage for pets
  repeated LodgingUsage usages = 8;
  // color code
  string color_code = 9;
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 10;
}

// lodging usage
message LodgingUsage {
  // pet detail id
  int64 id = 1;
  // pet id
  int64 pet_id = 2;
  // pet image
  string pet_image = 3;
  // pet name
  string pet_name = 4;
  // pet type
  string pet_type = 5;
}

// lodging unit view
message LodgingListView {
  // lodging unit type id
  int64 lodging_type_id = 1;
  // lodging unit type name
  string lodging_type_name = 2;
  // lodging unit list
  repeated LodgingUnitView lodging_units = 3;
}

// lodging unit view
message LodgingUnitView {
  // lodging unit id
  int64 id = 1;
  // lodging unit name
  string name = 2;
  // lodging status
  moego.models.appointment.v1.LodgingOccupiedStatus occupied_status = 3;
  // is applicable
  bool is_applicable = 4;
}

// lodging assign info
message LodgingAssignInfo {
  // lodging unit id
  int64 lodging_id = 1;
  // assign appointment
  repeated LodgingAssignAppointmentInfo appointments = 2;
}

// lodging assign appointment info
message LodgingAssignAppointmentInfo {
  // appointment id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // assign pet details
  repeated LodgingAssignPetDetailInfo pet_details = 3;
  // assign pet evaluation details
  repeated LodgingAssignPetEvaluationInfo pet_evaluations = 4;
}

// lodging assign pet detail info
message LodgingAssignPetDetailInfo {
  // pet detail id
  int64 id = 1;
  // pet id
  int32 pet_id = 2;
  // boarding service start date
  string start_date = 3;
  // boarding service end date
  string end_date = 4;
  // boarding service start time
  int32 start_time = 5;
  // boarding service end time
  int32 end_time = 6;
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 7;
  // pet detail certain dates
  repeated string specific_dates = 8;
  // pet detail date type
  optional PetDetailDateType date_type = 9;
  // is split lodging
  bool is_split_lodging = 10;
}

// lodging assign pet evaluation detail info
message LodgingAssignPetEvaluationInfo {
  // pet evaluation detail id
  int64 id = 1;
  // pet id
  int32 pet_id = 2;
  // boarding service start date
  string start_date = 3;
  // boarding service end date
  string end_date = 4;
  // boarding service start time
  int32 start_time = 5;
  // boarding service end time
  int32 end_time = 6;
}
