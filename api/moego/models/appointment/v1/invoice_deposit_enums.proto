// @since 2024-02-22 11:28:28
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// invoice deposit status
enum InvoiceDepositStatus {
  // deleted
  INVOICE_DEPOSIT_STATUS_DELETED = 0;
  // created
  INVOICE_DEPOSIT_STATUS_CREATED = 1;
  // paid
  INVOICE_DEPOSIT_STATUS_PAID = 2;
}
