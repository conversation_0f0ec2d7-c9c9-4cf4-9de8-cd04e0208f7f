syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/appointment/v1/appointment_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// the block time model
message BlockTimeModel {
  // appointment id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // company id
  int64 company_id = 3;
  // staff id
  int64 staff_id = 4;
  // start date
  string start_date = 5;
  // start time
  int32 start_time = 6;
  // end date
  string end_date = 7;
  // end time
  int32 end_time = 8;
  // created by
  int64 created_by = 9;
  // color code
  string color_code = 10;
  // description
  string description = 11;
  // Source of the appointment
  models.appointment.v1.AppointmentSource source = 12;
  // repeat id of the appointment
  int64 repeat_id = 13;
  // create time
  google.protobuf.Timestamp create_time = 14;
  // update time
  google.protobuf.Timestamp update_time = 15;
}
