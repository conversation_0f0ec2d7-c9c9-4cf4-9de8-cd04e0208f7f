syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// work mode
enum WorkMode {
  // parallel
  PARALLEL = 0;
  // sequence
  SEQUENCE = 1;
}

// pet detail status
enum PetDetailStatus {
  // unspecified
  PET_DETAIL_STATUS_UNSPECIFIED = 0;
  // normal
  NORMAL = 1;
  // deleted
  DELETED = 2;
  // deletion caused by modification
  MODIFIED = 3;
}

// pet detail date type
// non-storage field
// the judgment logic is as follows
// 0. for service, associated service means main service.
// 1. associated_service_id not exist, then it is PET_DETAIL_DATE_DATE_POINT
// 2. associated_service_id exist and specific_dates is not empty, then it is PET_DETAIL_DATE_SPECIFIC_DATE
// 3. associated_service_id exist and start_date is empty, then it is PET_DETAIL_DATE_EVERYDAY
//
// 当值为 PET_DETAIL_DATE_SPECIFIC_DATE 时，肯定会有 specific_dates/dates 属性
// 当值为 PET_DETAIL_DATE_DATE_POINT，肯定会有 start_date/date 属性
enum PetDetailDateType {
  // unspecified
  PET_DETAIL_DATE_TYPE_UNSPECIFIED = 0;
  // Everyday during whole stay period, except for checkout day
  PET_DETAIL_DATE_EVERYDAY = 1; // associated_service_id exist and start_date is empty
  // Specific date
  PET_DETAIL_DATE_SPECIFIC_DATE = 2; // associated_service_id exist and specific_dates is not empty
  // Date point, start date
  PET_DETAIL_DATE_DATE_POINT = 3; // associated_service_id not exist
  // Everyday during whole stay period, include checkout day
  PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY = 4; // associated_service_id exist and start_date is empty
  // every day except check-in day
  PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY = 5;
  // last day
  PET_DETAIL_DATE_LAST_DAY = 6;
  // first day
  PET_DETAIL_DATE_FIRST_DAY = 7;
}

// Add on date type
// deprecated. use PetDetailDateType instead
enum AddOnDateType {
  option deprecated = true;
  // unspecified
  ADD_ON_DATE_TYPE_UNSPECIFIED = 0;
  // Everyday during whole stay period
  EVERYDAY = 1;
  // Specific date
  SPECIFIC_DATE = 2;
  // Date point, start date and start time
  DATE_POINT = 3;
}

// Repeat appointment modify scope for service
enum RepeatAppointmentModifyScope {
  // unspecified
  REPEAT_APPOINTMENT_MODIFY_SCOPE_UNSPECIFIED = 0;
  // Only this one
  ONLY_THIS = 1;
  // Apply to this and all following appointments
  THIS_AND_FOLLOWING = 2;
  // Apply to all appointments in this repeat series
  ALL = 3;
}
