syntax = "proto3";

package moego.models.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_merge_enums.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// duplicate customer group, each group should have at least 2 customers
message DuplicateCustomerGroup {
  // duplication detect rule
  DuplicationDetectRule rule = 1;

  // customers
  repeated CustomerDuplicationCheckView customers = 2;
}

// customer duplication check view
message CustomerDuplicationCheckView {
  // id
  int64 id = 1;
  // first name
  string first_name = 2;
  // last name
  string last_name = 3;
  // phone number
  string phone_number = 4;
  // email
  string email = 5;
  // pets
  // passed away pets will not be included
  repeated PetDuplicationCheckView pets = 6;
}

// pet duplication check view
message PetDuplicationCheckView {
  // id
  int64 id = 1;
  // pet name
  string pet_name = 2;
  // pet type
  models.customer.v1.PetType pet_type = 3;
  // breed
  string breed = 4;
}
