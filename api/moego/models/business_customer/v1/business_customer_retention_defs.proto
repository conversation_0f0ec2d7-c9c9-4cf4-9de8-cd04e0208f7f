syntax = "proto3";

package moego.models.business_customer.v1;

import "google/type/interval.proto";
import "moego/models/reporting/v2/diagram_model.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Retention filter params
message RetentionFilter {
  // Care type
  enum CareType {
    // unspecified
    CARE_TYPE_UNSPECIFIED = 0;
    // boarding
    BOARDING = 1;
    // daycare
    DAYCARE = 2;
    // grooming
    GROOMING = 3;
    // evaluation
    EVALUATION = 4;
    // non-service sales
    NON_SERVICE_SALES = 20;
  }
  // selected date range
  google.type.Interval period = 1;
  // selected care types
  repeated CareType care_types = 2;
}

// Customer retention data
message CustomerRetentionData {
  // Table column enum
  enum TableColumn {
    // Unspecified
    TABLE_COLUMN_UNSPECIFIED = 0;
    // Client name
    CLIENT_NAME = 1;
    // Average
    AVERAGE = 2;
    // January
    JANUARY = 3;
    // February
    FEBRUARY = 4;
    // March
    MARCH = 5;
    // April
    APRIL = 6;
    // May
    MAY = 7;
    // June
    JUNE = 8;
    // July
    JULY = 9;
    // August
    AUGUST = 10;
    // September
    SEPTEMBER = 11;
    // October
    OCTOBER = 12;
    // November
    NOVEMBER = 13;
    // December
    DECEMBER = 14;
  }
  // Customer view definition
  message CustomerRetentionView {
    // customer id
    int64 id = 1;
    // first name
    string first_name = 2;
    // last name
    string last_name = 3;
    // avatar path
    string avatar_path = 4;
    // is new customer
    bool is_new = 5;
    // is prospect customer
    bool is_prospect = 6;
    // preferred frequency day, use old fields for compatibility
    int32 preferred_frequency_day = 7;
    // preferred frequency type, use old fields for compatibility
    int32 preferred_frequency_type = 8;
    // has pet parent app account
    bool has_pet_parent_app_account = 9;
    // is inactive
    bool inactive = 10;
  }
  // Retention data
  message RetentionData {
    // column
    TableColumn column = 1;
    // total appointments count
    moego.models.reporting.v2.NumberData total_visits = 2;
    // total collected amount
    moego.models.reporting.v2.NumberData total_spent = 3;
  }
  // customer info
  CustomerRetentionView customer = 1;
  // row data
  repeated RetentionData retention_data = 2;
}
