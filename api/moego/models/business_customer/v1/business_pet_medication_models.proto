syntax = "proto3";

package moego.models.business_customer.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Business pet medication model
message BusinessPetMedicationModel {
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // pet id
  int64 pet_id = 3;
  // medication amount, such as 1.2, 1/2, 1 etc.
  string medication_amount = 4;
  // medication unit, pet_metadata.metadata_value, metadata_name = 7
  string medication_unit = 5;
  // medication name, user input
  string medication_name = 6;
  // medication source, user input
  string medication_note = 7;
  // created at
  google.protobuf.Timestamp created_at = 8;
  // updated at
  google.protobuf.Timestamp updated_at = 9;
  // deleted at
  google.protobuf.Timestamp deleted_at = 10;
}
