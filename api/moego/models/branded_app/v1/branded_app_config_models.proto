// @since 2024-06-03 14:43:06
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.branded_app.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/account/v1/account_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/branded_app/v1;brandedapppb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.branded_app.v1";

// The BrandedAppConfig model
message BrandedAppConfigModel {
  // the unique id
  int64 id = 1;

  // the branded app id
  string branded_app_id = 2;

  // the theme color
  string theme_color = 3;

  // the logo url
  string logo_url = 4;

  // the app name
  string app_name = 5;

  // introduction
  string introduction = 6;

  // description
  string description = 7;

  // country code
  repeated string country_code = 8;

  // branded type, enterprise or company
  account.v1.AccountNamespaceType branded_type = 9;

  // branded id, enterprise id or company id
  int64 branded_id = 10;

  // phone number
  string phone_number = 11;

  // require payment method
  bool require_payment_method = 12;

  // the created time
  google.protobuf.Timestamp created_at = 14;
  // the updated time
  google.protobuf.Timestamp updated_at = 15;

  // iOS download link
  string ios_download_link = 16;

  // Android download link
  string android_download_link = 17;

  // App icon url
  string app_icon_url = 18;

  // Boot download link
  string boot_download_link = 19;

  // The flag of allowed to free cancel appointment
  bool is_free_cancellation = 20;

  // The X hours before can cancel for free, cancel within X hours for a fee
  int32 cancellation_threshold_hours = 21;

  // Fee to be paid if canceled within cancellation threshold hours
  double cancellation_fee = 22;

  // The flag of allowed to reschedule appointment
  bool is_allow_reschedule = 23;

  // X hours before can reschedule for free, reschedule unavailable within reschedule threshold hours
  int32 reschedule_threshold_hours = 24;

  // The flag of allowed to cancel appointment
  bool is_allow_cancel = 25;
}

// The BrandedAppConfig view
message BrandedAppConfigView {
  // the branded app id
  string branded_app_id = 1;

  // the theme color
  string theme_color = 2;

  // the logo url
  string logo_url = 3;

  // the app name
  string app_name = 4;

  // introduction
  string introduction = 5;

  // description
  string description = 6;

  // country code
  repeated string country_code = 7;

  // phone number
  string phone_number = 8;

  // branded type, enterprise or company
  account.v1.AccountNamespaceType branded_type = 9;

  // branded id, enterprise id or company id
  int64 branded_id = 10;

  // require payment method
  bool require_payment_method = 12;

  // The flag of allowed to free cancel appointment
  bool is_free_cancellation = 20;

  // The X hours before can cancel for free, cancel within X hours for a fee
  int32 cancellation_threshold_hours = 21;

  // Fee to be paid if canceled within cancellation threshold hours
  double cancellation_fee = 22;

  // The flag of allowed to reschedule appointment
  bool is_allow_reschedule = 23;

  // X hours before can reschedule for free, reschedule unavailable within reschedule threshold hours
  int32 reschedule_threshold_hours = 24;

  // The flag of allowed to cancel appointment
  bool is_allow_cancel = 25;
}
