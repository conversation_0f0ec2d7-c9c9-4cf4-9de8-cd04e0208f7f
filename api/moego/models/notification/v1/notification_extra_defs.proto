syntax = "proto3";

package moego.models.notification.v1;

import "moego/models/appointment/v1/appointment_tracking.proto";
import "moego/models/organization/v1/company_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/notification/v1;notificationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.notification.v1";

// notification extra information
message NotificationExtraDef {
  // extra information
  oneof extra {
    // booking request sent
    BookingRequestSentDef booking_request_sent = 1000;
    // booking request accepted
    BookingRequestAcceptedDef booking_request_accepted = 1001;
    // booking request moved to wait list
    BookingRequestMovedToWaitListDef booking_request_moved_to_wait_list = 1002;
    // booking request declined
    BookingRequestDeclinedDef booking_request_declined = 1003;
    // schedule online booking waitlist
    ScheduleOnlineBookingWaitlistDef schedule_online_booking_waitlist = 1004;
    // delete online booking waitlist
    DeleteOnlineBookingWaitlistDef delete_online_booking_waitlist = 1005;
    // appointment booked
    AppointmentBookedDef appointment_booked = 1100;
    // appointment rescheduled
    AppointmentRescheduledDef appointment_rescheduled = 1101;
    // appointment cancelled
    AppointmentCancelledDef appointment_cancelled = 1102;
    // appointment first reminder
    AppointmentFirstReminderDef appointment_first_reminder = 1103;
    // appointment second reminder
    AppointmentSecondReminderDef appointment_second_reminder = 1104;
    // appointment general reminder
    AppointmentGeneralReminderDef appointment_general_reminder = 1105;
    // appointment rebook reminder
    AppointmentRebookReminderDef appointment_rebook_reminder = 1106;
    // appointment day
    AppointmentDayDef appointment_day = 1201;
    // ready to pick up
    ReadyToPickUpDef ready_to_pick_up = 1202;
    // ask for review
    AskForReviewDef ask_for_review = 1203;

    // received app message
    ReceivedAppMessageDef received_app_message = 1301;

    // appointment tracking
    AppointmentTrackingDef appointment_tracking = 1401;
  }
}

// booking request sent definition
message BookingRequestSentDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff name
  string appointment_staff_name = 2 [(validate.rules).string = {max_len: 255}];
  // store name
  string business_name = 3 [(validate.rules).string = {max_len: 255}];
  // customer name
  string customer_name = 4 [(validate.rules).string = {max_len: 255}];
  // appointment date
  string appointment_date = 5 [(validate.rules).string = {max_len: 50}];
  // appointment time
  int32 appointment_time = 6 [(validate.rules).int32 = {gte: 0}];
  // pet&service
  string pet_service = 7 [(validate.rules).string = {max_len: 1024}];
  // customer primary address
  string customer_primary_address = 8 [(validate.rules).string = {max_len: 1024}];
  // business phone number
  string business_phone_number = 9 [(validate.rules).string = {max_len: 50}];
  // business email
  string business_email = 10 [(validate.rules).string = {max_len: 255}];
  // pet parent portal link
  string pet_parent_portal_link = 11 [(validate.rules).string = {max_len: 255}];
  // business id
  int64 business_id = 12 [(validate.rules).int64 = {gt: 0}];
}

// booking request accepted definition
message BookingRequestAcceptedDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff name
  string appointment_staff_name = 2 [(validate.rules).string = {max_len: 255}];
  // store name
  string business_name = 3 [(validate.rules).string = {max_len: 255}];
  // customer name
  string customer_name = 4 [(validate.rules).string = {max_len: 255}];
  // appointment date
  string appointment_date = 5 [(validate.rules).string = {max_len: 50}];
  // appointment time
  int32 appointment_time = 6 [(validate.rules).int32 = {gte: 0}];
  // pet&service
  string pet_service = 7 [(validate.rules).string = {max_len: 1024}];
  // customer primary address
  string customer_primary_address = 8 [(validate.rules).string = {max_len: 1024}];
  // business phone number
  string business_phone_number = 9 [(validate.rules).string = {max_len: 50}];
  // business email
  string business_email = 10 [(validate.rules).string = {max_len: 255}];
  // pet parent portal link
  string pet_parent_portal_link = 11 [(validate.rules).string = {max_len: 255}];
  // business id
  int64 business_id = 12 [(validate.rules).int64 = {gt: 0}];
}

// booking request moved to wait list definition
message BookingRequestMovedToWaitListDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff name
  string appointment_staff_name = 2 [(validate.rules).string = {max_len: 255}];
  // store name
  string business_name = 3 [(validate.rules).string = {max_len: 255}];
  // customer name
  string customer_name = 4 [(validate.rules).string = {max_len: 255}];
  // appointment date
  string appointment_date = 5 [(validate.rules).string = {max_len: 50}];
  // appointment time
  int32 appointment_time = 6 [(validate.rules).int32 = {gte: 0}];
  // pet&service
  string pet_service = 7 [(validate.rules).string = {max_len: 1024}];
  // customer primary address
  string customer_primary_address = 8 [(validate.rules).string = {max_len: 1024}];
  // business phone number
  string business_phone_number = 9 [(validate.rules).string = {max_len: 50}];
  // business email
  string business_email = 10 [(validate.rules).string = {max_len: 255}];
  // pet parent portal link
  string pet_parent_portal_link = 11 [(validate.rules).string = {max_len: 255}];
  // business id
  int64 business_id = 12 [(validate.rules).int64 = {gt: 0}];
}

// booking request declined definition
message BookingRequestDeclinedDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff name
  string appointment_staff_name = 2 [(validate.rules).string = {max_len: 255}];
  // store name
  string business_name = 3 [(validate.rules).string = {max_len: 255}];
  // customer name
  string customer_name = 4 [(validate.rules).string = {max_len: 255}];
  // appointment date
  string appointment_date = 5 [(validate.rules).string = {max_len: 50}];
  // appointment time
  int32 appointment_time = 6 [(validate.rules).int32 = {gte: 0}];
  // pet&service
  string pet_service = 7 [(validate.rules).string = {max_len: 1024}];
  // customer primary address
  string customer_primary_address = 8 [(validate.rules).string = {max_len: 1024}];
  // business phone number
  string business_phone_number = 9 [(validate.rules).string = {max_len: 50}];
  // business email
  string business_email = 10 [(validate.rules).string = {max_len: 255}];
  // pet parent portal link
  string pet_parent_portal_link = 11 [(validate.rules).string = {max_len: 255}];
  // business id
  int64 business_id = 12 [(validate.rules).int64 = {gt: 0}];
}

// schedule online booking waitlist definition
message ScheduleOnlineBookingWaitlistDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // store name
  string business_name = 3 [(validate.rules).string = {max_len: 255}];
}

// delete online booking waitlist definition
message DeleteOnlineBookingWaitlistDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // store name
  string business_name = 3 [(validate.rules).string = {max_len: 255}];
}

// appointment booked definition
message AppointmentBookedDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // store name
  string business_name = 2 [(validate.rules).string = {max_len: 255}];
  // customer name
  string customer_name = 3 [(validate.rules).string = {max_len: 255}];
  // pet name
  string pet_name = 4 [(validate.rules).string = {max_len: 1024}];
  // appointment date
  string appointment_date = 5 [(validate.rules).string = {max_len: 50}];
  // appointment time
  int32 appointment_time = 6 [(validate.rules).int32 = {gte: 0}];
  // day of week
  string day_of_week = 7 [(validate.rules).string = {max_len: 50}];
  // staff name
  string staff_name = 8 [(validate.rules).string = {max_len: 255}];
  // customer primary address
  string pet_service = 9 [(validate.rules).string = {max_len: 1024}];
  // pet parent portal link
  string pet_parent_portal_link = 10 [(validate.rules).string = {max_len: 255}];
  // month
  string month = 11 [(validate.rules).string = {max_len: 50}];
  // day
  string day = 12 [(validate.rules).string = {max_len: 50}];
  // business id
  int64 business_id = 13 [(validate.rules).int64 = {gt: 0}];
  // verb
  string verb = 14 [(validate.rules).string = {max_len: 50}];
}

// appointment rescheduled definition
message AppointmentRescheduledDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // store name
  string business_name = 2 [(validate.rules).string = {max_len: 255}];
  // customer name
  string customer_name = 3 [(validate.rules).string = {max_len: 255}];
  // pet name
  string pet_name = 4 [(validate.rules).string = {max_len: 1024}];
  // appointment date
  string appointment_date = 5 [(validate.rules).string = {max_len: 50}];
  // appointment time
  int32 appointment_time = 6 [(validate.rules).int32 = {gte: 0}];
  // day of week
  string day_of_week = 7 [(validate.rules).string = {max_len: 50}];
  // staff name
  string staff_name = 8 [(validate.rules).string = {max_len: 255}];
  // customer primary address
  string pet_service = 9 [(validate.rules).string = {max_len: 1024}];
  // pet parent portal link
  string pet_parent_portal_link = 10 [(validate.rules).string = {max_len: 255}];
  // month
  string month = 11 [(validate.rules).string = {max_len: 50}];
  // day
  string day = 12 [(validate.rules).string = {max_len: 50}];
  // business id
  int64 business_id = 13 [(validate.rules).int64 = {gt: 0}];
  // verb
  string verb = 14 [(validate.rules).string = {max_len: 50}];
}

// appointment cancelled definition
message AppointmentCancelledDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // store name
  string business_name = 2 [(validate.rules).string = {max_len: 255}];
  // customer name
  string customer_name = 3 [(validate.rules).string = {max_len: 255}];
  // pet name
  string pet_name = 4 [(validate.rules).string = {max_len: 1024}];
  // appointment date
  string appointment_date = 5 [(validate.rules).string = {max_len: 50}];
  // appointment time
  int32 appointment_time = 6 [(validate.rules).int32 = {gte: 0}];
  // day of week
  string day_of_week = 7 [(validate.rules).string = {max_len: 50}];
  // staff name
  string staff_name = 8 [(validate.rules).string = {max_len: 255}];
  // customer primary address
  string pet_service = 9 [(validate.rules).string = {max_len: 1024}];
  // pet parent portal link
  string pet_parent_portal_link = 10 [(validate.rules).string = {max_len: 255}];
  // month
  string month = 11 [(validate.rules).string = {max_len: 50}];
  // day
  string day = 12 [(validate.rules).string = {max_len: 50}];
  // business id
  int64 business_id = 13 [(validate.rules).int64 = {gt: 0}];
  // verb
  string verb = 14 [(validate.rules).string = {max_len: 50}];
}

// appointment first reminder definition
message AppointmentFirstReminderDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet name
  string pet_name = 2 [(validate.rules).string = {max_len: 1024}];
  // verb
  string verb = 3 [(validate.rules).string = {max_len: 50}];
  // appointment time, 09:00 am
  string appointment_time = 4 [(validate.rules).string = {max_len: 50}];
  // day of week
  string day_of_week = 5 [(validate.rules).string = {max_len: 50}];
  // month
  string month = 6 [(validate.rules).string = {max_len: 50}];
  // day
  string day = 7 [(validate.rules).string = {max_len: 50}];
  // business id
  int64 business_id = 8 [(validate.rules).int64 = {gt: 0}];
  // store name
  string business_name = 9 [(validate.rules).string = {max_len: 255}];
}

// appointment second reminder definition
message AppointmentSecondReminderDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet name
  string pet_name = 2 [(validate.rules).string = {max_len: 1024}];
  // verb
  string verb = 3 [(validate.rules).string = {max_len: 50}];
  // appointment time, 09:00 am
  string appointment_time = 4 [(validate.rules).string = {max_len: 50}];
  // day of week
  string day_of_week = 5 [(validate.rules).string = {max_len: 50}];
  // month
  string month = 6 [(validate.rules).string = {max_len: 50}];
  // day
  string day = 7 [(validate.rules).string = {max_len: 50}];
  // business id
  int64 business_id = 8 [(validate.rules).int64 = {gt: 0}];
  // store name
  string business_name = 9 [(validate.rules).string = {max_len: 255}];
}

// appointment general reminder definition
message AppointmentGeneralReminderDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet name
  string pet_name = 2 [(validate.rules).string = {max_len: 1024}];
  // verb
  string verb = 3 [(validate.rules).string = {max_len: 50}];
  // appointment time, 09:00 am
  string appointment_time = 4 [(validate.rules).string = {max_len: 50}];
  // day of week
  string day_of_week = 5 [(validate.rules).string = {max_len: 50}];
  // month
  string month = 6 [(validate.rules).string = {max_len: 50}];
  // day
  string day = 7 [(validate.rules).string = {max_len: 50}];
  // business id
  int64 business_id = 8 [(validate.rules).int64 = {gt: 0}];
  // store name
  string business_name = 9 [(validate.rules).string = {max_len: 255}];
}

// appointment rebook reminder definition
message AppointmentRebookReminderDef {
  // pet name
  string pet_name = 1 [(validate.rules).string = {max_len: 1024}];
  // store name
  string business_name = 9 [(validate.rules).string = {max_len: 255}];
}

// appointment day definition
message AppointmentDayDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business name
  string business_name = 2 [(validate.rules).string = {max_len: 255}];
  // business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // pet name
  string pet_name = 4 [(validate.rules).string = {max_len: 1024}];
}

// ready to pick up definition
message ReadyToPickUpDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business name
  string business_name = 2 [(validate.rules).string = {max_len: 255}];
  // pet name
  string pet_name = 3 [(validate.rules).string = {max_len: 1024}];
  // verb
  string verb = 4 [(validate.rules).string = {max_len: 50}];
  // business id
  int64 business_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// ask for review definition
message AskForReviewDef {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business name
  string business_name = 2 [(validate.rules).string = {max_len: 255}];
  // business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // appointment staff name
  string appointment_staff_name = 4 [(validate.rules).string = {max_len: 255}];
}

// received new message definition
message ReceivedAppMessageDef {
  // 目标对话的 ID
  uint64 chat_id = 2 [(validate.rules).uint64 = {gt: 0}];

  // message id
  optional uint64 message_id = 1 [(validate.rules).uint64 = {gt: 0}];
}

// appointment tracking def
message AppointmentTrackingDef {
  // tracking info
  moego.models.appointment.v1.AppointmentTracking tracking = 1;
  // company preference
  moego.models.organization.v1.CompanyPreferenceSettingModel company_preference = 2;
}
