syntax = "proto3";

package moego.models.map.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/map/v1;mappb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.map.v1";

// TravelMode
// A set of values used to specify the mode of travel.
// NOTE: `WALKING`, `BICYCLE`, and `TWO_WHEELER` routes are in beta and might sometimes be missing clear sidewalks, pedestrian paths, or bicycling paths.
enum TravelMode {
  // Unspecified travel mode.
  TRAVEL_MODE_UNSPECIFIED = 0;
  // Travel by passenger car.
  DRIVING = 1;
  // Travel by walking.
  WALKING = 2;
  // Travel by bicycle.
  BICYCLING = 3;
  // Two-wheeled, motorized vehicle. For example, motorcycle. Note that this
  // differs from the `BICYCLE` travel mode which covers human-powered mode.
  TWO_WHEELER = 4;
  // Travel by public transit routes, where available.
  TRANSIT = 5;
}

// TrafficModel
// Specifies the assumptions to use when calculating time in traffic.
// This setting affects the value returned in the `duration` field in the response,
// which contains the predicted time in traffic based on historical averages.
enum TrafficModel {
  // Unused. If specified, will default to `BEST_GUESS`.
  TRAFFIC_MODEL_UNSPECIFIED = 0;
  // Indicates that the returned `duration` should be the best estimate of travel time given
  // what is known about both historical traffic conditions and live traffic.
  // Live traffic becomes more important the closer the `departure_time` is to now.
  BEST_GUESS = 1;
  // Indicates that the returned duration should be longer than the actual travel time on most days,
  // though occasional days with particularly bad traffic conditions may exceed this value.
  PESSIMISTIC = 2;
  // Indicates that the returned duration should be shorter than the actual travel time on most days,
  // though occasional days with particularly good traffic conditions may be faster than this value.
  OPTIMISTIC = 3;
}

// RouteLabel
// Labels for the [Route] that are useful to identify specific properties of the route to compare against others.
enum RouteLabel {
  // Unspecified route label.
  ROUTE_LABEL_UNSPECIFIED = 0;
  // The default "best" route returned for the route computation.
  DEFAULT_ROUTE = 1;
  // An alternative to the default "best" route.
  // Routes like this will be returned when [QueryRoutesRequest.alternative_route] is specified.
  ALTERNATE = 2;
  // Fuel efficient route. Routes labeled with this value are determined to be optimized for Eco parameters such as fuel consumption.
  // Routes like this will be returned when [QueryRoutesRequest.fuel_efficient] is specified.
  FUEL_EFFICIENT = 3;
}

// RoutePreference
// A set of values that specify factors to take into consideration when calculating the route.
enum RoutePreference {
  // Unspecified route preference.
  ROUTE_PREFERENCE_UNSPECIFIED = 0;
  // Computes routes without taking live traffic conditions into consideration.
  // Suitable when traffic conditions don't matter or are not applicable. Using this value produces the lowest latency.
  // Note: For `DRIVING` and `TWO_WHEELER` choice of route and duration are based on road network and average time-independent traffic conditions.
  // Results for a given request may vary over time due to changes in the road network, updated average traffic conditions, and the distributed nature of the service.
  // Results may also vary between nearly-equivalent routes at any time or frequency.
  TRAFFIC_UNAWARE = 1;
  // Calculates routes taking live traffic conditions into consideration.
  // In contrast to `ROUTE_PREFERENCE_OPTIMAL`, some optimizations are applied to significantly reduce latency.
  TRAFFIC_AWARE = 2;
  // Calculates the routes taking live traffic conditions into consideration,
  // without applying most performance optimizations. Using this value produces the highest latency.
  TRAFFIC_AWARE_OPTIMAL = 3;
}

// ExtraComputation
// Extra computations to perform while completing the request.
enum ExtraComputation {
  // Unspecified extra computation.
  EXTRA_COMPUTATION_UNSPECIFIED = 0;
  // Toll information for the route(s).
  TOLLS = 1;
  // Estimated fuel consumption for the route(s).
  FUEL_CONSUMPTION = 2;
  // Traffic aware polylines for the route(s).
  TRAFFIC_ON_POLYLINE = 3;
  // [NavigationInstructions] presented as a formatted HTML text string. This content is meant to be read as-is. This content is for display only.
  // Do not programmatically parse it.
  NAVIGATION_INSTRUCTIONS = 4;
}

// TransitTravelMode
// A set of values used to specify the mode of transit.
enum TransitTravelMode {
  // Unspecified transit travel mode.
  TRANSIT_TRAVEL_MODE_UNSPECIFIED = 0;
  // Travel by bus.
  BUS = 1;
  // Travel by subway.
  SUBWAY = 2;
  // Travel by train.
  TRAIN = 3;
  // Travel by light rail or tram.
  LIGHT_RAIL = 4;
}

// TransitRoutePreference
// Specifies routing preferences for transit routes.
enum TransitRoutePreference {
  // Unspecified transit route preference.
  TRANSIT_ROUTE_PREFERENCE_UNSPECIFIED = 0;
  // Indicates that the calculated route should prefer limited amounts of walking.
  LESS_WALKING = 1;
  // Indicates that the calculated route should prefer a limited number of transfers.
  FEWER_TRANSFERS = 2;
}

// VehicleEmissionType
// A set of values describing the vehicle's emission type. Applies only to the `DRIVING`.
enum VehicleEmissionType {
  // Unspecified vehicle emission type.
  VEHICLE_EMISSION_TYPE_UNSPECIFIED = 0;
  // Gasoline/petrol fueled vehicle.
  GASOLINE = 1;
  // Diesel fueled vehicle.
  DIESEL = 2;
  // Electricity powered vehicle.
  ELECTRIC = 3;
  // Hybrid fuel (such as gasoline + electric) vehicle.
  HYBRID = 4;
}

// TrafficSituation
// The classification of polyline speed based on traffic data.
enum TrafficCondition {
  // Unspecified traffic condition.
  TRAFFIC_CONDITION_UNSPECIFIED = 0;
  // Normal speed, no slowdown is detected.
  NORMAL = 1;
  // Slowdown detected, but no traffic jam formed.
  SLOW = 2;
  // Traffic jam detected.
  TRAFFIC_JAM = 3;
}

// PolylineQuality
// A set of values that specify the quality of the polyline.
enum PolylineQuality {
  // No polyline quality preference specified. Defaults to `OVERVIEW`.
  POLYLINE_QUALITY_UNSPECIFIED = 0;
  // Specifies a high-quality polyline - which is composed using more points than `OVERVIEW`,
  // at the cost of increased response size. Use this value when you need more precision.
  HIGH_QUALITY = 1;
  // Specifies an overview polyline - which is composed using a small number of points.
  // Use this value when displaying an overview of the route.
  // Using this option has a lower request latency compared to using the `HIGH_QUALITY` option.
  OVERVIEW = 2;
}

// PolylineEncoding
enum PolylineEncoding {
  // No polyline type preference specified. Defaults to `ENCODED_POLYLINE`.
  POLYLINE_ENCODING_UNSPECIFIED = 0;

  // Specifies a polyline encoded using the [polyline encoding algorithm](https://developers.google.com/maps/documentation/utilities/polylinealgorithm).
  ENCODED_POLYLINE = 1;

  // Specifies a polyline using the [GeoJSON LineString format](https://tools.ietf.org/html/rfc7946#section-3.1.4)
  GEO_JSON_LINESTRING = 2;
}

// Units
// A set of values that specify the unit of measure used in the display.
enum Units {
  // Units of measure not specified. Defaults to the unit of measure inferred from the request.
  UNITS_UNSPECIFIED = 0;
  // Metric units of measure.
  METRIC = 1;
  // Imperial (English) units of measure.
  IMPERIAL = 2;
}

// FallbackReason
// Reasons for using fallback response.
enum FallbackReason {
  // No fallback reason specified.
  FALLBACK_REASON_UNSPECIFIED = 0;

  // A server error happened while calculating routes with your preferred routing mode,
  // but we were able to return a result calculated by an alternative mode.
  SERVER_ERROR = 1;

  // We were not able to finish the calculation with your preferred routing mode on time,
  // but we were able to return a result calculated by an alternative mode.
  LATENCY_EXCEEDED = 2;
}

// FallbackRoutingMode
// Actual routing mode used for returned fallback response.
enum FallbackRoutingMode {
  // Not used.
  FALLBACK_ROUTING_MODE_UNSPECIFIED = 0;

  // Indicates the `ROUTE_PREFERENCE_UNAWARE` was used to compute the response.
  FALLBACK_TRAFFIC_UNAWARE = 1;

  // Indicates the `ROUTE_PREFERENCE_AWARE` was used to compute the response.
  FALLBACK_TRAFFIC_AWARE = 2;
}
