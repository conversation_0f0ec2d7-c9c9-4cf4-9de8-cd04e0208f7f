syntax = "proto3";

package moego.models.billing.v1;

import "google/protobuf/timestamp.proto";
import "google/type/interval.proto";
import "moego/models/billing/v1/subscription_defs.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/billing/v1;billingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.billing.v1";

// subscription Object
message SubscriptionModel {
  // moego defined subscription status
  enum Status {
    // unknown status
    STATUS_UNSPECIFIED = 0;
    // active
    ACTIVE = 1;
    // pending，waiting for payment
    PENDING = 2;
    // incomplete, payment tried but failed
    INCOMPLETE = 3;
    // expired, renewal failed
    EXPIRED = 4;
  }
  // From Stripe
  // https://docs.stripe.com/api/subscriptions/create#create_subscription-payment_behavior
  enum PaymentBehavior {
    // unknown behavior
    BEHAVIOR_UNSPECIFIED = 0;
    // allow incomplete
    ALLOW_INCOMPLETE = 1;
    // default incomplete
    DEFAULT_INCOMPLETE = 2;
    // error if incomplete
    ERROR_IF_INCOMPLETE = 3;
    // pending if incomplete
    PENDING_IF_INCOMPLETE = 4;
  }
  // subscription relation ID
  int64 id = 1;
  // subscription object id
  string subscription_id = 2;
  // vendor subscription id
  string vendor_subscription_id = 3;
  // subscription items
  repeated models.billing.v1.PlanUnit plan_units = 4;
  // payment method
  string payment_method = 5;
  // coupon id
  optional int64 coupon_id = 6;
  // metadata
  map<string, string> metadata = 7;
  // vendor_status
  string vendor_status = 8;
  // status
  Status status = 9;
  // vendor customer id
  string vendor_customer_id = 10;
}

// subscription payment pause collection
message PauseCollection {
  // payment collection behavior
  enum Behavior {
    // unknown behavior
    BEHAVIOR_UNSPECIFIED = 0;
    // keep as draft
    KEEP_AS_DRAFT = 1;
    // mark as uncollectible
    MARK_UNCOLLECTIBLE = 2;
    // void
    VOID = 3;
  }
  // payment collection behavior
  Behavior behavior = 1;
  // resumes at
  google.protobuf.Timestamp resumes_at = 2;
}

// subscription schedule model
message SubscriptionScheduleModel {
  // phase model
  message Phase {
    // items
    repeated models.billing.v1.PlanUnit plan_units = 1;
    // interval
    google.type.Interval interval = 2;
  }

  // vendor subscription schedule id
  string vendor_subscription_schedule_id = 1;
  // subscription id
  int64 subscription_id = 2;
  // phases
  repeated Phase phases = 3;
}
