syntax = "proto3";

package moego.models.reporting.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v1;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v1";

// business metrics
message BusinessMetricsModel {
  // business ID
  int64 business_id = 1;
  // business type
  BusinessType business_type = 2;
  // business country
  string business_country = 3;
  // Monthly Rev: amount of finished invoice during last 30 days
  double monthly_rev = 4;
  // Total Units: # of locations and vans purchased in the latest plan
  int64 total_units = 5;
  // Total Staff: # of staff on current staff list
  int64 total_staff = 6;
  // Total Appt: # of appointments created on calendar of last 30 days
  int64 total_appt = 7;
  // Total Finished Appt: # of appointments marked as finished in the last 30 days
  int64 total_finished_appt = 8;
  // Total Msg Usage: # of msg estimated to be sent in the current billing period
  int64 total_msg_usage = 9;
  // MoeGo Pay Status: Uninitialized/Operated
  MoeGoPayStatus moego_pay_status = 10;
  // MoeGo Pay Count: # of transactions through MoeGo Pay last 30 days
  int64 moego_pay_count = 11;
  // MoeGo Pay Transactions: amount of transactions through MoeGo Pay last 30 days
  double moego_pay_transactions = 12;
  // OB Status: Not Enabled/ OB 2.0
  OBStatus ob_status = 13;
  // OB Request: # of requests submitted through OB in the last 30 days
  int64 ob_requests = 14;
  // Van Credits: # of vans purchased
  int64 van_credits = 15;
  // Van Created: # of vans set up
  int64 van_created = 16;
  // Location Credits: # of locations purchased
  int64 location_credits = 17;
  // Location Created: # of locations set up
  int64 location_created = 18;
  // Permission Level: 101, 102, 1001, 1002, 1003
  string permission_level = 19;
  // Metricized At: UTC data Time of yesterday
  google.protobuf.Timestamp metricized_at = 20;
}

// Business Type
enum BusinessType {
  // unspecified
  BUSINESS_TYPE_UNSPECIFIED = 0;
  // mobile
  MOBILE = 1;
  // salon
  SALON = 2;
  // hybrid
  HYBRID = 3;
}

// Online Booking Status
enum OBStatus {
  // unspecified
  OB_STATUS_UNSPECIFIED = 0;
  // not enabled
  NOT_ENABLED = 1;
  // ob 2.0
  OB_2 = 2;
}

// MoeGo Pay Status
enum MoeGoPayStatus {
  // unspecified
  MOEGO_PAY_STATUS_UNSPECIFIED = 0;
  // uninitialized
  UNINITIALIZED = 1;
  // operated
  OPERATED = 2;
}

// Permission Level
enum PermissionLevel {
  // unspecified
  PERMISSION_LEVEL_UNSPECIFIED = 0;
}

// campaign metrics
message CampaignMetricsModel {
  // campaign id
  int64 campaign_id = 1;
  // starter
  string starter = 2;
  // created at
  google.protobuf.Timestamp started_at = 3;
  // notification title
  string notification_title = 4;
  // notification body
  string notification_body = 5;
  // notification read count
  int64 notification_read_count = 6;
  // notification app clicked count
  int64 notification_app_clicked_count = 7;
  // company count
  int64 company_count = 8;
  // business count
  int64 business_count = 9;
  // staff count
  int64 staff_count = 10;
}

// staff campaign metrics
message StaffCampaignMetricsModel {
  // receiver staff id
  int64 receiver_staff_id = 1;
  // receiver name
  string receiver_name = 2;
  // notification read count
  int64 notification_read_count = 3;
  // notification app clicked count
  int64 notification_app_clicked_count = 4;
  // notification count
  int64 notification_count = 5;
  // campaign count
  int64 campaign_count = 6;
}
