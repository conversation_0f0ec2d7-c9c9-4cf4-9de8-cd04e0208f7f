// @since 2024-03-21 15:56:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// Grooming service detail model for booking request
message GroomingServiceDetailDef {
  // pet id
  int64 pet_id = 3 [(validate.rules).int64.gt = 0];

  // staff id
  int64 staff_id = 4 [(validate.rules).int64.gt = 0];

  // service id
  int64 service_id = 5 [(validate.rules).int64.gt = 0];

  // start date, format: yyyy-mm-dd
  string start_date = 10 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, the minutes from 00:00
  int32 start_time = 11 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}
