syntax = "proto3";

package moego.models.organization.v1;

import "moego/models/organization/v1/payroll_enums.proto";
import "moego/models/organization/v1/staff_enums.proto";
import "moego/models/organization/v1/staff_models.proto";
import "moego/models/organization/v1/van_model.proto";
import "moego/utils/v1/time_of_day_interval.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// create staff definition
message CreateStaffDef {
  // first name of the staff
  string first_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // last name of the staff
  string last_name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // avatar path of the staff
  optional string avatar_path = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // hire date of the staff, timestamp
  int64 hire_date = 4 [(validate.rules).int64 = {gte: 0}];
  // role id of the staff
  int64 role_id = 5 [(validate.rules).int64 = {gt: 0}];
  // note for the staff
  optional string note = 6 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // profile email of the staff
  optional string profile_email = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 255
    email: true
  }];
  // color code
  optional string color_code = 8 [(validate.rules).string = {
    min_len: 1
    max_len: 20
  }];
  // phone number
  optional string phone_number = 9;
}

// update staff definition
message UpdateStaffDef {
  // staff id, required but moved to outside of this message
  //  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // first name of the staff
  optional string first_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // last name of the staff
  optional string last_name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // avatar path of the staff
  optional string avatar_path = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // hire date of the staff, timestamp
  optional int64 hire_date = 4 [(validate.rules).int64 = {gte: 0}];
  // role id of the staff
  optional int64 role_id = 5 [(validate.rules).int64 = {gte: 0}];
  // note for the staff
  optional string note = 6 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // profile email of the staff
  optional string profile_email = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 255
    email: true
  }];
  // color code
  optional string color_code = 8 [(validate.rules).string = {
    min_len: 1
    max_len: 20
  }];
  // phone number
  optional string phone_number = 9;
}

// staff working location definition
message StaffWorkingLocationDef {
  // working in all locations switch
  optional bool working_in_all_locations = 1;
  // working location id list
  repeated int64 working_location_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// staff access by location definition
message StaffAccessByLocationDef {
  // location id
  int64 location_id = 1 [(validate.rules).int64 = {gt: 0}];
  // access staff id list
  repeated int64 staff_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
  // access current location staffs switch
  bool access_location_all_staffs = 3;
}

// staff shown on locations
message StaffShowOnCalendarDef {
  // show location ids
  repeated int64 location_ids = 1 [(validate.rules).repeated = {
    min_items: 0
    max_items: 500
    items: {
      int64: {gt: 0}
    }
  }];
  // staff shown on all working locations
  bool shown_on_all_working_locations = 2;
}

// staff access list definition
message StaffAccessListDef {
  // staff access list
  repeated moego.models.organization.v1.StaffAccessByLocationDef staff_ids_by_location = 2;
}

// staff access control definition
message StaffAccessControlDef {
  // access staff list
  optional StaffAccessListDef access_list = 1;
  // show on calendar switch
  optional bool is_show_on_calendar = 2;
  // require access code switch
  optional bool require_access_code = 3;
  // access code
  optional string access_code = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 10
  }];
  // access all working locations staffs
  optional bool access_all_working_locations_staffs = 5;
  // staff shown on Calendar
  optional StaffShowOnCalendarDef staff_show_on_calendar = 6;
}

// staff notification setting definition: 0 - no notify, 1 - at their working business, 2 - related to them
message StaffNotificationDef {
  // appointment created notify
  optional int32 booking_created = 1 [(validate.rules).int32 = {
    gte: 0
    lte: 2
  }];
  // appointment cancelled notify
  optional int32 booking_cancelled = 2 [(validate.rules).int32 = {
    gte: 0
    lte: 2
  }];
  // appointment updated notify
  optional int32 booking_rescheduled = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 2
  }];
  // new booking requested notify
  optional int32 new_booking = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 2
  }];
  // new intake form submitted notify
  optional int32 new_intake_form = 5 [(validate.rules).int32 = {
    gte: 0
    lte: 2
  }];
  // agreement signed from client notify
  optional int32 agreement_signed = 6 [(validate.rules).int32 = {
    gte: 0
    lte: 2
  }];
  // invoice paid notify
  optional int32 invoice_paid = 7 [(validate.rules).int32 = {
    gte: 0
    lte: 2
  }];
  // review submission notify
  optional int32 review_submitted = 8 [(validate.rules).int32 = {
    gte: 0
    lte: 2
  }];
  // new abandoned bookings notify
  optional int32 new_abandoned_bookings = 9 [(validate.rules).int32 = {
    gte: 0
    lte: 2
  }];
  // assign task to staff notify
  optional int32 assigned_task = 10 [(validate.rules).int32 = {
    gte: 0
    lte: 2
  }];
}

// staff payroll setting definition
message StaffPayrollSettingDef {
  // service commission switch
  optional bool service_commission_enable = 1;
  // service commission type
  optional moego.models.organization.v1.ServiceCommissionType service_commission_type = 2;
  // tier type
  optional moego.models.organization.v1.TierType tier_type = 3;
  // tier config
  repeated TierConfigDef tier_config = 4;
  // service pay rate
  optional double service_pay_rate = 5 [(validate.rules).double = {
    gte: 0
    lte: 100
  }];
  // add-on pay rate
  optional double addon_pay_rate = 6 [(validate.rules).double = {
    gte: 0
    lte: 100
  }];
  // hourly commission switch
  optional bool hourly_commission_enable = 7;
  // hourly pay
  optional double hourly_pay = 8 [(validate.rules).double = {gte: 0}];
  // tips commission enable
  optional bool tips_commission_enable = 9;
  // tips pay rate
  optional double tips_pay_rate = 10 [(validate.rules).double = {
    gte: 0
    lte: 100
  }];

  // tier config definition
  message TierConfigDef {
    // start price
    double start = 1 [(validate.rules).double = {gte: 0}];
    // end price, -1 means unlimited
    double end = 2 [(validate.rules).double = {gte: -1}];
    // tier rate
    double rate = 3 [(validate.rules).double = {
      gte: 0
      lte: 100
    }];
  }
}

// staff email definition
message StaffEmailDef {
  // staff profile email
  string email = 1;
  // staff link status
  StaffLinkStatus link_status = 2;
}

// staff van definition
message StaffVanDef {
  // van id
  int64 van_id = 1;
}

// staff with extra info definition, currently only assigned van, can add more in the future
message StaffInfoDef {
  // staff id
  int64 staff_id = 1;
  // staff info
  moego.models.organization.v1.StaffBasicView staff = 2;
  // assigned van, one staff will only have one van for now
  moego.models.organization.v1.VanModel assigned_van = 3;
  // staff email
  StaffEmailDef staff_email = 4;
}

// location staffs definition
message LocationStaffsDef {
  // business id, working location
  int64 business_id = 1;
  // staff list
  repeated moego.models.organization.v1.StaffModel staffs = 2;
}

// location staff ids definition
message LocationStaffIdsDef {
  // business id
  int64 business_id = 1;
  // staff id list
  repeated int64 staff_ids = 2;
}

// send staff invite link params
message SendInviteLinkParamsDef {
  // email
  optional string email = 1;
  // send invite link method type
  optional StaffInviteLinkSendMethodType method_type = 2;
  // whether to send invite link now or not
  optional bool is_send_invite_link = 3;
}

// staff clock in out info definition
message ClockInOutStaffDef {
  // staff id
  int64 staff_id = 1;
  // staff first name
  string first_name = 2;
  // staff last name
  string last_name = 3;
  // staff access code
  string access_code = 4;
  // staff require access code
  bool require_access_code = 5;
  // staff avatar path
  string avatar_path = 6;
  // staff color code
  string color_code = 7;
  // staff working location list, where the token staff can clock in out
  repeated LocationClockInOutInfoDef locations = 8;
  // sort
  int32 sort = 9;
}

// staff location clock in out info definition
message LocationClockInOutInfoDef {
  // location id
  int64 location_id = 1;
  // staff is clock in, from old api
  bool is_clock_in = 2;
  // staff is clock out, from old api
  bool is_clock_out = 3;
  // staff clock in time, from old api
  int64 clock_in_time = 4;
  // staff clock out time, from old api
  int64 clock_out_time = 5;
}

// owner staff def
message OwnerStaffDef {
  // staff model
  message StaffWithEmailDef {
    // staff model
    models.organization.v1.StaffModel staff = 1;
    // staff email
    optional models.organization.v1.StaffEmailDef staff_email_def = 2;
  }
  // active staff
  StaffWithEmailDef active_staff = 1;
  // temporary staff
  StaffWithEmailDef temporary_staff = 2;
}

// send invite link def
message SendInviteLinkDef {
  // invite code
  string invite_code = 1 [(validate.rules).string = {max_len: 100}];
  // email
  string email = 2 [(validate.rules).string = {
    email: true
    max_len: 100
  }];
  // method type
  models.organization.v1.StaffInviteLinkSendMethodType method_type = 3;
}

// staff login time def
message StaffLoginTimeDef {
  // login limit type
  StaffLoginLimitType login_limit_type = 1 [(validate.rules).enum = {
    not_in: 0
    defined_only: true
  }];
  // accurate to the minute now
  optional utils.v1.TimeOfDayInterval time_range = 2;
}
