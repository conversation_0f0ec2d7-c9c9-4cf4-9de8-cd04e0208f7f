syntax = "proto3";

package moego.models.organization.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/organization/v1/camera_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// Camera Model
message CameraModel {
  // ID
  int64 id = 1;
  // Company ID
  int64 company_id = 2;
  // Business ID
  int64 business_id = 3;
  // Config ID
  int64 config_id = 4;
  // Camera Type
  CameraConfigType type = 5;
  // Origin Camera ID
  string origin_camera_id = 6;
  // Origin Camera Title
  string origin_camera_title = 7;
  // video url
  string video_url = 8;
  // Origin Status
  OriginStatus origin_status = 9;
  // Is Active
  bool is_active = 10;
  // Visibility Type
  VisibilityType visibility_type = 11;
  // Created Time
  google.protobuf.Timestamp created_at = 12;
  // Updated Time
  google.protobuf.Timestamp updated_at = 13;
}
