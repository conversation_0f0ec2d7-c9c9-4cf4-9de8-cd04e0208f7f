syntax = "proto3";

package moego.models.organization.v1;

import "moego/models/business/v1/business_enums.proto";
import "moego/models/organization/v1/address_defs.proto";
import "moego/models/organization/v1/location_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// model for location
message LocationModel {
  // location id
  int64 id = 1;
  // location name
  string name = 2;
  // business type
  BusinessType business_type = 3;
  // contact email
  string contact_email = 4;
  // address
  AddressDef address = 5;
  // website
  string website = 6;
  // avatar path
  string avatar_path = 7;
  // facebook link
  string facebook_link = 8;
  // instagram link
  string instagram_link = 9;
  // google link
  string google_link = 10;
  // yelp link
  string yelp_link = 11;
  // assigned twilio phone number
  string twilio_phone_number = 12;
  // contact phone number
  string contact_phone_number = 13;
  // business mode
  BusinessType business_mode = 14;
  // company id
  int64 company_id = 15;
  // tiktok link
  string tiktok_link = 16;
  // primary pay type
  models.business.v1.BusinessPayType primary_pay_type = 17;
}

// model for location
message LocationModelPublicView {
  // location id
  int64 id = 1;
  // location name
  string name = 2;
  // contact email
  string contact_email = 4;
  // address
  AddressDef address = 5;
  // website
  string website = 6;
  // avatar path
  string avatar_path = 7;
  // facebook link
  string facebook_link = 8;
  // instagram link
  string instagram_link = 9;
  // google link
  string google_link = 10;
  // yelp link
  string yelp_link = 11;
  // contact phone number
  string contact_phone_number = 13;
  // company id
  int64 company_id = 15;
}

// view for location list
message LocationBriefView {
  // location id
  int64 id = 1;
  // location name
  string name = 2;
  // business type
  BusinessType business_type = 3;
  // address(only for salon)
  AddressDef address = 4;
  // number of vans(only for mobile)
  int32 number_of_vans_used = 5;
  // avatar path
  string avatar_path = 6;
  // whether the location is the staff's working location
  bool is_working_location = 7;
  // assigned twilio phone number
  string twilio_phone_number = 8;
}
