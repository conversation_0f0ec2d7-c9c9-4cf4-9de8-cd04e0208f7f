syntax = "proto3";

package moego.models.organization.v1;

import "google/type/dayofweek.proto";
import "moego/models/organization/v1/staff_availability_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// update staff availability def
message StaffAvailabilityDef {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64 = {gt: 0}];
  // is available, slot场景下无对应
  bool is_available = 2;
  // schedule type
  optional ScheduleType schedule_type = 3;
  // slot availability day defs
  repeated SlotAvailabilityDayDef slot_availability_day_list = 4 [(validate.rules).repeated = {max_items: 100}];
  // time schedule type
  optional ScheduleType time_schedule_type = 6;
  // time availability day defs
  repeated TimeAvailabilityDayDef time_availability_day_list = 5 [(validate.rules).repeated = {max_items: 100}];
}

// slot availability day def
message SlotAvailabilityDayDef {
  // day of week
  optional google.type.DayOfWeek day_of_week = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // is available
  bool is_available = 2;
  // schedule type
  optional ScheduleType schedule_type = 3;
  // slot daily setting def
  optional SlotDailySettingDef slot_daily_setting = 4;
  // staff available hour defs
  repeated SlotHourSettingDef slot_hour_setting_list = 5 [(validate.rules).repeated = {max_items: 100}];
  // override date
  optional string override_date = 6;
}

// time availability day def
message TimeAvailabilityDayDef {
  // day of week
  optional google.type.DayOfWeek day_of_week = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // is available
  bool is_available = 2;
  // schedule type
  optional ScheduleType schedule_type = 3;
  // time daily setting info
  TimeDailySettingDef time_daily_setting = 4;
  // time available hour defs
  repeated TimeHourSettingDef time_hour_setting_list = 5 [(validate.rules).repeated = {max_items: 100}];
  // override date
  optional string override_date = 6;
}

// by slot daily setting def
message SlotDailySettingDef {
  // start time
  int32 start_time = 1 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // end time
  optional int32 end_time = 2 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // capacity
  optional int32 capacity = 3 [(validate.rules).int32 = {gte: 0}];
  // booking limit
  optional BookingLimitationDef limit = 4;
}

// by time daily setting def
message TimeDailySettingDef {
  // booking limit
  optional BookingLimitationDef limit = 1;
}

// by slot hour setting def
message SlotHourSettingDef {
  // slot start time
  optional int32 start_time = 1 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // slot capacity
  optional int32 capacity = 2 [(validate.rules).int32 = {gte: 0}];
  // booking limit
  optional BookingLimitationDef limit = 3;
  // slot end time
  optional int32 end_time = 4 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
}

// by time hour setting def
message TimeHourSettingDef {
  // start time
  optional int32 start_time = 1 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // end time
  optional int32 end_time = 2 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // booking limit
  optional BookingLimitationDef limit = 3;
}

// slot/time limit setting
message BookingLimitationDef {
  // service limits
  repeated ServiceLimitation service_limits = 1 [(validate.rules).repeated = {max_items: 100}];
  // pet size limits
  repeated PetSizeLimitation pet_size_limits = 2 [(validate.rules).repeated = {max_items: 100}];
  // pet breed limits
  repeated PetBreedLimitation pet_breed_limits = 3 [(validate.rules).repeated = {max_items: 100}];
  // service limitation
  message ServiceLimitation {
    // service_id
    repeated int64 service_ids = 1 [(validate.rules).repeated = {
      max_items: 1024
      unique: true
    }];
    // is_all_service
    bool is_all_service = 2;
    // capacity
    int32 capacity = 3 [(validate.rules).int32 = {gte: 0}];
  }

  // pet size limitation
  message PetSizeLimitation {
    // pet_size_id
    repeated int64 pet_size_ids = 1 [(validate.rules).repeated = {
      max_items: 1024
      unique: true
    }];
    // is_all_size
    bool is_all_size = 2;
    // capacity
    int32 capacity = 3 [(validate.rules).int32 = {gte: 0}];
  }

  // pet breed limitation
  message PetBreedLimitation {
    // pet_type_id
    int64 pet_type_id = 1 [(validate.rules).int64 = {gt: 0}];
    // is_all_breed
    bool is_all_breed = 2;
    // breed_ids
    repeated int64 breed_ids = 3 [(validate.rules).repeated = {
      max_items: 1024
      unique: true
    }];
    // capacity
    int32 capacity = 4 [(validate.rules).int32 = {gte: 0}];
  }
}
