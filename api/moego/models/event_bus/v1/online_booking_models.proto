syntax = "proto3";

package moego.models.event_bus.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// model for online booking submitted event
message OnlineBookingSubmittedEvent {
  // appointment id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // start time
  optional google.protobuf.Timestamp start_time = 3;
  // service item types included in the appointment
  repeated models.offering.v1.ServiceItemType service_item_types = 4;
}

// model for online booking submitted event
message OnlineBookingAbandonedEvent {
  // customer id 可能为空
  optional int64 customer_id = 1;
  // service item types 可能为空数组，基于当前的实现一定没有 EVALUATION
  repeated models.offering.v1.ServiceItemType service_item_types = 4;
}

// model for online booking accepted event
message OnlineBookingAcceptedEvent {
  // appointment id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // start time
  google.protobuf.Timestamp start_time = 3;
  // service item types included in the appointment
  repeated models.offering.v1.ServiceItemType service_item_types = 4;
}
