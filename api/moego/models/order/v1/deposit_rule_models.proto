syntax = "proto3";

package moego.models.order.v1;

import "google/type/decimal.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// Deposit rule.
message DepositRule {
  // ID
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // Name
  string name = 2 [(validate.rules).string = {min_len: 1}];
  // Filters. Due to protojson limitation, we have to wrap the repeated filters into a message.
  DepositFilters filters = 3;
  // Deposit config
  oneof deposit_config {
    // By amount
    google.type.Money deposit_by_amount = 4;
    // By percentage (1 for 1%)
    google.type.Decimal deposit_by_percentage = 5;
  }
}

// Deposit filters
message DepositFilters {
  // Filters. If multiple filters are present, they are combined with AND. If a filter of some type is not present, this
  // type of filter is not applied.
  repeated DepositFilter filters = 1;
}

// Deposit filter
message DepositFilter {
  // The specific filter.
  oneof filter {
    // Client group
    // If absent, no filter on client group is applied.
    DepositRuleClientGroupFilter client_group_filter = 1;
    // Services
    // If absent, no filter on services is applied.
    DepositRuleServiceFilter service_filter = 2;
    // Date range
    // If absent, no expiration.
    DepositRuleDateRangeFilter date_range_filter = 3;
  }
}

// Client group filter
message DepositRuleClientGroupFilter {
  // Match new visitor
  bool new_visitors = 1;
  // Existing customers
  bool existing_customers = 2;
  // Filter json for existing customers. The structure is the same as the request body.filters of
  // POST/customer/smart-list.
  string existing_customers_filter_json = 3;
}

// Service filter
message DepositRuleServiceFilter {
  // Applied services. These services are filtered with OR.
  repeated ServicesByType services_by_type = 1;

  // Service by type
  message ServicesByType {
    // Service type
    moego.models.offering.v1.ServiceItemType service_type = 1;
    // Is all
    bool is_all = 2;
    // Service IDs, only if is_all is false
    repeated int64 service_ids = 3;
  }
}

// Date range filter
message DepositRuleDateRangeFilter {
  // Range
  google.type.Interval range = 1;
}
