syntax = "proto3";

package moego.models.order.v1;

import "google/type/decimal.proto";
import "google/type/money.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

/**
 * line discount info
 */
message OrderLineDiscountModel {
  // id
  optional int64 id = 1;
  // businessId
  int64 business_id = 2;
  // orderId
  int64 order_id = 3;
  // orderItemId
  optional int64 order_item_id = 4;
  // applyType
  string apply_type = 5;
  // isDeleted
  optional bool is_deleted = 6;
  // discountType
  string discount_type = 7;
  // discountAmount
  optional double discount_amount = 8;
  // discountRate
  optional double discount_rate = 9;
  // applyBy
  int64 apply_by = 10;
  // applySequence
  optional int32 apply_sequence = 11;
  // create time
  optional int64 create_time = 12;
  // update time
  optional int64 update_time = 13;
  // discount code id
  optional int64 discount_code_id = 14;
}

// 与 OrderLineDiscountModel 相同，仅替换枚举值和钱有关字段类型.
message OrderLineDiscountModelV1 {
  // id
  int64 id = 1;
  // businessId
  int64 business_id = 2;
  // orderId
  int64 order_id = 3;
  // orderItemId
  int64 order_item_id = 4;
  // applyType
  string apply_type = 5;
  // isDeleted
  bool is_deleted = 6;
  // discountType
  string discount_type = 7;
  // discountAmount
  google.type.Money discount_amount = 8;
  // discountRate
  google.type.Decimal discount_rate = 9;
  // applyBy
  int64 apply_by = 10;
  // applySequence
  int32 apply_sequence = 11;
  // create time
  int64 create_time = 12;
  // update time
  int64 update_time = 13;
  // discount code id
  int64 discount_code_id = 14;
}
