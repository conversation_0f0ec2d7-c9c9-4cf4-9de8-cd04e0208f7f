syntax = "proto3";

package moego.models.accounting.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/accounting/v1;accountingmodpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.accounting.v1";

// visibility class
enum VisibilityClass {
  // Unspecified
  VISIBILITY_CLASS_UNSPECIFIED = 0;
  // default
  DEFAULT = 1;
  // full service only
  FULL_SERVICE_ONLY = 2;
}

// onboarding status
enum OnboardingStatus {
  // Unspecified
  ONBOARDING_STATUS_UNSPECIFIED = 0;
  // not subscribed
  NOT_SUBSCRIBED = 1;
  // not onboarded
  NOT_ONBOARDED = 2;
  // onboarded
  ONBOARDED = 3;
  // ONBOARDING_ONLY 只允许onboarding，不允许其他操作
  ONBOARDING_ONLY = 4;
}

// sync log entity type
enum SyncEntityType {
  // Unspecified
  SYNC_ENTITY_TYPE_UNSPECIFIED = 0;
  // invoice
  INVOICE = 1;
  // payment
  PAYMENT = 2;
  // refund
  REFUND = 3;
  // write-off
  WRITE_OFF = 4;
  // dispute, deprecated 暂时不使用，dispute当前使用DISPUTE_FUND_OPERATE
  DISPUTE = 5;
  // dispute won, deprecated 暂时不使用，dispute当前使用DISPUTE_FUND_OPERATE
  DISPUTE_WON = 6;
  // payout
  PAYOUT = 7;
  // loan payout
  LOAN_PAYOUT = 8;
  // loan repayment
  LOAN_REPAYMENT = 9;
  // payroll
  PAYROLL = 10;
  // customer
  CUSTOMER = 11;
  // dispute fund operate
  DISPUTE_FUND_OPERATE = 12;
}

// sync status
enum SyncStatus {
  // Unspecified
  SYNC_STATUS_UNSPECIFIED = 0;
  // init
  INIT = 1;
  // processing
  PROCESSING = 2;
  // success
  SUCCESS = 3;
  // failed
  FAILED = 4;
  // canceled
  CANCELED = 5;
}

// channel type
enum ChannelType {
  // Unspecified
  CHANNEL_TYPE_UNSPECIFIED = 0;
  // layer
  LAYER = 1;
}

// enable
enum Enable {
  // Unspecified
  ENABLE_UNSPECIFIED = 0;
  // open
  OPEN = 1;
  // close
  CLOSE = 2;
}

// account side
enum AccountSide {
  // Unspecified
  ACCOUNT_SIDE_UNSPECIFIED = 0;
  // credit
  CREDIT = 1;
  // debit
  DEBIT = 2;
}

// us state
enum USState {
  // Unspecified
  US_STATE_UNSPECIFIED = 0;
  // Alabama
  AL = 1;
  // Alaska
  AK = 2;
  // Arizona
  AZ = 3;
  // Arkansas
  AR = 4;
  // California
  CA = 5;
  // Colorado
  CO = 6;
  // Connecticut
  CT = 7;
  // Delaware
  DE = 8;
  // Florida
  FL = 9;
  // Georgia
  GA = 10;
  // Hawaii
  HI = 11;
  // Idaho
  ID = 12;
  // Illinois
  IL = 13;
  // Indiana
  IN = 14;
  // Iowa
  IA = 15;
  // Kansas
  KS = 16;
  // Kentucky
  KY = 17;
  // Louisiana
  LA = 18;
  // Maine
  ME = 19;
  // Maryland
  MD = 20;
  // Massachusetts
  MA = 21;
  // Michigan
  MI = 22;
  // Minnesota
  MN = 23;
  // Mississippi
  MS = 24;
  // Missouri
  MO = 25;
  // Montana
  MT = 26;
  // Nebraska
  NE = 27;
  // Nevada
  NV = 28;
  // New Hampshire
  NH = 29;
  // New Jersey
  NJ = 30;
  // New Mexico
  NM = 31;
  // New York
  NY = 32;
  // North Carolina
  NC = 33;
  // North Dakota
  ND = 34;
  // Ohio
  OH = 35;
  // Oklahoma
  OK = 36;
  // Oregon
  OR = 37;
  // Pennsylvania
  PA = 38;
  // Rhode Island
  RI = 39;
  // South Carolina
  SC = 40;
  // South Dakota
  SD = 41;
  // Tennessee
  TN = 42;
  // Texas
  TX = 43;
  // Utah
  UT = 44;
  // Vermont
  VT = 45;
  // Virginia
  VA = 46;
  // Washington
  WA = 47;
  // West Virginia
  WV = 48;
  // Wisconsin
  WI = 49;
  // Wyoming
  WY = 50;
}

// entity type
enum EntityType {
  // Unspecified
  ENTITY_TYPE_UNSPECIFIED = 0;
  // individual
  SOLE_PROP = 1;
  // corporation
  C_CORP = 2;
  // llc
  LLC = 3;
  // s corp
  S_CORP = 4;
  // partnership
  PARTNERSHIP = 5;
}
