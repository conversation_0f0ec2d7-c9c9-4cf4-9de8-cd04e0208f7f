// @since 2023-12-07 11:36:34
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.message.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// message template use case enum
enum MessageTemplateUseCase {
  // unspecified value
  USE_CASE_UNSPECIFIED = 0;

  // use case for online booking request submitted
  USE_CASE_ONLINE_BOOKING_REQUEST_SUBMITTED = 1;
  // use case for online booking request accepted
  USE_CASE_ONLINE_BOOKING_REQUEST_ACCEPTED = 2;
  // use case for online booking request declined
  USE_CASE_ONLINE_BOOKING_REQUEST_DECLINED = 3;
  // use case for online booking request waitlist
  USE_CASE_ONLINE_BOOKING_REQUEST_AUTO_MOVED_TO_WAITLIST = 4;

  // use case for appointment booked
  USE_CASE_APPOINTMENT_BOOKED = 5;
  // use case for appointment rescheduled
  USE_CASE_APPOINTMENT_RESCHEDULED = 6;
  // use case for appointment cancelled
  USE_CASE_APPOINTMENT_CANCELLED = 7;
  // use case for appointment moved to waitlist
  USE_CASE_APPOINTMENT_MOVED_TO_WAITLIST = 8;

  // use case for appointment confirmed by client
  USE_CASE_APPOINTMENT_CONFIRMED_BY_CLIENT = 9;
  // use case for appointment cancelled by client
  USE_CASE_APPOINTMENT_CANCELLED_BY_CLIENT = 10;

  // use case for ready for pick up
  USE_CASE_READY_FOR_PICK_UP = 11;
  // use case for send receipt when fully paid
  USE_CASE_SEND_RECEIPT_WHEN_FULLY_PAID = 12;

  // use case for first reminder
  USE_CASE_FIRST_REMINDER = 13;
  // use case for second reminder
  USE_CASE_SECOND_REMINDER = 14;
  // use case for general reminder
  USE_CASE_GENERAL_REMINDER = 15;

  // use case for pet birthday
  USE_CASE_PET_BIRTHDAY = 16;
  // use case for rebook reminder
  USE_CASE_REBOOK_REMINDER = 17;
  // use case for unsubmitted card on file reminder
  USE_CASE_UNSUBMITTED_CARD_ON_FILE_REMINDER = 18;

  // use case for auto reply
  USE_CASE_AUTO_REPLY = 19;

  // use case for saved reply
  USE_CASE_SAVED_REPLY = 90;
  // use case for eta
  USE_CASE_ETA = 91;
  // use case for send invoice to pay online
  USE_CASE_SEND_INVOICE_TO_PAY_ONLINE = 92;

  // use case for engagement follow up message
  USE_CASE_ENGAGEMENT_FOLLOWUP = 93;
}

// list of message template use case enum
message MessageTemplateUseCaseEnumList {
  // list of message template use case enum
  repeated MessageTemplateUseCase values = 1 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}
