syntax = "proto3";

package moego.models.message.v1;

import "moego/models/message/v1/twilio_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// business twilio model
message BusinessTwilioModel {
  // business id
  int64 business_id = 1;
  // twilio sid
  string twilio_sid = 2;
  // twilio token
  string twilio_token = 3;
  // twilio phone number
  string twilio_number = 4;
  // twilio number alias
  string friendly_name = 5;
  // can handle type, call forwarding only for US/CA
  bool call_handle_type = 6;
  // call forwarding
  bool can_phone_number = 7;
  // reply message
  string reply_message = 8;
  // twilio number assign status
  TwilioNumberAssignStatus assign_statue = 9;
  // twilio number use status
  TwilioNumberUseStatus use_status = 10;
  // share status
  bool share = 11;
  // bind time
  int64 create_time = 12;
  // note, bind failure reason
  string remark = 13;
}

// business twilio number view
message BusinessTwilioNumberView {
  // business id
  int64 business_id = 1;
  // twilio number
  string twilio_number = 2;
}
