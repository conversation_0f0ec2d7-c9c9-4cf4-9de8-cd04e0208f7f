syntax = "proto3";

package moego.models.grooming.v1;

import "moego/models/grooming/v1/pet_detail_enums.proto";
import "moego/models/grooming/v1/service_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// the appointment pet detail model
message PetDetailModel {
  // pet detail id
  int64 id = 1;
  // appointment id
  int64 grooming_id = 2;
  // pet id
  int64 pet_id = 3;
  // staff id
  int64 staff_id = 4;
  // service id
  int64 service_id = 5;
  // service type
  ServiceType service_type = 6;
  // service time, in minutes
  int32 service_time = 7;
  // service price
  double service_price = 8;
  // start time, in minutes
  int32 start_time = 9;
  // end time, in minutes
  int32 end_time = 10;
  // status
  PetDetailStatus status = 11;
  // update time
  int64 update_time = 12;
  // scope type price
  ServiceScopeType scope_type_price = 13;
  // scope type time
  ServiceScopeType scope_type_time = 14;
  // star staff id
  int64 star_staff_id = 15;
  // package service id
  int64 package_service_id = 16;
  // service name
  string service_name = 17;
  // service description
  string service_description = 18;
  // tax id
  int64 tax_id = 19;
  // tax rate
  double tax_rate = 20;
  // enable operation
  bool enable_operation = 21;
  // work mode, 0-parallel, 1-sequence
  WorkMode work_mode = 22;
  // service color code
  string service_color_code = 23;
}

// the appointment pet detail model client view
message PetDetailModelClientView {
  // pet id
  int64 pet_id = 1;
  // staff id
  int64 staff_id = 2;
  // service id
  int64 service_id = 3;
  // service time, in minutes
  int32 service_time = 5;
  // service price
  double service_price = 6;
}
