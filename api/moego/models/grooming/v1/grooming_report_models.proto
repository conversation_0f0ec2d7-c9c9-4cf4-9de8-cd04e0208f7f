syntax = "proto3";

package moego.models.grooming.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/grooming/v1/grooming_report_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// the grooming report model
message GroomingReportModel {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // grooming id
  int64 grooming_id = 4;
  // pet id
  int64 pet_id = 5;
  // pet type
  moego.models.customer.v1.PetType pet_type = 6;
  // uuid
  string uuid = 7;
  // template publish time
  google.protobuf.Timestamp template_publish_time = 8;
  // template json
  string template_json = 9;
  // content json
  string content_json = 10;
  // status
  GroomingReportStatus status = 11;
  // submitted time
  google.protobuf.Timestamp submitted_time = 12;
  // link opened count
  int64 link_opened_count = 13;
  // last update staff id
  int64 updated_by = 14;
  // create time
  google.protobuf.Timestamp created_time = 15;
  // update time
  google.protobuf.Timestamp updated_time = 16;
  // theme code
  string theme_code = 17;
}

// grooming report model in c app client view
message GroomingReportModelClientView {
  // grooming id
  int64 grooming_id = 4;
  // pet id
  int64 pet_id = 5;
  // uuid
  string uuid = 7;
}
