syntax = "proto3";

package moego.models.grooming.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/grooming/v1/service_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// the grooming service category model
message ServiceCategoryModel {
  // category id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // category name
  string name = 3;
  // category type
  ServiceType type = 4;
  // sort number
  int32 sort = 5;
  // is deleted
  bool is_deleted = 6;
  // create time
  google.protobuf.Timestamp create_time = 7;
  // update time
  google.protobuf.Timestamp update_time = 8;
}
