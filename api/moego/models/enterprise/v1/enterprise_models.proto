syntax = "proto3";

package moego.models.enterprise.v1;

import "google/type/latlng.proto";
import "moego/models/enterprise/v1/country_defs.proto";
import "moego/models/enterprise/v1/enterprise_enums.proto";
import "moego/models/enterprise/v1/time_zone.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// EnterpriseModel
message EnterpriseModel {
  // id
  int64 id = 1;
  // name
  string name = 2;
  // account_id
  int64 account_id = 3;
  // email
  string email = 4;
  // theme color
  string theme_color = 5;
  // currency code
  string currency_code = 6;
  // currency symbol
  string currency_symbol = 7;
  // logo path
  string logo_path = 8;
  // date format
  DateFormat date_format_type = 9;
  // time format
  TimeFormat time_format_type = 10;
  // unit of weight
  WeightUnit unit_of_weight_type = 11;
  // unit of distance
  DistanceUnit unit_of_distance_type = 12;
  // whether the notification sound is on
  bool notification_sound_enable = 13;
  // country
  CountryDef country = 14;
  // timezone
  TimeZone time_zone = 15;
  // address
  AddressDef address = 16;
  // source
  Source source = 17;

  // source
  // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
  // source
  enum Source {
    // normally add
    NORMALLY_ADD = 0;
    // manually add
    MANUALLY_ADD = 1;
    // split company
    SPLIT_COMPANY = 2;
    // demo enterprise
    DEMO_ENTERPRISE = 3;
  }
}

// definition of address
message AddressDef {
  // address 1
  optional string address1 = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // address 2
  optional string address2 = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // city
  optional string city = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
  // state
  optional string state = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
  // zip code
  optional string zipcode = 5 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
  // country
  optional string country = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // latitude and longitude
  optional google.type.LatLng coordinate = 7;
}
