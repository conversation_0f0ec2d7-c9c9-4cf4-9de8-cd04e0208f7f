syntax = "proto3";

package moego.models.payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// credit card model
message CreditCardModel {
  // id of the credit card
  string id = 1;
  // brand of the credit card, visa
  string card_brand = 2;
  // expiration month of the credit card
  int32 exp_month = 3;
  // expiration year of the credit card
  int32 exp_year = 4;
  // last 4 digits of the credit card
  string last4 = 5;
  // name of the credit card holder
  string card_holder_name = 6;
  // type of the credit card, credit or debit
  string card_type = 7;
  // expired flag of the credit card
  bool is_expired = 8;
}

// credit card model public view
message CreditCardModelPublicView {
  // id of the credit card
  string id = 1;
  // brand of the credit card, visa
  string card_brand = 2;
  // expiration month of the credit card
  int32 exp_month = 3;
  // expiration year of the credit card
  int32 exp_year = 4;
  // last 4 digits of the credit card
  string last4 = 5;
  // name of the credit card holder
  string card_holder_name = 6;
  // type of the credit card, credit or debit
  string card_type = 7;
}
