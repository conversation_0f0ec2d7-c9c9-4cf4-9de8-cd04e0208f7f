syntax = "proto3";

package moego.models.payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// payer type
enum PayerType {
  // unspecified
  PAYER_TYPE_UNSPECIFIED = 0;
  // company
  PAYER_TYPE_COMPANY = 1;
  // enterprise
  PAYER_TYPE_ENTERPRISE = 2;
}

// bill type
enum BillType {
  // unspecified
  BILLING_TYPE_UNSPECIFIED = 0;
  // subscription
  BILLING_TYPE_SUBSCRIPTION = 1;
  // sms package
  BILLING_TYPE_SMS = 2;
  // email package
  BILLING_TYPE_EMAIL = 3;
}
