<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
<head data-id="__react-email-head">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
</head>
<div id="__react-email-preview" style="display:none;overflow:hidden;line-height:1px;opacity:0;max-height:0;max-width:0">
  ${businessName!} is inviting you to join the team on MoeGo.<div>
     ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿ ‌​‍‎‏﻿
  </div>
</div>
<body data-id="__react-email-body" style="background-color:#ffffff;padding:0;margin:0">
  <table align="center" width="100%" data-id="__react-email-container" role="presentation" cellSpacing="0"
    cellPadding="0" border="0" style="max-width:600px;padding:48px 32px 24px;background-color:#f5f2f0">
    <tbody>
      <tr style="width:100%">
        <td><img data-id="react-email-img" alt="MoeGo&#x27;s Logo"
            src="https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1692009447711532469d20495986a5fd5ff1e77730.png?name=moego-logo.png"
            width="104" height="28"
            style="display:block;outline:none;border:none;text-decoration:none;margin:0 auto" /><img
            data-id="react-email-img" alt="MoeGo&#x27;s Logo" src="${headImg}" width="80" height="80"
            style="display:block;outline:none;border:none;text-decoration:none;margin:36px auto 0;border-radius:50%;object-fit:cover;background:#fff" />
          <p data-id="react-email-text"
            style="font-size:28px;line-height:34px;margin:36px auto 0;font-family:Helvetica, Arial, &#x27;PingFang SC&#x27;, &#x27;Microsoft YaHei&#x27;;font-weight:700;letter-spacing:-0.34px">
            ${businessName!} is inviting you to join the team on MoeGo.</p>
          <table align="center" width="100%" data-id="react-email-section"
            style="margin-top:36px;background-color:#fff;border-radius:36px;padding:48px 32px" border="0"
            cellPadding="0" cellSpacing="0" role="presentation">
            <tbody>
              <tr>
                <td><a href="${inviteLink!}" data-id="react-email-button" target="_blank"
                    style="background-color:#F96B18;border-radius:56px;box-shadow:0px 8px 24px -2px rgba(249, 107, 24, 0.45), 0px 2px 6px -1px rgba(249, 107, 24, 0.45);text-decoration:none;text-align:center;display:inline-block;width:100%;height:56px;line-height:100%;max-width:100%;"><span><!--[if mso]><i style="mso-font-width:-100%;mso-text-raise:0" hidden>&nbsp;</i><![endif]--></span><span
                      style="max-width:100%;display:inline-block;line-height:120%;mso-padding-alt:0px;mso-text-raise:0">
                      <p data-id="react-email-text"
                        style="font-size:18px;line-height:24px;margin:16px 0;color:#fff;font-weight:bold;font-family:Helvetica, Arial, &#x27;PingFang SC&#x27;, &#x27;Microsoft YaHei&#x27;">
                        Accept Invitation</p>
                    </span><span><!--[if mso]><i style="mso-font-width:-100%" hidden>&nbsp;</i><![endif]--></span></a>
                </td>
              </tr>
            </tbody>
          </table>
          <p data-id="react-email-text"
            style="font-size:14px;line-height:18px;margin:36px auto 0;font-family:Helvetica, Arial, &#x27;PingFang SC&#x27;, &#x27;Microsoft YaHei&#x27;;font-weight:normal;letter-spacing:0.14px;text-align:center;color:#888c96">
            ${businessName!}<br />${businessAddress!}</p>
          <p data-id="react-email-text"
            style="font-size:14px;line-height:18px;margin:36px auto 0;font-family:Helvetica, Arial, &#x27;PingFang SC&#x27;, &#x27;Microsoft YaHei&#x27;;font-weight:normal;letter-spacing:0.14px;text-align:center;color:#888c96">
            Copyright © ${copyRightYear} Moement Inc.</p>
        </td>
      </tr>
    </tbody>
  </table>
</body>
</html>
