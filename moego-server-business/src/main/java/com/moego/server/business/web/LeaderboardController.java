package com.moego.server.business.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.service.LeaderboardService;
import com.moego.server.business.utils.ReportUtil;
import com.moego.server.business.web.param.QueryLeaderboardInsightParams;
import com.moego.server.business.web.param.QueryLeaderboardRankParams;
import com.moego.server.business.web.param.QueryReportParams;
import com.moego.server.business.web.vo.report.LeaderboardInsightResponse;
import com.moego.server.business.web.vo.report.LeaderboardRankVo;
import com.moego.server.business.web.vo.report.LeaderboardStaffResponse;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/business/leaderboard")
public class LeaderboardController {

    @Autowired
    private LeaderboardService leaderboardService;

    @GetMapping("/dashboard/insight")
    @Auth(AuthType.BUSINESS)
    public LeaderboardInsightResponse getInsight(@Valid QueryLeaderboardInsightParams params) {
        // date 检查, 查询范围不能超过1年
        ReportUtil.checkQueryRange(params.getStartDate(), params.getEndDate());
        params.setBusinessId(AuthContext.get().getBusinessId())
                .setTokenStaffId(AuthContext.get().staffId())
                .setTokenCompanyId(AuthContext.get().companyId());
        return leaderboardService.getLeaderboardInsightV2(params);
    }

    @GetMapping("/dashboard/staff")
    @Auth(AuthType.BUSINESS)
    public LeaderboardStaffResponse getStaff(@Valid QueryReportParams params) {
        // date 检查, 查询范围不能超过1年
        ReportUtil.checkQueryRange(params.getStartDate(), params.getEndDate());
        params.setBusinessId(AuthContext.get().getBusinessId())
                .setTokenStaffId(AuthContext.get().staffId())
                .setTokenCompanyId(AuthContext.get().companyId());
        return leaderboardService.getLeaderboardStaffV2(params);
    }

    @GetMapping("/dashboard/rank")
    @Auth(AuthType.BUSINESS)
    public List<LeaderboardRankVo> getRank(@Valid QueryLeaderboardRankParams params) {
        // date 检查, 查询范围不能超过1年
        ReportUtil.checkQueryRange(params.getStartDate(), params.getEndDate());
        params.setBusinessId(AuthContext.get().getBusinessId())
                .setTokenStaffId(AuthContext.get().staffId())
                .setTokenCompanyId(AuthContext.get().companyId());
        return leaderboardService.getLeaderboardRankV2(params);
    }
}
