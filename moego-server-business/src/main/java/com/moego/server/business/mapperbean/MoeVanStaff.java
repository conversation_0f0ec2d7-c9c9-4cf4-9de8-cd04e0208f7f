package com.moego.server.business.mapperbean;

public class <PERSON><PERSON><PERSON><PERSON>taff {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.van_id
     *
     * @mbg.generated
     */
    private Integer vanId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.start_time
     *
     * @mbg.generated
     */
    private Long startTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.end_time
     *
     * @mbg.generated
     */
    private Long endTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_van_staff.id
     *
     * @return the value of moe_van_staff.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_van_staff.id
     *
     * @param id the value for moe_van_staff.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_van_staff.business_id
     *
     * @return the value of moe_van_staff.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_van_staff.business_id
     *
     * @param businessId the value for moe_van_staff.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_van_staff.van_id
     *
     * @return the value of moe_van_staff.van_id
     *
     * @mbg.generated
     */
    public Integer getVanId() {
        return vanId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_van_staff.van_id
     *
     * @param vanId the value for moe_van_staff.van_id
     *
     * @mbg.generated
     */
    public void setVanId(Integer vanId) {
        this.vanId = vanId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_van_staff.staff_id
     *
     * @return the value of moe_van_staff.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_van_staff.staff_id
     *
     * @param staffId the value for moe_van_staff.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_van_staff.status
     *
     * @return the value of moe_van_staff.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_van_staff.status
     *
     * @param status the value for moe_van_staff.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_van_staff.start_time
     *
     * @return the value of moe_van_staff.start_time
     *
     * @mbg.generated
     */
    public Long getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_van_staff.start_time
     *
     * @param startTime the value for moe_van_staff.start_time
     *
     * @mbg.generated
     */
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_van_staff.end_time
     *
     * @return the value of moe_van_staff.end_time
     *
     * @mbg.generated
     */
    public Long getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_van_staff.end_time
     *
     * @param endTime the value for moe_van_staff.end_time
     *
     * @mbg.generated
     */
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_van_staff.create_time
     *
     * @return the value of moe_van_staff.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_van_staff.create_time
     *
     * @param createTime the value for moe_van_staff.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_van_staff.update_time
     *
     * @return the value of moe_van_staff.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_van_staff.update_time
     *
     * @param updateTime the value for moe_van_staff.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_van_staff.company_id
     *
     * @return the value of moe_van_staff.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_van_staff.company_id
     *
     * @param companyId the value for moe_van_staff.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
