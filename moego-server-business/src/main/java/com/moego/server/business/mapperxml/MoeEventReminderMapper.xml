<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.business.mapper.MoeEventReminderMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.business.mapperbean.MoeEventReminder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="reminder_type" jdbcType="TINYINT" property="reminderType" />
    <result column="page_id" jdbcType="TINYINT" property="pageId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="reminder_code" jdbcType="TINYINT" property="reminderCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, account_id, company_id, reminder_type, page_id, status, create_time, update_time, 
    reminder_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_event_reminder
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_event_reminder
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.business.mapperbean.MoeEventReminder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_event_reminder (account_id, company_id, reminder_type, 
      page_id, status, create_time, 
      update_time, reminder_code)
    values (#{accountId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{reminderType,jdbcType=TINYINT}, 
      #{pageId,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{reminderCode,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.business.mapperbean.MoeEventReminder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_event_reminder
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="reminderType != null">
        reminder_type,
      </if>
      <if test="pageId != null">
        page_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="reminderCode != null">
        reminder_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="reminderType != null">
        #{reminderType,jdbcType=TINYINT},
      </if>
      <if test="pageId != null">
        #{pageId,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reminderCode != null">
        #{reminderCode,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.business.mapperbean.MoeEventReminder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_event_reminder
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="reminderType != null">
        reminder_type = #{reminderType,jdbcType=TINYINT},
      </if>
      <if test="pageId != null">
        page_id = #{pageId,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reminderCode != null">
        reminder_code = #{reminderCode,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.business.mapperbean.MoeEventReminder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_event_reminder
    set account_id = #{accountId,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=INTEGER},
      reminder_type = #{reminderType,jdbcType=TINYINT},
      page_id = #{pageId,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      reminder_code = #{reminderCode,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="countEventReminderList" resultType="int">
    select
    count(*)
    from moe_event_reminder
    where account_id = #{param.accountId,jdbcType=INTEGER}
    and company_id = #{param.companyId,jdbcType=INTEGER}
    and status = ${@com.moego.common.enums.BusinessReferralConst@BONUS_PENDING}
    and reminder_type in
    <foreach collection="param.reminderType" item="reminderType" open="(" close=")" separator=",">
      #{reminderType,jdbcType=TINYINT}
    </foreach>
    <if test="param.pageId != null">
      and page_id = #{param.pageId,jdbcType=TINYINT}
    </if>
  </select>

  <select id="getEventReminderList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_event_reminder
    where account_id = #{param.accountId,jdbcType=INTEGER}
    and company_id = #{param.companyId,jdbcType=INTEGER}
    and status = ${@com.moego.common.enums.BusinessReferralConst@BONUS_PENDING}
    and reminder_type in
    <foreach collection="param.reminderType" item="reminderType" open="(" close=")" separator=",">
      #{reminderType,jdbcType=TINYINT}
    </foreach>
    <if test="param.pageId != null">
      and page_id = #{param.pageId,jdbcType=TINYINT}
    </if>
  </select>

  <update id="updateUnconfirmedReminderList">
    update
    moe_event_reminder
    set status = ${@com.moego.common.enums.BusinessReferralConst@BONUS_CONFIRMED}
    where account_id = #{param.accountId,jdbcType=INTEGER}
    and company_id = #{param.companyId,jdbcType=INTEGER}
    and status = ${@com.moego.common.enums.BusinessReferralConst@BONUS_PENDING}
    and reminder_type in
    <foreach collection="param.autoConfirmType" item="confirmType" open="(" close=")" separator=",">
      #{confirmType,jdbcType=TINYINT}
    </foreach>
    <if test="param.pageId != null">
      and page_id = #{param.pageId,jdbcType=TINYINT}
    </if>
  </update>

  <update id="batchUpdateStatusById">
    update
      moe_event_reminder
    set status = #{status,jdbcType=TINYINT}
    where id in
    <foreach close=")" collection="list" item="id" open="(" separator=",">
      #{id,jdbcType=INTEGER}
    </foreach>
  </update>

  <insert id="insertSelectiveIfNotExists" parameterType="com.moego.server.business.mapperbean.MoeEventReminder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_event_reminder
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="reminderType != null">
        reminder_type,
      </if>
      <if test="pageId != null">
        page_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="reminderCode != null">
        reminder_code,
      </if>
    </trim>
    <trim prefix="select * from ( select " suffix=" ) as t" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER} as account_id,
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER} as company_id,
      </if>
      <if test="reminderType != null">
        #{reminderType,jdbcType=TINYINT} as reminder_type,
      </if>
      <if test="pageId != null">
        #{pageId,jdbcType=TINYINT} as page_id,
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT} as status,
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP} as create_time,
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP} as update_time,
      </if>
      <if test="reminderCode != null">
        #{reminderCode,jdbcType=TINYINT} as reminder_code,
      </if>
    </trim>
       where
           not exists (
               select
                   id
               from
                   moe_event_reminder
               where
                   account_id = #{accountId,jdbcType=INTEGER}
                 and company_id = #{companyId,jdbcType=INTEGER}
                 and reminder_code = #{reminderCode,jdbcType=TINYINT})
       limit 1

  </insert>
</mapper>