package com.moego.server.business.server;

import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.dto.BusinessPreferenceDto;
import com.moego.common.dto.CompanyFunctionControlDto;
import com.moego.common.dto.NewPricingLevelDto;
import com.moego.common.enums.BusinessConst;
import com.moego.common.enums.CompanyFunctionControlConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.AccountUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.api.IBusinessBusinessServiceBase;
import com.moego.server.business.converter.BusinessConverter;
import com.moego.server.business.converter.CompanyConverter;
import com.moego.server.business.dto.BatchQueryBusinessResult;
import com.moego.server.business.dto.BusinessIdWithLevelDto;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.business.dto.CompanyIdDTO;
import com.moego.server.business.dto.DescribeBusinessesDTO;
import com.moego.server.business.dto.DescribeCompaniesDTO;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.dto.SendDailyEmailDto;
import com.moego.server.business.dto.StaffCompanyDto;
import com.moego.server.business.mapper.MoeBusinessMapper;
import com.moego.server.business.mapperbean.MoeBusiness;
import com.moego.server.business.mapperbean.MoeBusinessExample;
import com.moego.server.business.mapperbean.MoeCompany;
import com.moego.server.business.params.BatchQueryBusinessParams;
import com.moego.server.business.params.CommonIdsParams;
import com.moego.server.business.params.DescribeBusinessesParams;
import com.moego.server.business.params.DescribeCompaniesParams;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.service.BusinessService;
import com.moego.server.business.service.CacheControlService;
import com.moego.server.business.service.CompanyService;
import com.moego.server.business.service.DefaultValueService;
import com.moego.server.business.service.RelevantAccountService;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
public class BusinessBusinessServer extends IBusinessBusinessServiceBase {

    private final BusinessService businessService;
    private final CacheControlService cacheControlService;
    private final CompanyService companyService;
    private final DefaultValueService defaultValueService;
    private final RelevantAccountService relevantAccountService;
    private final MoeBusinessMapper businessMapper;

    @Override
    public String getBusinessInvitationCodeById(Integer businessId) {
        MoeBusiness business = businessMapper.selectByPrimaryKey(businessId);
        return business.getInvitationCode();
    }

    @Override
    public Integer getBusinessIdByInvitationCode(String invitationCode) {
        return businessService.getBusinessIdByInvitationCode(invitationCode);
    }

    @Override
    public List<Integer> listBusinessIdHasInvitationCode() {
        MoeBusinessExample example = new MoeBusinessExample();
        example.createCriteria().andInvitationCodeNotEqualTo("");
        List<MoeBusiness> businesses = businessMapper.selectByExample(example);
        return businesses.stream().map(MoeBusiness::getId).toList();
    }

    @Override
    public OBBusinessInfoDTO getBusinessInfoForOB(InfoIdParams businessIdParams) {
        return businessService.getOnlyBusinessInfoForOB(businessIdParams.getInfoId());
    }

    @Override
    public List<OBBusinessInfoDTO> getBusinessInfoListForOB(CommonIdsParams idsParams) {
        return businessService.getBusinessInfoListForOB(idsParams.getIds());
    }

    @Override
    public MoeBusinessDto getBusinessInfo(@RequestBody InfoIdParams businessIdParams) {
        return businessService.getOnlyBusinessInfo(businessIdParams.getInfoId());
    }

    @Override
    public Map<Integer, MoeBusinessDto> getOnlyBusinessInfoBatch(@RequestBody List<Integer> ids) {
        return businessService.getOnlyBusinessInfoBatch(ids);
    }

    @Override
    public MoeBusinessDto getBusinessInfoWithOwnerEmail(@RequestBody InfoIdParams businessIdParams) {
        return businessService.getBusinessInfoForApi(businessIdParams.getInfoId());
    }

    @Override
    public MoeBusinessDto getBusinessInfoWithOwnerEmailV2(InfoIdParams businessIdParams) {
        return businessService.getBusinessInfoWithOwnerEmailV2(businessIdParams.getInfoId());
    }

    @Override
    public BusinessPreferenceDto getBusinessPreference(@RequestParam("businessId") Integer businessId) {
        return businessService.getBusinessPreference(businessId);
    }

    @Override
    public List<StaffCompanyDto> getCompaniesForStaff(@RequestParam("accountId") Integer accountId) {
        return companyService.getCompaniesForStaff(accountId);
    }

    @Override
    public Map<Integer, BusinessIdWithLevelDto> queryBusinessIdByCompanyId(
            @RequestParam("companyIds") List<Integer> companyIds) {
        return companyService.queryBusinessIdsByCompanyId(companyIds);
    }

    @Override
    public CompanyFunctionControlDto queryCompanyPermissionByCompanyId(@RequestParam("companyId") Integer companyId) {
        return companyService.queryCompanyPermissionByCompanyId(companyId);
    }

    @Override
    public CompanyFunctionControlDto queryCompanyPermissionByBusinessId(
            @RequestParam("businessId") Integer businessId) {
        return companyService.queryCompanyPermissionByBusinessId(businessId);
    }

    @Override
    public List<Integer> getAllBusinessIds(@RequestParam("businessId") Integer businessId) {
        return companyService.getAllBusinessIds(businessId);
    }

    @Override
    public Integer getFranchisorAccountId(Integer businessId) {
        return relevantAccountService.getFranchisorAccountId(businessId);
    }

    /**
     * only for ob
     *
     * @param businessId
     * @return
     */
    @Override
    public List<Integer> getAllRelevantBusinessIds(@RequestParam("businessId") Integer businessId) {
        return companyService.getAllRelevantBusinessIdsForOb(businessId);
    }

    @Override
    public CompanyDto getCompanyById(@RequestParam("companyId") Integer companyId) {
        MoeCompany company = companyService.getCompanyById(companyId);
        if (company == null) {
            throw new CommonException(ResponseCodeEnum.COMPANY_NOT_FOUND);
        }
        return CompanyConverter.INSTANCE.toCompanyDTO(company);
    }

    @Override
    public CompanyDto getCompanyByIdFromMaster(Integer companyId) {
        MoeCompany company = companyService.getCompanyByIdFromMaster(companyId);
        if (company == null) {
            throw new CommonException(ResponseCodeEnum.COMPANY_NOT_FOUND);
        }
        return CompanyConverter.INSTANCE.toCompanyDTO(company);
    }

    @Override
    public CompanyDto getCompanyByBusinessId(@RequestParam("businessId") Integer businessId) {
        MoeCompany company = companyService.getCompanyByBusinessId(businessId);
        if (company == null) {
            log.error("Company is null for businessId={}", businessId);
            return null;
        }
        return CompanyConverter.INSTANCE.toCompanyDTO(company);
    }

    @Override
    public Map<Integer, MoeBusinessDto> getBusinessByCompanyId(@RequestParam("companyId") Integer companyId) {
        return businessService.getBusinessByCompanyId(companyId);
    }

    @Override
    public Integer updateCompany(
            @RequestParam("companyId") Integer companyId,
            @RequestParam("level") Integer level,
            @RequestParam("locationNum") Integer locationNum,
            @RequestParam("vansNum") Integer vansNum,
            @RequestParam("isAdmin") Boolean isAdmin) {
        MoeCompany update = new MoeCompany();
        update.setId(companyId);
        update.setLevel(level);
        update.setIsNewPricing(1);
        // 修改staff num 限制：不同级别的套餐，staffNum的数量不同
        // tier1是3，tier2和3是无限，旧用户是无限
        // 旧用户这里会通过sql的方式更新，new pricing用户在切换套餐的时候更新
        NewPricingLevelDto planTier = AccountUtil.convertNewPricingType(level);
        if (CompanyFunctionControlConst.PLAN_VERSION_3.equals(planTier.getPlanVersion())) {
            if (CompanyFunctionControlConst.TIER_LEVEL_FIRST.equals(planTier.getPremiumType())) {
                update.setStaffNum(CompanyFunctionControlConst.TIER_FIRST_STAFF_NUM);
            } else if (CompanyFunctionControlConst.TIER_LEVEL_SECOND.equals(planTier.getPremiumType())) {
                update.setStaffNum(CompanyFunctionControlConst.TIER_SECOND_STAFF_NUM);
            } else {
                update.setStaffNum(CompanyFunctionControlConst.TIER_THIRD_STAFF_NUM);
            }
        }
        if (CompanyFunctionControlConst.PREMIUM_TYPE_FREE.equals(planTier.getPremiumType())) {
            update.setStaffNum(CompanyFunctionControlConst.TIER_FREE_STAFF_NUM);
        }
        if (isAdmin || locationNum + vansNum > 0) {
            update.setLocationNum(locationNum);
            update.setVansNum(vansNum);
            // change business type when need
            // 1. bug for free company https://moego.atlassian.net/browse/ERP-1767
            // 2. change type for only unit owner
            if (vansNum == 0 || locationNum == 0) {
                Map<Integer, MoeBusinessDto> existingBiz = getBusinessByCompanyId(companyId);
                MoeBusinessDto biz = existingBiz.values().stream().findFirst().get();
                if (BusinessConst.APP_TYPE_HYBRID.equals(biz.getAppType())) {
                    biz.setAppType(BusinessConst.APP_TYPE_MOBILE);
                }
                MoeBusiness toUpdate = new MoeBusiness();
                toUpdate.setId(biz.getId());
                if (existingBiz.size() == 1) {
                    log.warn("auto change business type for subscription. companyId = {} ", companyId);
                    if (vansNum == 0 && BusinessConst.APP_TYPE_MOBILE.equals(biz.getAppType())) {
                        toUpdate.setAppType(BusinessConst.APP_TYPE_SALON);
                        toUpdate.setBusinessMode(BusinessConst.BIZ_MODE_SALON);
                        businessService.updateBusinessSelective(toUpdate);
                    } else if (locationNum == 0 && BusinessConst.APP_TYPE_SALON.equals(biz.getAppType())) {
                        toUpdate.setAppType(BusinessConst.APP_TYPE_MOBILE);
                        toUpdate.setBusinessMode(BusinessConst.BIZ_MODE_MOBILE);
                        businessService.updateBusinessSelective(toUpdate);
                    }
                }
            }
        }
        return companyService.updateCompany(update);
    }

    @Override
    public Boolean getBusinessShareUpcomingShowServicePrice(@RequestParam("tokenBusinessId") Integer tokenBusinessId) {
        return defaultValueService.getShareUpcomingShowServicePrice(tokenBusinessId);
    }

    @Override
    public List<SendDailyEmailDto> getAllNeedSendDailyBusiness() {
        return businessService.getAllNeedSendDailyBusiness();
    }

    @Override
    public Boolean cacheFlush(@RequestParam("businessId") Integer businessId) {
        cacheControlService.removeMoeBusinessDtoCache(businessId);
        return true;
    }

    @Override
    public List<Integer> getAllBusinessIds2() {
        return businessService.getAllBusinessIds();
    }

    @Override
    public List<Integer> getTargetLevelBusinessIds(
            @RequestParam("filterCompanyIds") List<Integer> filterCompanyIds,
            @RequestParam("startLevel") Integer startLevel,
            @RequestParam("endLevel") Integer endLevel) {
        return companyService.getTargetLevelBusinessIds(filterCompanyIds, startLevel, endLevel);
    }

    @Override
    public Boolean updateBizInfoAndFlushCache(OBBusinessInfoDTO businessInfoDTO) {
        MoeBusiness business = new MoeBusiness();
        BeanUtils.copyProperties(businessInfoDTO, business);
        business.setUpdateTime(DateUtil.get10Timestamp());
        return businessService.updateBusinessSelective(business);
    }

    @Override
    public BusinessDateTimeDTO getBusinessDateTime(Integer businessId) {
        return businessService.getBusinessDateTime(businessId);
    }

    @Override
    public Map<Integer, BusinessDateTimeDTO> listBusinessDateTime(List<Integer> businessIds) {
        if (ObjectUtils.isEmpty(businessIds)) {
            return Map.of();
        }
        return businessService.listBusinessDateTime(businessIds).stream()
                .collect(Collectors.toMap(BusinessDateTimeDTO::getBusinessId, Function.identity(), (o, n) -> o));
    }

    @Override
    public List<Integer> getUSBusinessId() {
        return businessService.getUSBusinessId();
    }

    @Override
    public DescribeBusinessesDTO describeBusinesses(DescribeBusinessesParams params) {
        var data = businessService.describeBusinesses(params, params.pagination());
        return new DescribeBusinessesDTO(
                data.getFirst().stream()
                        .map(BusinessConverter.INSTANCE::toBusinessDTO)
                        .toList(),
                data.getSecond());
    }

    @Override
    public DescribeCompaniesDTO describeCompanies(DescribeCompaniesParams params) {
        var data = companyService.describeCompanies(params, params.pagination());
        return new DescribeCompaniesDTO(
                data.getFirst().stream()
                        .map(CompanyConverter.INSTANCE::toCompanyDTO)
                        .toList(),
                data.getSecond());
    }

    @Override
    public CompanyDto updateCompanyBlindly(CompanyDto vo) {
        var po = new MoeCompany();
        BeanUtils.copyProperties(vo, po);
        companyService.updateCompany(po);
        var dto = new CompanyDto();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    @Override
    public CompanyIdDTO getCompanyIdByBusinessId(Integer businessId) {
        var companyId = businessService.getCompanyIdByBusinessId(businessId);
        if (Objects.isNull(companyId)) {
            throw ExceptionUtil.bizException(Code.CODE_BUSINESS_NOT_FOUND);
        }
        return new CompanyIdDTO(companyId.longValue());
    }

    @Override
    public BatchQueryBusinessResult batchQueryBusiness(BatchQueryBusinessParams params) {
        return businessService.batchQueryBusinessInfo(params);
    }

    @Override
    public Boolean isBusinessRetailEnable(Integer businessId) {
        return businessService.isBusinessRetailEnable(businessId);
    }

    @Override
    public Map<Integer, Boolean> isRetailEnable(List<Integer> businessIdList) {
        return businessService.isBusinessRetailEnable(businessIdList);
    }
}
