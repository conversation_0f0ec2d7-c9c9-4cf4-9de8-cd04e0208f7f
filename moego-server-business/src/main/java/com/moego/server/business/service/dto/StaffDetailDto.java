package com.moego.server.business.service.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StaffDetailDto {

    private Integer roleId;
    private String avatarPath;
    private String firstName;
    private String lastName;
    private Byte employeeCategory;
    private String phoneNumber;
    private Byte allowLogin;
    private String email;
    private Long hireDate;
    private String note;
    private Byte inactive;
    private Byte status;
    private Integer createById;
    private Integer sort;
    private Byte bookOnlineAvailable;
    private Byte showOnCalendar;
    private Byte showCalendarStaffAll;
    private String accessCode;
    private String inviteCode;
    private Byte payBy;
    private Integer servicePayRate;
    private Integer addonPayRate;
    private BigDecimal hourlyPayRate;
    private Integer tipsPayRate;

    /**
     * 邀请员工注册时，发送的邮箱地址
     * 为空，表示没有发送过，或者缓存过期，或者已经注册成功
     */
    private String inviteLinkRecipientEmail;
}
