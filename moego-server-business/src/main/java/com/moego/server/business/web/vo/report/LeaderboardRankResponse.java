package com.moego.server.business.web.vo.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LeaderboardRankResponse {

    private List<LeaderboardRankVo> avgSalesPerStaff;

    private List<LeaderboardRankVo> salesByService;

    private List<LeaderboardRankVo> salesByClients;

    private List<LeaderboardRankVo> amountByPetBreeds;

    private List<LeaderboardRankVo> salesByStaff;

    private List<LeaderboardRankVo> workingProductivityByStaff;
}
