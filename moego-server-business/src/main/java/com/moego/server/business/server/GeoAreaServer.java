package com.moego.server.business.server;

import com.moego.server.business.api.IGeoAreaServiceBase;
import com.moego.server.business.converter.GeoAreaConverter;
import com.moego.server.business.dto.GeoAreaDTO;
import com.moego.server.business.mapper.MoeGeoareaMapper;
import java.util.Collection;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class GeoAreaServer extends IGeoAreaServiceBase {

    private final MoeGeoareaMapper geoAreaMapper;

    @Override
    public List<GeoAreaDTO> listGeoArea(Collection<Integer> areaIds) {
        if (ObjectUtils.isEmpty(areaIds)) {
            return List.of();
        }
        return geoAreaMapper.queryNotDeletedByPrimaryIds(List.copyOf(areaIds)).stream()
                .map(GeoAreaConverter.INSTANCE::entityToDTO)
                .toList();
    }
}
