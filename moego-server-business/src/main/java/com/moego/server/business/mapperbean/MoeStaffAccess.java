package com.moego.server.business.mapperbean;

import java.util.Date;

public class MoeStaffAccess {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_access.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_access.staff_id
     *
     * @mbg.generated
     */
    private Long staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_access.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_access.location_id
     *
     * @mbg.generated
     */
    private Long locationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_access.access_location_all_staffs
     *
     * @mbg.generated
     */
    private Boolean accessLocationAllStaffs;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_access.access_staff_ids
     *
     * @mbg.generated
     */
    private String accessStaffIds;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_access.updated_at
     *
     * @mbg.generated
     */
    private Date updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_access.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_access.id
     *
     * @return the value of moe_staff_access.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_access.id
     *
     * @param id the value for moe_staff_access.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_access.staff_id
     *
     * @return the value of moe_staff_access.staff_id
     *
     * @mbg.generated
     */
    public Long getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_access.staff_id
     *
     * @param staffId the value for moe_staff_access.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_access.company_id
     *
     * @return the value of moe_staff_access.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_access.company_id
     *
     * @param companyId the value for moe_staff_access.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_access.location_id
     *
     * @return the value of moe_staff_access.location_id
     *
     * @mbg.generated
     */
    public Long getLocationId() {
        return locationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_access.location_id
     *
     * @param locationId the value for moe_staff_access.location_id
     *
     * @mbg.generated
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_access.access_location_all_staffs
     *
     * @return the value of moe_staff_access.access_location_all_staffs
     *
     * @mbg.generated
     */
    public Boolean getAccessLocationAllStaffs() {
        return accessLocationAllStaffs;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_access.access_location_all_staffs
     *
     * @param accessLocationAllStaffs the value for moe_staff_access.access_location_all_staffs
     *
     * @mbg.generated
     */
    public void setAccessLocationAllStaffs(Boolean accessLocationAllStaffs) {
        this.accessLocationAllStaffs = accessLocationAllStaffs;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_access.access_staff_ids
     *
     * @return the value of moe_staff_access.access_staff_ids
     *
     * @mbg.generated
     */
    public String getAccessStaffIds() {
        return accessStaffIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_access.access_staff_ids
     *
     * @param accessStaffIds the value for moe_staff_access.access_staff_ids
     *
     * @mbg.generated
     */
    public void setAccessStaffIds(String accessStaffIds) {
        this.accessStaffIds = accessStaffIds == null ? null : accessStaffIds.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_access.updated_at
     *
     * @return the value of moe_staff_access.updated_at
     *
     * @mbg.generated
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_access.updated_at
     *
     * @param updatedAt the value for moe_staff_access.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_access.updated_by
     *
     * @return the value of moe_staff_access.updated_by
     *
     * @mbg.generated
     */
    public Long getUpdatedBy() {
        return updatedBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_access.updated_by
     *
     * @param updatedBy the value for moe_staff_access.updated_by
     *
     * @mbg.generated
     */
    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }
}
