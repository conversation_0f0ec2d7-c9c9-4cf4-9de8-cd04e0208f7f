package com.moego.server.business.service.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class InviteCodeStatusDto {
    private String businessName;
    private String enterpriseName;
    private Byte isValid;
    private Byte invalidType;
    private String businessAvatarPath;
    private Integer businessId;
    private Integer staffId;
    private Byte staffStatus;
    private Byte employeeCategory;
    private Integer companyId;
    // 目前是给 account structure 使用，1 - 表示迁移之前的版本，2 - 表示迁移之后的版本
    private Integer version;
    private Integer enterpriseId;
}
