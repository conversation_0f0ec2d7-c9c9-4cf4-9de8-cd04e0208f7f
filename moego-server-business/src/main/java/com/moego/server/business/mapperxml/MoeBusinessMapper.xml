<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.business.mapper.MoeBusinessMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.business.mapperbean.MoeBusiness">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="avatar_path" jdbcType="VARCHAR" property="avatarPath" />
    <result column="website" jdbcType="VARCHAR" property="website" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="address1" jdbcType="VARCHAR" property="address1" />
    <result column="address2" jdbcType="VARCHAR" property="address2" />
    <result column="address_city" jdbcType="VARCHAR" property="addressCity" />
    <result column="address_state" jdbcType="VARCHAR" property="addressState" />
    <result column="address_zipcode" jdbcType="VARCHAR" property="addressZipcode" />
    <result column="address_country" jdbcType="VARCHAR" property="addressCountry" />
    <result column="address_lat" jdbcType="VARCHAR" property="addressLat" />
    <result column="address_lng" jdbcType="VARCHAR" property="addressLng" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="country_alpha2_code" jdbcType="CHAR" property="countryAlpha2Code" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="currency_symbol" jdbcType="VARCHAR" property="currencySymbol" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="time_format_type" jdbcType="TINYINT" property="timeFormatType" />
    <result column="unit_of_weight_type" jdbcType="TINYINT" property="unitOfWeightType" />
    <result column="unit_of_distance_type" jdbcType="TINYINT" property="unitOfDistanceType" />
    <result column="timezone_name" jdbcType="VARCHAR" property="timezoneName" />
    <result column="timezone_seconds" jdbcType="INTEGER" property="timezoneSeconds" />
    <result column="date_format_type" jdbcType="TINYINT" property="dateFormatType" />
    <result column="calendar_format_type" jdbcType="TINYINT" property="calendarFormatType" />
    <result column="number_format_type" jdbcType="TINYINT" property="numberFormatType" />
    <result column="book_online_name" jdbcType="VARCHAR" property="bookOnlineName" />
    <result column="app_type" jdbcType="TINYINT" property="appType" />
    <result column="primary_pay_type" jdbcType="TINYINT" property="primaryPayType" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="clock_in_out_enable" jdbcType="TINYINT" property="clockInOutEnable" />
    <result column="clock_in_out_notify" jdbcType="TINYINT" property="clockInOutNotify" />
    <result column="is_enable_access_code" jdbcType="TINYINT" property="isEnableAccessCode" />
    <result column="smart_schedule_max_dist" jdbcType="INTEGER" property="smartScheduleMaxDist" />
    <result column="smart_schedule_max_time" jdbcType="INTEGER" property="smartScheduleMaxTime" />
    <result column="service_area_enable" jdbcType="TINYINT" property="serviceAreaEnable" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="facebook" jdbcType="VARCHAR" property="facebook" />
    <result column="instagram" jdbcType="VARCHAR" property="instagram" />
    <result column="google" jdbcType="VARCHAR" property="google" />
    <result column="yelp" jdbcType="VARCHAR" property="yelp" />
    <result column="smart_schedule_start_lat" jdbcType="VARCHAR" property="smartScheduleStartLat" />
    <result column="smart_schedule_start_lng" jdbcType="VARCHAR" property="smartScheduleStartLng" />
    <result column="smart_schedule_end_lat" jdbcType="VARCHAR" property="smartScheduleEndLat" />
    <result column="smart_schedule_end_lng" jdbcType="VARCHAR" property="smartScheduleEndLng" />
    <result column="smart_schedule_service_range" jdbcType="INTEGER" property="smartScheduleServiceRange" />
    <result column="smart_schedule_start_addr" jdbcType="VARCHAR" property="smartScheduleStartAddr" />
    <result column="smart_schedule_end_addr" jdbcType="VARCHAR" property="smartScheduleEndAddr" />
    <result column="source_from" jdbcType="TINYINT" property="sourceFrom" />
    <result column="message_send_by" jdbcType="TINYINT" property="messageSendBy" />
    <result column="send_daily" jdbcType="TINYINT" property="sendDaily" />
    <result column="business_mode" jdbcType="TINYINT" property="businessMode" />
    <result column="know_about_us" jdbcType="VARCHAR" property="knowAboutUs" />
    <result column="appt_per_week" jdbcType="TINYINT" property="apptPerWeek" />
    <result column="business_years" jdbcType="TINYINT" property="businessYears" />
    <result column="move_from" jdbcType="TINYINT" property="moveFrom" />
    <result column="retail_enable" jdbcType="TINYINT" property="retailEnable" />
    <result column="notification_sound_enable" jdbcType="TINYINT" property="notificationSoundEnable" />
    <result column="contact_email" jdbcType="VARCHAR" property="contactEmail" />
    <result column="invitation_code" jdbcType="VARCHAR" property="invitationCode" />
    <result column="tiktok" jdbcType="VARCHAR" property="tiktok" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, business_name, phone_number, avatar_path, website, address, address1, 
    address2, address_city, address_state, address_zipcode, address_country, address_lat, 
    address_lng, country, country_alpha2_code, country_code, currency_symbol, currency_code, 
    time_format_type, unit_of_weight_type, unit_of_distance_type, timezone_name, timezone_seconds, 
    date_format_type, calendar_format_type, number_format_type, book_online_name, app_type, 
    primary_pay_type, source, clock_in_out_enable, clock_in_out_notify, is_enable_access_code, 
    smart_schedule_max_dist, smart_schedule_max_time, service_area_enable, create_time, 
    update_time, facebook, instagram, google, yelp, smart_schedule_start_lat, smart_schedule_start_lng, 
    smart_schedule_end_lat, smart_schedule_end_lng, smart_schedule_service_range, smart_schedule_start_addr, 
    smart_schedule_end_addr, source_from, message_send_by, send_daily, business_mode, 
    know_about_us, appt_per_week, business_years, move_from, retail_enable, notification_sound_enable, 
    contact_email, invitation_code, tiktok
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.business.mapperbean.MoeBusinessExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_business
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_business
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.business.mapperbean.MoeBusinessExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.business.mapperbean.MoeBusiness">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business (company_id, business_name, phone_number, 
      avatar_path, website, address, 
      address1, address2, address_city, 
      address_state, address_zipcode, address_country, 
      address_lat, address_lng, country, 
      country_alpha2_code, country_code, currency_symbol, 
      currency_code, time_format_type, unit_of_weight_type, 
      unit_of_distance_type, timezone_name, timezone_seconds, 
      date_format_type, calendar_format_type, number_format_type, 
      book_online_name, app_type, primary_pay_type, 
      source, clock_in_out_enable, clock_in_out_notify, 
      is_enable_access_code, smart_schedule_max_dist, 
      smart_schedule_max_time, service_area_enable, 
      create_time, update_time, facebook, 
      instagram, google, yelp, 
      smart_schedule_start_lat, smart_schedule_start_lng, 
      smart_schedule_end_lat, smart_schedule_end_lng, 
      smart_schedule_service_range, smart_schedule_start_addr, 
      smart_schedule_end_addr, source_from, message_send_by, 
      send_daily, business_mode, know_about_us, 
      appt_per_week, business_years, move_from, 
      retail_enable, notification_sound_enable, 
      contact_email, invitation_code, tiktok
      )
    values (#{companyId,jdbcType=INTEGER}, #{businessName,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR}, 
      #{avatarPath,jdbcType=VARCHAR}, #{website,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{address1,jdbcType=VARCHAR}, #{address2,jdbcType=VARCHAR}, #{addressCity,jdbcType=VARCHAR}, 
      #{addressState,jdbcType=VARCHAR}, #{addressZipcode,jdbcType=VARCHAR}, #{addressCountry,jdbcType=VARCHAR}, 
      #{addressLat,jdbcType=VARCHAR}, #{addressLng,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, 
      #{countryAlpha2Code,jdbcType=CHAR}, #{countryCode,jdbcType=VARCHAR}, #{currencySymbol,jdbcType=VARCHAR}, 
      #{currencyCode,jdbcType=VARCHAR}, #{timeFormatType,jdbcType=TINYINT}, #{unitOfWeightType,jdbcType=TINYINT}, 
      #{unitOfDistanceType,jdbcType=TINYINT}, #{timezoneName,jdbcType=VARCHAR}, #{timezoneSeconds,jdbcType=INTEGER}, 
      #{dateFormatType,jdbcType=TINYINT}, #{calendarFormatType,jdbcType=TINYINT}, #{numberFormatType,jdbcType=TINYINT}, 
      #{bookOnlineName,jdbcType=VARCHAR}, #{appType,jdbcType=TINYINT}, #{primaryPayType,jdbcType=TINYINT}, 
      #{source,jdbcType=TINYINT}, #{clockInOutEnable,jdbcType=TINYINT}, #{clockInOutNotify,jdbcType=TINYINT}, 
      #{isEnableAccessCode,jdbcType=TINYINT}, #{smartScheduleMaxDist,jdbcType=INTEGER}, 
      #{smartScheduleMaxTime,jdbcType=INTEGER}, #{serviceAreaEnable,jdbcType=TINYINT}, 
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{facebook,jdbcType=VARCHAR}, 
      #{instagram,jdbcType=VARCHAR}, #{google,jdbcType=VARCHAR}, #{yelp,jdbcType=VARCHAR}, 
      #{smartScheduleStartLat,jdbcType=VARCHAR}, #{smartScheduleStartLng,jdbcType=VARCHAR}, 
      #{smartScheduleEndLat,jdbcType=VARCHAR}, #{smartScheduleEndLng,jdbcType=VARCHAR}, 
      #{smartScheduleServiceRange,jdbcType=INTEGER}, #{smartScheduleStartAddr,jdbcType=VARCHAR}, 
      #{smartScheduleEndAddr,jdbcType=VARCHAR}, #{sourceFrom,jdbcType=TINYINT}, #{messageSendBy,jdbcType=TINYINT}, 
      #{sendDaily,jdbcType=TINYINT}, #{businessMode,jdbcType=TINYINT}, #{knowAboutUs,jdbcType=VARCHAR}, 
      #{apptPerWeek,jdbcType=TINYINT}, #{businessYears,jdbcType=TINYINT}, #{moveFrom,jdbcType=TINYINT}, 
      #{retailEnable,jdbcType=TINYINT}, #{notificationSoundEnable,jdbcType=TINYINT}, 
      #{contactEmail,jdbcType=VARCHAR}, #{invitationCode,jdbcType=VARCHAR}, #{tiktok,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.business.mapperbean.MoeBusiness">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="avatarPath != null">
        avatar_path,
      </if>
      <if test="website != null">
        website,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="address1 != null">
        address1,
      </if>
      <if test="address2 != null">
        address2,
      </if>
      <if test="addressCity != null">
        address_city,
      </if>
      <if test="addressState != null">
        address_state,
      </if>
      <if test="addressZipcode != null">
        address_zipcode,
      </if>
      <if test="addressCountry != null">
        address_country,
      </if>
      <if test="addressLat != null">
        address_lat,
      </if>
      <if test="addressLng != null">
        address_lng,
      </if>
      <if test="country != null">
        country,
      </if>
      <if test="countryAlpha2Code != null">
        country_alpha2_code,
      </if>
      <if test="countryCode != null">
        country_code,
      </if>
      <if test="currencySymbol != null">
        currency_symbol,
      </if>
      <if test="currencyCode != null">
        currency_code,
      </if>
      <if test="timeFormatType != null">
        time_format_type,
      </if>
      <if test="unitOfWeightType != null">
        unit_of_weight_type,
      </if>
      <if test="unitOfDistanceType != null">
        unit_of_distance_type,
      </if>
      <if test="timezoneName != null">
        timezone_name,
      </if>
      <if test="timezoneSeconds != null">
        timezone_seconds,
      </if>
      <if test="dateFormatType != null">
        date_format_type,
      </if>
      <if test="calendarFormatType != null">
        calendar_format_type,
      </if>
      <if test="numberFormatType != null">
        number_format_type,
      </if>
      <if test="bookOnlineName != null">
        book_online_name,
      </if>
      <if test="appType != null">
        app_type,
      </if>
      <if test="primaryPayType != null">
        primary_pay_type,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="clockInOutEnable != null">
        clock_in_out_enable,
      </if>
      <if test="clockInOutNotify != null">
        clock_in_out_notify,
      </if>
      <if test="isEnableAccessCode != null">
        is_enable_access_code,
      </if>
      <if test="smartScheduleMaxDist != null">
        smart_schedule_max_dist,
      </if>
      <if test="smartScheduleMaxTime != null">
        smart_schedule_max_time,
      </if>
      <if test="serviceAreaEnable != null">
        service_area_enable,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="facebook != null">
        facebook,
      </if>
      <if test="instagram != null">
        instagram,
      </if>
      <if test="google != null">
        google,
      </if>
      <if test="yelp != null">
        yelp,
      </if>
      <if test="smartScheduleStartLat != null">
        smart_schedule_start_lat,
      </if>
      <if test="smartScheduleStartLng != null">
        smart_schedule_start_lng,
      </if>
      <if test="smartScheduleEndLat != null">
        smart_schedule_end_lat,
      </if>
      <if test="smartScheduleEndLng != null">
        smart_schedule_end_lng,
      </if>
      <if test="smartScheduleServiceRange != null">
        smart_schedule_service_range,
      </if>
      <if test="smartScheduleStartAddr != null">
        smart_schedule_start_addr,
      </if>
      <if test="smartScheduleEndAddr != null">
        smart_schedule_end_addr,
      </if>
      <if test="sourceFrom != null">
        source_from,
      </if>
      <if test="messageSendBy != null">
        message_send_by,
      </if>
      <if test="sendDaily != null">
        send_daily,
      </if>
      <if test="businessMode != null">
        business_mode,
      </if>
      <if test="knowAboutUs != null">
        know_about_us,
      </if>
      <if test="apptPerWeek != null">
        appt_per_week,
      </if>
      <if test="businessYears != null">
        business_years,
      </if>
      <if test="moveFrom != null">
        move_from,
      </if>
      <if test="retailEnable != null">
        retail_enable,
      </if>
      <if test="notificationSoundEnable != null">
        notification_sound_enable,
      </if>
      <if test="contactEmail != null">
        contact_email,
      </if>
      <if test="invitationCode != null">
        invitation_code,
      </if>
      <if test="tiktok != null">
        tiktok,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="avatarPath != null">
        #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="website != null">
        #{website,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="address1 != null">
        #{address1,jdbcType=VARCHAR},
      </if>
      <if test="address2 != null">
        #{address2,jdbcType=VARCHAR},
      </if>
      <if test="addressCity != null">
        #{addressCity,jdbcType=VARCHAR},
      </if>
      <if test="addressState != null">
        #{addressState,jdbcType=VARCHAR},
      </if>
      <if test="addressZipcode != null">
        #{addressZipcode,jdbcType=VARCHAR},
      </if>
      <if test="addressCountry != null">
        #{addressCountry,jdbcType=VARCHAR},
      </if>
      <if test="addressLat != null">
        #{addressLat,jdbcType=VARCHAR},
      </if>
      <if test="addressLng != null">
        #{addressLng,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="countryAlpha2Code != null">
        #{countryAlpha2Code,jdbcType=CHAR},
      </if>
      <if test="countryCode != null">
        #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="currencySymbol != null">
        #{currencySymbol,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="timeFormatType != null">
        #{timeFormatType,jdbcType=TINYINT},
      </if>
      <if test="unitOfWeightType != null">
        #{unitOfWeightType,jdbcType=TINYINT},
      </if>
      <if test="unitOfDistanceType != null">
        #{unitOfDistanceType,jdbcType=TINYINT},
      </if>
      <if test="timezoneName != null">
        #{timezoneName,jdbcType=VARCHAR},
      </if>
      <if test="timezoneSeconds != null">
        #{timezoneSeconds,jdbcType=INTEGER},
      </if>
      <if test="dateFormatType != null">
        #{dateFormatType,jdbcType=TINYINT},
      </if>
      <if test="calendarFormatType != null">
        #{calendarFormatType,jdbcType=TINYINT},
      </if>
      <if test="numberFormatType != null">
        #{numberFormatType,jdbcType=TINYINT},
      </if>
      <if test="bookOnlineName != null">
        #{bookOnlineName,jdbcType=VARCHAR},
      </if>
      <if test="appType != null">
        #{appType,jdbcType=TINYINT},
      </if>
      <if test="primaryPayType != null">
        #{primaryPayType,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="clockInOutEnable != null">
        #{clockInOutEnable,jdbcType=TINYINT},
      </if>
      <if test="clockInOutNotify != null">
        #{clockInOutNotify,jdbcType=TINYINT},
      </if>
      <if test="isEnableAccessCode != null">
        #{isEnableAccessCode,jdbcType=TINYINT},
      </if>
      <if test="smartScheduleMaxDist != null">
        #{smartScheduleMaxDist,jdbcType=INTEGER},
      </if>
      <if test="smartScheduleMaxTime != null">
        #{smartScheduleMaxTime,jdbcType=INTEGER},
      </if>
      <if test="serviceAreaEnable != null">
        #{serviceAreaEnable,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="facebook != null">
        #{facebook,jdbcType=VARCHAR},
      </if>
      <if test="instagram != null">
        #{instagram,jdbcType=VARCHAR},
      </if>
      <if test="google != null">
        #{google,jdbcType=VARCHAR},
      </if>
      <if test="yelp != null">
        #{yelp,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleStartLat != null">
        #{smartScheduleStartLat,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleStartLng != null">
        #{smartScheduleStartLng,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleEndLat != null">
        #{smartScheduleEndLat,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleEndLng != null">
        #{smartScheduleEndLng,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleServiceRange != null">
        #{smartScheduleServiceRange,jdbcType=INTEGER},
      </if>
      <if test="smartScheduleStartAddr != null">
        #{smartScheduleStartAddr,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleEndAddr != null">
        #{smartScheduleEndAddr,jdbcType=VARCHAR},
      </if>
      <if test="sourceFrom != null">
        #{sourceFrom,jdbcType=TINYINT},
      </if>
      <if test="messageSendBy != null">
        #{messageSendBy,jdbcType=TINYINT},
      </if>
      <if test="sendDaily != null">
        #{sendDaily,jdbcType=TINYINT},
      </if>
      <if test="businessMode != null">
        #{businessMode,jdbcType=TINYINT},
      </if>
      <if test="knowAboutUs != null">
        #{knowAboutUs,jdbcType=VARCHAR},
      </if>
      <if test="apptPerWeek != null">
        #{apptPerWeek,jdbcType=TINYINT},
      </if>
      <if test="businessYears != null">
        #{businessYears,jdbcType=TINYINT},
      </if>
      <if test="moveFrom != null">
        #{moveFrom,jdbcType=TINYINT},
      </if>
      <if test="retailEnable != null">
        #{retailEnable,jdbcType=TINYINT},
      </if>
      <if test="notificationSoundEnable != null">
        #{notificationSoundEnable,jdbcType=TINYINT},
      </if>
      <if test="contactEmail != null">
        #{contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="invitationCode != null">
        #{invitationCode,jdbcType=VARCHAR},
      </if>
      <if test="tiktok != null">
        #{tiktok,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.business.mapperbean.MoeBusinessExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=INTEGER},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneNumber != null">
        phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.avatarPath != null">
        avatar_path = #{record.avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="record.website != null">
        website = #{record.website,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.address1 != null">
        address1 = #{record.address1,jdbcType=VARCHAR},
      </if>
      <if test="record.address2 != null">
        address2 = #{record.address2,jdbcType=VARCHAR},
      </if>
      <if test="record.addressCity != null">
        address_city = #{record.addressCity,jdbcType=VARCHAR},
      </if>
      <if test="record.addressState != null">
        address_state = #{record.addressState,jdbcType=VARCHAR},
      </if>
      <if test="record.addressZipcode != null">
        address_zipcode = #{record.addressZipcode,jdbcType=VARCHAR},
      </if>
      <if test="record.addressCountry != null">
        address_country = #{record.addressCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.addressLat != null">
        address_lat = #{record.addressLat,jdbcType=VARCHAR},
      </if>
      <if test="record.addressLng != null">
        address_lng = #{record.addressLng,jdbcType=VARCHAR},
      </if>
      <if test="record.country != null">
        country = #{record.country,jdbcType=VARCHAR},
      </if>
      <if test="record.countryAlpha2Code != null">
        country_alpha2_code = #{record.countryAlpha2Code,jdbcType=CHAR},
      </if>
      <if test="record.countryCode != null">
        country_code = #{record.countryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.currencySymbol != null">
        currency_symbol = #{record.currencySymbol,jdbcType=VARCHAR},
      </if>
      <if test="record.currencyCode != null">
        currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.timeFormatType != null">
        time_format_type = #{record.timeFormatType,jdbcType=TINYINT},
      </if>
      <if test="record.unitOfWeightType != null">
        unit_of_weight_type = #{record.unitOfWeightType,jdbcType=TINYINT},
      </if>
      <if test="record.unitOfDistanceType != null">
        unit_of_distance_type = #{record.unitOfDistanceType,jdbcType=TINYINT},
      </if>
      <if test="record.timezoneName != null">
        timezone_name = #{record.timezoneName,jdbcType=VARCHAR},
      </if>
      <if test="record.timezoneSeconds != null">
        timezone_seconds = #{record.timezoneSeconds,jdbcType=INTEGER},
      </if>
      <if test="record.dateFormatType != null">
        date_format_type = #{record.dateFormatType,jdbcType=TINYINT},
      </if>
      <if test="record.calendarFormatType != null">
        calendar_format_type = #{record.calendarFormatType,jdbcType=TINYINT},
      </if>
      <if test="record.numberFormatType != null">
        number_format_type = #{record.numberFormatType,jdbcType=TINYINT},
      </if>
      <if test="record.bookOnlineName != null">
        book_online_name = #{record.bookOnlineName,jdbcType=VARCHAR},
      </if>
      <if test="record.appType != null">
        app_type = #{record.appType,jdbcType=TINYINT},
      </if>
      <if test="record.primaryPayType != null">
        primary_pay_type = #{record.primaryPayType,jdbcType=TINYINT},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.clockInOutEnable != null">
        clock_in_out_enable = #{record.clockInOutEnable,jdbcType=TINYINT},
      </if>
      <if test="record.clockInOutNotify != null">
        clock_in_out_notify = #{record.clockInOutNotify,jdbcType=TINYINT},
      </if>
      <if test="record.isEnableAccessCode != null">
        is_enable_access_code = #{record.isEnableAccessCode,jdbcType=TINYINT},
      </if>
      <if test="record.smartScheduleMaxDist != null">
        smart_schedule_max_dist = #{record.smartScheduleMaxDist,jdbcType=INTEGER},
      </if>
      <if test="record.smartScheduleMaxTime != null">
        smart_schedule_max_time = #{record.smartScheduleMaxTime,jdbcType=INTEGER},
      </if>
      <if test="record.serviceAreaEnable != null">
        service_area_enable = #{record.serviceAreaEnable,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.facebook != null">
        facebook = #{record.facebook,jdbcType=VARCHAR},
      </if>
      <if test="record.instagram != null">
        instagram = #{record.instagram,jdbcType=VARCHAR},
      </if>
      <if test="record.google != null">
        google = #{record.google,jdbcType=VARCHAR},
      </if>
      <if test="record.yelp != null">
        yelp = #{record.yelp,jdbcType=VARCHAR},
      </if>
      <if test="record.smartScheduleStartLat != null">
        smart_schedule_start_lat = #{record.smartScheduleStartLat,jdbcType=VARCHAR},
      </if>
      <if test="record.smartScheduleStartLng != null">
        smart_schedule_start_lng = #{record.smartScheduleStartLng,jdbcType=VARCHAR},
      </if>
      <if test="record.smartScheduleEndLat != null">
        smart_schedule_end_lat = #{record.smartScheduleEndLat,jdbcType=VARCHAR},
      </if>
      <if test="record.smartScheduleEndLng != null">
        smart_schedule_end_lng = #{record.smartScheduleEndLng,jdbcType=VARCHAR},
      </if>
      <if test="record.smartScheduleServiceRange != null">
        smart_schedule_service_range = #{record.smartScheduleServiceRange,jdbcType=INTEGER},
      </if>
      <if test="record.smartScheduleStartAddr != null">
        smart_schedule_start_addr = #{record.smartScheduleStartAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.smartScheduleEndAddr != null">
        smart_schedule_end_addr = #{record.smartScheduleEndAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceFrom != null">
        source_from = #{record.sourceFrom,jdbcType=TINYINT},
      </if>
      <if test="record.messageSendBy != null">
        message_send_by = #{record.messageSendBy,jdbcType=TINYINT},
      </if>
      <if test="record.sendDaily != null">
        send_daily = #{record.sendDaily,jdbcType=TINYINT},
      </if>
      <if test="record.businessMode != null">
        business_mode = #{record.businessMode,jdbcType=TINYINT},
      </if>
      <if test="record.knowAboutUs != null">
        know_about_us = #{record.knowAboutUs,jdbcType=VARCHAR},
      </if>
      <if test="record.apptPerWeek != null">
        appt_per_week = #{record.apptPerWeek,jdbcType=TINYINT},
      </if>
      <if test="record.businessYears != null">
        business_years = #{record.businessYears,jdbcType=TINYINT},
      </if>
      <if test="record.moveFrom != null">
        move_from = #{record.moveFrom,jdbcType=TINYINT},
      </if>
      <if test="record.retailEnable != null">
        retail_enable = #{record.retailEnable,jdbcType=TINYINT},
      </if>
      <if test="record.notificationSoundEnable != null">
        notification_sound_enable = #{record.notificationSoundEnable,jdbcType=TINYINT},
      </if>
      <if test="record.contactEmail != null">
        contact_email = #{record.contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.invitationCode != null">
        invitation_code = #{record.invitationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.tiktok != null">
        tiktok = #{record.tiktok,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business
    set id = #{record.id,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=INTEGER},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      avatar_path = #{record.avatarPath,jdbcType=VARCHAR},
      website = #{record.website,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      address1 = #{record.address1,jdbcType=VARCHAR},
      address2 = #{record.address2,jdbcType=VARCHAR},
      address_city = #{record.addressCity,jdbcType=VARCHAR},
      address_state = #{record.addressState,jdbcType=VARCHAR},
      address_zipcode = #{record.addressZipcode,jdbcType=VARCHAR},
      address_country = #{record.addressCountry,jdbcType=VARCHAR},
      address_lat = #{record.addressLat,jdbcType=VARCHAR},
      address_lng = #{record.addressLng,jdbcType=VARCHAR},
      country = #{record.country,jdbcType=VARCHAR},
      country_alpha2_code = #{record.countryAlpha2Code,jdbcType=CHAR},
      country_code = #{record.countryCode,jdbcType=VARCHAR},
      currency_symbol = #{record.currencySymbol,jdbcType=VARCHAR},
      currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      time_format_type = #{record.timeFormatType,jdbcType=TINYINT},
      unit_of_weight_type = #{record.unitOfWeightType,jdbcType=TINYINT},
      unit_of_distance_type = #{record.unitOfDistanceType,jdbcType=TINYINT},
      timezone_name = #{record.timezoneName,jdbcType=VARCHAR},
      timezone_seconds = #{record.timezoneSeconds,jdbcType=INTEGER},
      date_format_type = #{record.dateFormatType,jdbcType=TINYINT},
      calendar_format_type = #{record.calendarFormatType,jdbcType=TINYINT},
      number_format_type = #{record.numberFormatType,jdbcType=TINYINT},
      book_online_name = #{record.bookOnlineName,jdbcType=VARCHAR},
      app_type = #{record.appType,jdbcType=TINYINT},
      primary_pay_type = #{record.primaryPayType,jdbcType=TINYINT},
      source = #{record.source,jdbcType=TINYINT},
      clock_in_out_enable = #{record.clockInOutEnable,jdbcType=TINYINT},
      clock_in_out_notify = #{record.clockInOutNotify,jdbcType=TINYINT},
      is_enable_access_code = #{record.isEnableAccessCode,jdbcType=TINYINT},
      smart_schedule_max_dist = #{record.smartScheduleMaxDist,jdbcType=INTEGER},
      smart_schedule_max_time = #{record.smartScheduleMaxTime,jdbcType=INTEGER},
      service_area_enable = #{record.serviceAreaEnable,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      facebook = #{record.facebook,jdbcType=VARCHAR},
      instagram = #{record.instagram,jdbcType=VARCHAR},
      google = #{record.google,jdbcType=VARCHAR},
      yelp = #{record.yelp,jdbcType=VARCHAR},
      smart_schedule_start_lat = #{record.smartScheduleStartLat,jdbcType=VARCHAR},
      smart_schedule_start_lng = #{record.smartScheduleStartLng,jdbcType=VARCHAR},
      smart_schedule_end_lat = #{record.smartScheduleEndLat,jdbcType=VARCHAR},
      smart_schedule_end_lng = #{record.smartScheduleEndLng,jdbcType=VARCHAR},
      smart_schedule_service_range = #{record.smartScheduleServiceRange,jdbcType=INTEGER},
      smart_schedule_start_addr = #{record.smartScheduleStartAddr,jdbcType=VARCHAR},
      smart_schedule_end_addr = #{record.smartScheduleEndAddr,jdbcType=VARCHAR},
      source_from = #{record.sourceFrom,jdbcType=TINYINT},
      message_send_by = #{record.messageSendBy,jdbcType=TINYINT},
      send_daily = #{record.sendDaily,jdbcType=TINYINT},
      business_mode = #{record.businessMode,jdbcType=TINYINT},
      know_about_us = #{record.knowAboutUs,jdbcType=VARCHAR},
      appt_per_week = #{record.apptPerWeek,jdbcType=TINYINT},
      business_years = #{record.businessYears,jdbcType=TINYINT},
      move_from = #{record.moveFrom,jdbcType=TINYINT},
      retail_enable = #{record.retailEnable,jdbcType=TINYINT},
      notification_sound_enable = #{record.notificationSoundEnable,jdbcType=TINYINT},
      contact_email = #{record.contactEmail,jdbcType=VARCHAR},
      invitation_code = #{record.invitationCode,jdbcType=VARCHAR},
      tiktok = #{record.tiktok,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.business.mapperbean.MoeBusiness">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="avatarPath != null">
        avatar_path = #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="website != null">
        website = #{website,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="address1 != null">
        address1 = #{address1,jdbcType=VARCHAR},
      </if>
      <if test="address2 != null">
        address2 = #{address2,jdbcType=VARCHAR},
      </if>
      <if test="addressCity != null">
        address_city = #{addressCity,jdbcType=VARCHAR},
      </if>
      <if test="addressState != null">
        address_state = #{addressState,jdbcType=VARCHAR},
      </if>
      <if test="addressZipcode != null">
        address_zipcode = #{addressZipcode,jdbcType=VARCHAR},
      </if>
      <if test="addressCountry != null">
        address_country = #{addressCountry,jdbcType=VARCHAR},
      </if>
      <if test="addressLat != null">
        address_lat = #{addressLat,jdbcType=VARCHAR},
      </if>
      <if test="addressLng != null">
        address_lng = #{addressLng,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="countryAlpha2Code != null">
        country_alpha2_code = #{countryAlpha2Code,jdbcType=CHAR},
      </if>
      <if test="countryCode != null">
        country_code = #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="currencySymbol != null">
        currency_symbol = #{currencySymbol,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null">
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="timeFormatType != null">
        time_format_type = #{timeFormatType,jdbcType=TINYINT},
      </if>
      <if test="unitOfWeightType != null">
        unit_of_weight_type = #{unitOfWeightType,jdbcType=TINYINT},
      </if>
      <if test="unitOfDistanceType != null">
        unit_of_distance_type = #{unitOfDistanceType,jdbcType=TINYINT},
      </if>
      <if test="timezoneName != null">
        timezone_name = #{timezoneName,jdbcType=VARCHAR},
      </if>
      <if test="timezoneSeconds != null">
        timezone_seconds = #{timezoneSeconds,jdbcType=INTEGER},
      </if>
      <if test="dateFormatType != null">
        date_format_type = #{dateFormatType,jdbcType=TINYINT},
      </if>
      <if test="calendarFormatType != null">
        calendar_format_type = #{calendarFormatType,jdbcType=TINYINT},
      </if>
      <if test="numberFormatType != null">
        number_format_type = #{numberFormatType,jdbcType=TINYINT},
      </if>
      <if test="bookOnlineName != null">
        book_online_name = #{bookOnlineName,jdbcType=VARCHAR},
      </if>
      <if test="appType != null">
        app_type = #{appType,jdbcType=TINYINT},
      </if>
      <if test="primaryPayType != null">
        primary_pay_type = #{primaryPayType,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
      <if test="clockInOutEnable != null">
        clock_in_out_enable = #{clockInOutEnable,jdbcType=TINYINT},
      </if>
      <if test="clockInOutNotify != null">
        clock_in_out_notify = #{clockInOutNotify,jdbcType=TINYINT},
      </if>
      <if test="isEnableAccessCode != null">
        is_enable_access_code = #{isEnableAccessCode,jdbcType=TINYINT},
      </if>
      <if test="smartScheduleMaxDist != null">
        smart_schedule_max_dist = #{smartScheduleMaxDist,jdbcType=INTEGER},
      </if>
      <if test="smartScheduleMaxTime != null">
        smart_schedule_max_time = #{smartScheduleMaxTime,jdbcType=INTEGER},
      </if>
      <if test="serviceAreaEnable != null">
        service_area_enable = #{serviceAreaEnable,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="facebook != null">
        facebook = #{facebook,jdbcType=VARCHAR},
      </if>
      <if test="instagram != null">
        instagram = #{instagram,jdbcType=VARCHAR},
      </if>
      <if test="google != null">
        google = #{google,jdbcType=VARCHAR},
      </if>
      <if test="yelp != null">
        yelp = #{yelp,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleStartLat != null">
        smart_schedule_start_lat = #{smartScheduleStartLat,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleStartLng != null">
        smart_schedule_start_lng = #{smartScheduleStartLng,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleEndLat != null">
        smart_schedule_end_lat = #{smartScheduleEndLat,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleEndLng != null">
        smart_schedule_end_lng = #{smartScheduleEndLng,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleServiceRange != null">
        smart_schedule_service_range = #{smartScheduleServiceRange,jdbcType=INTEGER},
      </if>
      <if test="smartScheduleStartAddr != null">
        smart_schedule_start_addr = #{smartScheduleStartAddr,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleEndAddr != null">
        smart_schedule_end_addr = #{smartScheduleEndAddr,jdbcType=VARCHAR},
      </if>
      <if test="sourceFrom != null">
        source_from = #{sourceFrom,jdbcType=TINYINT},
      </if>
      <if test="messageSendBy != null">
        message_send_by = #{messageSendBy,jdbcType=TINYINT},
      </if>
      <if test="sendDaily != null">
        send_daily = #{sendDaily,jdbcType=TINYINT},
      </if>
      <if test="businessMode != null">
        business_mode = #{businessMode,jdbcType=TINYINT},
      </if>
      <if test="knowAboutUs != null">
        know_about_us = #{knowAboutUs,jdbcType=VARCHAR},
      </if>
      <if test="apptPerWeek != null">
        appt_per_week = #{apptPerWeek,jdbcType=TINYINT},
      </if>
      <if test="businessYears != null">
        business_years = #{businessYears,jdbcType=TINYINT},
      </if>
      <if test="moveFrom != null">
        move_from = #{moveFrom,jdbcType=TINYINT},
      </if>
      <if test="retailEnable != null">
        retail_enable = #{retailEnable,jdbcType=TINYINT},
      </if>
      <if test="notificationSoundEnable != null">
        notification_sound_enable = #{notificationSoundEnable,jdbcType=TINYINT},
      </if>
      <if test="contactEmail != null">
        contact_email = #{contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="invitationCode != null">
        invitation_code = #{invitationCode,jdbcType=VARCHAR},
      </if>
      <if test="tiktok != null">
        tiktok = #{tiktok,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.business.mapperbean.MoeBusiness">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business
    set company_id = #{companyId,jdbcType=INTEGER},
      business_name = #{businessName,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      avatar_path = #{avatarPath,jdbcType=VARCHAR},
      website = #{website,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      address1 = #{address1,jdbcType=VARCHAR},
      address2 = #{address2,jdbcType=VARCHAR},
      address_city = #{addressCity,jdbcType=VARCHAR},
      address_state = #{addressState,jdbcType=VARCHAR},
      address_zipcode = #{addressZipcode,jdbcType=VARCHAR},
      address_country = #{addressCountry,jdbcType=VARCHAR},
      address_lat = #{addressLat,jdbcType=VARCHAR},
      address_lng = #{addressLng,jdbcType=VARCHAR},
      country = #{country,jdbcType=VARCHAR},
      country_alpha2_code = #{countryAlpha2Code,jdbcType=CHAR},
      country_code = #{countryCode,jdbcType=VARCHAR},
      currency_symbol = #{currencySymbol,jdbcType=VARCHAR},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      time_format_type = #{timeFormatType,jdbcType=TINYINT},
      unit_of_weight_type = #{unitOfWeightType,jdbcType=TINYINT},
      unit_of_distance_type = #{unitOfDistanceType,jdbcType=TINYINT},
      timezone_name = #{timezoneName,jdbcType=VARCHAR},
      timezone_seconds = #{timezoneSeconds,jdbcType=INTEGER},
      date_format_type = #{dateFormatType,jdbcType=TINYINT},
      calendar_format_type = #{calendarFormatType,jdbcType=TINYINT},
      number_format_type = #{numberFormatType,jdbcType=TINYINT},
      book_online_name = #{bookOnlineName,jdbcType=VARCHAR},
      app_type = #{appType,jdbcType=TINYINT},
      primary_pay_type = #{primaryPayType,jdbcType=TINYINT},
      source = #{source,jdbcType=TINYINT},
      clock_in_out_enable = #{clockInOutEnable,jdbcType=TINYINT},
      clock_in_out_notify = #{clockInOutNotify,jdbcType=TINYINT},
      is_enable_access_code = #{isEnableAccessCode,jdbcType=TINYINT},
      smart_schedule_max_dist = #{smartScheduleMaxDist,jdbcType=INTEGER},
      smart_schedule_max_time = #{smartScheduleMaxTime,jdbcType=INTEGER},
      service_area_enable = #{serviceAreaEnable,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      facebook = #{facebook,jdbcType=VARCHAR},
      instagram = #{instagram,jdbcType=VARCHAR},
      google = #{google,jdbcType=VARCHAR},
      yelp = #{yelp,jdbcType=VARCHAR},
      smart_schedule_start_lat = #{smartScheduleStartLat,jdbcType=VARCHAR},
      smart_schedule_start_lng = #{smartScheduleStartLng,jdbcType=VARCHAR},
      smart_schedule_end_lat = #{smartScheduleEndLat,jdbcType=VARCHAR},
      smart_schedule_end_lng = #{smartScheduleEndLng,jdbcType=VARCHAR},
      smart_schedule_service_range = #{smartScheduleServiceRange,jdbcType=INTEGER},
      smart_schedule_start_addr = #{smartScheduleStartAddr,jdbcType=VARCHAR},
      smart_schedule_end_addr = #{smartScheduleEndAddr,jdbcType=VARCHAR},
      source_from = #{sourceFrom,jdbcType=TINYINT},
      message_send_by = #{messageSendBy,jdbcType=TINYINT},
      send_daily = #{sendDaily,jdbcType=TINYINT},
      business_mode = #{businessMode,jdbcType=TINYINT},
      know_about_us = #{knowAboutUs,jdbcType=VARCHAR},
      appt_per_week = #{apptPerWeek,jdbcType=TINYINT},
      business_years = #{businessYears,jdbcType=TINYINT},
      move_from = #{moveFrom,jdbcType=TINYINT},
      retail_enable = #{retailEnable,jdbcType=TINYINT},
      notification_sound_enable = #{notificationSoundEnable,jdbcType=TINYINT},
      contact_email = #{contactEmail,jdbcType=VARCHAR},
      invitation_code = #{invitationCode,jdbcType=VARCHAR},
      tiktok = #{tiktok,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getBusinessByIdList" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM moe_business
    <where>
      <if test="idList != null">
        AND `id` IN
        <foreach close=")" collection="idList" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
  </select>
  <select id="getBusinessByCompanyId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM moe_business
    where company_id = #{companyId,jdbcType=INTEGER}
  </select>
  <select id="selectAllBusiness" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM moe_business
  </select>

  <select id="selectAllBusinessIds" resultType="integer">
    SELECT id
    FROM moe_business
  </select>

  <select id="selectAllBusinessByCompanyId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM moe_business
    where company_id IN
    <foreach close=")" collection="companyIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="getUSBusinessId" resultType="integer">
    select id
    from moe_business
    where country = 'United States';
  </select>

  <select id="describeBusinesses" parameterType="com.moego.server.business.params.DescribeBusinessesParams" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_business
    <where>
      <if test="ids != null">
        and id in
        <foreach close=")" collection="ids" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="companyIds != null">
        and company_id in
        <foreach close=")" collection="companyIds" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="companyNames != null">
        and company_id in (select id from moe_company where name in
        <foreach close=")" collection="companyNames" item="item" open="(" separator=",">
          #{item}
        </foreach>
        )
      </if>
      <if test="ownerIds != null">
        and id in (select business_id from moe_staff where account_id in
        <foreach close=")" collection="ownerIds" item="item" open="(" separator=",">
          #{item}
        </foreach>
        and status = 1 and employee_category = 1)
      </if>
      <if test="countries != null">
        and country in
        <foreach close=")" collection="countries" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="nameLike != null">
        and business_name like concat(#{nameLike}, '%')
      </if>
    </where>
  </select>
  <select id="getCompanyIdByBusinessId" resultType="java.lang.Integer">
    select ABS(company_id) from moe_business where id = #{businessId,jdbcType=INTEGER}
  </select>
    <select id="batchQueryBusinessInfoByParam" resultType="com.moego.server.business.dto.TaskBusinessInfoDto">
        select id,
               company_id as companyId,
               time_format_type as timeFormatType,
               date_format_type as dateFormatType,
               timezone_name as timezoneName
        from moe_business
        <where>
          <if test="isPremium != null and isPremium">
            and company_id in ( select id from moe_company where level &gt;0 )
          </if>
        <if test="isPushCalendar != null and isPushCalendar">
          and id in ( select distinct s.business_id from moe_staff s inner join moe_staff_notification sn on  s.id = sn.staff_id where s.status = 1 and sn.push_calendar_switch = 1)
        </if>
        </where>
        order by id asc
    </select>
</mapper>