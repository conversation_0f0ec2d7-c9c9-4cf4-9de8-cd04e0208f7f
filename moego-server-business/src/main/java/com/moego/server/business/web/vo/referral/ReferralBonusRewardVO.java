package com.moego.server.business.web.vo.referral;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
public class ReferralBonusRewardVO {

    @Schema(description = "bonus奖励主键，确认领取时传递")
    private Integer id;

    @Schema(description = "bonus奖励关联的推荐人")
    private Integer referralId;

    @Schema(description = "bonus奖励对应的规则主键")
    private Integer bonusRuleId;

    @Schema(description = "bonus状态（0待领取，1已领取）")
    private Byte status;

    @Schema(description = "bonus发放时间")
    private Long createTime;

    @Schema(description = "bonus领取时间")
    private Long updateTime;

    @Schema(description = "bonus对应的目标数量")
    private Integer targetNumber;

    @Schema(description = "bonus对应的实体奖励金额")
    private BigDecimal bonusAmount;
}
