package com.moego.server.business.web.vo;

import com.moego.server.business.dto.MoeStaffDto;
import java.math.BigDecimal;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class StaffCalendarVO extends MoeStaffDto {
    private Map<String, BigDecimal> dateToEstimatedRevenue;

    public StaffCalendarVO() {
        super();
    }

    public StaffCalendarVO(MoeStaffDto dto) {
        setId(dto.getId());
        setCompanyId(dto.getCompanyId().intValue());
        setBusinessId(dto.getBusinessId());
        setAccountId(dto.getAccountId());
        setRoleId(dto.getRoleId());
        setAvatarPath(dto.getAvatarPath());
        setFirstName(dto.getFirstName());
        setLastName(dto.getLastName());
        setEmployeeCategory(dto.getEmployeeCategory());
        setPhoneNumber(dto.getPhoneNumber());
        setHireDate(dto.getHireDate());
        setFireDate(dto.getFireDate());
        setAllowLogin(dto.getAllowLogin());
        setGroupLeaderId(dto.getGroupLeaderId());
        setNote(dto.getNote());
        setInactive(dto.getInactive());
        setStatus(dto.getStatus());
        setCreateById(dto.getCreateById());
        setSort(dto.getSort());
        setBookOnlineAvailable(dto.getBookOnlineAvailable());
        setCreateTime(dto.getCreateTime());
        setUpdateTime(dto.getUpdateTime());
        setShowOnCalendar(dto.getShowOnCalendar());
        setShowCalendarStaffAll(dto.getShowCalendarStaffAll());
        setAccessCode(dto.getAccessCode());
        setToken(dto.getToken());
        setInviteCode(dto.getInviteCode());
        setPayBy(dto.getPayBy());
        setServicePayRate(dto.getServicePayRate());
        setAddonPayRate(dto.getAddonPayRate());
        setHourlyPayRate(dto.getHourlyPayRate());
        setTipsPayRate(dto.getTipsPayRate());
        setIsWorkingStaff(dto.getIsWorkingStaff());
        setVanId(dto.getVanId());
        setVanName(dto.getVanName());
    }
}
