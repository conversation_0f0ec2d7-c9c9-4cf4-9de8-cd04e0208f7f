package com.moego.server.business.mapperbean;

public class MoeWorkingWeekly {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_working_weekly.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_working_weekly.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_working_weekly.business_account_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_working_weekly.monday
     *
     * @mbg.generated
     */
    private String monday;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_working_weekly.tuesday
     *
     * @mbg.generated
     */
    private String tuesday;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_working_weekly.wednesday
     *
     * @mbg.generated
     */
    private String wednesday;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_working_weekly.thursday
     *
     * @mbg.generated
     */
    private String thursday;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_working_weekly.friday
     *
     * @mbg.generated
     */
    private String friday;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_working_weekly.saturday
     *
     * @mbg.generated
     */
    private String saturday;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_working_weekly.sunday
     *
     * @mbg.generated
     */
    private String sunday;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_working_weekly.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_working_weekly.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_working_weekly.id
     *
     * @return the value of moe_working_weekly.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_working_weekly.id
     *
     * @param id the value for moe_working_weekly.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_working_weekly.business_id
     *
     * @return the value of moe_working_weekly.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_working_weekly.business_id
     *
     * @param businessId the value for moe_working_weekly.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_working_weekly.business_account_id
     *
     * @return the value of moe_working_weekly.business_account_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_working_weekly.business_account_id
     *
     * @param staffId the value for moe_working_weekly.business_account_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_working_weekly.monday
     *
     * @return the value of moe_working_weekly.monday
     *
     * @mbg.generated
     */
    public String getMonday() {
        return monday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_working_weekly.monday
     *
     * @param monday the value for moe_working_weekly.monday
     *
     * @mbg.generated
     */
    public void setMonday(String monday) {
        this.monday = monday == null ? null : monday.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_working_weekly.tuesday
     *
     * @return the value of moe_working_weekly.tuesday
     *
     * @mbg.generated
     */
    public String getTuesday() {
        return tuesday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_working_weekly.tuesday
     *
     * @param tuesday the value for moe_working_weekly.tuesday
     *
     * @mbg.generated
     */
    public void setTuesday(String tuesday) {
        this.tuesday = tuesday == null ? null : tuesday.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_working_weekly.wednesday
     *
     * @return the value of moe_working_weekly.wednesday
     *
     * @mbg.generated
     */
    public String getWednesday() {
        return wednesday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_working_weekly.wednesday
     *
     * @param wednesday the value for moe_working_weekly.wednesday
     *
     * @mbg.generated
     */
    public void setWednesday(String wednesday) {
        this.wednesday = wednesday == null ? null : wednesday.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_working_weekly.thursday
     *
     * @return the value of moe_working_weekly.thursday
     *
     * @mbg.generated
     */
    public String getThursday() {
        return thursday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_working_weekly.thursday
     *
     * @param thursday the value for moe_working_weekly.thursday
     *
     * @mbg.generated
     */
    public void setThursday(String thursday) {
        this.thursday = thursday == null ? null : thursday.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_working_weekly.friday
     *
     * @return the value of moe_working_weekly.friday
     *
     * @mbg.generated
     */
    public String getFriday() {
        return friday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_working_weekly.friday
     *
     * @param friday the value for moe_working_weekly.friday
     *
     * @mbg.generated
     */
    public void setFriday(String friday) {
        this.friday = friday == null ? null : friday.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_working_weekly.saturday
     *
     * @return the value of moe_working_weekly.saturday
     *
     * @mbg.generated
     */
    public String getSaturday() {
        return saturday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_working_weekly.saturday
     *
     * @param saturday the value for moe_working_weekly.saturday
     *
     * @mbg.generated
     */
    public void setSaturday(String saturday) {
        this.saturday = saturday == null ? null : saturday.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_working_weekly.sunday
     *
     * @return the value of moe_working_weekly.sunday
     *
     * @mbg.generated
     */
    public String getSunday() {
        return sunday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_working_weekly.sunday
     *
     * @param sunday the value for moe_working_weekly.sunday
     *
     * @mbg.generated
     */
    public void setSunday(String sunday) {
        this.sunday = sunday == null ? null : sunday.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_working_weekly.create_time
     *
     * @return the value of moe_working_weekly.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_working_weekly.create_time
     *
     * @param createTime the value for moe_working_weekly.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_working_weekly.update_time
     *
     * @return the value of moe_working_weekly.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_working_weekly.update_time
     *
     * @param updateTime the value for moe_working_weekly.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
