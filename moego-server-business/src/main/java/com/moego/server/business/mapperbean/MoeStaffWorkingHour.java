package com.moego.server.business.mapperbean;

import java.util.Date;

public class MoeStaffWorkingHour {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.schedule_type
     *
     * @mbg.generated
     */
    private Byte scheduleType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.start_date
     *
     * @mbg.generated
     */
    private String startDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.end_date
     *
     * @mbg.generated
     */
    private String endDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.first_week
     *
     * @mbg.generated
     */
    private String firstWeek;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.second_week
     *
     * @mbg.generated
     */
    private String secondWeek;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.third_week
     *
     * @mbg.generated
     */
    private String thirdWeek;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.forth_week
     *
     * @mbg.generated
     */
    private String forthWeek;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.id
     *
     * @return the value of moe_staff_working_hour.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.id
     *
     * @param id the value for moe_staff_working_hour.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.business_id
     *
     * @return the value of moe_staff_working_hour.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.business_id
     *
     * @param businessId the value for moe_staff_working_hour.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.staff_id
     *
     * @return the value of moe_staff_working_hour.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.staff_id
     *
     * @param staffId the value for moe_staff_working_hour.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.schedule_type
     *
     * @return the value of moe_staff_working_hour.schedule_type
     *
     * @mbg.generated
     */
    public Byte getScheduleType() {
        return scheduleType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.schedule_type
     *
     * @param scheduleType the value for moe_staff_working_hour.schedule_type
     *
     * @mbg.generated
     */
    public void setScheduleType(Byte scheduleType) {
        this.scheduleType = scheduleType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.start_date
     *
     * @return the value of moe_staff_working_hour.start_date
     *
     * @mbg.generated
     */
    public String getStartDate() {
        return startDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.start_date
     *
     * @param startDate the value for moe_staff_working_hour.start_date
     *
     * @mbg.generated
     */
    public void setStartDate(String startDate) {
        this.startDate = startDate == null ? null : startDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.end_date
     *
     * @return the value of moe_staff_working_hour.end_date
     *
     * @mbg.generated
     */
    public String getEndDate() {
        return endDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.end_date
     *
     * @param endDate the value for moe_staff_working_hour.end_date
     *
     * @mbg.generated
     */
    public void setEndDate(String endDate) {
        this.endDate = endDate == null ? null : endDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.first_week
     *
     * @return the value of moe_staff_working_hour.first_week
     *
     * @mbg.generated
     */
    public String getFirstWeek() {
        return firstWeek;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.first_week
     *
     * @param firstWeek the value for moe_staff_working_hour.first_week
     *
     * @mbg.generated
     */
    public void setFirstWeek(String firstWeek) {
        this.firstWeek = firstWeek == null ? null : firstWeek.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.second_week
     *
     * @return the value of moe_staff_working_hour.second_week
     *
     * @mbg.generated
     */
    public String getSecondWeek() {
        return secondWeek;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.second_week
     *
     * @param secondWeek the value for moe_staff_working_hour.second_week
     *
     * @mbg.generated
     */
    public void setSecondWeek(String secondWeek) {
        this.secondWeek = secondWeek == null ? null : secondWeek.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.third_week
     *
     * @return the value of moe_staff_working_hour.third_week
     *
     * @mbg.generated
     */
    public String getThirdWeek() {
        return thirdWeek;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.third_week
     *
     * @param thirdWeek the value for moe_staff_working_hour.third_week
     *
     * @mbg.generated
     */
    public void setThirdWeek(String thirdWeek) {
        this.thirdWeek = thirdWeek == null ? null : thirdWeek.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.forth_week
     *
     * @return the value of moe_staff_working_hour.forth_week
     *
     * @mbg.generated
     */
    public String getForthWeek() {
        return forthWeek;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.forth_week
     *
     * @param forthWeek the value for moe_staff_working_hour.forth_week
     *
     * @mbg.generated
     */
    public void setForthWeek(String forthWeek) {
        this.forthWeek = forthWeek == null ? null : forthWeek.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.create_time
     *
     * @return the value of moe_staff_working_hour.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.create_time
     *
     * @param createTime the value for moe_staff_working_hour.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.update_time
     *
     * @return the value of moe_staff_working_hour.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.update_time
     *
     * @param updateTime the value for moe_staff_working_hour.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_working_hour.company_id
     *
     * @return the value of moe_staff_working_hour.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_working_hour.company_id
     *
     * @param companyId the value for moe_staff_working_hour.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
