package com.moego.server.business.web;

import com.moego.api.thirdparty.IHubspotClient;
import com.moego.common.response.ResponseResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.dto.MoeWorkingDailyDTO;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.WorkingDailySaveVo;
import com.moego.server.business.params.WorkingDailyQueryRangeVo;
import com.moego.server.business.service.WorkingDailyService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/business/workingDaily")
@Slf4j
public class WorkingDailyController {

    @Autowired
    private WorkingDailyService workingDailyService;

    @Autowired
    private IHubspotClient iHubspotClient;

    @PostMapping("/insertOrUpdate")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> insert(
            AuthContext context, @Valid @RequestBody WorkingDailySaveVo workingDailySaveVo) {
        return ResponseResult.success(
                workingDailyService.insertOrUpdate(context.companyId(), context.getBusinessId(), workingDailySaveVo));
    }

    /**
     * Deprecated
     *
     * @see com.moego.server.business.web.StaffWorkingHourController#getStaffWorkingHour
     */
    @Deprecated
    @PostMapping("/query")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<MoeWorkingDailyDTO>> query(
            AuthContext context, @RequestBody MoeWorkingDailyDTO moeWorkingDailyDTO) {
        moeWorkingDailyDTO.setBusinessId(context.getBusinessId());
        return ResponseResult.success(workingDailyService.query(moeWorkingDailyDTO));
    }

    @PostMapping("/queryRange")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<StaffWorkingRangeDto>> queryRange(
            AuthContext context, @Valid @RequestBody WorkingDailyQueryRangeVo rangeVo) {
        return ResponseResult.success(
                workingDailyService.simpleQueryStaffWorkTime(context.getBusinessId(), context.getStaffId(), rangeVo));
    }

    @PostMapping("/shiftManagement")
    @Auth(AuthType.BUSINESS)
    public List<StaffWorkingRangeDto> shiftManagement(
            AuthContext context, @Valid @RequestBody WorkingDailyQueryRangeVo rangeVo) {
        return workingDailyService.queryRangeShiftManagementWorkTime(
                context.getBusinessId(), rangeVo.getStartDate(), rangeVo.getEndDate());
    }
}
