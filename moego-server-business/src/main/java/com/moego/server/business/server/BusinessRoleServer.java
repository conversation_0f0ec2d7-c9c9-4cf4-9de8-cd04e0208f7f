/*
 * @since 2023-12-04 10:23:51
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.business.server;

import com.moego.server.business.api.IBusinessRoleServiceBase;
import com.moego.server.business.converter.BusinessRoleConverter;
import com.moego.server.business.dto.DescribeRolesDTO;
import com.moego.server.business.params.DescribeRolesParams;
import com.moego.server.business.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
public class BusinessRoleServer extends IBusinessRoleServiceBase {
    private final BusinessRoleConverter roleConverter;
    private final RoleService roleService;

    @Override
    public DescribeRolesDTO describeRoles(DescribeRolesParams params) {
        final var data = roleService.describeRoles(params);
        return new DescribeRolesDTO(
                data.getFirst().stream().map(roleConverter::dto).toList(), data.getSecond());
    }
}
