package com.moego.server.business.service;

import static com.moego.server.business.common.consts.DataSourceConst.READER;

import com.moego.common.constant.CommonConstant;
import com.moego.common.constant.Dictionary;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.converter.SmartScheduleSettingConverter;
import com.moego.server.business.dto.SmartScheduleDrivingRuleDTO;
import com.moego.server.business.dto.SmartScheduleLocationDTO;
import com.moego.server.business.dto.SmartScheduleSettingV2DTO;
import com.moego.server.business.dto.StaffSmartScheduleSettingDTO;
import com.moego.server.business.dto.VanDTO;
import com.moego.server.business.dto.VanSmartScheduleRelationDTO;
import com.moego.server.business.mapper.MoeSmartScheduleDrivingRuleMapper;
import com.moego.server.business.mapper.MoeSmartScheduleLocationMapper;
import com.moego.server.business.mapper.MoeVanSmartScheduleRelationMapper;
import com.moego.server.business.mapperbean.MoeBusiness;
import com.moego.server.business.mapperbean.MoeSmartScheduleDrivingRule;
import com.moego.server.business.mapperbean.MoeSmartScheduleLocation;
import com.moego.server.business.mapperbean.MoeVanSmartScheduleRelation;
import com.moego.server.business.params.SaveSmartScheduleDrivingRuleParams;
import com.moego.server.business.params.SaveSmartScheduleLocationParams;
import com.moego.server.business.params.SaveVanSmartScheduleRelationParams;
import com.moego.server.business.service.params.VanSmartScheduleBindingParams;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class SmartScheduleSettingService {

    private final BusinessService businessService;

    private final MoeSmartScheduleDrivingRuleMapper smartScheduleDrivingRuleMapper;

    private final MoeSmartScheduleLocationMapper smartScheduleLocationMapper;

    private final MoeVanSmartScheduleRelationMapper vanSmartScheduleRelationMapper;

    private final VanStaffService vanStaffService;

    // Converter 单例对象
    private final SmartScheduleSettingConverter converter = SmartScheduleSettingConverter.INSTANCE;

    /**
     * 查询 business driving rule 列表
     *
     * @param businessId token businessId
     * @return List<SmartScheduleDrivingRuleDTO>
     */
    public List<SmartScheduleDrivingRuleDTO> getDrivingRuleList(Integer businessId) {
        List<SmartScheduleDrivingRuleDTO> resultList =
                smartScheduleDrivingRuleMapper.selectByBusinessId(businessId).stream()
                        .map(converter::toDrivingRuleDTO)
                        .toList();
        if (CollectionUtils.isEmpty(resultList)) {
            return resultList;
        }
        // 查询已绑定的 van
        List<Integer> drivingRuleIdList = resultList.stream()
                .map(SmartScheduleDrivingRuleDTO::getId)
                .distinct()
                .toList();
        Map<Integer, List<Integer>> assignedVanIdListMap = getAssignedVanIdListMap(businessId, drivingRuleIdList, true);
        resultList.forEach(drivingRule ->
                drivingRule.setAssignedVanIdList(assignedVanIdListMap.getOrDefault(drivingRule.getId(), List.of())));
        return resultList;
    }

    private SmartScheduleDrivingRuleDTO getDrivingRuleById(Integer drivingRuleId) {
        MoeSmartScheduleDrivingRule drivingRule = smartScheduleDrivingRuleMapper.selectByPrimaryKey(drivingRuleId);
        if (drivingRule == null || drivingRule.getIsDeleted()) {
            return null;
        }
        Map<Integer, List<Integer>> assignVanIdListMap =
                getAssignedVanIdListMap(drivingRule.getBusinessId(), List.of(drivingRuleId), true);
        SmartScheduleDrivingRuleDTO result = converter.toDrivingRuleDTO(drivingRule);
        result.setAssignedVanIdList(assignVanIdListMap.getOrDefault(drivingRuleId, List.of()));
        return result;
    }

    private Map<Integer, SmartScheduleDrivingRuleDTO> getDrivingRuleMapByIdList(
            Integer businessId, List<Integer> drivingRuleIdList) {
        if (CollectionUtils.isEmpty(drivingRuleIdList)) {
            return Map.of();
        }
        List<MoeSmartScheduleDrivingRule> drivingRuleList = smartScheduleDrivingRuleMapper
                .useDataSource(READER)
                .selectByIdListAndBusinessId(drivingRuleIdList, businessId);

        // 查询 business 信息
        MoeBusiness businessInfo = businessService.getBusinessInfo(businessId);
        return drivingRuleList.stream()
                .map(drivingRule -> {
                    SmartScheduleDrivingRuleDTO result = converter.toDrivingRuleDTO(drivingRule);
                    result.setMaxDistInMile(
                            getDistanceInMile(drivingRule.getMaxDist(), businessInfo.getUnitOfDistanceType()));
                    return result;
                })
                .collect(Collectors.toMap(SmartScheduleDrivingRuleDTO::getId, Function.identity()));
    }

    private MoeSmartScheduleDrivingRule checkAndGetDrivingRule(Integer drivingRuleId, Integer businessId) {
        MoeSmartScheduleDrivingRule record =
                smartScheduleDrivingRuleMapper.selectByIdAndBusinessId(drivingRuleId, businessId);
        if (record == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "record not exist.");
        }
        return record;
    }

    public SmartScheduleDrivingRuleDTO saveDrivingRule(Long companyId, SaveSmartScheduleDrivingRuleParams params) {
        MoeSmartScheduleDrivingRule saveRecord = converter.toDrivingRuleBean(params);
        boolean isNew = false;
        if (params.getId() != null) {
            // check exist record
            MoeSmartScheduleDrivingRule existRecord = checkAndGetDrivingRule(params.getId(), params.getBusinessId());
            saveRecord.setUpdatedBy(params.getOperatorId());
            if (params.getMaxTime() == null) {
                params.setMaxTime(existRecord.getMaxTime());
            }
            if (params.getMaxDist() == null) {
                params.setMaxDist(existRecord.getMaxDist());
            }
        } else {
            // 新创建 max dist 和 max time 不能为空
            if (params.getMaxDist() == null || params.getMaxTime() == null) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "max distance and max time could not be empty.");
            }
            isNew = true;
            saveRecord.setCreatedBy(params.getOperatorId());
        }
        // duplicate check
        if (smartScheduleDrivingRuleMapper.countByParams(params) > 0) {
            throw ExceptionUtil.bizException(
                    Code.CODE_SMART_SCHEDULE_RULE_ALREADY_EXISTS, "Rule already exists, please change it.");
        }
        // 保存到数据库
        if (isNew) {
            saveRecord.setCompanyId(companyId);
            smartScheduleDrivingRuleMapper.insertSelective(saveRecord);
        } else {
            smartScheduleDrivingRuleMapper.updateByPrimaryKeySelective(saveRecord);
        }
        // 绑定 van
        if (params.getAssignedVanIdList() != null) {
            saveRelationBySmartScheduleSetting(
                    companyId,
                    new VanSmartScheduleBindingParams(
                            params.getBusinessId(),
                            params.getAssignedVanIdList(),
                            saveRecord.getId(),
                            null,
                            params.getOperatorId()));
        }

        return getDrivingRuleById(saveRecord.getId());
    }

    public Boolean deleteDrivingRule(Integer businessId, Integer operatorId, Integer drivingRuleId) {
        // check exist record
        checkAndGetDrivingRule(drivingRuleId, businessId);

        MoeSmartScheduleDrivingRule saveRecord = new MoeSmartScheduleDrivingRule();
        saveRecord.setId(drivingRuleId);
        saveRecord.setIsDeleted(true);
        saveRecord.setUpdatedBy(operatorId);
        return smartScheduleDrivingRuleMapper.updateByPrimaryKeySelective(saveRecord) > 0;
    }

    public List<SmartScheduleLocationDTO> getLocationList(Integer businessId) {
        List<SmartScheduleLocationDTO> resultList = smartScheduleLocationMapper.selectByBusinessId(businessId).stream()
                .map(converter::toLocationDTO)
                .toList();
        if (CollectionUtils.isEmpty(resultList)) {
            return resultList;
        }
        // 查询已绑定的 van
        List<Integer> locationIdList = resultList.stream()
                .map(SmartScheduleLocationDTO::getId)
                .distinct()
                .toList();
        Map<Integer, List<Integer>> assignedVanIdListMap = getAssignedVanIdListMap(businessId, locationIdList, false);
        resultList.forEach(location ->
                location.setAssignedVanIdList(assignedVanIdListMap.getOrDefault(location.getId(), List.of())));
        return resultList;
    }

    private SmartScheduleLocationDTO getLocationById(Integer locationId) {
        MoeSmartScheduleLocation location = smartScheduleLocationMapper.selectByPrimaryKey(locationId);
        if (location == null) {
            return null;
        }
        Map<Integer, List<Integer>> assignVanIdListMap =
                getAssignedVanIdListMap(location.getBusinessId(), List.of(locationId), false);
        SmartScheduleLocationDTO result = converter.toLocationDTO(location);
        result.setAssignedVanIdList(assignVanIdListMap.getOrDefault(locationId, List.of()));
        return result;
    }

    private Map<Integer, SmartScheduleLocationDTO> getLocationMapByIdList(
            Integer businessId, List<Integer> locationIdList) {
        if (CollectionUtils.isEmpty(locationIdList)) {
            return Map.of();
        }
        return smartScheduleLocationMapper
                .useDataSource(READER)
                .selectByIdListAndBusinessId(locationIdList, businessId)
                .stream()
                .map(converter::toLocationDTO)
                .collect(Collectors.toMap(SmartScheduleLocationDTO::getId, Function.identity()));
    }

    private void checkExistLocation(Integer locationId, Integer businessId) {
        MoeSmartScheduleLocation record = smartScheduleLocationMapper.selectByIdAndBusinessId(locationId, businessId);
        if (record == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "record not exist.");
        }
    }

    public SmartScheduleLocationDTO saveLocation(Long companyId, SaveSmartScheduleLocationParams params) {
        // duplicate check
        if (smartScheduleLocationMapper.countByParams(params) > 0) {
            throw ExceptionUtil.bizException(
                    Code.CODE_SMART_SCHEDULE_RULE_ALREADY_EXISTS, "Rule already exists, please change it.");
        }
        MoeSmartScheduleLocation saveRecord = converter.toLocationBean(params);
        boolean isNew = false;
        if (params.getId() != null) {
            // check exist record
            checkExistLocation(params.getId(), params.getBusinessId());
            saveRecord.setUpdatedBy(params.getOperatorId());
        } else {
            isNew = true;
            saveRecord.setCreatedBy(params.getOperatorId());
        }

        if (isNew) {
            saveRecord.setCompanyId(companyId);
            smartScheduleLocationMapper.insertSelective(saveRecord);
        } else {
            smartScheduleLocationMapper.updateByPrimaryKeySelective(saveRecord);
        }

        // 绑定 van
        if (params.getAssignedVanIdList() != null) {
            saveRelationBySmartScheduleSetting(
                    companyId,
                    new VanSmartScheduleBindingParams(
                            params.getBusinessId(),
                            params.getAssignedVanIdList(),
                            null,
                            saveRecord.getId(),
                            params.getOperatorId()));
        }

        return getLocationById(saveRecord.getId());
    }

    public Boolean deleteLocation(Integer businessId, Integer operatorId, Integer locationId) {
        // check exist record
        checkExistLocation(locationId, businessId);

        MoeSmartScheduleLocation saveRecord = new MoeSmartScheduleLocation();
        saveRecord.setId(locationId);
        saveRecord.setIsDeleted(true);
        saveRecord.setUpdatedBy(operatorId);
        return smartScheduleLocationMapper.updateByPrimaryKeySelective(saveRecord) > 0;
    }

    public List<MoeVanSmartScheduleRelation> getVanSmartScheduleRelationList(
            Integer businessId, List<Integer> vanIdList) {
        if (CollectionUtils.isEmpty(vanIdList)) {
            return List.of();
        }
        return vanSmartScheduleRelationMapper.useDataSource(READER).selectByVanIdList(businessId, vanIdList);
    }

    public Map<Integer, SmartScheduleSettingV2DTO> getSmartScheduleSettingMapByVanIdList(
            Integer businessId, List<Integer> vanIdList) {
        List<MoeVanSmartScheduleRelation> relationList = getVanSmartScheduleRelationList(businessId, vanIdList);
        if (CollectionUtils.isEmpty(relationList)) {
            return Map.of();
        }
        List<Integer> drivingRuleIdList = new ArrayList<>();
        List<Integer> locationIdList = new ArrayList<>();
        for (MoeVanSmartScheduleRelation relation : relationList) {
            if (relation.getDrivingRuleId() != null && relation.getDrivingRuleId() > 0) {
                drivingRuleIdList.add(relation.getDrivingRuleId());
            }
            if (relation.getLocationId() != null && relation.getLocationId() > 0) {
                locationIdList.add(relation.getLocationId());
            }
        }
        Map<Integer, SmartScheduleDrivingRuleDTO> drivingRuleMap =
                getDrivingRuleMapByIdList(businessId, drivingRuleIdList);
        Map<Integer, SmartScheduleLocationDTO> locationMap = getLocationMapByIdList(businessId, locationIdList);

        return relationList.stream().collect(Collectors.toMap(MoeVanSmartScheduleRelation::getVanId, relation -> {
            SmartScheduleSettingV2DTO result = new SmartScheduleSettingV2DTO();
            result.setVanId(relation.getVanId());
            result.setDrivingRule(drivingRuleMap.get(relation.getDrivingRuleId()));
            result.setLocation(locationMap.get(relation.getLocationId()));
            return result;
        }));
    }

    public Map<Integer, VanSmartScheduleRelationDTO> getVanSmartScheduleRelationMap(
            Integer businessId, List<Integer> vanIdList) {
        return getVanSmartScheduleRelationList(businessId, vanIdList).stream()
                .collect(Collectors.toMap(
                        MoeVanSmartScheduleRelation::getVanId, converter::toVanRelationDTO, (r1, r2) -> r1));
    }

    private void batchInsertOrUpdateRelation(Long companyId, VanSmartScheduleBindingParams params) {
        Integer businessId = params.getBusinessId();
        List<Integer> validVanIdList = params.getVanIdList();
        Integer operatorId = params.getOperatorId();

        Map<Integer, VanSmartScheduleRelationDTO> vanExistRelationMap =
                getVanSmartScheduleRelationMap(businessId, validVanIdList);

        List<MoeVanSmartScheduleRelation> newRecordList = new ArrayList<>();
        List<MoeVanSmartScheduleRelation> updateRecordList = new ArrayList<>();
        for (Integer vanId : validVanIdList) {
            MoeVanSmartScheduleRelation saveRecord = new MoeVanSmartScheduleRelation();
            // 有记录就更新，无记录则新建
            VanSmartScheduleRelationDTO existRecord = vanExistRelationMap.get(vanId);
            if (existRecord == null) {
                saveRecord.setCompanyId(companyId);
                saveRecord.setBusinessId(businessId);
                saveRecord.setVanId(vanId);
                // 新记录没传时补0
                Optional.ofNullable(params.getDrivingRuleId())
                        .ifPresentOrElse(saveRecord::setDrivingRuleId, () -> saveRecord.setDrivingRuleId(0));
                Optional.ofNullable(params.getLocationId())
                        .ifPresentOrElse(saveRecord::setLocationId, () -> saveRecord.setLocationId(0));
                saveRecord.setCreatedBy(operatorId);
                newRecordList.add(saveRecord);
            } else {
                saveRecord.setDrivingRuleId(params.getDrivingRuleId());
                saveRecord.setLocationId(params.getLocationId());
                saveRecord.setId(existRecord.getId());
                saveRecord.setUpdatedBy(operatorId);
                updateRecordList.add(saveRecord);
            }
        }
        if (!CollectionUtils.isEmpty(newRecordList)) {
            vanSmartScheduleRelationMapper.batchInsertForInitialize(newRecordList);
        }
        if (!CollectionUtils.isEmpty(updateRecordList)) {
            vanSmartScheduleRelationMapper.batchUpdateByPrimaryKeySelective(updateRecordList);
        }
    }

    private void saveRelationBySmartScheduleSetting(Long companyId, VanSmartScheduleBindingParams params) {
        Integer businessId = params.getBusinessId();
        Integer drivingRuleId = params.getDrivingRuleId();
        Integer locationId = params.getLocationId();
        Integer operatorId = params.getOperatorId();
        List<Integer> assignedVanIdList = params.getVanIdList();

        if (drivingRuleId == null && locationId == null) {
            // 同时为 null 时，不更新
            return;
        }

        // van id list check
        List<Integer> validVanIdList = getValidVanIdList(businessId, assignedVanIdList);
        // 解绑
        checkAndRemoveRelation(businessId, validVanIdList, drivingRuleId, locationId, operatorId);
        // save relation
        params.setVanIdList(validVanIdList); // 重新赋值为 validVanIdList
        batchInsertOrUpdateRelation(companyId, params);
    }

    public Boolean saveVanSmartScheduleSettingRelation(Long companyId, SaveVanSmartScheduleRelationParams params) {
        if (params.getDrivingRuleId() == null && params.getLocationId() == null) {
            return false;
        }
        Integer businessId = params.getBusinessId();
        Integer vanId = params.getVanId();
        Integer drivingRuleId = params.getDrivingRuleId();
        Integer locationId = params.getLocationId();
        Integer operatorId = params.getOperatorId();

        // check van id
        vanStaffService.checkVanIsBelongBusiness(businessId, vanId);
        // check driving rule and location
        if (drivingRuleId != null && drivingRuleId > 0) {
            checkAndGetDrivingRule(drivingRuleId, businessId);
        }
        if (locationId != null && locationId > 0) {
            checkExistLocation(locationId, businessId);
        }
        // save relation
        batchInsertOrUpdateRelation(
                companyId,
                new VanSmartScheduleBindingParams(businessId, List.of(vanId), drivingRuleId, locationId, operatorId));
        return true;
    }

    private void checkAndRemoveRelation(
            Integer businessId,
            List<Integer> validVanIdList,
            Integer drivingRuleId,
            Integer locationId,
            Integer operatorId) {
        // 当前 rule/location 绑定的 van，如果没有在绑定列表中，则删除
        List<MoeVanSmartScheduleRelation> saveRecordList = new ArrayList<>();
        if (drivingRuleId != null && drivingRuleId > 0) {
            List<MoeVanSmartScheduleRelation> existingRecords =
                    vanSmartScheduleRelationMapper.selectByDrivingRuleId(businessId, drivingRuleId);
            for (MoeVanSmartScheduleRelation record : existingRecords) {
                if (validVanIdList.contains(record.getVanId())) {
                    continue;
                }
                MoeVanSmartScheduleRelation saveRecord = new MoeVanSmartScheduleRelation();
                saveRecord.setId(record.getId());
                saveRecord.setDrivingRuleId(0);
                saveRecord.setUpdatedBy(operatorId);
                saveRecordList.add(saveRecord);
            }
        }
        if (locationId != null && locationId > 0) {
            List<MoeVanSmartScheduleRelation> existingRecords =
                    vanSmartScheduleRelationMapper.selectByLocationId(businessId, locationId);
            for (MoeVanSmartScheduleRelation record : existingRecords) {
                if (validVanIdList.contains(record.getVanId())) {
                    continue;
                }
                MoeVanSmartScheduleRelation saveRecord = new MoeVanSmartScheduleRelation();
                saveRecord.setId(record.getId());
                saveRecord.setLocationId(0);
                saveRecord.setUpdatedBy(operatorId);
                saveRecordList.add(saveRecord);
            }
        }
        if (!CollectionUtils.isEmpty(saveRecordList)) {
            vanSmartScheduleRelationMapper.batchUpdateByPrimaryKeySelective(saveRecordList);
        }
    }

    /**
     * 查询 ss 配置绑定的 van id list
     * key - drivingRuleId/locationId, value - vanIdList
     */
    private Map<Integer, List<Integer>> getAssignedVanIdListMap(
            Integer businessId, List<Integer> keyIdList, Boolean isDrivingRule) {
        List<MoeVanSmartScheduleRelation> relationList;
        if (isDrivingRule) {
            relationList = vanSmartScheduleRelationMapper.selectByDrivingRuleIdList(businessId, keyIdList);
        } else {
            relationList = vanSmartScheduleRelationMapper.selectByLocationIdList(businessId, keyIdList);
        }
        if (CollectionUtils.isEmpty(relationList)) {
            return Map.of();
        }
        List<Integer> validVanIdList = getValidVanIdList(
                businessId,
                relationList.stream().map(MoeVanSmartScheduleRelation::getVanId).toList());

        Map<Integer, List<Integer>> resultMap = new HashMap<>();
        for (MoeVanSmartScheduleRelation relation : relationList) {
            if (!validVanIdList.contains(relation.getVanId())) {
                continue;
            }
            if (isDrivingRule) {
                resultMap
                        .computeIfAbsent(relation.getDrivingRuleId(), k -> new ArrayList<>())
                        .add(relation.getVanId());
            } else {
                resultMap
                        .computeIfAbsent(relation.getLocationId(), k -> new ArrayList<>())
                        .add(relation.getVanId());
            }
        }
        return resultMap;
    }

    private List<Integer> getValidVanIdList(Integer businessId, List<Integer> vanIdList) {
        List<Integer> validVanIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(vanIdList)) {
            validVanIdList = vanStaffService.queryVanByIdList(businessId, vanIdList).stream()
                    .map(VanDTO::getVanId)
                    .distinct()
                    .toList();
        }
        return validVanIdList;
    }

    /**
     * 查询 staff 列表的 SS 配置，当 staff 列表为空时，返回 business 下全部 staff
     *
     * @param businessId  business id
     * @param staffIdList staff id list
     * @return key - staffId, value - StaffSmartScheduleSettingDTO
     */
    public Map<Integer, StaffSmartScheduleSettingDTO> getStaffSmartScheduleSettingMap(
            Integer businessId, List<Integer> staffIdList) {
        Map<Integer, StaffSmartScheduleSettingDTO> resultMap = new HashMap<>();

        // 查询 staff 绑定的 van，一个 staff 只会关联一个 van
        Map<Integer, Integer> staffVanMap = vanStaffService.queryStaffRelation(businessId, staffIdList);
        // 查询 van 绑定的 SS 配置
        Map<Integer, SmartScheduleSettingV2DTO> vanSsSettingMap = getSmartScheduleSettingMapByVanIdList(
                businessId, staffVanMap.values().stream().toList());

        staffVanMap.forEach((staffId, vanId) -> {
            StaffSmartScheduleSettingDTO staffSsSetting = new StaffSmartScheduleSettingDTO();
            staffSsSetting.setStaffId(staffId);
            if (vanSsSettingMap.containsKey(vanId)) {
                staffSsSetting.setDrivingRule(vanSsSettingMap.get(vanId).getDrivingRule());
                staffSsSetting.setLocation(vanSsSettingMap.get(vanId).getLocation());
            }
            resultMap.put(staffId, staffSsSetting);
        });

        return resultMap;
    }

    /**
     * 根据单位类型转换距离为 mile
     *
     * @param distance           距离
     * @param unitOfDistanceType business 距离单位类型
     * @return distance in mile
     */
    private Integer getDistanceInMile(Integer distance, Byte unitOfDistanceType) {
        if (Objects.isNull(distance)
                || !Objects.equals(unitOfDistanceType.intValue(), Dictionary.UNITED_DISTANCE_TYPE_2)) {
            return distance;
        }
        // kilometer to mile
        return Double.valueOf(distance / CommonConstant.MILE_TO_KILOMETER).intValue();
    }
}
