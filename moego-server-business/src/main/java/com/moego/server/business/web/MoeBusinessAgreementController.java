package com.moego.server.business.web;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.dto.BusinessAgreementDTO;
import com.moego.server.business.dto.BusinessAgreementInfoDTO;
import com.moego.server.business.params.CommonIdsParams;
import com.moego.server.business.service.BusinessService;
import com.moego.server.business.service.MoeBusinessAgreementService;
import com.moego.server.business.web.vo.BusinessAgreementVo;
import com.moego.server.customer.dto.AddResultDTO;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class MoeBusinessAgreementController {

    @Autowired
    private MoeBusinessAgreementService moeBusinessAgreementService;

    @Autowired
    private BusinessService businessService;

    @PostMapping("/business/agreement")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<AddResultDTO> addMoeBusinessAgreement(
            @RequestBody BusinessAgreementVo businessAgreementVo, AuthContext context) {
        businessAgreementVo.setId(null);
        businessAgreementVo.setBusinessId(context.getBusinessId());
        businessAgreementVo.setStaffId(context.getStaffId());
        if (StringUtils.isBlank(businessAgreementVo.getAgreementHeader())
                || StringUtils.isBlank(businessAgreementVo.getAgreementContent())
                || businessAgreementVo.getAgreementRequiredType() == null
                || StringUtils.isBlank(businessAgreementVo.getServicesType())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "check necessary params");
        }
        return moeBusinessAgreementService.addMoeBusinessAgreement(businessAgreementVo);
    }

    @PutMapping("/business/agreement")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> modifyMoeBusinessAgreement(
            @RequestBody BusinessAgreementVo businessAgreementVo, Boolean isNeedFlush, AuthContext context) {
        businessAgreementVo.setBusinessId(context.getBusinessId());
        return moeBusinessAgreementService.modifyMoeBusinessAgreement(businessAgreementVo, isNeedFlush);
    }

    @GetMapping("/business/agreement/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<BusinessAgreementDTO>> queryBusinessAgreementInfoList(
            AuthContext context, String servicesType) {
        return ResponseResult.success(moeBusinessAgreementService.queryBusinessAgreementInfoList(
                context.companyId(), context.getBusinessId(), context.staffId(), servicesType));
    }

    @PutMapping("/business/agreement/list")
    @Auth(AuthType.BUSINESS)
    public Integer modifyMoeBusinessAgreementList(AuthContext context, @RequestBody CommonIdsParams idList) {
        return moeBusinessAgreementService.modifyMoeBusinessAgreementList(context.getBusinessId(), idList.getIds());
    }

    @GetMapping("/business/agreement/info")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<BusinessAgreementInfoDTO> queryBusinessAgreementInfoById(
            @RequestParam Integer id, AuthContext context) {
        return moeBusinessAgreementService.queryBusinessAgreementInfoById(context.getBusinessId(), id);
    }

    @DeleteMapping("/business/agreement")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> deleteBusinessAgreementInfoById(
            AuthContext context, @RequestBody CommonIdsParams commonIdsParams) {
        return moeBusinessAgreementService.deleteBusinessAgreementInfoById(
                context.getBusinessId(), commonIdsParams.getIds());
    }

    /**
     * Frank：由前端确认，此接口已经没有被调用了。
     * 查询customer 可发送agreement 列表
     *
     * @param context
     * @param customerId
     * @param servicesType
     * @return
     */
    @GetMapping("/business/customer/agreement/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<BusinessAgreementInfoDTO>> queryBusinessCustomerAgreementInfoList(
            AuthContext context, @RequestParam Integer customerId, String servicesType, Boolean isCreateAppt) {
        return ResponseResult.fail(ResponseCodeEnum.SERVER_ERROR);
        /*
        if (isCreateAppt == null) {
            isCreateAppt = false;
        }
        return moeBusinessAgreementService.queryBusinessCustomerAgreementInfoList(
            context.getBusinessId(),
            customerId,
            servicesType,
            isCreateAppt
        );
        */
    }

    @GetMapping("/business/bookOnline/client/agreement")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<List<BusinessAgreementDTO>> getBookOnlineClientAgreements(
            @RequestParam String businessName, String servicesType) {
        Integer businessId = businessService.getBusinessId(businessName);
        List<BusinessAgreementDTO> agreementInfoDTOS =
                moeBusinessAgreementService.queryBusinessOBAgreementInfoList(businessId, servicesType, null);
        return ResponseResult.success(agreementInfoDTOS);
    }
}
