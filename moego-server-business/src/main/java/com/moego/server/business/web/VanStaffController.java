package com.moego.server.business.web;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.params.SortIdListParams;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.service.VanStaffService;
import com.moego.server.business.service.dto.van.VanHistoryDto;
import com.moego.server.business.service.dto.van.VanListDto;
import com.moego.server.business.web.vo.van.VanInsertVo;
import com.moego.server.business.web.vo.van.VanTransferStaffVo;
import com.moego.server.business.web.vo.van.VanUpdateVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/business/van")
public class VanStaffController {

    @Autowired
    private VanStaffService vanStaffService;

    @GetMapping("list")
    @Auth(AuthType.COMPANY)
    public List<VanListDto> queryVanList(AuthContext context) {
        return vanStaffService.queryVanListDto(context.companyId(), context.businessId());
    }

    @PostMapping
    @Auth(AuthType.COMPANY)
    public Integer addVan(AuthContext context, @RequestBody VanInsertVo vo) {
        if (!vanStaffService.checkVanNumIsEnough(context.companyId())) {
            throw new CommonException(ResponseCodeEnum.NOT_ENOUGH_VANS_NUM2);
        }
        return vanStaffService.addVan(context.companyId(), context.businessId(), vo, context.getStaffId());
    }

    @PutMapping
    @Auth(AuthType.COMPANY)
    public void updateVan(AuthContext context, @RequestBody VanUpdateVo vo) {
        vanStaffService.updateVan(context.companyId(), context.businessId(), vo, context.getStaffId());
    }

    @DeleteMapping
    @Auth(AuthType.COMPANY)
    public void deleteVan(AuthContext context, @RequestParam("vanId") Integer vanId) {
        vanStaffService.deleteVan(context.companyId(), context.businessId(), vanId);
    }

    @PutMapping("/sort")
    @Auth(AuthType.COMPANY)
    public void sortVan(AuthContext context, @RequestBody SortIdListParams idList) {
        vanStaffService.sortVan(context.companyId(), context.businessId(), idList.getIdList());
    }

    @PutMapping("/transfer/staff")
    @Auth(AuthType.COMPANY)
    public void transferStaff(AuthContext context, @RequestBody VanTransferStaffVo staffVo) {
        vanStaffService.transferStaff(context.companyId(), context.businessId(), staffVo);
    }

    @GetMapping("/staff/history")
    @Auth(AuthType.COMPANY)
    public List<VanHistoryDto> queryVanStaffHistory(AuthContext context, @RequestParam("vanId") Integer vanId) {
        return vanStaffService.queryVanStaffHistory(context.companyId(), context.businessId(), vanId);
    }
}
