<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.business.mapper.MoeReferralFixedRewardRuleMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.business.mapperbean.MoeReferralFixedRewardRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="start_offset" jdbcType="INTEGER" property="startOffset" />
    <result column="end_offset" jdbcType="INTEGER" property="endOffset" />
    <result column="fixed_amount" jdbcType="DECIMAL" property="fixedAmount" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, start_offset, end_offset, fixed_amount, status, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_referral_fixed_reward_rule
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_referral_fixed_reward_rule
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.business.mapperbean.MoeReferralFixedRewardRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_referral_fixed_reward_rule (start_offset, end_offset, fixed_amount, 
      status, create_time, update_time
      )
    values (#{startOffset,jdbcType=INTEGER}, #{endOffset,jdbcType=INTEGER}, #{fixedAmount,jdbcType=DECIMAL}, 
      #{status,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.business.mapperbean.MoeReferralFixedRewardRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_referral_fixed_reward_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="startOffset != null">
        start_offset,
      </if>
      <if test="endOffset != null">
        end_offset,
      </if>
      <if test="fixedAmount != null">
        fixed_amount,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="startOffset != null">
        #{startOffset,jdbcType=INTEGER},
      </if>
      <if test="endOffset != null">
        #{endOffset,jdbcType=INTEGER},
      </if>
      <if test="fixedAmount != null">
        #{fixedAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.business.mapperbean.MoeReferralFixedRewardRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_referral_fixed_reward_rule
    <set>
      <if test="startOffset != null">
        start_offset = #{startOffset,jdbcType=INTEGER},
      </if>
      <if test="endOffset != null">
        end_offset = #{endOffset,jdbcType=INTEGER},
      </if>
      <if test="fixedAmount != null">
        fixed_amount = #{fixedAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.business.mapperbean.MoeReferralFixedRewardRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_referral_fixed_reward_rule
    set start_offset = #{startOffset,jdbcType=INTEGER},
      end_offset = #{endOffset,jdbcType=INTEGER},
      fixed_amount = #{fixedAmount,jdbcType=DECIMAL},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getFixedRuleList" parameterType="java.lang.Byte" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_referral_fixed_reward_rule
    <if test="status != null">
      where status = #{status,jdbcType=TINYINT}
    </if>
    order by start_offset asc
  </select>
</mapper>