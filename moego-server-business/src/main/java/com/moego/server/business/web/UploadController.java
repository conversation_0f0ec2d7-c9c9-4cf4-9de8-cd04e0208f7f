package com.moego.server.business.web;

import com.moego.common.response.ResponseResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.service.AssertService;
import com.moego.server.business.service.dto.UploadResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/upload/")
@Slf4j
public class UploadController {

    private final AssertService assertService;

    @Autowired
    public UploadController(AssertService assertService) {
        this.assertService = assertService;
    }

    @PostMapping("/uploadFile")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<UploadResultDto> upload(@RequestParam("file") MultipartFile file) {
        return ResponseResult.success(assertService.uploadPublicImage(file));
    }

    @PostMapping("/bookOnlinePetPhotosUpload")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<UploadResultDto> uploadForOnlineBookingClient(@RequestParam("file") MultipartFile file) {
        return ResponseResult.success(assertService.uploadPublicImage(file));
    }
}
