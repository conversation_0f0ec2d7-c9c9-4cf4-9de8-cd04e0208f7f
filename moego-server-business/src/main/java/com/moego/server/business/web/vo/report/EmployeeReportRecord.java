package com.moego.server.business.web.vo.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmployeeReportRecord implements ReportRecord {

    private Integer employeeId;
    private String employeeName;
    /* including tax and tips */
    private BigDecimal collectedRevenue;
    private BigDecimal totalPayment; // 总付款金额
    private BigDecimal totalRefund; // 总退款金额
    private BigDecimal unpaidRevenue;
    private BigDecimal totalTips;
    private BigDecimal totalTax;
    private Integer totalAppts;
    private Integer totalPets;
    private Integer totalClients;
    private Double totalServiceHour;
    private Double avgMinutePerPet;
    private String status;

    private String apptDate;
    private Integer apptTime;
    private String apptDateTime;
    private String ownerName;
    private BigDecimal revenue;
    private Integer petNum;
    private BigDecimal tips;
    private BigDecimal tax;
    private BigDecimal discount;

    // commission
    private BigDecimal collectedServicePrice;
    private BigDecimal collectedProductPrice;
    private BigDecimal serviceCommissionBasedValue;
    private String serviceCommissionBased;
    private String servicePayRate;
    private BigDecimal serviceCommissionValue;
    private String serviceCommission;
    private BigDecimal collectedAddonPrice;
    private BigDecimal addonCommissionBasedValue;
    private String addonCommissionBased;
    private String addonPayRate;
    private BigDecimal addonCommissionValue;
    private String addonCommission;
    private BigDecimal collectedTips;
    private BigDecimal assignedTipsValue;
    private String assignedTips;
    private String tipsPayRate;
    private BigDecimal tipsCommissionValue;
    private String tipsCommission;
    private BigDecimal totalPayroll;

    private Double totalWorkingHour;
    private Integer clockDays;
    private Double avgHourPerDay;
    private String hourlyPayRate;
    private BigDecimal hourlyCommission;

    private Integer aptId;

    private String apptStatus;
    private List<String> services;
    private List<String> addOns;
    private List<Integer> petIds;
    private List<String> petNames;
    private Integer customerId;
    private String clientType;
    private String clientAddress;

    private Integer invoiceId;
    private Map<String, BigDecimal> paymentMap;

    private Integer servicedPetNum; // 服务过的pet，去重统计
    private BigDecimal totalServicesAmount;
    private BigDecimal totalAddOnsAmount;
    private BigDecimal totalProductAmount;
}
