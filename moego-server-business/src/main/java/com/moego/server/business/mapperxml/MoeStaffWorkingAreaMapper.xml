<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.business.mapper.MoeStaffWorkingAreaMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.business.mapperbean.MoeStaffWorkingArea">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="schedule_type" jdbcType="TINYINT" property="scheduleType" />
    <result column="start_date" jdbcType="VARCHAR" property="startDate" />
    <result column="end_date" jdbcType="VARCHAR" property="endDate" />
    <result column="first_week" jdbcType="CHAR" property="firstWeek" typeHandler="com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler" />
    <result column="second_week" jdbcType="CHAR" property="secondWeek" typeHandler="com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler" />
    <result column="third_week" jdbcType="CHAR" property="thirdWeek" typeHandler="com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler" />
    <result column="forth_week" jdbcType="CHAR" property="forthWeek" typeHandler="com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, staff_id, schedule_type, start_date, end_date, first_week, second_week, 
    third_week, forth_week, create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_staff_working_area
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_staff_working_area
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.business.mapperbean.MoeStaffWorkingArea">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff_working_area (business_id, staff_id, schedule_type, 
      start_date, end_date, first_week, 
      second_week, 
      third_week, 
      forth_week, 
      create_time, update_time, company_id
      )
    values (#{businessId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, #{scheduleType,jdbcType=TINYINT}, 
      #{startDate,jdbcType=VARCHAR}, #{endDate,jdbcType=VARCHAR}, #{firstWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler}, 
      #{secondWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler}, 
      #{thirdWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler}, 
      #{forthWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.business.mapperbean.MoeStaffWorkingArea">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff_working_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="scheduleType != null">
        schedule_type,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="firstWeek != null">
        first_week,
      </if>
      <if test="secondWeek != null">
        second_week,
      </if>
      <if test="thirdWeek != null">
        third_week,
      </if>
      <if test="forthWeek != null">
        forth_week,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="scheduleType != null">
        #{scheduleType,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="firstWeek != null">
        #{firstWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="secondWeek != null">
        #{secondWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="thirdWeek != null">
        #{thirdWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="forthWeek != null">
        #{forthWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.business.mapperbean.MoeStaffWorkingArea">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_working_area
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="scheduleType != null">
        schedule_type = #{scheduleType,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="firstWeek != null">
        first_week = #{firstWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="secondWeek != null">
        second_week = #{secondWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="thirdWeek != null">
        third_week = #{thirdWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="forthWeek != null">
        forth_week = #{forthWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.business.mapperbean.MoeStaffWorkingArea">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_working_area
    set business_id = #{businessId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      schedule_type = #{scheduleType,jdbcType=TINYINT},
      start_date = #{startDate,jdbcType=VARCHAR},
      end_date = #{endDate,jdbcType=VARCHAR},
      first_week = #{firstWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      second_week = #{secondWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      third_week = #{thirdWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      forth_week = #{forthWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByBusinessIdAndStaffId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_staff_working_area
    where business_id = #{businessId,jdbcType=INTEGER}
    and staff_id = #{staffId,jdbcType=INTEGER}
    limit 1
  </select>

  <select id="query" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_staff_working_area
    where business_id = #{businessId,jdbcType=INTEGER}
    <if test="staffIds != null">
      and staff_id in
      <foreach collection="staffIds" item="staffId" open="(" close=")" separator=",">
        #{staffId,jdbcType=INTEGER}
      </foreach>
    </if>
  </select>

  <update id="updateByBusinessIdAndStaffId" parameterType="com.moego.server.business.mapperbean.MoeStaffWorkingArea">
    update moe_staff_working_area
    <set>
      <if test="scheduleType != null">
        schedule_type = #{scheduleType,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="firstWeek != null">
        first_week = #{firstWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="secondWeek != null">
        second_week = #{secondWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="thirdWeek != null">
        third_week = #{thirdWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="forthWeek != null">
        forth_week = #{forthWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where business_id = #{businessId,jdbcType=INTEGER}
    and staff_id = #{staffId,jdbcType=INTEGER}
  </update>

  <insert id="batchInsertRecords" useGeneratedKeys="true">
    insert into moe_staff_working_area (business_id, staff_id, schedule_type,
    start_date, end_date, first_week,
    second_week,
    third_week,
    forth_week,
    company_id
    )
    values
    <foreach collection="records" item="record" separator=",">
      (#{record.businessId,jdbcType=INTEGER}, #{record.staffId,jdbcType=INTEGER}, #{record.scheduleType,jdbcType=TINYINT},
      #{record.startDate,jdbcType=VARCHAR}, #{record.endDate,jdbcType=VARCHAR},
      #{record.firstWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      #{record.secondWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      #{record.thirdWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      #{record.forthWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      #{record.companyId,jdbcType=INTEGER}
      )
    </foreach>
  </insert>

  <update id="batchUpdateById" parameterType="java.util.List">
    update moe_staff_working_area
    <set>
      <trim prefix="schedule_type = case id" suffix="end,">
        <foreach collection="records" item="item">
          when #{item.id} then ifnull(#{item.scheduleType},schedule_type)
        </foreach>
      </trim>
      <trim prefix="start_date = case id" suffix="end,">
        <foreach collection="records" item="item">
          when #{item.id} then ifnull(#{item.startDate},start_date)
        </foreach>
      </trim>
      <trim prefix="end_date = case id" suffix="end,">
        <foreach collection="records" item="item">
          when #{item.id} then ifnull(#{item.endDate},end_date)
        </foreach>
      </trim>
      <trim prefix="first_week = case id" suffix="end,">
        <foreach collection="records" item="item">
          when #{item.id} then ifnull(#{item.firstWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},first_week)
        </foreach>
      </trim>
      <trim prefix="second_week = case id" suffix="end,">
        <foreach collection="records" item="item">
          when #{item.id} then ifnull(#{item.secondWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},second_week)
        </foreach>
      </trim>
      <trim prefix="third_week = case id" suffix="end,">
        <foreach collection="records" item="item">
          when #{item.id} then ifnull(#{item.thirdWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},third_week)
        </foreach>
      </trim>
      <trim prefix="forth_week = case id" suffix="end,">
        <foreach collection="records" item="item">
          when #{item.id} then ifnull(#{item.forthWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},forth_week)
        </foreach>
      </trim>
    </set>
    <where>
      business_id = #{businessId,jdbcType=INTEGER} and id in
      <foreach collection="records" item="item" open="(" separator="," close=")">
        #{item.id}
      </foreach>
    </where>
  </update>

  <insert id="insertSelectiveOnDuplicateKey" parameterType="com.moego.server.business.mapperbean.MoeStaffWorkingArea">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff_working_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      business_id,
      staff_id,
      <if test="scheduleType != null">
        schedule_type,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="firstWeek != null">
        first_week,
      </if>
      <if test="secondWeek != null">
        second_week,
      </if>
      <if test="thirdWeek != null">
        third_week,
      </if>
      <if test="forthWeek != null">
        forth_week,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{businessId,jdbcType=INTEGER},
      #{staffId,jdbcType=INTEGER},
      <if test="scheduleType != null">
        #{scheduleType,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="firstWeek != null">
        #{firstWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="secondWeek != null">
        #{secondWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="thirdWeek != null">
        #{thirdWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
      <if test="forthWeek != null">
        #{forthWeek,jdbcType=CHAR,typeHandler=com.moego.server.business.mapper.typehandler.StaffWorkingAreaDayDetailDTOHandler},
      </if>
    </trim>
    ON DUPLICATE KEY UPDATE update_time = update_time
  </insert>
</mapper>