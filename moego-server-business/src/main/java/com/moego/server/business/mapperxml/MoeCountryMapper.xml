<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.business.mapper.MoeCountryMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.business.mapperbean.MoeCountry">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="alpha2" jdbcType="VARCHAR" property="alpha2" />
    <result column="alpha3" jdbcType="VARCHAR" property="alpha3" />
    <result column="country_calling_codes" jdbcType="VARCHAR" property="countryCallingCodes" />
    <result column="currencies" jdbcType="VARCHAR" property="currencies" />
    <result column="emoji" jdbcType="VARCHAR" property="emoji" />
    <result column="ioc" jdbcType="VARCHAR" property="ioc" />
    <result column="languages" jdbcType="VARCHAR" property="languages" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, alpha2, alpha3, country_calling_codes, currencies, emoji, ioc, languages, name,
    status, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_country
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_country
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.business.mapperbean.MoeCountry">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_country (alpha2, alpha3, country_calling_codes,
      currencies, emoji, ioc,
      languages, name, status,
      create_time, update_time)
    values (#{alpha2,jdbcType=VARCHAR}, #{alpha3,jdbcType=VARCHAR}, #{countryCallingCodes,jdbcType=VARCHAR},
      #{currencies,jdbcType=VARCHAR}, #{emoji,jdbcType=VARCHAR}, #{ioc,jdbcType=VARCHAR},
      #{languages,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.business.mapperbean.MoeCountry">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_country
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="alpha2 != null">
        alpha2,
      </if>
      <if test="alpha3 != null">
        alpha3,
      </if>
      <if test="countryCallingCodes != null">
        country_calling_codes,
      </if>
      <if test="currencies != null">
        currencies,
      </if>
      <if test="emoji != null">
        emoji,
      </if>
      <if test="ioc != null">
        ioc,
      </if>
      <if test="languages != null">
        languages,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="alpha2 != null">
        #{alpha2,jdbcType=VARCHAR},
      </if>
      <if test="alpha3 != null">
        #{alpha3,jdbcType=VARCHAR},
      </if>
      <if test="countryCallingCodes != null">
        #{countryCallingCodes,jdbcType=VARCHAR},
      </if>
      <if test="currencies != null">
        #{currencies,jdbcType=VARCHAR},
      </if>
      <if test="emoji != null">
        #{emoji,jdbcType=VARCHAR},
      </if>
      <if test="ioc != null">
        #{ioc,jdbcType=VARCHAR},
      </if>
      <if test="languages != null">
        #{languages,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.business.mapperbean.MoeCountry">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_country
    <set>
      <if test="alpha2 != null">
        alpha2 = #{alpha2,jdbcType=VARCHAR},
      </if>
      <if test="alpha3 != null">
        alpha3 = #{alpha3,jdbcType=VARCHAR},
      </if>
      <if test="countryCallingCodes != null">
        country_calling_codes = #{countryCallingCodes,jdbcType=VARCHAR},
      </if>
      <if test="currencies != null">
        currencies = #{currencies,jdbcType=VARCHAR},
      </if>
      <if test="emoji != null">
        emoji = #{emoji,jdbcType=VARCHAR},
      </if>
      <if test="ioc != null">
        ioc = #{ioc,jdbcType=VARCHAR},
      </if>
      <if test="languages != null">
        languages = #{languages,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.business.mapperbean.MoeCountry">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_country
    set alpha2 = #{alpha2,jdbcType=VARCHAR},
      alpha3 = #{alpha3,jdbcType=VARCHAR},
      country_calling_codes = #{countryCallingCodes,jdbcType=VARCHAR},
      currencies = #{currencies,jdbcType=VARCHAR},
      emoji = #{emoji,jdbcType=VARCHAR},
      ioc = #{ioc,jdbcType=VARCHAR},
      languages = #{languages,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByCountryName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_country
    where name = #{countryName,jdbcType=INTEGER}
  </select>

  <select id="listAssignedCountry" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_country
    where status = 'assigned'
  </select>
</mapper>
