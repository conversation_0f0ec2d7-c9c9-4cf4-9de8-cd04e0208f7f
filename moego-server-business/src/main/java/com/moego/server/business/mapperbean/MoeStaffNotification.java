package com.moego.server.business.mapperbean;

public class MoeStaffNotification {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.booking_created
     *
     * @mbg.generated
     */
    private Byte bookingCreated;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.booking_cancelled
     *
     * @mbg.generated
     */
    private Byte bookingCancelled;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.booking_rescheduled
     *
     * @mbg.generated
     */
    private Byte bookingRescheduled;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.new_booking
     *
     * @mbg.generated
     */
    private Byte newBooking;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.new_intake_form
     *
     * @mbg.generated
     */
    private Byte newIntakeForm;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.agreement_signed
     *
     * @mbg.generated
     */
    private Byte agreementSigned;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.invoice_paid
     *
     * @mbg.generated
     */
    private Byte invoicePaid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.review_submitted
     *
     * @mbg.generated
     */
    private Byte reviewSubmitted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.push_calendar_switch
     *
     * @mbg.generated
     */
    private Byte pushCalendarSwitch;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.before_mins
     *
     * @mbg.generated
     */
    private Integer beforeMins;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.new_abandoned_bookings
     *
     * @mbg.generated
     */
    private Byte newAbandonedBookings;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.business_id
     *
     * @mbg.generated
     */
    private Long businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.assigned_task
     *
     * @mbg.generated
     */
    private Byte assignedTask;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.id
     *
     * @return the value of moe_staff_notification.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.id
     *
     * @param id the value for moe_staff_notification.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.staff_id
     *
     * @return the value of moe_staff_notification.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.staff_id
     *
     * @param staffId the value for moe_staff_notification.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.booking_created
     *
     * @return the value of moe_staff_notification.booking_created
     *
     * @mbg.generated
     */
    public Byte getBookingCreated() {
        return bookingCreated;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.booking_created
     *
     * @param bookingCreated the value for moe_staff_notification.booking_created
     *
     * @mbg.generated
     */
    public void setBookingCreated(Byte bookingCreated) {
        this.bookingCreated = bookingCreated;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.booking_cancelled
     *
     * @return the value of moe_staff_notification.booking_cancelled
     *
     * @mbg.generated
     */
    public Byte getBookingCancelled() {
        return bookingCancelled;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.booking_cancelled
     *
     * @param bookingCancelled the value for moe_staff_notification.booking_cancelled
     *
     * @mbg.generated
     */
    public void setBookingCancelled(Byte bookingCancelled) {
        this.bookingCancelled = bookingCancelled;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.booking_rescheduled
     *
     * @return the value of moe_staff_notification.booking_rescheduled
     *
     * @mbg.generated
     */
    public Byte getBookingRescheduled() {
        return bookingRescheduled;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.booking_rescheduled
     *
     * @param bookingRescheduled the value for moe_staff_notification.booking_rescheduled
     *
     * @mbg.generated
     */
    public void setBookingRescheduled(Byte bookingRescheduled) {
        this.bookingRescheduled = bookingRescheduled;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.new_booking
     *
     * @return the value of moe_staff_notification.new_booking
     *
     * @mbg.generated
     */
    public Byte getNewBooking() {
        return newBooking;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.new_booking
     *
     * @param newBooking the value for moe_staff_notification.new_booking
     *
     * @mbg.generated
     */
    public void setNewBooking(Byte newBooking) {
        this.newBooking = newBooking;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.new_intake_form
     *
     * @return the value of moe_staff_notification.new_intake_form
     *
     * @mbg.generated
     */
    public Byte getNewIntakeForm() {
        return newIntakeForm;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.new_intake_form
     *
     * @param newIntakeForm the value for moe_staff_notification.new_intake_form
     *
     * @mbg.generated
     */
    public void setNewIntakeForm(Byte newIntakeForm) {
        this.newIntakeForm = newIntakeForm;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.agreement_signed
     *
     * @return the value of moe_staff_notification.agreement_signed
     *
     * @mbg.generated
     */
    public Byte getAgreementSigned() {
        return agreementSigned;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.agreement_signed
     *
     * @param agreementSigned the value for moe_staff_notification.agreement_signed
     *
     * @mbg.generated
     */
    public void setAgreementSigned(Byte agreementSigned) {
        this.agreementSigned = agreementSigned;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.invoice_paid
     *
     * @return the value of moe_staff_notification.invoice_paid
     *
     * @mbg.generated
     */
    public Byte getInvoicePaid() {
        return invoicePaid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.invoice_paid
     *
     * @param invoicePaid the value for moe_staff_notification.invoice_paid
     *
     * @mbg.generated
     */
    public void setInvoicePaid(Byte invoicePaid) {
        this.invoicePaid = invoicePaid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.review_submitted
     *
     * @return the value of moe_staff_notification.review_submitted
     *
     * @mbg.generated
     */
    public Byte getReviewSubmitted() {
        return reviewSubmitted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.review_submitted
     *
     * @param reviewSubmitted the value for moe_staff_notification.review_submitted
     *
     * @mbg.generated
     */
    public void setReviewSubmitted(Byte reviewSubmitted) {
        this.reviewSubmitted = reviewSubmitted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.push_calendar_switch
     *
     * @return the value of moe_staff_notification.push_calendar_switch
     *
     * @mbg.generated
     */
    public Byte getPushCalendarSwitch() {
        return pushCalendarSwitch;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.push_calendar_switch
     *
     * @param pushCalendarSwitch the value for moe_staff_notification.push_calendar_switch
     *
     * @mbg.generated
     */
    public void setPushCalendarSwitch(Byte pushCalendarSwitch) {
        this.pushCalendarSwitch = pushCalendarSwitch;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.before_mins
     *
     * @return the value of moe_staff_notification.before_mins
     *
     * @mbg.generated
     */
    public Integer getBeforeMins() {
        return beforeMins;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.before_mins
     *
     * @param beforeMins the value for moe_staff_notification.before_mins
     *
     * @mbg.generated
     */
    public void setBeforeMins(Integer beforeMins) {
        this.beforeMins = beforeMins;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.create_time
     *
     * @return the value of moe_staff_notification.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.create_time
     *
     * @param createTime the value for moe_staff_notification.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.update_time
     *
     * @return the value of moe_staff_notification.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.update_time
     *
     * @param updateTime the value for moe_staff_notification.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.new_abandoned_bookings
     *
     * @return the value of moe_staff_notification.new_abandoned_bookings
     *
     * @mbg.generated
     */
    public Byte getNewAbandonedBookings() {
        return newAbandonedBookings;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.new_abandoned_bookings
     *
     * @param newAbandonedBookings the value for moe_staff_notification.new_abandoned_bookings
     *
     * @mbg.generated
     */
    public void setNewAbandonedBookings(Byte newAbandonedBookings) {
        this.newAbandonedBookings = newAbandonedBookings;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.company_id
     *
     * @return the value of moe_staff_notification.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.company_id
     *
     * @param companyId the value for moe_staff_notification.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.business_id
     *
     * @return the value of moe_staff_notification.business_id
     *
     * @mbg.generated
     */
    public Long getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.business_id
     *
     * @param businessId the value for moe_staff_notification.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_staff_notification.assigned_task
     *
     * @return the value of moe_staff_notification.assigned_task
     *
     * @mbg.generated
     */
    public Byte getAssignedTask() {
        return assignedTask;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_staff_notification.assigned_task
     *
     * @param assignedTask the value for moe_staff_notification.assigned_task
     *
     * @mbg.generated
     */
    public void setAssignedTask(Byte assignedTask) {
        this.assignedTask = assignedTask;
    }
}
