package com.moego.server.business.mapper;

import com.moego.common.dto.SortDto;
import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.server.business.mapperbean.MoeVan;
import com.moego.server.business.mapperbean.MoeVanExample;
import com.moego.server.business.params.DescribeVansParams;
import com.moego.server.business.service.dto.BusinessCountDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeVanMapper extends DynamicDataSource<MoeVanMapper> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    long countByExample(MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int deleteByExample(MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int insert(MoeVan record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int insertSelective(MoeVan record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    List<MoeVan> selectByExampleWithBLOBs(MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    List<MoeVan> selectByExample(MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    MoeVan selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") MoeVan record, @Param("example") MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") MoeVan record, @Param("example") MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MoeVan record, @Param("example") MoeVanExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeVan record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeVan record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeVan record);

    List<MoeVan> selectByBusinessId(@Param("businessId") Integer businessId);

    List<BusinessCountDto> selectUsedVanByBidList(@Param("bidList") List<Integer> bidList);

    Integer selectAllVanByCompanyId(@Param("companyId") Integer companyId);

    Integer sortVan(@Param("businessId") Integer businessId, @Param("sortDtos") List<SortDto> sortDtos);

    List<MoeVan> describeVans(DescribeVansParams params);
}
