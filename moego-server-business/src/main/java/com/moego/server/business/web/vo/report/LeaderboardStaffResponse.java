package com.moego.server.business.web.vo.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LeaderboardStaffResponse {

    private List<LeaderboardStaffVo> topServiceSales;

    private List<LeaderboardStaffVo> topNumberOfTickets;

    private List<LeaderboardStaffVo> topSalesPerTicket;

    private List<LeaderboardStaffVo> topNumberOfPets;

    private List<LeaderboardStaffVo> topTips;

    private List<LeaderboardStaffVo> topBookingCapacity;
}
