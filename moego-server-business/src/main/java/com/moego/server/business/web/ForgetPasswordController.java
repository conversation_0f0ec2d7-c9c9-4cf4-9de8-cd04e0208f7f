package com.moego.server.business.web;

import com.moego.common.response.ResponseResult;
import com.moego.common.utils.BindingErrorUtil;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.service.ForgetPasswordService;
import com.moego.server.business.web.vo.ForgetPasswordCodeVo;
import com.moego.server.business.web.vo.ForgetPasswordEmailVo;
import com.moego.server.business.web.vo.ForgetPasswordSaveVo;
import java.security.NoSuchAlgorithmException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/account")
public class ForgetPasswordController {

    @Autowired
    private ForgetPasswordService forgetPasswordService;

    @PostMapping("/resetPassword/sendEmail")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<Boolean> sendEmail(
            @Validated @RequestBody ForgetPasswordEmailVo emailVo, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        String email = emailVo.getEmail().trim();
        return ResponseResult.success(forgetPasswordService.sendEmail(email));
    }

    @PostMapping("/resetPassword/checkCode")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<Boolean> checkCode(
            @Validated @RequestBody ForgetPasswordCodeVo codeVo, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        return ResponseResult.success(forgetPasswordService.checkCode(codeVo.getEmail(), codeVo.getCode()));
    }

    @PostMapping("/resetPassword")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<Boolean> resetPassword(
            @Validated @RequestBody ForgetPasswordSaveVo saveVo, BindingResult bindingResult)
            throws NoSuchAlgorithmException {
        BindingErrorUtil.handleResult(bindingResult);
        return ResponseResult.success(forgetPasswordService.resetPassword(saveVo));
    }
}
