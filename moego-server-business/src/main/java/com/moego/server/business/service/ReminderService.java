package com.moego.server.business.service;

import com.moego.common.enums.BusinessReferralConst;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.mapper.MoeCompanyMapper;
import com.moego.server.business.mapper.MoeEventReminderMapper;
import com.moego.server.business.mapper.MoeReferralInfoMapper;
import com.moego.server.business.mapperbean.MoeCompany;
import com.moego.server.business.mapperbean.MoeEventReminder;
import com.moego.server.business.mapperbean.MoeReferralInfo;
import com.moego.server.business.service.params.EventReminderParams;
import com.moego.server.business.web.vo.EventReminderVO;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2022/7/19
 */
@Slf4j
@Service
@AllArgsConstructor
public class ReminderService {

    private final MoeEventReminderMapper eventReminderMapper;

    private final MoeCompanyMapper companyMapper;

    private final MoeReferralInfoMapper referralInfoMapper;

    public Boolean hasReminderList(EventReminderParams reminderParams) {
        return eventReminderMapper.countEventReminderList(reminderParams) != 0;
    }

    public List<EventReminderVO> getReminderList(EventReminderParams reminderParams) {
        // get reminder list
        List<MoeEventReminder> reminderList = eventReminderMapper.getEventReminderList(reminderParams);
        // update reminder status
        if (!CollectionUtils.isEmpty(reminderParams.getAutoConfirmType())) {
            ThreadPool.execute(() -> eventReminderMapper.updateUnconfirmedReminderList(reminderParams));
        }
        if (CollectionUtils.isEmpty(reminderList)) {
            return Collections.emptyList();
        }
        return reminderList.stream()
                .map(reminder -> {
                    EventReminderVO reminderVO = new EventReminderVO();
                    BeanUtils.copyProperties(reminder, reminderVO);
                    return reminderVO;
                })
                .collect(Collectors.toList());
    }

    public void addFixedRewardEventReminder(MoeReferralInfo refererInfo, int successOrder) {
        MoeEventReminder eventReminder = this.initializeEventReminder(refererInfo);
        if (successOrder == 1) {
            eventReminder.setReminderType(BusinessReferralConst.REMINDER_TYPE_POPUPS);
            eventReminder.setReminderCode(BusinessReferralConst.REMINDER_CODE_FIRST_EARN);
        } else {
            eventReminder.setReminderType(BusinessReferralConst.REMINDER_TYPE_DOT);
            eventReminder.setReminderCode(BusinessReferralConst.REMINDER_CODE_COMMON_CHANGE);
        }
        eventReminderMapper.insertSelective(eventReminder);
    }

    public void addBonusRewardEventReminder(MoeReferralInfo refererInfo) {
        MoeEventReminder eventReminder = this.initializeEventReminder(refererInfo);
        eventReminder.setReminderType(BusinessReferralConst.REMINDER_TYPE_POPUPS);
        eventReminder.setReminderCode(BusinessReferralConst.REMINDER_CODE_BONUS_TIER);
        eventReminderMapper.insertSelective(eventReminder);
    }

    private MoeReferralInfo getRefererInfoByRefereeCompanyId(Integer companyId) {
        MoeCompany company = companyMapper.selectByPrimaryKey(companyId);
        MoeReferralInfo refereeInfo =
                referralInfoMapper.selectByCompanyIdAndAccountId(companyId, company.getAccountId());
        if (Objects.isNull(refereeInfo)
                || Objects.isNull(refereeInfo.getReferralId())
                || Objects.equals(refereeInfo.getReferralId(), 0)) {
            return null;
        }
        return referralInfoMapper.selectByPrimaryKey(refereeInfo.getReferralId());
    }

    private MoeReferralInfo getRefererInfoByRefereeCompany(MoeCompany company) {
        MoeReferralInfo refereeInfo =
                referralInfoMapper.selectByCompanyIdAndAccountId(company.getId(), company.getAccountId());
        if (Objects.isNull(refereeInfo)
                || Objects.isNull(refereeInfo.getReferralId())
                || Objects.equals(refereeInfo.getReferralId(), 0)) {
            return null;
        }
        return referralInfoMapper.selectByPrimaryKey(refereeInfo.getReferralId());
    }

    public void addCommonDotEventReminder(Integer companyId) {
        MoeReferralInfo refererInfo = getRefererInfoByRefereeCompanyId(companyId);
        if (Objects.isNull(refererInfo)) {
            return;
        }
        MoeEventReminder eventReminder = this.initializeEventReminder(refererInfo);
        eventReminder.setReminderType(BusinessReferralConst.REMINDER_TYPE_DOT);
        eventReminder.setReminderCode(BusinessReferralConst.REMINDER_CODE_COMMON_CHANGE);
        eventReminderMapper.insertSelective(eventReminder);
    }

    public void addCommonDotEventReminder(MoeCompany company) {
        MoeReferralInfo refererInfo = getRefererInfoByRefereeCompany(company);
        if (Objects.isNull(refererInfo)) {
            return;
        }
        MoeEventReminder eventReminder = this.initializeEventReminder(refererInfo);
        eventReminder.setReminderType(BusinessReferralConst.REMINDER_TYPE_DOT);
        eventReminder.setReminderCode(BusinessReferralConst.REMINDER_CODE_COMMON_CHANGE);
        eventReminderMapper.insertSelective(eventReminder);
    }

    public void addCommonDotEventReminder(MoeReferralInfo refererInfo) {
        MoeEventReminder eventReminder = this.initializeEventReminder(refererInfo);
        eventReminder.setReminderType(BusinessReferralConst.REMINDER_TYPE_DOT);
        eventReminder.setReminderCode(BusinessReferralConst.REMINDER_CODE_COMMON_CHANGE);
        eventReminderMapper.insertSelective(eventReminder);
    }

    public void addFirstCharge3TimesEventReminder(Integer companyId) {
        MoeReferralInfo refererInfo = getRefererInfoByRefereeCompanyId(companyId);
        if (Objects.isNull(refererInfo)) {
            return;
        }
        MoeEventReminder eventReminder = this.initializeEventReminder(refererInfo);
        eventReminder.setReminderType(BusinessReferralConst.REMINDER_TYPE_POPUPS);
        eventReminder.setReminderCode(BusinessReferralConst.REMINDER_CODE_FIRST_CHARGE_THIRD);
        eventReminderMapper.insertSelectiveIfNotExists(eventReminder);
    }

    public void addFirstCharge3TimesEventReminder(MoeReferralInfo refererInfo) {
        MoeEventReminder eventReminder = this.initializeEventReminder(refererInfo);
        eventReminder.setReminderType(BusinessReferralConst.REMINDER_TYPE_POPUPS);
        eventReminder.setReminderCode(BusinessReferralConst.REMINDER_CODE_FIRST_CHARGE_THIRD);
        eventReminderMapper.insertSelectiveIfNotExists(eventReminder);
    }

    public MoeEventReminder initializeEventReminder(MoeReferralInfo refererInfo) {
        MoeEventReminder eventReminder = new MoeEventReminder();
        eventReminder.setAccountId(refererInfo.getAccountId());
        eventReminder.setCompanyId(refererInfo.getCompanyId());
        eventReminder.setPageId(BusinessReferralConst.PAGE_ID_REFERRAL);
        eventReminder.setStatus(BusinessReferralConst.BONUS_PENDING);
        return eventReminder;
    }
}
