package com.moego.server.business.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.dto.StaffWorkingHourDetailDTO;
import com.moego.server.business.service.StaffWorkingHourService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/business/staff/working/hour")
public class StaffWorkingHourController {

    @Autowired
    private StaffWorkingHourService staffWorkingHourService;

    @GetMapping
    @Auth(AuthType.BUSINESS)
    public StaffWorkingHourDetailDTO getStaffWorkingHour(
            AuthContext context, @RequestParam("staffId") Integer staffId) {
        return staffWorkingHourService.getStaffWorkingHourDetail(context.companyId(), context.getBusinessId(), staffId);
    }

    @PutMapping
    @Auth(AuthType.BUSINESS)
    public void saveStaffWorkingHour(
            AuthContext context, @RequestBody @Valid StaffWorkingHourDetailDTO staffWorkingHourDetailDTO) {
        staffWorkingHourDetailDTO.setBusinessId(context.getBusinessId());
        staffWorkingHourService.saveStaffWorkingHour(context.companyId(), staffWorkingHourDetailDTO);
    }
}
