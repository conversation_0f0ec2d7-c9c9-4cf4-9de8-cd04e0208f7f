package com.moego.server.business.web.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StaffServiceArea {

    private Integer staffId;
    private String date;
    private Integer areaId;
    private Integer businessId;
    private Boolean isDefault;
    private String areaName;
    private String colorCode;
    private List<List<Double>> polygon;
}
