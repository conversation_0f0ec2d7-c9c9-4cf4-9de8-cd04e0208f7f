<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.business.mapper.MoeBusinessClockInOutLogMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.business.mapperbean.MoeBusinessClockInOutLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="clock_date" jdbcType="VARCHAR" property="clockDate" />
    <result column="clock_in_time" jdbcType="BIGINT" property="clockInTime" />
    <result column="clock_out_time" jdbcType="BIGINT" property="clockOutTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, account_id, staff_id, clock_date, clock_in_time, clock_out_time,
    update_time, status, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_business_clock_in_out_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_business_clock_in_out_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.business.mapperbean.MoeBusinessClockInOutLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business_clock_in_out_log (business_id, account_id, staff_id,
      clock_date, clock_in_time, clock_out_time,
      update_time, status, company_id
      )
    values (#{businessId,jdbcType=INTEGER}, #{accountId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER},
      #{clockDate,jdbcType=VARCHAR}, #{clockInTime,jdbcType=BIGINT}, #{clockOutTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{companyId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.business.mapperbean.MoeBusinessClockInOutLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business_clock_in_out_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="clockDate != null">
        clock_date,
      </if>
      <if test="clockInTime != null">
        clock_in_time,
      </if>
      <if test="clockOutTime != null">
        clock_out_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="clockDate != null">
        #{clockDate,jdbcType=VARCHAR},
      </if>
      <if test="clockInTime != null">
        #{clockInTime,jdbcType=BIGINT},
      </if>
      <if test="clockOutTime != null">
        #{clockOutTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.business.mapperbean.MoeBusinessClockInOutLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_clock_in_out_log
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="clockDate != null">
        clock_date = #{clockDate,jdbcType=VARCHAR},
      </if>
      <if test="clockInTime != null">
        clock_in_time = #{clockInTime,jdbcType=BIGINT},
      </if>
      <if test="clockOutTime != null">
        clock_out_time = #{clockOutTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.business.mapperbean.MoeBusinessClockInOutLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_clock_in_out_log
    set business_id = #{businessId,jdbcType=INTEGER},
      account_id = #{accountId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      clock_date = #{clockDate,jdbcType=VARCHAR},
      clock_in_time = #{clockInTime,jdbcType=BIGINT},
      clock_out_time = #{clockOutTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="selectByClockDate" resultType="com.moego.server.business.service.dto.ClockInOutLogDto">
        SELECT
        log.id AS logId,
        log.staff_id as staffId,
        log.clock_date as clockDate,
        staff.first_name as firstName,
        staff.last_name as lastName,
        log.clock_in_time as clockInTime,
        log.clock_out_time as clockOutTime
        FROM
        moe_business_clock_in_out_log log
        LEFT JOIN moe_staff staff ON log.staff_id = staff.id
        WHERE
        log.business_id in
        <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
            #{businessId}
        </foreach>
        and log.clock_date &gt;= #{startDate}
        and log.clock_date &lt;= #{endDate}
        and log.status = 1
        <if test="staffId != null">
            and log.staff_id = #{staffId}
        </if>
        ORDER BY
        log.clock_date desc,log.id desc
        <if test="limitOffset != null and pageSize != null">
          limit #{limitOffset},#{pageSize}
        </if>
    </select>

    <select id="calculateClockTimeByClockDate" resultType="long">
        SELECT SUM(clock_out_time - clock_in_time)
        FROM moe_business_clock_in_out_log log
        where log.business_id = #{businessId}
        and log.clock_date &gt;= #{startDate}
        and log.clock_date &lt;= #{endDate}
        and log.status = 1
        <if test="staffId != null">
            and log.staff_id = #{staffId}
        </if>
        and log.clock_in_time != 0
        and log.clock_out_time != 0
    </select>


    <select id="selectByClockDateCount" resultType="int">
        SELECT
         count(*) as count
        FROM
        moe_business_clock_in_out_log log
        LEFT JOIN moe_staff staff ON log.staff_id = staff.id
        WHERE
        log.business_id in
        <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
            #{businessId}
        </foreach>
        and log.clock_date &gt;= #{startDate}
        and log.clock_date &lt;= #{endDate}
        and log.status = 1
        <if test="staffId != null">
            and log.staff_id = #{staffId}
        </if>
    </select>

  <select id="selectStaffCountsByClockDate" resultType="com.moego.server.business.service.dto.ClockInOutLogCountDTO">
    SELECT staff_id as staffId, count(*) as count
    FROM moe_business_clock_in_out_log
    WHERE
      business_id = #{businessId}
      and clock_date &gt;= #{startDate}
      and clock_date &lt;= #{endDate}
      and status = 1
    <if test="staffIds != null and staffIds.size &gt; 0">
      and staff_id in
      <foreach close=")" collection="staffIds" item="staffId" open="(" separator=",">
        #{staffId}
      </foreach>
    </if>
    GROUP BY staff_id
  </select>

  <select id="selectLatestLog" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_business_clock_in_out_log
    WHERE business_id = #{businessId} and clock_date = #{clockDate} and staff_id = #{staffId} and status = 1
    ORDER BY update_time DESC limit 1
  </select>

  <select id="queryByBusinessIdAndDateRange" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_business.moe_business_clock_in_out_log
    where business_id = #{businessId}
    and clock_date &gt;= #{startDate}
    and clock_date &lt;= #{endDate}
    and status = 1
  </select>

  <select id="selectLatestUnfinishedLog" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_business_clock_in_out_log
    WHERE business_id = #{businessId}
      and staff_id = #{staffId}
      and clock_out_time = 0
      and status = 1
    ORDER BY update_time DESC limit 1
  </select>

</mapper>