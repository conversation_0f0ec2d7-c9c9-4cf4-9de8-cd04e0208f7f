package com.moego.server.business.service.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReferralBonusParams {

    @JsonIgnore
    private Integer referralId;

    @JsonIgnore
    private Integer bonusRuleId;

    @Schema(description = "公司ID")
    @NotNull
    private Integer companyId;

    @Schema(description = "bonus奖励ID")
    @NotNull
    private Integer bonusRewardId;
}
