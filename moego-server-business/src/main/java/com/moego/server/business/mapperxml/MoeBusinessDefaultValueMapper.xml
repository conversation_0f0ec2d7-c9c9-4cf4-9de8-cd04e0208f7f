<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.business.mapper.MoeBusinessDefaultValueMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.business.mapperbean.MoeBusinessDefaultValue">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="value1" jdbcType="VARCHAR" property="value1" />
    <result column="value2" jdbcType="VARCHAR" property="value2" />
    <result column="value3" jdbcType="VARCHAR" property="value3" />
    <result column="value4" jdbcType="VARCHAR" property="value4" />
    <result column="value5" jdbcType="VARCHAR" property="value5" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, type, value1, value2, value3, value4, value5, create_time, update_time,
    company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_business_default_value
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_business_default_value
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.business.mapperbean.MoeBusinessDefaultValue">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business_default_value (business_id, type, value1,
      value2, value3, value4,
      value5, create_time, update_time,
      company_id)
    values (#{businessId,jdbcType=INTEGER}, #{type,jdbcType=TINYINT}, #{value1,jdbcType=VARCHAR},
      #{value2,jdbcType=VARCHAR}, #{value3,jdbcType=VARCHAR}, #{value4,jdbcType=VARCHAR},
      #{value5,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT},
      #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.business.mapperbean.MoeBusinessDefaultValue">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business_default_value
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="value1 != null">
        value1,
      </if>
      <if test="value2 != null">
        value2,
      </if>
      <if test="value3 != null">
        value3,
      </if>
      <if test="value4 != null">
        value4,
      </if>
      <if test="value5 != null">
        value5,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="value1 != null">
        #{value1,jdbcType=VARCHAR},
      </if>
      <if test="value2 != null">
        #{value2,jdbcType=VARCHAR},
      </if>
      <if test="value3 != null">
        #{value3,jdbcType=VARCHAR},
      </if>
      <if test="value4 != null">
        #{value4,jdbcType=VARCHAR},
      </if>
      <if test="value5 != null">
        #{value5,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.business.mapperbean.MoeBusinessDefaultValue">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_default_value
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="value1 != null">
        value1 = #{value1,jdbcType=VARCHAR},
      </if>
      <if test="value2 != null">
        value2 = #{value2,jdbcType=VARCHAR},
      </if>
      <if test="value3 != null">
        value3 = #{value3,jdbcType=VARCHAR},
      </if>
      <if test="value4 != null">
        value4 = #{value4,jdbcType=VARCHAR},
      </if>
      <if test="value5 != null">
        value5 = #{value5,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.business.mapperbean.MoeBusinessDefaultValue">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_default_value
    set business_id = #{businessId,jdbcType=INTEGER},
      type = #{type,jdbcType=TINYINT},
      value1 = #{value1,jdbcType=VARCHAR},
      value2 = #{value2,jdbcType=VARCHAR},
      value3 = #{value3,jdbcType=VARCHAR},
      value4 = #{value4,jdbcType=VARCHAR},
      value5 = #{value5,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectDefaultFrequency" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_business_default_value
    where     type = 1
    and business_id = #{businessId}
  </select>

  <select id="selectShareUpcomingShowServicePrice" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_business_default_value
    where     type = 2
          and business_id = #{businessId}
  </select>

  <select id="selectShareUpcomingShowServicePriceByCompanyId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_business_default_value
    where     type = 2
    and company_id = #{companyId}
  </select>

  <select id="selectAllShareUpcomingShowServicePriceByCompanyId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_business_default_value
    where     type = 2
    and company_id = #{companyId}
  </select>
</mapper>
