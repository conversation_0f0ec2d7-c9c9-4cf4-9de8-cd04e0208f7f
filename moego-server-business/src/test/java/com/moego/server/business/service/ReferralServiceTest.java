package com.moego.server.business.service;

import com.moego.common.distributed.LockManager;
import com.moego.common.enums.BusinessReferralConst;
import com.moego.common.exception.CommonException;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.server.business.config.ReferralConfig;
import com.moego.server.business.dto.ReferralStatusDTO;
import com.moego.server.business.mapper.MoeCompanyMapper;
import com.moego.server.business.mapper.MoeReferralBonusRewardRuleMapper;
import com.moego.server.business.mapper.MoeReferralFixedRewardRuleMapper;
import com.moego.server.business.mapper.MoeReferralInfoMapper;
import com.moego.server.business.mapperbean.MoeCompany;
import com.moego.server.business.mapperbean.MoeReferralFixedRewardRule;
import com.moego.server.business.mapperbean.MoeReferralInfo;
import com.moego.server.business.params.CommonIdsParams;
import com.moego.server.business.params.ReferralInfoParams;
import com.moego.server.business.service.dto.referral.ReferralCountDTO;
import com.moego.server.business.web.vo.referral.ReferralInfoVO;
import com.moego.server.business.web.vo.referral.ReferralItemInfoVO;
import com.moego.server.payment.client.IPaymentStripeClient;
import com.moego.server.payment.client.IPaymentSubscriptionClient;
import com.moego.server.payment.dto.CompanyPermissionStateDto;
import com.moego.server.payment.dto.StripeCustomerDTO;
import com.moego.server.payment.dto.StripeInvoiceCountDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ReferralServiceTest {

    @InjectMocks
    private ReferralService referralService;

    @Mock
    private AccountService accountService;

    @Mock
    private MoeReferralInfoMapper referralInfoMapper;

    @Mock
    private ReferralRewardService referralRewardService;

    @Mock
    private IPaymentSubscriptionClient paymentSubscriptionClient;

    @Mock
    private IPaymentStripeClient paymentStripeClient;

    @Mock
    private MoeCompanyMapper companyMapper;

    @Mock
    private MoeReferralFixedRewardRuleMapper fixedRewardRuleMapper;

    @Mock
    private MoeReferralBonusRewardRuleMapper bonusRewardRuleMapper;

    @Mock
    private ReferralConfig referralConfig;

    @Mock
    private ReminderService reminderService;

    @Mock
    private ReferralRuleService referralRuleService;

    @Mock
    private ReferralSyncService referralSyncService;

    @Mock
    private LockManager lockManager;

    @Test
    public void generateReferralCode() {
        Mockito.doReturn(null).when(referralInfoMapper).selectByReferralCode(Mockito.anyString());

        String referralCode = referralService.generateReferralCode();
        Assertions.assertThat(referralCode.startsWith(BusinessReferralConst.CODE_PREFIX))
                .as("generate success")
                .isTrue();
    }

    @Test
    public void bindingReferral() {
        Integer referralId = 1;
        String referralCode = "MOE1234";
        MoeReferralInfo refereeInfo = new MoeReferralInfo();
        refereeInfo.setReferralId(0);
        MoeReferralInfo referralInfo = new MoeReferralInfo();
        referralInfo.setId(referralId);
        Mockito.doReturn(referralInfo).when(referralInfoMapper).selectByReferralCode(referralCode);

        referralService.bindingReferral(refereeInfo, referralCode);
        Assertions.assertThat(refereeInfo.getReferralId())
                .as("binding referral error")
                .isEqualTo(referralId);
    }

    @Test
    public void bindingReferralException() {
        String referralCode = "MOE1234";
        MoeReferralInfo refereeInfo = new MoeReferralInfo();
        refereeInfo.setReferralId(0);
        Mockito.doReturn(null).when(referralInfoMapper).selectByReferralCode(referralCode);
        Assertions.assertThatThrownBy(() -> referralService.bindingReferral(refereeInfo, referralCode))
                .isInstanceOf(CommonException.class)
                .hasMessageContaining("Illegal Referral Code");
    }

    @Test
    public void addReferralInfo() {
        String code = "MOE1234";
        ReferralInfoParams referralInfoParams = ReferralInfoParams.builder().build();
        referralInfoParams.setAccountId(123);
        referralInfoParams.setCompanyId(123);
        referralInfoParams.setStatus(BusinessReferralConst.REFERRAL_SUCCESS);
        referralInfoParams.setReferralCode(code);

        Mockito.doReturn(null).when(referralInfoMapper).selectByReferralCode(Mockito.anyString());
        Mockito.doReturn(1).when(referralInfoMapper).insertSelective(Mockito.any(MoeReferralInfo.class));
        Mockito.doReturn("123123").when(lockManager).getResourceKey(Mockito.anyString(), Mockito.anyString());
        Mockito.doReturn(true).when(lockManager).lockWithRetry(Mockito.anyString(), Mockito.anyString());

        referralService.addReferralInfo(referralInfoParams);
    }

    @Test
    public void addReferralInfoException() {
        ReferralInfoParams referralInfoParams = ReferralInfoParams.builder().build();
        Assertions.assertThatThrownBy(() -> referralService.addReferralInfo(referralInfoParams))
                .isInstanceOf(CommonException.class)
                .hasMessageContaining("accountId or companyId is null");
    }

    @Test
    public void updateReferralInfoNotExistsStatus() {
        ReferralInfoParams referralInfoParams =
                ReferralInfoParams.builder().companyId(123).accountId(123).build();
        Mockito.doReturn(null)
                .when(referralInfoMapper)
                .selectByCompanyIdAndAccountId(Mockito.anyInt(), Mockito.anyInt());
        Assertions.assertThatThrownBy(() -> referralService.updateReferralInfo(referralInfoParams))
                .isInstanceOf(CommonException.class)
                .hasMessageContaining("status is null");
    }

    @Test
    public void updateReferralInfoInitialize() {
        ReferralInfoParams referralInfoParams = ReferralInfoParams.builder()
                .companyId(123)
                .accountId(123)
                .status(BusinessReferralConst.REFERRAL_UPGRADED)
                .build();
        Mockito.doReturn(null)
                .when(referralInfoMapper)
                .selectByCompanyIdAndAccountId(Mockito.anyInt(), Mockito.anyInt());
        Mockito.doReturn(null).when(referralInfoMapper).selectByReferralCode(Mockito.anyString());
        Mockito.doReturn(1).when(referralInfoMapper).insertSelective(Mockito.any(MoeReferralInfo.class));
        Mockito.doReturn("123123").when(lockManager).getResourceKey(Mockito.anyString(), Mockito.anyString());
        Mockito.doReturn(true).when(lockManager).lockWithRetry(Mockito.anyString(), Mockito.anyString());

        referralService.updateReferralInfo(referralInfoParams);
    }

    @Test
    public void updateReferralInfo() {
        MoeReferralInfo refereeInfo = new MoeReferralInfo();
        refereeInfo.setReferralId(0);
        MoeReferralInfo refererInfo = new MoeReferralInfo();
        refererInfo.setId(1);
        ReferralInfoParams referralInfoParams = ReferralInfoParams.builder()
                .companyId(123)
                .accountId(123)
                .status(BusinessReferralConst.REFERRAL_UPGRADED)
                .referralCode("MOE1234")
                .build();
        Mockito.doReturn(refereeInfo)
                .when(referralInfoMapper)
                .selectByCompanyIdAndAccountId(Mockito.anyInt(), Mockito.anyInt());
        Mockito.doReturn(refererInfo).when(referralInfoMapper).selectByReferralCode(Mockito.anyString());
        Mockito.doReturn(1).when(referralInfoMapper).updateByPrimaryKeySelective(Mockito.any(MoeReferralInfo.class));

        referralService.updateReferralInfo(referralInfoParams);
        Assertions.assertThat(refereeInfo.getReferralId())
                .as("referral binding error")
                .isEqualTo(refererInfo.getId());
    }

    @Test
    public void getReferralListUpgraded() {
        Integer referralId = 1;
        Integer companyId = 123;
        Integer accountId = 123;
        Long beginDate = 1656640800L;
        List<MoeReferralInfo> referralInfoList = new ArrayList<>();
        MoeReferralInfo referralInfo = new MoeReferralInfo();
        referralInfo.setCompanyId(companyId);
        referralInfo.setAccountId(accountId);
        referralInfo.setStatus(BusinessReferralConst.REFERRAL_UPGRADED);
        referralInfoList.add(referralInfo);
        Mockito.doReturn(referralInfoList).when(referralInfoMapper).getReferralListByReferralId(referralId);
        List<CompanyPermissionStateDto> permissionStateDtoList = new ArrayList<>();
        CompanyPermissionStateDto permissionStateDto = new CompanyPermissionStateDto();
        permissionStateDto.setCompanyId(companyId);
        permissionStateDto.setCompanyId(companyId);
        permissionStateDto.setBeginDate(beginDate);
        permissionStateDtoList.add(permissionStateDto);
        Mockito.doReturn(permissionStateDtoList)
                .when(paymentSubscriptionClient)
                .getPermissionStateByCompanyIdList(Mockito.any(CommonIdsParams.class));
        List<Integer> companyIdList = new ArrayList<>();
        companyIdList.add(companyId);
        List<StripeInvoiceCountDTO> countDTOList = new ArrayList<>();
        StripeInvoiceCountDTO stripeInvoiceCountDTO = new StripeInvoiceCountDTO();
        stripeInvoiceCountDTO.setCompanyId(companyId);
        stripeInvoiceCountDTO.setChargeCount(1);
        countDTOList.add(stripeInvoiceCountDTO);
        Mockito.doReturn(countDTOList).when(paymentStripeClient).countPaidInvoiceByCompanyIdList(companyIdList);
        Map<Integer, AccountModel> accountMap = new HashMap<>();
        AccountModel accountModel = AccountModel.newBuilder().setId(accountId).build();
        accountMap.put(accountId, accountModel);
        List<Integer> accountIdList = new ArrayList<>();
        accountIdList.add(accountId);
        Mockito.doReturn(accountMap).when(accountService).getAccountByIdList(accountIdList);

        List<ReferralItemInfoVO> referralList = referralService.getReferralList(referralId);
        ReferralItemInfoVO referralItemInfoVO = referralList.get(0);
        Assertions.assertThat(referralItemInfoVO.getRemainDays())
                .as("upgraded referee remain days not equals 1")
                .isEqualTo(Integer.valueOf(1));
    }

    @Test
    public void getReferralListSuccess() {
        Integer referralId = 1;
        Integer companyId = 123;
        Integer accountId = 123;
        Integer chargeCount = 1;
        Long beginDate = 1656640800L;
        List<MoeReferralInfo> referralInfoList = new ArrayList<>();
        MoeReferralInfo referralInfo = new MoeReferralInfo();
        referralInfo.setCompanyId(companyId);
        referralInfo.setAccountId(accountId);
        referralInfo.setStatus(BusinessReferralConst.REFERRAL_SUCCESS);
        referralInfoList.add(referralInfo);
        Mockito.doReturn(referralInfoList).when(referralInfoMapper).getReferralListByReferralId(referralId);
        List<CompanyPermissionStateDto> permissionStateDtoList = new ArrayList<>();
        CompanyPermissionStateDto permissionStateDto = new CompanyPermissionStateDto();
        permissionStateDto.setCompanyId(companyId);
        permissionStateDto.setCompanyId(companyId);
        permissionStateDto.setBeginDate(beginDate);
        permissionStateDtoList.add(permissionStateDto);
        Mockito.doReturn(permissionStateDtoList)
                .when(paymentSubscriptionClient)
                .getPermissionStateByCompanyIdList(Mockito.any(CommonIdsParams.class));
        List<Integer> companyIdList = new ArrayList<>();
        companyIdList.add(companyId);
        List<StripeInvoiceCountDTO> countDTOList = new ArrayList<>();
        StripeInvoiceCountDTO stripeInvoiceCountDTO = new StripeInvoiceCountDTO();
        stripeInvoiceCountDTO.setCompanyId(companyId);
        stripeInvoiceCountDTO.setChargeCount(chargeCount);
        countDTOList.add(stripeInvoiceCountDTO);
        Mockito.doReturn(countDTOList).when(paymentStripeClient).countPaidInvoiceByCompanyIdList(companyIdList);
        Map<Integer, AccountModel> accountMap = new HashMap<>();
        AccountModel accountModel = AccountModel.newBuilder().setId(accountId).build();
        accountMap.put(accountId, accountModel);
        List<Integer> accountIdList = new ArrayList<>();
        accountIdList.add(accountId);
        Mockito.doReturn(accountMap).when(accountService).getAccountByIdList(accountIdList);

        List<ReferralItemInfoVO> referralList = referralService.getReferralList(referralId);
        ReferralItemInfoVO referralItemInfoVO = referralList.get(0);
        Assertions.assertThat(referralItemInfoVO.getChargeCount())
                .as("success referee remain days not equals 1")
                .isEqualTo(chargeCount);
    }

    @Test
    public void initializeReferralInfo() {
        ReferralInfoParams referralInfoParams =
                ReferralInfoParams.builder().companyId(123).accountId(123).build();
        CompanyPermissionStateDto permissionStateDto = new CompanyPermissionStateDto();
        permissionStateDto.setLevel(1);
        Mockito.doReturn(permissionStateDto)
                .when(paymentSubscriptionClient)
                .getPermissionStateByCompanyId(referralInfoParams.getCompanyId());
        MoeReferralInfo referralInfo = new MoeReferralInfo();
        referralInfo.setId(1);
        referralInfo.setStatus(BusinessReferralConst.REFERRAL_UPGRADED);
        Mockito.doReturn(1).when(referralInfoMapper).insertSelective(Mockito.any(MoeReferralInfo.class));
        Mockito.doReturn(referralInfo).when(referralInfoMapper).selectByPrimaryKey(null);
        Mockito.doReturn("123123").when(lockManager).getResourceKey(Mockito.anyString(), Mockito.anyString());
        Mockito.doReturn(true).when(lockManager).lockWithRetry(Mockito.anyString(), Mockito.anyString());

        MoeReferralInfo moeReferralInfo = referralService.initializeReferralInfo(referralInfoParams);
        Assertions.assertThat(moeReferralInfo.getStatus())
                .as("isSubscribing status not equals REFERRAL_UPGRADED")
                .isEqualTo(referralInfo.getStatus());
    }

    @Test
    public void getRefererInfo() {
        ReferralInfoParams referralInfoParams =
                ReferralInfoParams.builder().companyId(123).accountId(123).build();
        MoeReferralInfo refererInfo = new MoeReferralInfo();
        refererInfo.setTotalEarning(BigDecimal.valueOf(200));

        Mockito.doReturn(refererInfo)
                .when(referralInfoMapper)
                .selectByCompanyIdAndAccountId(referralInfoParams.getCompanyId(), referralInfoParams.getAccountId());
        StripeCustomerDTO stripeCustomerDTO = new StripeCustomerDTO();
        // cents
        stripeCustomerDTO.setBalance(BigDecimal.valueOf(-10000));
        Mockito.doReturn(stripeCustomerDTO)
                .when(paymentStripeClient)
                .getStripeCustomerBalance(referralInfoParams.getCompanyId());
        BigDecimal earnReward = BigDecimal.valueOf(50);
        Mockito.doReturn(earnReward)
                .when(referralRewardService)
                .getEarnRewardAndChecked(referralInfoParams.getCompanyId());

        List<MoeReferralFixedRewardRule> fixedRuleList = new ArrayList<>();
        MoeReferralFixedRewardRule first = new MoeReferralFixedRewardRule();
        BigDecimal firstAmount = BigDecimal.valueOf(40);
        first.setFixedAmount(firstAmount);
        MoeReferralFixedRewardRule common = new MoeReferralFixedRewardRule();
        BigDecimal fixedAmount = BigDecimal.valueOf(25);
        common.setFixedAmount(fixedAmount);
        fixedRuleList.add(first);
        fixedRuleList.add(common);
        Mockito.doReturn(fixedRuleList).when(referralRuleService).getActiveFixedRule();

        ReferralInfoVO referralInfoVO = referralService.getRefererInfo(referralInfoParams);
        Assertions.assertThat(referralInfoVO.getTotalEarning().subtract(earnReward))
                .as("last earning not equals")
                .isEqualTo(referralInfoVO.getLastEarning());
        Assertions.assertThat(referralInfoVO.getFirstFixedReward())
                .as("first fixed reward not equals 40")
                .isEqualTo(firstAmount);
        Assertions.assertThat(referralInfoVO.getFixedReward())
                .as("fixed reward not equals 25")
                .isEqualTo(fixedAmount);
    }

    @Test
    public void testGetRefererInfo() {
        Mockito.doReturn(null)
                .when(referralInfoMapper)
                .selectByCompanyIdAndAccountId(Mockito.anyInt(), Mockito.anyInt());
        referralService.getRefererInfo(Mockito.anyInt(), Mockito.anyInt());
    }

    @Test
    public void getRefereeInfoMap() {
        Byte status = BusinessReferralConst.REFERRAL_UPGRADED;
        Integer companyId = 123;
        Integer accountId = 321;
        List<Integer> companyIdList = new ArrayList<>();
        companyIdList.add(companyId);
        List<MoeReferralInfo> refereeList = new ArrayList<>();
        MoeReferralInfo refereeInfo = new MoeReferralInfo();
        refereeInfo.setCompanyId(companyId);
        refereeInfo.setAccountId(accountId);
        refereeList.add(refereeInfo);
        Mockito.doReturn(refereeList).when(referralInfoMapper).getReferralListByStatus(status, companyIdList);
        List<MoeCompany> companyList = new ArrayList<>();
        MoeCompany company = new MoeCompany();
        company.setId(companyId);
        company.setAccountId(accountId);
        companyList.add(company);
        Mockito.doReturn(companyList).when(companyMapper).getCompanyByIdList(companyIdList);
        Map<Integer, MoeReferralInfo> refereeInfoMap = referralService.getRefereeInfoMap(status, companyIdList);

        Assertions.assertThat(refereeInfoMap.size())
                .as("referee size not equals 1")
                .isEqualTo(1);
    }

    @Test
    public void getRefererInfoMap() {
        List<Integer> idList = new ArrayList<>();
        idList.add(123);
        List<MoeReferralInfo> referralInfoList = new ArrayList<>();
        referralInfoList.add(new MoeReferralInfo());
        Mockito.doReturn(referralInfoList).when(referralInfoMapper).getReferralListByIdList(idList);

        Map<Integer, MoeReferralInfo> refererInfoMap = referralService.getRefererInfoMap(idList);
        Assertions.assertThat(refererInfoMap.size())
                .as("referer size not equals 1")
                .isEqualTo(1);
    }

    @Test
    public void getReferralStatusCountMap() {
        Byte status = BusinessReferralConst.REFERRAL_SUCCESS;
        int count = 1;
        Integer referralIdExist = 123;
        Integer referralIdNotExist = 321;
        List<Integer> referralIdList = new ArrayList<>();
        referralIdList.add(referralIdExist);
        referralIdList.add(referralIdNotExist);
        List<ReferralCountDTO> referralCountDTOList = new ArrayList<>();
        ReferralCountDTO referralCountDTO = new ReferralCountDTO();
        referralCountDTO.setCount(count);
        referralCountDTO.setReferralId(referralIdExist);
        referralCountDTOList.add(referralCountDTO);
        Mockito.doReturn(referralCountDTOList).when(referralInfoMapper).countGroupByReferralId(referralIdList, status);

        Map<Integer, AtomicInteger> referralStatusCountMap =
                referralService.getReferralStatusCountMap(referralIdList, status);
        AtomicInteger one = referralStatusCountMap.get(referralIdExist);
        Assertions.assertThat(one.get()).as("count not equals 1").isEqualTo(count);
        AtomicInteger zero = referralStatusCountMap.get(referralIdNotExist);
        Assertions.assertThat(zero.get()).as("count not equals 0").isEqualTo(0);
    }

    @Test
    public void isValidReferralCodeUnbindExists() {
        Integer referralId = 0;
        MoeReferralInfo refereeInfo = new MoeReferralInfo();
        refereeInfo.setReferralId(referralId);
        refereeInfo.setReferralCode("MOE4321");
        ReferralInfoParams referralInfoParams = ReferralInfoParams.builder().build();
        referralInfoParams.setReferralCode("MOE1234");
        Mockito.doReturn(Mockito.mock(MoeReferralInfo.class))
                .when(referralInfoMapper)
                .selectByReferralCode(referralInfoParams.getReferralCode());
        boolean isValid = referralService.isValidReferralCode(refereeInfo, referralInfoParams);
        Assertions.assertThat(isValid).as("isValid must be true").isTrue();
        Mockito.verify(referralInfoMapper, Mockito.never()).selectByPrimaryKey(referralId);
    }

    @Test
    public void isValidReferralCodeUnbindNotExists() {
        Integer referralId = 0;
        MoeReferralInfo refereeInfo = new MoeReferralInfo();
        refereeInfo.setReferralId(referralId);
        refereeInfo.setReferralCode("MOE4321");
        ReferralInfoParams referralInfoParams = ReferralInfoParams.builder().build();
        referralInfoParams.setReferralCode("MOE1234");
        boolean isValid = referralService.isValidReferralCode(refereeInfo, referralInfoParams);
        Assertions.assertThat(isValid).as("isValid must be false").isFalse();
        Mockito.verify(referralInfoMapper, Mockito.never()).selectByPrimaryKey(referralId);
    }

    @Test
    public void isValidReferralCodeBindMyself() {
        Integer referralId = 0;
        MoeReferralInfo refereeInfo = new MoeReferralInfo();
        refereeInfo.setReferralId(referralId);
        refereeInfo.setReferralCode("MOE1234");
        ReferralInfoParams referralInfoParams = ReferralInfoParams.builder().build();
        referralInfoParams.setReferralCode("MOE1234");
        boolean isValid = referralService.isValidReferralCode(refereeInfo, referralInfoParams);
        Assertions.assertThat(isValid).as("isValid must be false").isFalse();
        Mockito.verify(referralInfoMapper, Mockito.never()).selectByPrimaryKey(referralId);
    }

    @Test
    public void isValidReferralCodeTrue() {
        String referralCode = "MOE1234";
        Integer referralId = 1;
        MoeReferralInfo refererInfo = new MoeReferralInfo();
        refererInfo.setReferralCode(referralCode);
        Mockito.doReturn(refererInfo).when(referralInfoMapper).selectByPrimaryKey(referralId);
        MoeReferralInfo refereeInfo = new MoeReferralInfo();
        refereeInfo.setReferralId(referralId);
        ReferralInfoParams referralInfoParams =
                ReferralInfoParams.builder().referralCode(referralCode).build();
        boolean isValid = referralService.isValidReferralCode(refereeInfo, referralInfoParams);
        Assertions.assertThat(isValid).as("isValid must be true").isTrue();
    }

    @Test
    public void isValidReferralCodeFalse() {
        String referralCode = "MOE1234";
        Integer referralId = 1;
        MoeReferralInfo refererInfo = new MoeReferralInfo();
        refererInfo.setReferralCode(referralCode);
        Mockito.doReturn(refererInfo).when(referralInfoMapper).selectByPrimaryKey(referralId);
        MoeReferralInfo refereeInfo = new MoeReferralInfo();
        refereeInfo.setReferralId(referralId);
        ReferralInfoParams referralInfoParams =
                ReferralInfoParams.builder().referralCode("MOE4321").build();
        boolean isValid = referralService.isValidReferralCode(refereeInfo, referralInfoParams);
        Assertions.assertThat(isValid).as("isValid must be false").isFalse();
    }

    @Test
    public void getReferralStatusValidTrue() {
        ReferralInfoParams referralInfoParams = ReferralInfoParams.builder().build();
        referralInfoParams.setCompanyId(123);
        referralInfoParams.setAccountId(123);
        Mockito.doReturn(0).when(paymentSubscriptionClient).countRecordListByCompanyId(Mockito.anyInt());
        MoeReferralInfo refereeInfo = new MoeReferralInfo();
        Mockito.doReturn(refereeInfo)
                .when(referralInfoMapper)
                .selectByCompanyIdAndAccountId(Mockito.anyInt(), Mockito.anyInt());
        CompanyPermissionStateDto permissionStateDto = new CompanyPermissionStateDto();
        permissionStateDto.setLevel(1);
        Mockito.doReturn(permissionStateDto)
                .when(paymentSubscriptionClient)
                .getPermissionStateByCompanyId(Mockito.anyInt());

        ReferralStatusDTO referralStatus = referralService.getReferralStatus(referralInfoParams);
        Assertions.assertThat(referralStatus.getIsPurchased())
                .as("isPurchased must be false")
                .isFalse();
        Assertions.assertThat(referralStatus.getIsValid())
                .as("isValid must be false")
                .isFalse();
        Assertions.assertThat(referralStatus.getIsSubscribing())
                .as("isSubscribing must be true")
                .isTrue();
    }

    @Test
    public void getReferralStatusValidFalse() {
        String referralCode = "MOE1234";
        ReferralInfoParams referralInfoParams = ReferralInfoParams.builder().build();
        referralInfoParams.setCompanyId(123);
        referralInfoParams.setAccountId(123);
        referralInfoParams.setReferralCode(referralCode);
        Mockito.doReturn(1).when(paymentSubscriptionClient).countRecordListByCompanyId(Mockito.anyInt());
        MoeReferralInfo refereeInfo = new MoeReferralInfo();
        refereeInfo.setReferralId(1);
        MoeReferralInfo refererInfo = new MoeReferralInfo();
        refererInfo.setReferralCode("MOE4321");
        Mockito.doReturn(refererInfo).when(referralInfoMapper).selectByPrimaryKey(refereeInfo.getReferralId());
        Mockito.doReturn(refereeInfo)
                .when(referralInfoMapper)
                .selectByCompanyIdAndAccountId(Mockito.anyInt(), Mockito.anyInt());
        Mockito.doReturn(null).when(paymentSubscriptionClient).getPermissionStateByCompanyId(Mockito.anyInt());

        ReferralStatusDTO referralStatus = referralService.getReferralStatus(referralInfoParams);
        Assertions.assertThat(referralStatus.getIsPurchased())
                .as("isPurchased must be true")
                .isTrue();
        Assertions.assertThat(referralStatus.getIsValid())
                .as("isValid must be false")
                .isFalse();
        Assertions.assertThat(referralStatus.getIsSubscribing())
                .as("isSubscribing must be false")
                .isFalse();
    }

    @Test
    public void syncReferralEndStatus() {
        Integer companyId = 234;
        List<MoeReferralInfo> refereeList = new ArrayList<>();
        MoeReferralInfo referralInfo = new MoeReferralInfo();
        referralInfo.setCompanyId(companyId);
        refereeList.add(referralInfo);
        Mockito.doReturn(refereeList)
                .when(referralInfoMapper)
                .getReferralListByStatus(Mockito.any(), Mockito.anyList());
        Mockito.doReturn(Collections.singletonList(companyId))
                .when(paymentStripeClient)
                .getPaidGteCountByCompanyIdList(Mockito.anyList(), Mockito.anyInt());
        Mockito.doNothing().when(reminderService).addFirstCharge3TimesEventReminder(companyId);
        referralService.syncReferralEndStatus(companyId);
        Mockito.verify(reminderService).addFirstCharge3TimesEventReminder(companyId);
    }

    @Test
    public void syncReferralEndStatusEmpty() {
        Integer companyId = 1;
        referralService.syncReferralEndStatus(companyId);
        Mockito.verify(reminderService).addCommonDotEventReminder(companyId);
    }

    @Test
    public void cancelReferral() {
        Integer companyId = 123;
        MoeCompany company = new MoeCompany();
        company.setId(companyId);
        Mockito.doReturn(company).when(companyMapper).selectByPrimaryKey(companyId);
        referralService.cancelReferral(companyId);
    }

    @Test
    public void activeReferral() {
        Integer companyId = 123;
        MoeCompany company = new MoeCompany();
        company.setId(companyId);
        Mockito.doReturn(company).when(companyMapper).selectByPrimaryKey(companyId);
        referralService.activeReferral(companyId);
    }

    @Test
    public void resetReferralBalance() {
        Integer companyId = 123;
        Integer accountId = 123;
        referralService.resetReferralBalance(companyId, accountId);
    }

    @Test
    public void syncSuccessToOver3MonthCountEmpty() {
        List<Integer> companyIdList = new ArrayList<>();
        companyIdList.add(123);
        List<Integer> emptyList = referralService.syncSuccessToOver3Month(companyIdList);
        Assertions.assertThat(emptyList).isEmpty();
    }

    @Test
    public void syncSuccessToOver3MonthPaidEmpty() {
        Integer companyId = 123;
        List<Integer> companyIdList = new ArrayList<>();
        companyIdList.add(companyId);
        List<MoeReferralInfo> refereeList = new ArrayList<>();
        MoeReferralInfo referralInfo = new MoeReferralInfo();
        referralInfo.setCompanyId(companyId);
        refereeList.add(referralInfo);
        Mockito.doReturn(refereeList)
                .when(referralInfoMapper)
                .getReferralListByStatus(BusinessReferralConst.REFERRAL_SUCCESS, companyIdList);
        List<Integer> emptyList = referralService.syncSuccessToOver3Month(companyIdList);
        Assertions.assertThat(emptyList).isEmpty();
    }

    @Test
    public void syncUpgradedToSuccess() {
        Integer companyId = 123;
        List<Integer> companyIdList = new ArrayList<>();
        companyIdList.add(companyId);
        referralService.syncUpgradedToSuccess(companyIdList);
    }
}
