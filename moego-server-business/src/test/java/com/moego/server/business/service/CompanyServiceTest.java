package com.moego.server.business.service;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 * @since 2022/8/3
 */
@ExtendWith(MockitoExtension.class)
public class CompanyServiceTest {

    @InjectMocks
    private CompanyService companyService;

    @Test
    public void queryCompanyRemainVanNumByCompanyId() {
        Integer companyId = 0;
        Integer maxVansNum = companyService.queryCompanyRemainVanNumByCompanyId(companyId);
        Assertions.assertThat(maxVansNum).as("max vans nums not 0").isEqualTo(0);
    }
}
