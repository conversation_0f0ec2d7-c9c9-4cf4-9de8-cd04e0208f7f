package com.moego.lib.actimvemq.autoconfigure;

import static com.moego.lib.actimvemq.autoconfigure.ActiveMQProperties.PREFIX;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(PREFIX)
public class ActiveMQProperties {

    public static final String PREFIX = "spring.activemq";

    private boolean enabled = false;
    /**
     * The prefix of the destination name.
     */
    private String destinationPrefix;

    private String brokerUrl;

    private String user;
    private String password;
}
