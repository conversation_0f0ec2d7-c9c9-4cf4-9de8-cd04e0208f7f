# `moego-bff` 使用最新的 `@moego/api-node`

-   安装最新的 `@moego/api-node` 包：`npx pnpm@9 i @moego/api-node@your-branch-name`
-   引入想要使用的 `serviceClient`，比如:

```tsx
import { AccountServiceClient } from '@moego/api-node/serviceClient';

app.openapi(validateAccountRoute, async (c) => {
    const { id, password } = c.req.valid('json');
    try {
        const { correct } = await AccountServiceClient.validatePassword({
            id,
            password: password,
        });
        return c.json({ correct }, Code.SUCCESS);
    } catch (e) {
        return c.json({ error: e }, Code.SUCCESS);
    }
});
```

# 若 serviceClient 未找到

后端 api-v3 项目使用了 [application.yaml](/templates/node/application.yaml) 手动维护 `service` 和 `authority` 的映射关系，前端在 GitHub Actions 构建时会自动据此生成 `templates/node/serviceClient.ts`，并发布到 NPM 源。

若后端没有修改 YAML 文件，前端也无法生成对应的 serviceClient。此时需要通知后端相关的研发来更新。
