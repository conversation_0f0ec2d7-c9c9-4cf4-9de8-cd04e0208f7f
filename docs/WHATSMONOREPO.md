# 什么是Mono-repo

Mono-repo 是一种将多个相关或依赖的代码模块集中到单一代码仓库中进行管理的软件开发模式。与 multi-repo 不同，它将所有模块和服务统一到一个仓库中，使得代码共享和协作更加高效。

## 代码管理的三个阶段

1. **单仓库应用：**一个 Git 仓库维护着项目代码，随着迭代业务复杂度的提升，项目代码会变得越来越多，越来越复杂，大量代码构建效率也会降低，最终导致了单体巨石应用，这种代码管理方式称之为 Monolith。
2. **多仓库多模块：**于是将项目拆解成多个业务模块，并在多个 Git 仓库管理，模块解耦，降低了巨石应用的复杂度，每个模块都可以独立编码、测试、发版，代码管理变得简化，构建效率也得以提升，这种代码管理方式称之为 MultiRepo。
3. **单仓库多模块应用：**随着业务复杂度的提升，模块仓库越来越多，MultiRepo这种方式虽然从业务上解耦了，但增加了项目工程管理的难度，随着模块仓库达到一定数量级，会有几个问题：跨仓库代码难共享；分散在单仓库的模块依赖管理复杂（底层模块升级后，其他上层依赖需要及时更新，否则有问题）；增加了构建耗时。于是将多个项目集成到一个仓库下，共享工程配置，同时又快捷地共享模块代码，成为趋势，这种代码管理方式称之为 MonoRepo。

# 背景&&目的

目前，MoeGo 项目采用 multi-repo 模式，结合作者以往的经验以及近一年的实际体验，这种模式存在许多不便之处。以下是作者以及周边同事在讨论中总结出的主要痛点：

1. **需求变更影响范围广，协作效率低**
   1. **影响多个仓库**：一个功能需求的变更通常会波及 5-6 个仓库，每个仓库都需要单独提 PR，增加了工作量。
   2. **依赖顺序限制**：PR 的合入必须严格遵循依赖关系，这往往导致开发人员需要额外花费时间协调和等待，严重影响开发节奏。
   3. **沟通成本高**：涉及多个仓库时，团队成员需要频繁沟通，以确保不同仓库的变更保持一致。
2. **Go 项目的依赖管理复杂**
   1. **模块版本维护困难**：由于 Go 项目使用 go mod 管理依赖，不同仓库之间的模块版本经常发生冲突。当某个仓库的依赖发生变更时，其他仓库可能需要手动更新模块版本，增加了维护成本。
   2. **无法原子性更新**：多仓库依赖的场景下，更新某一模块后无法一次性确保所有依赖模块同步更新，可能导致版本不一致问题。
3. **CI/CD** **流程复杂且脆弱**
   1. **构建与部署成本高，**每个仓库需要单独维护 CI/CD 流程，导致重复劳动。某些仓库的 CI/CD 流程依赖其他仓库的变更，可能导致失败的传递效应，增加调试难度。多仓库模式下，跨模块的集成测试需要额外设计和维护测试脚本，且难以保证全面覆盖。
4.  **代码管理与团队协作难度增加**
   1. **代码分散**：代码分布在多个仓库，难以全局管理和审视系统的整体架构。并且，由于多仓库之间代码共享机制不足，可能导致重复代码的产生和维护问题。当需要分析某个功能时，必须跨多个仓库查看上下游调用关系，难以直观追踪代码逻辑。
5. **统一标准难以推行**
   1. **代码风格与工具配置不一致**：多仓库模式导致代码风格和开发工具配置难以统一，增加了新人上手的成本。

总结来看，multi-repo 模式在 MoeGo 项目中带来的主要问题集中在 **开发效率**、**维护成本** 和 **协作复杂性** 上，尤其是在依赖关系和版本管理方面表现尤为突出。

因此，个人认为，有必要考虑将项目迁移至 mono-repo 模式，以简化流程、提高效率并降低协作成本。

## 比较Mono-repo/Mulit-repo的优劣

| 场景       | Mulit-repo                                                                                                                                                                                                                                                | Mono-repo                                                                                                                                                                                                                                                   |
| :--------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 依赖管理   | ❌ 多个仓库都有自己的 go.mod，不仅存在依赖重复安装情况，占用磁盘内存大。在依赖版本上也不好管理，常有更改一个依赖需要升级一堆下游的情况                                                                                                                     | ✅ 多项目代码都在一个仓库中，只有顶层有一个go.mod控制全局版本和全局依赖，相同依赖在整个仓库中只安装一次，节省磁盘内存，并且升级依赖和语言版本也很方便                                                                                                        |
| 代码可见性 | ✅ 代码隔离，研发者只需关注自己负责的仓库❌ 包管理按照各自owner划分，当出现问题时，需要到依赖包中进行判断并解决。                                                                                                                                           | ✅ 一个仓库中多个相关项目，很容易看到整个代码库的变化趋势，更好的团队协作。❌ 增加了非owner改动代码的风险                                                                                                                                                     |
| 开发迭代   | ✅ 仓库体积小，模块划分清晰，可维护性强。 ❌ 多仓库来回切换（编辑器及命令行），项目多的话效率很低。多仓库之间存在依赖时，需要手动 go mod，操作繁琐。 ❌ 依赖管理不便，多个依赖可能在多个仓库中存在不同版本，重复安装，go mod tidy 时不同项目的依赖会存在冲突 | ✅ 多个项目都在一个仓库中，可看到相关项目全貌，编码非常方便。 ✅ 代码复用高，方便进行代码重构。 ❌ 多项目在一个仓库中，代码体积多大几个 G，`git clone`时间较长。 ✅ 依赖调试方便，依赖包迭代场景下，借助工具自动 go mod，直接使用最新版本依赖，简化了操作流程。 |
| 工程配置   | ❌ 各项目构建、打包、代码校验都各自维护，不一致时会导致代码差异或构建差异。                                                                                                                                                                                | ✅ 多项目在一个仓库，工程配置一致，代码质量标准及风格也很容易一致。                                                                                                                                                                                          |
| 构建部署   | ❌ 多个项目间存在依赖，部署时需要手动到不同的仓库根据先后顺序去修改版本及进行部署，操作繁琐效率低。                                                                                                                                                        | ✅ 构建性 Monorepo 工具可以配置依赖项目的构建优先级，可以实现一次命令完成所有的部署。                                                                                                                                                                        |
| 代码权限   | ✅ 各项目单独仓库，不会出现代码被误改的情况，单个项目出现问题不会影响其他项目。                                                                                                                                                                            | ❌ 多个项目代码都在一个仓库中，没有项目粒度的权限管控，一个项目出问题，可能影响所有项目。                                                                                                                                                                    |

## 使用Mono-repo目的是什么

1. **提升开发效率**
   1. **单一代码库管理**：将多个仓库的代码整合到一个代码库中，减少跨仓库操作的繁琐流程。开发人员可以一次性提交和审核涉及多个模块的改动，无需等待依赖 PR 的合入。
   2. **依赖管理统一化**：消除多仓库之间的模块版本冲突问题，所有模块在同一代码库中共享依赖，可通过一次提交同时更新依赖关系，保证版本一致性。并且改动可以在一个代码库中直接完成，跨模块的上下游变更变得更直观。
2. **优化** **CI/CD** **流程**
   1. **统一的流水线配置**：通过 mono-repo 模式，可以集中管理构建、测试和部署流程，减少重复配置的工作量。多个模块的变更可以统一构建和测试，避免多仓库下需要单独触发 CI/CD 的额外开销。
   2. 并且，集成测试覆盖整个代码库的所有模块，确保变更能够一次性通过所有必要的验证流程，提高发布质量和效率。
3. **强化系统的整体可维护性**
   1. 所有代码、依赖和文档集中在一个仓库中，便于维护和全局优化系统架构。通过 mono-repo，可以更方便地实现代码共享和复用，避免多仓库中相似逻辑的重复实现。集中代码后，可以更好地明确模块间的边界和依赖关系，从而优化架构设计。
4. **提高团队协作与规范化水平**
   1. 在 mono-repo 中，代码风格、工具配置和工作流可以实现完全统一，降低协作成本。新成员只需熟悉一个代码库的结构，即可快速理解项目全貌，减少学习成本。
5. **增强版本控制能力**
   1. mono-repo 模式下，所有变更记录集中管理，便于快速回溯问题来源，多模块版本可以同步发布，避免多仓库中版本更新的漏项或不一致问题。

# Moego mono-repo实践

项目地址：https://github.com/MoeGolibrary/moego

### 项目结构：

```Go
.
├── backend           # 后端项目
│   ├── app          # 微服务代码
│   ├── common       # 公共库
│   ├── docker       # Docker配置
│   ├── test         # API测试代码
│   ├── tools        # 工具代码
│   └── proto        # Proto文件
├── bazel            # Bazel相关
├── scripts          # 脚本
└── template         # 项目模板
```

### 模块依赖管理：

#### 项目依赖

项目使用`bazel` 进行依赖管理，bazel提供了高效的构建和测试功能，并且能进行依赖追踪，确保代码变更只会触发相关部分的重建和测试，并且bazel支持增量构建和并行构建，尤其适合mono-repo的模式

```Python
module(
    name = "moego",
    version = "0.1",
)

# 核心依赖
bazel_dep(name = "rules_go", version = "0.50.1")
bazel_dep(name = "gazelle", version = "0.40.0")
bazel_dep(name = "grpc", version = "1.66.0")
bazel_dep(name = "protobuf", version = "29.1")
```

#### Go依赖

使用`go.mod` 管理 Go 模块依赖

```Go
module github.com/MoeGolibrary/moego

go 1.23.2

require (
        github.com/MoeGolibrary/go-lib v0.0.0-20241205101454-e57da08d8a43
        github.com/MoeGolibrary/moego-api-definitions v0.0.0-20241206063100-271ac513fe8a
        github.com/bytedance/sonic v1.12.5
        github.com/envoyproxy/protoc-gen-validate v1.1.0
        github.com/google/uuid v1.6.0
        github.com/spf13/cast v1.3.1
        github.com/stretchr/testify v1.9.0
        go.uber.org/automaxprocs v1.6.0
        go.uber.org/zap v1.27.0
        google.golang.org/grpc v1.68.0
        google.golang.org/protobuf v1.35.2
        gopkg.in/DataDog/dd-trace-go.v1 v1.69.1
        gopkg.in/yaml.v3 v3.0.1
        gorm.io/driver/mysql v1.5.7
        gorm.io/driver/postgres v1.5.9
        gorm.io/gorm v1.25.11
)
```

通过`gazelle` 自动同步`go.mod` 到 `bazel`

```Bash
# 更新BUILD文件,
make gazelle mode=(all|update|diff) 
```

### 构建 && CI

项目采用`bazel` 构建，对比原有`go build`, `bazel` 可以以最小目录级别去查找依赖，当某个**文件**发生改动时，bazel可以以该文件为root结点向下递归，找到最小依赖项(最小叶子节点), 然后从最小叶子节点开始编译，并且仅编译递归到达的文件。

```Plain
    A               A包依赖BC两个目录, BC目录依赖D,E,F,G四个目录, 而G又刚好是proto目录
   / \              此时项目中只有hello.proto, 还没有编译成go\valida\grpc等文件
  B   C             但是bazel会在本次编译周期中先从G开始编译, 并生成go文件放入临时目录中
 / \ / \            然后再编译其他包
D  E F  G (proto)
```

通过此方式，代码发生改动时发生的编译都是最小粒度。bazel也支持 remote cache，通过S3等方式，可以将构建缓存在远端，不管是CI编译还是本地编译，只有第一次clone and build 会花上一些时间，之后的每一次编译都非常迅速，极大加快开发、构建、部署的效率。