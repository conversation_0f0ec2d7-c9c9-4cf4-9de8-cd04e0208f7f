package translator_test

import (
	"context"
	"fmt"
	"os"
	"testing"

	"github.com/stretchr/testify/require"
	yaml "sigs.k8s.io/yaml/goyaml.v3"

	"github.com/MoeGolibrary/moego/cli/devops_executor/logic/translator"
)

func TestTranslateKustomization(t *testing.T) {
	err := translator.TranslateKustomization(context.Background(), translator.TranslateKustomizationParams{
		File:      "non-existent",
		Cluster:   "development",
		Namespace: "ns-testing",
		Repo:      "moego-server-grooming",
		Branch:    "main",
	})
	require.Equal(t, true, err != nil)

	notExistsLabel := "unavailable/key=uninvalid_value"
	require.Equal(t, true, compareLabelSelector("main", notExistsLabel))
	require.Equal(t, true, compareLabelSelector("staging", notExistsLabel))
	require.Equal(t, true, compareLabelSelector("production", notExistsLabel))
	require.Equal(t, true, compareLabelSelector("feature-xxx", "branch=feature-xxx"))
	require.Equal(t, true, compareLabelSelector("feature-demo-xxx", notExistsLabel))
	require.Equal(t, true, compareLabelSelector("bugfix-xxx", "branch=bugfix-xxx"))
	require.Equal(t, true, compareLabelSelector("bugfix-demo-xxx", "branch=bugfix-demo-xxx"))

	os.RemoveAll("testdata/output")
}

func compareLabelSelector(branch string, expected string) bool {
	resourcesDir := "testdata"
	targetFilename := "kustomization.yaml"
	outputDir := resourcesDir + "/output"
	outputPath := outputDir + "/" + targetFilename
	sourcePath := resourcesDir + "/" + targetFilename
	err := translator.TranslateKustomization(context.Background(), translator.TranslateKustomizationParams{
		File:      sourcePath,
		Output:    outputPath,
		Cluster:   "development",
		Namespace: "ns-testing",
		Repo:      "moego-server-grooming",
		Branch:    branch,
	})
	if err != nil {
		fmt.Println("translate kustomization failed:", err)
		return false
	}

	return expected == takeLabelSelector(outputPath)
}

func takeLabelSelector(file string) string {
	data, _ := os.ReadFile(file)
	var value map[string]any
	if err := yaml.Unmarshal(data, &value); err != nil {
		return ""
	}

	patches, ok := value["patches"].([]any)
	if ok {
		for _, p := range patches {
			patch, ok := p.(map[string]any)
			if ok {
				target, ok := patch["target"].(map[string]any)
				if ok {
					if labelSelector, exists := target["labelSelector"].(string); exists {
						return labelSelector
					}
				}
			}
		}
	}

	return ""
}
