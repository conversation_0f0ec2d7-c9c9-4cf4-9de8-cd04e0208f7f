package translator

import (
	"github.com/spf13/cobra"

	"github.com/MoeGolibrary/moego/cli/devops_executor/logic/translator/istio"
)

var translateEnvoyFilterParams istio.TranslateEnvoyFilterParams

var GrpcTranscoderCmd = &cobra.Command{
	Use:   "istio/grpc-transcoder",
	Short: "Translate an envoyfilter resource file that defines a grpc-transcoder for istio.",
	Long: `Translate an envoyfilter resource file that defines a grpc-transcoder.
Where the list of services required by the grpc-transcoder is parsed from the specified protobuf descriptor file.`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return istio.TranslateEnvoyFilter(cmd.Context(), translateEnvoyFilterParams)
	},
}

func init() {
	txtDescriptor := "[required] Specify the protobuf descriptor set file to be parsed."
	txtEnvoyFilter := "[required] Specify the grpc-Transcoder EnvoyFilter that needs to be translated."
	txtOutput := "[optional] Specify the output path of the grpc transcoder for istio EnvoyFilter, default: same as --envoyfilter."

	GrpcTranscoderCmd.Flags().SortFlags = false
	GrpcTranscoderCmd.Flags().StringVar(&translateEnvoyFilterParams.Descriptor, "descriptor", "", txtDescriptor)
	GrpcTranscoderCmd.Flags().StringVar(&translateEnvoyFilterParams.EnvoyFilter, "envoyfilter", "", txtEnvoyFilter)
	GrpcTranscoderCmd.Flags().StringVarP(&translateEnvoyFilterParams.Output, "output", "o", "", txtOutput)
	GrpcTranscoderCmd.MarkFlagRequired("descriptor")
	GrpcTranscoderCmd.MarkFlagRequired("envoyfilter")
}
