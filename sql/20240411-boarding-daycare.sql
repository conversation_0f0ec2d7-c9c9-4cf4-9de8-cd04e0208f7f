create table if not exists moego_online_booking.public.boarding_service_detail
(
  id                 bigserial
    primary key,
  booking_request_id bigint         default 0                 not null,
  pet_id             bigint         default 0                 not null,
  lodging_id         bigint         default 0                 not null,
  service_id         bigint         default 0                 not null,
  service_price      numeric(10, 2) default 0.00              not null,
  tax_id             bigint         default 0                 not null,
  start_date         date,
  start_time         integer,
  end_date           date,
  end_time           integer,
  created_at         timestamp      default CURRENT_TIMESTAMP not null,
  updated_at         timestamp      default CURRENT_TIMESTAMP not null,
  deleted_at         timestamp
);

comment on table moego_online_booking.public.boarding_service_detail is 'The boarding service detail';

comment on column moego_online_booking.public.boarding_service_detail.booking_request_id is 'The id of booking request';

comment on column moego_online_booking.public.boarding_service_detail.pet_id is 'The id of pet, associated with the current service';

comment on column moego_online_booking.public.boarding_service_detail.lodging_id is 'The id of lodging, associated with the current service';

comment on column moego_online_booking.public.boarding_service_detail.service_id is 'The id of current service';

comment on column moego_online_booking.public.boarding_service_detail.service_price is 'The price of current service';

comment on column moego_online_booking.public.boarding_service_detail.start_date is 'The pet arrival date of the service, yyyy-MM-dd';

comment on column moego_online_booking.public.boarding_service_detail.start_time is 'The pet arrival time of the service, unit minute, 540 means 09:00';

comment on column moego_online_booking.public.boarding_service_detail.end_date is 'The pet pickup date of the service, yyyy-MM-dd';



comment on column moego_online_booking.public.boarding_service_detail.end_time is 'The pet pickup time of the service, unit minute, 540 means 09:00';

create index if not exists boarding_service_detail_idx_request_id
  on moego_online_booking.public.boarding_service_detail (booking_request_id);

create table if not exists moego_online_booking.public.daycare_service_detail
(
  id                 bigserial
    primary key,
  booking_request_id bigint         default 0                 not null,
  pet_id             bigint         default 0                 not null,
  service_id         bigint         default 0                 not null,
  specific_dates     jsonb          default '[]'::jsonb,
  service_price      numeric(10, 2) default 0.00              not null,
  tax_id             bigint         default 0                 not null,
  max_duration       integer        default 0                 not null,
  start_time         integer,
  end_time           integer,
  created_at         timestamp      default CURRENT_TIMESTAMP not null,
  updated_at         timestamp      default CURRENT_TIMESTAMP not null,
  deleted_at         timestamp
);

comment on table moego_online_booking.public.daycare_service_detail is 'The daycare service detail';

comment on column moego_online_booking.public.daycare_service_detail.booking_request_id is 'The id of booking request';

comment on column moego_online_booking.public.daycare_service_detail.pet_id is 'The id of pet, associated with the current service';

comment on column moego_online_booking.public.daycare_service_detail.service_id is 'The id of current service';

comment on column moego_online_booking.public.daycare_service_detail.specific_dates is 'The specific dates of the daycare service';

comment on column moego_online_booking.public.daycare_service_detail.service_price is 'The price of current service';

comment on column moego_online_booking.public.daycare_service_detail.max_duration is 'The max duration of the daycare service, unit minute';

comment on column moego_online_booking.public.daycare_service_detail.start_time is 'The arrival time of the service, unit minute, 540 means 09:00';



comment on column moego_online_booking.public.daycare_service_detail.end_time is 'The pickup time of the service, unit minute, 540 means 09:00';

create index if not exists daycare_service_detail_idx_request_id
  on moego_online_booking.public.daycare_service_detail (booking_request_id);

create table if not exists moego_online_booking.public.boarding_add_on_detail
(
  id                 bigserial
    primary key,
  booking_request_id bigint         default 0                 not null,
  service_detail_id  bigint         default 0                 not null,
  pet_id             bigint         default 0                 not null,
  add_on_id          bigint         default 0                 not null,
  specific_dates     jsonb          default '[]'::jsonb       not null,
  is_everyday        boolean        default false             not null,
  service_price      numeric(10, 2) default 0.00              not null,
  tax_id             bigint         default 0                 not null,
  duration           integer        default 0                 not null,
  created_at         timestamp      default CURRENT_TIMESTAMP not null,
  updated_at         timestamp      default CURRENT_TIMESTAMP not null,
  deleted_at         timestamp
);

comment on table moego_online_booking.public.boarding_add_on_detail is 'The boarding add-on detail';

comment on column moego_online_booking.public.boarding_add_on_detail.booking_request_id is 'The id of booking request';

comment on column moego_online_booking.public.boarding_add_on_detail.service_detail_id is 'The id of boarding service detail, associated with the current add-on';

comment on column moego_online_booking.public.boarding_add_on_detail.pet_id is 'The id of pet, associated with the current add-on';

comment on column moego_online_booking.public.boarding_add_on_detail.add_on_id is 'The id of current add-on service';

comment on column moego_online_booking.public.boarding_add_on_detail.specific_dates is 'The specific dates of the add-on service';

comment on column moego_online_booking.public.boarding_add_on_detail.is_everyday is 'Whether the add-on service is everyday';



comment on column moego_online_booking.public.boarding_add_on_detail.service_price is 'The price of current add-on service';

create index if not exists boarding_add_on_detail_idx_request_id
  on moego_online_booking.public.boarding_add_on_detail (booking_request_id);

create index if not exists boarding_add_on_detail_idx_service_detail_id
  on moego_online_booking.public.boarding_add_on_detail (service_detail_id);

create table if not exists moego_online_booking.public.daycare_add_on_detail
(
  id                 bigserial
    primary key,
  booking_request_id bigint         default 0                 not null,
  service_detail_id  bigint         default 0                 not null,
  pet_id             bigint         default 0                 not null,
  add_on_id          bigint         default 0                 not null,
  specific_dates     jsonb          default '[]'::jsonb       not null,
  is_everyday        boolean        default false             not null,
  service_price      numeric(10, 2) default 0.00              not null,
  tax_id             bigint         default 0                 not null,
  duration           integer        default 0                 not null,
  created_at         timestamp      default CURRENT_TIMESTAMP not null,
  updated_at         timestamp      default CURRENT_TIMESTAMP not null,
  deleted_at         timestamp
);

comment on table moego_online_booking.public.daycare_add_on_detail is 'The daycare add-on detail';

comment on column moego_online_booking.public.daycare_add_on_detail.booking_request_id is 'The id of booking request';

comment on column moego_online_booking.public.daycare_add_on_detail.service_detail_id is 'The id of daycare service detail, associated with the current add-on';

comment on column moego_online_booking.public.daycare_add_on_detail.pet_id is 'The id of pet, associated with the current add-on';

comment on column moego_online_booking.public.daycare_add_on_detail.add_on_id is 'The id of current add-on service';

comment on column moego_online_booking.public.daycare_add_on_detail.specific_dates is 'The specific dates of add-on service';

comment on column moego_online_booking.public.daycare_add_on_detail.is_everyday is 'The flag to indicate if the add-on service is everyday';

comment on column moego_online_booking.public.daycare_add_on_detail.service_price is 'The price of current add-on service';

comment on column moego_online_booking.public.daycare_add_on_detail.tax_id is 'The id of tax, associated with the current add-on';



comment on column moego_online_booking.public.daycare_add_on_detail.duration is 'The duration of current add-on service';

create index if not exists daycare_add_on_detail_idx_request_id
  on moego_online_booking.public.daycare_add_on_detail (booking_request_id);

create index if not exists daycare_add_on_detail_idx_service_detail_id
  on moego_online_booking.public.daycare_add_on_detail (service_detail_id);

create table if not exists moego_online_booking.public.evaluation_test_detail
(
  id                 bigserial
    primary key,
  booking_request_id bigint         default 0                 not null,
  pet_id             bigint         default 0                 not null,
  evaluation_id      bigint         default 0                 not null,
  service_price      numeric(10, 2) default 0.00              not null,
  duration           integer        default 0                 not null,
  start_date         date,
  start_time         integer,
  end_date           date,
  end_time           integer,
  created_at         timestamp      default CURRENT_TIMESTAMP not null,
  updated_at         timestamp      default CURRENT_TIMESTAMP not null,
  deleted_at         timestamp
);

comment on table moego_online_booking.public.evaluation_test_detail is 'The evaluation test detail';

comment on column moego_online_booking.public.evaluation_test_detail.booking_request_id is 'The id of booking request';

comment on column moego_online_booking.public.evaluation_test_detail.pet_id is 'The id of pet, associated with the current evaluation test';

comment on column moego_online_booking.public.evaluation_test_detail.evaluation_id is 'The id of current evaluation test';

comment on column moego_online_booking.public.evaluation_test_detail.service_price is 'The price of current evaluation test';

comment on column moego_online_booking.public.evaluation_test_detail.duration is 'The duration of current evaluation test, unit minute';

comment on column moego_online_booking.public.evaluation_test_detail.start_date is 'The start date of the evaluation test, yyyy-MM-dd';

comment on column moego_online_booking.public.evaluation_test_detail.start_time is 'The start time of the evaluation test, unit minute, 540 means 09:00';

comment on column moego_online_booking.public.evaluation_test_detail.end_date is 'The end date of the evaluation test, yyyy-MM-dd';



comment on column moego_online_booking.public.evaluation_test_detail.end_time is 'The end time of the evaluation test, unit minute, 540 means 09:00';

create index if not exists evaluation_test_detail_idx_request_id
  on moego_online_booking.public.evaluation_test_detail (booking_request_id);

create table if not exists moego_online_booking.public.feeding
(
  id                  bigserial
    primary key,
  booking_request_id  bigint                                       not null,
  service_detail_id   bigint         default 0                     not null,
  service_detail_type integer        default 0                     not null,
  time                jsonb          default '[]'::jsonb           not null,
  amount              numeric(10, 2) default 0                     not null,
  unit                varchar(255)   default ''::character varying not null,
  food_type           varchar(255)   default ''::character varying not null,
  food_source         varchar(255)   default ''::character varying not null,
  instruction         varchar(2048)  default ''::character varying not null,
  created_at          timestamp      default CURRENT_TIMESTAMP     not null,
  updated_at          timestamp      default CURRENT_TIMESTAMP     not null,
  deleted_at          timestamp
);

comment on table moego_online_booking.public.feeding is 'Stores information about feedings.';

comment on column moego_online_booking.public.feeding.id is 'The primary key identifier for each feeding.';

comment on column moego_online_booking.public.feeding.booking_request_id is 'The booking request identifier.';

comment on column moego_online_booking.public.feeding.service_detail_id is 'The service detail identifier.';

comment on column moego_online_booking.public.feeding.service_detail_type is 'service detail type, 1: boarding, 2: daycare';

comment on column moego_online_booking.public.feeding.time is 'Feeding time.';

comment on column moego_online_booking.public.feeding.amount is 'Feeding amount, must be greater than 0.';

comment on column moego_online_booking.public.feeding.unit is 'Feeding unit.';

comment on column moego_online_booking.public.feeding.food_type is 'Food type.';

comment on column moego_online_booking.public.feeding.food_source is 'Food source.';



comment on column moego_online_booking.public.feeding.instruction is 'Feeding instructions.';

create index if not exists feeding_service_detail_id_index
  on moego_online_booking.public.feeding (service_detail_id);

create index if not exists feeding_booking_request_id_index
  on moego_online_booking.public.feeding (booking_request_id);

create table if not exists moego_online_booking.public.medication
(
  id                  bigserial
    primary key,
  booking_request_id  bigint                                       not null,
  service_detail_id   bigint         default 0                     not null,
  service_detail_type integer        default 0                     not null,
  time                jsonb          default '[]'::jsonb           not null,
  amount              numeric(10, 2) default 0                     not null,
  unit                varchar(255)   default ''::character varying not null,
  medication_name     varchar(255)   default ''::character varying not null,
  notes               varchar(2048)  default ''::character varying not null,
  created_at          timestamp      default CURRENT_TIMESTAMP     not null,
  updated_at          timestamp      default CURRENT_TIMESTAMP     not null,
  deleted_at          timestamp
);

comment on table moego_online_booking.public.medication is 'Stores information about medication events.';

comment on column moego_online_booking.public.medication.id is 'The primary key identifier for each medication event.';

comment on column moego_online_booking.public.medication.booking_request_id is 'The booking request identifier.';

comment on column moego_online_booking.public.medication.service_detail_id is 'The service detail identifier.';

comment on column moego_online_booking.public.medication.service_detail_type is 'service detail type, 1: boarding, 2: daycare';

comment on column moego_online_booking.public.medication.time is 'Medication time.';

comment on column moego_online_booking.public.medication.amount is 'Medication amount, must be greater than 0.';

comment on column moego_online_booking.public.medication.unit is 'Medication unit.';

comment on column moego_online_booking.public.medication.medication_name is 'Medication name.';



comment on column moego_online_booking.public.medication.notes is 'Additional notes about the medication.';

create index if not exists medication_service_detail_id_index
  on moego_online_booking.public.medication (service_detail_id);

create index if not exists medication_booking_request_id_index
  on moego_online_booking.public.medication (booking_request_id);

create table if not exists moego_online_booking.public.booking_date_range_setting
(
  id                    bigserial
    primary key,
  business_id           bigint    default 0                 not null,
  company_id            bigint    default 0                 not null,
  service_item_type     integer   default 0                 not null,
  start_date_type       integer   default 0                 not null,
  specific_start_date   date,
  max_start_date_offset integer   default 0                 not null,
  end_date_type         integer   default 0                 not null,
  specific_end_date     date,
  max_end_date_offset   integer   default 0                 not null,
  created_at            timestamp default CURRENT_TIMESTAMP not null,
  updated_at            timestamp default CURRENT_TIMESTAMP not null,
  updated_by            bigint    default 0                 not null
);

comment on column moego_online_booking.public.booking_date_range_setting.start_date_type is '0: no limit, 1: offset from today, 2: specific date';



comment on column moego_online_booking.public.booking_date_range_setting.end_date_type is '0: no limit, 1: offset from today, 2: specific date';

create unique index if not exists booking_date_range_setting_idx_bid_service
  on moego_online_booking.public.booking_date_range_setting (business_id, service_item_type);



create table if not exists moego_online_booking.public.accept_pet_setting
(
  id                 bigserial
    primary key,
  business_id        bigint    default 0                 not null,
  company_id         bigint    default 0                 not null,
  service_item_type  integer   default 0                 not null,
  accepted_pet_types integer[] default ARRAY []::integer[],
  created_at         timestamp default CURRENT_TIMESTAMP not null,
  updated_at         timestamp default CURRENT_TIMESTAMP not null,
  update_by          bigint    default 0                 not null
);

create unique index if not exists accept_pet_setting_idx_bid_service
  on moego_online_booking.public.accept_pet_setting (business_id, service_item_type);

create table if not exists moego_online_booking.public.boarding_auto_assign
(
  id                         bigserial
    primary key,
  booking_request_id         bigint    default 0                 not null,
  boarding_service_detail_id bigint    default 0                 not null,
  lodging_id                 bigint,
  created_at                 timestamp default CURRENT_TIMESTAMP not null,
  updated_at                 timestamp default CURRENT_TIMESTAMP not null,
  deleted_at                 timestamp
);

comment on table moego_online_booking.public.boarding_auto_assign is 'boarding auto assign record';

comment on column moego_online_booking.public.boarding_auto_assign.booking_request_id is 'The id of booking request';

comment on column moego_online_booking.public.boarding_auto_assign.lodging_id is 'The id of lodging';

create unique index if not exists boarding_auto_assign_boarding_service_detail_id_uindex
  on moego_online_booking.public.boarding_auto_assign (boarding_service_detail_id)
  where (deleted_at IS NULL);

create index if not exists boarding_auto_assign_booking_request_id_index
  on moego_online_booking.public.boarding_auto_assign (booking_request_id);

alter table moego_online_booking.public.grooming_service_detail
  add column if not exists tax_id bigint not null default 0;

alter table moego_online_booking.public.grooming_add_on_detail
  add column if not exists tax_id bigint not null default 0;
