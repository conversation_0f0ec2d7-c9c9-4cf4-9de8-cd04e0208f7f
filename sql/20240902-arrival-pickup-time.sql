
create table booking_time_range_setting
(
  id                bigserial primary key,
  company_id        bigint    default 0                  not null,
  business_id       bigint    default 0                  not null,
  service_item_type integer   default 0                  not null,
  is_customized     boolean                              not null,
  schedule_type     integer   default 1                  not null,
  start_date        date                                 not null,
  end_date          date      default '9999-12-31'::date not null,
  updated_by        bigint    default 0                  not null,
  created_at        timestamp default CURRENT_TIMESTAMP  not null,
  updated_at        timestamp default CURRENT_TIMESTAMP  not null,
  unique (company_id, business_id, service_item_type)
);


create table booking_time_range_detail
(
  id              bigserial primary key,
  setting_id      bigint   default 0                 not null,
  time_range_type integer   default 0                 not null,
  first_week      jsonb     default '{}'::jsonb       not null,
  second_week     jsonb     default '{}'::jsonb       not null,
  third_week      jsonb     default '{}'::jsonb       not null,
  forth_week      jsonb     default '{}'::jsonb       not null,
  created_at      timestamp default CURRENT_TIMESTAMP not null,
  updated_at      timestamp default CURRENT_TIMESTAMP not null
);

create index idx_setting_id
  on booking_time_range_detail (setting_id);


create table block_customer
(
  id                bigserial primary key,
  company_id        bigint    default 0                 not null,
  service_item_type integer   default 0                 not null,
  customer_id       bigint    default 0                 not null,
  is_active         boolean                             not null,
  updated_by        bigint    default 0                 not null,
  created_at        timestamp default CURRENT_TIMESTAMP not null,
  updated_at        timestamp default CURRENT_TIMESTAMP not null,
  unique (company_id, service_item_type, customer_id)
);

create table accept_customer_setting
(
  id                     bigserial
    primary key,
  business_id            bigint    default 0                 not null,
  company_id             bigint    default 0                 not null,
  service_item_type      integer   default 0                 not null,
  accepted_customer_type integer   default 0                 not null,
  created_at             timestamp default CURRENT_TIMESTAMP not null,
  updated_at             timestamp default CURRENT_TIMESTAMP not null,
  update_by              bigint    default 0                 not null,
  unique (company_id, business_id, service_item_type)
);

create table booking_time_range_override
(
    id              bigserial primary key,
    setting_id      bigint    default 0                 not null,
    time_range_type integer   default 0                 not null,
    is_available    boolean   default false             not null,
    start_date      date                                not null,
    end_date        date                                not null,
    day_time_range  jsonb     default '{}'::jsonb       not null,
    created_at      timestamp default CURRENT_TIMESTAMP not null,
    updated_at      timestamp default CURRENT_TIMESTAMP not null,
    deleted_at      timestamp
);

comment on column booking_time_range_override.time_range_type is '1.arrival time 2.pick up time';
comment on column public.booking_time_range_override.setting_id is 'The id of booking time range setting';

alter table booking_time_range_override
    owner to moego_developer_240310_eff7a0dc;

create index booking_time_range_override__idx_setting_id
    on booking_time_range_override (setting_id);