INSERT INTO `moe_payment`.`moe_plan_feature_relation` (`level`, `code`, `allow_type`, `enable`, `quota`, `create_time`, `update_time`, `is_deleted`) VALUES
(1001, 'mapViewV2', 0, 0, -1, '2024-06-25 16:20:00', '2024-06-25 16:20:00', 0)
,(1101, 'mapViewV2', 0, 1, -1, '2024-06-25 16:20:00', '2024-06-25 16:20:00', 0)
,(1201, 'mapViewV2', 0, 1, -1, '2024-06-25 16:20:00', '2024-06-25 16:20:00', 0)
,(1010, 'mapViewV2', 0, 0, -1, '2024-06-25 16:20:00', '2024-06-25 16:20:00', 0);

INSERT INTO `moe_payment`.`moe_feature` (`name`, `code`, `allow_type`, `create_time`, `enable`, `quota`, `update_time`, `is_deleted`) VALUES ('map view v2', 'mapViewV2', 0, '2024-06-25 16:20:00', 0, 0, '2024-06-25 16:20:00', 0);



INSERT INTO "public"."permission" ("name", "display_name", "description", "parent_permission_id", "category_id") VALUES ('accessStaffLocation', 'Access staff location', '', 0, 7);


CREATE TABLE `moe_staff_tracking` (
      `id` bigint unsigned NOT NULL AUTO_INCREMENT,
      `company_id` bigint NOT NULL DEFAULT '0',
      `staff_id` bigint NOT NULL DEFAULT '0',
      `device_id` varchar(100) NOT NULL DEFAULT '',
      `lat` decimal(11,8) NOT NULL DEFAULT '0.00000000' COMMENT '经度',
      `lng` decimal(11,8) NOT NULL DEFAULT '0.00000000' COMMENT '纬度',
      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `idx_cs` (`company_id`,`staff_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci