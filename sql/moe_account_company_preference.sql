CREATE TABLE `moe_account_company_preference` (
  `id` bigint  NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `account_id` bigint NOT NULL DEFAULT 0 COMMENT '账号id',
  `company_id` bigint NOT NULL DEFAULT 0 COMMENT '公司id',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序值',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_account_company` (`account_id`, `company_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='账号公司个性化配置';
