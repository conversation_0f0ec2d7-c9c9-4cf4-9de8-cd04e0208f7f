package com.moego.lib.event_bus.producer;

import com.moego.lib.common.observability.tracing.Headers;
import com.moego.lib.common.thread.ThreadContextHolder;
import com.moego.lib.event_bus.autoconfigure.EventBusProperties;
import com.moego.lib.event_bus.env.EnvHelper;
import com.moego.lib.event_bus.event.EventFormatter;
import com.moego.lib.event_bus.event.EventRecord;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.kafka.support.SendResult;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.util.CollectionUtils;

@Slf4j
public class Producer {

    private final Map<String, KafkaTemplate<String, String>> brokers;
    private final KafkaTemplate<String, String> defaultBroker;

    private final EventBusProperties properties;

    private final EnvHelper envHelper;

    @SuppressFBWarnings("CT_CONSTRUCTOR_THROW")
    public Producer(
            Map<String, KafkaTemplate<String, String>> brokers, EventBusProperties properties, EnvHelper envHelper) {
        if (CollectionUtils.isEmpty(brokers)) {
            throw new IllegalArgumentException("No kafkaTemplate found");
        }

        this.brokers = brokers;
        this.properties = properties;
        this.envHelper = envHelper;

        this.defaultBroker = this.brokers.values().stream().findFirst().orElse(null);
    }

    /**
     * 向指定 topic 发送 event, event 会被投递到默认的 broker
     * @param topicName topic name
     * @param event event to send
     */
    public void send(String topicName, EventRecord<?> event) {
        send(null, topicName, event);
    }

    /**
     * 向指定 topic 发送 event, event 会被投递到指定的 broker.
     * 如果 brokerName 为 null, 则 event 会被投递到默认的 broker.
     * @param brokerName broker name
     * @param topic topic name
     * @param event event to send
     */
    public void send(String brokerName, String topic, EventRecord<?> event) {
        var broker = brokerName != null ? brokers.get(brokerName) : defaultBroker;

        if (broker == null) {
            throw new IllegalArgumentException("No kafkaTemplate found for broker name: " + brokerName);
        }

        try {
            var data = EventFormatter.format(event);
            var headers = buildHeaders(topic, event);
            var msg = new GenericMessage<>(data, headers);

            broker.send(msg).thenAccept(this::afterSuccess).exceptionally(this::afterFailure);
        } catch (Throwable t) {
            // EventFormatter.format may throw exception if it fails to format the message
            // kafkaTemplate.send may throw exception if connection to Kafka is broken
            afterFailure(t);
        }
    }

    private HashMap<String, Object> buildHeaders(String topic, EventRecord<?> event) {
        var headers = new HashMap<String, Object>();

        // set topic
        var topicAddress = envHelper != null ? envHelper.convertTopicName(topic) : topic;
        headers.put(KafkaHeaders.TOPIC, topicAddress);

        // set key
        if (event.key() != null) {
            headers.put(KafkaHeaders.KEY, event.key());
        }

        // add request headers to message headers
        var requestHeaders = ThreadContextHolder.getContext(Headers.class);
        if (requestHeaders != null) {
            headers.putAll(requestHeaders.getHeaders());
        }
        return headers;
    }

    private void afterSuccess(SendResult<String, String> result) {
        if (properties.getProducer().isLogSuccess()) {
            log.info("Event sent: {}", result);
        }
        // TODO: 采集指标, 统计消息投递成功的次数
    }

    private Void afterFailure(Throwable throwable) {
        if (properties.getProducer().isLogFailure()) {
            log.error("Failed to send event", throwable);
        }
        // TODO: 采集指标, 统计消息投递失败的次数
        return null;
    }
}
