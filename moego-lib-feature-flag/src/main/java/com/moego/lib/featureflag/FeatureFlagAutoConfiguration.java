package com.moego.lib.featureflag;

import com.moego.lib.featureflag.impl.growthbook.GrowthBookFeatureFlagApi;
import growthbook.sdk.java.FeatureFetchException;
import growthbook.sdk.java.FeatureRefreshStrategy;
import growthbook.sdk.java.GBFeaturesRepository;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@AutoConfiguration
@EnableConfigurationProperties(FeatureFlagProperties.class)
public class FeatureFlagAutoConfiguration {

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass(GBFeaturesRepository.class)
    static class GrowthBook {

        @Bean
        @ConditionalOnMissingBean
        public GBFeaturesRepository growthBookFeaturesRepository(FeatureFlagProperties properties)
                throws FeatureFetchException {
            var growthBook = properties.getGrowthBook();

            checkArgs(growthBook);

            var repository = GBFeaturesRepository.builder()
                    .apiHost(growthBook.getApiHost())
                    .clientKey(growthBook.getClientKey())
                    .refreshStrategy(FeatureRefreshStrategy.STALE_WHILE_REVALIDATE)
                    .swrTtlSeconds(growthBook.getTtl())
                    .build();

            repository.initialize();

            return repository;
        }

        @Bean
        @ConditionalOnMissingBean
        public FeatureFlagApi growthBookFeatureFlagApi(GBFeaturesRepository repository) {
            return new GrowthBookFeatureFlagApi(repository);
        }

        private static void checkArgs(FeatureFlagProperties.GrowthBook growthBook) {
            if (growthBook == null) {
                throw new IllegalArgumentException("moego.feature-flag.growth-book must be provided.");
            }
            if (!StringUtils.hasText(growthBook.getApiHost())) {
                throw new IllegalArgumentException("moego.feature-flag.growth-book.api-host must be provided.");
            }
            if (!StringUtils.hasText(growthBook.getClientKey())) {
                throw new IllegalArgumentException("moego.feature-flag.growth-book.client-key must be provided.");
            }
        }
    }
}
