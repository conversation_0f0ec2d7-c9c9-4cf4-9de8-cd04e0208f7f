rootProject.name = 'moego-server-api'

include(':moego-server-api')
include(':moego-server-api-doc-server')

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
configureSourceBranchControl()

new File("${rootDir}/.githooks").eachFile(groovy.io.FileType.FILES) {
  java.nio.file.Files.copy(it.toPath(), new File("${rootDir}/.git/hooks", it.name).toPath(), java.nio.file.StandardCopyOption.REPLACE_EXISTING)
}
