{"$schema": "https://moego.s3.us-west-2.amazonaws.com/ops/github-actions/ci-json-schema.json", "service_name": "moego-server-api", "language": {"type": "java", "version": "17"}, "lint": {"commands": ["./gradlew spotlessCheck spotbugsMain --stacktrace --no-daemon -Dsbc.scriptBranch=GitHubActions"]}, "build": {"commands": ["./gradlew bootJar --stacktrace --no-daemon -Dsbc.scriptBranch=GitHubActions"], "build_image": [{"dockerfile": "ci/Dockerfile", "context": "."}]}, "deploy": {"type": "service"}, "install": {"commands": ["./gradlew classes --stacktrace --no-daemon -Dsbc.scriptBranch=GitHubActions"], "cache_dir": ".gradle"}}