package grpc

import (
	"sync"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
)

type pool struct {
	connections sync.Map
}

func (p *pool) Get(address string) (*grpc.ClientConn, error) {
	if v, ok := p.connections.Load(address); ok {
		return v.(*grpc.ClientConn), nil // nolint
	}

	conn, err := grpc.NewClient(address,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	if err != nil {
		return nil, errs.NewFrameError(codes.Unavailable, "connect fail: "+err.Error())
	}
	v, loaded := p.connections.LoadOrStore(address, conn)
	if !loaded {
		return conn, nil
	}
	return v.(*grpc.ClientConn), nil // nolint
}
