package grpc

import (
	"context"
	"errors"
	"reflect"

	"google.golang.org/grpc"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/server"
)

type RegisterInfo struct {
	Impl        any
	MethodsInfo map[string]RegisterMethodsInfo
	Metadata    any
}

type RegisterMethodsInfo struct {
	Method  server.Method
	ReqType reflect.Type
	RspType reflect.Type
}

var (
	grpcRegisterInfo = make(map[string]*RegisterInfo)
)

func register(serviceName string, impl any, metadata any, methodInfos []RegisterMethodsInfo) error {
	registerInfo, ok := grpcRegisterInfo[serviceName]
	if !ok {
		registerInfo = &RegisterInfo{
			Impl:        impl,
			Metadata:    metadata,
			MethodsInfo: make(map[string]RegisterMethodsInfo),
		}
		grpcRegisterInfo[serviceName] = registerInfo
	}

	for _, methodInfo := range methodInfos {
		if registerInfo.MethodsInfo == nil {
			registerInfo.MethodsInfo = make(map[string]RegisterMethodsInfo)
		}
		registerInfo.MethodsInfo[methodInfo.Method.Name] = methodInfo
	}

	return nil
}

func Register(s *server.Server, grpcDesc *grpc.ServiceDesc, imp interface{}) {
	methods, methodInfos := convertToMethod(imp, grpcDesc.Methods)

	if err := register(grpcDesc.ServiceName, imp, grpcDesc.Metadata, methodInfos); err != nil {
		panic("failed to register service: " + err.Error())
	}

	d := toTRPCServiceDesc(grpcDesc, methods)

	if err := s.Service(grpcDesc.ServiceName).Register(d, imp); err != nil {
		panic("failed to register service: " + err.Error())
	}
}

func convertToMethod(imp any, grpcMethods []grpc.MethodDesc) ([]server.Method, []RegisterMethodsInfo) {
	var (
		methodInfos []RegisterMethodsInfo
		methods     []server.Method
	)
	for _, grpcMethod := range grpcMethods {
		method := reflect.ValueOf(imp).MethodByName(grpcMethod.MethodName)
		if !method.IsValid() {
			panic("method not found: " + grpcMethod.MethodName)
		}
		reqType := method.Type().In(1).Elem()
		rspType := method.Type().Out(0).Elem()
		m := server.Method{
			Name: grpcMethod.MethodName,
			Func: func(_ interface{}, ctx context.Context, f server.FilterFunc) (rspBody interface{}, err error) {
				header := GetHeader(ctx)
				req := header.Req
				filters, err := f(req)
				if err != nil {
					return nil, err
				}
				handleFunc := func(ctx context.Context, req interface{}) (interface{}, error) {
					const length = 2
					in := []reflect.Value{reflect.ValueOf(ctx), reflect.ValueOf(req)}
					results := method.Call(in)
					if len(results) != length {
						return nil, errors.New("method should return 2 values")
					}

					if err, ok := results[1].Interface().(error); ok && err != nil {
						return nil, err
					}
					return results[0].Interface(), nil
				}
				rsp, err := filters.Filter(ctx, req, handleFunc)
				header.Rsp = rsp
				return rsp, err
			},
		}
		methods = append(methods, m)
		methodInfos = append(methodInfos, RegisterMethodsInfo{
			Method:  m,
			ReqType: reqType,
			RspType: rspType,
		})
	}
	return methods, methodInfos
}

func toTRPCServiceDesc(grpcDesc *grpc.ServiceDesc, methods []server.Method) *server.ServiceDesc {
	// trpc 通过桩代码完成了这个逻辑，所以要在这里重新拼装 method name
	trpcMethods := make([]server.Method, 0, len(methods))
	for _, m := range methods {
		trpcMethods = append(trpcMethods, server.Method{
			Name: getGRPCMethod(grpcDesc.ServiceName, m.Name),
			Func: m.Func,
		})
	}
	return &server.ServiceDesc{
		ServiceName: grpcDesc.ServiceName,
		HandlerType: grpcDesc.HandlerType,
		Methods:     trpcMethods,
	}
}
