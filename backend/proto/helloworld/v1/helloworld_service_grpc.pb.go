// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/helloworld/v1/helloworld_service.proto

package helloworldpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	HelloworldService_SendPing_FullMethodName = "/backend.proto.helloworld.v1.HelloworldService/SendPing"
)

// HelloworldServiceClient is the client API for HelloworldService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// HelloworldService
type HelloworldServiceClient interface {
	// SendPing sends a ping request and returns a pong response
	SendPing(ctx context.Context, in *SendPingRequest, opts ...grpc.CallOption) (*SendPingResponse, error)
}

type helloworldServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHelloworldServiceClient(cc grpc.ClientConnInterface) HelloworldServiceClient {
	return &helloworldServiceClient{cc}
}

func (c *helloworldServiceClient) SendPing(ctx context.Context, in *SendPingRequest, opts ...grpc.CallOption) (*SendPingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendPingResponse)
	err := c.cc.Invoke(ctx, HelloworldService_SendPing_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HelloworldServiceServer is the server API for HelloworldService service.
// All implementations must embed UnimplementedHelloworldServiceServer
// for forward compatibility.
//
// HelloworldService
type HelloworldServiceServer interface {
	// SendPing sends a ping request and returns a pong response
	SendPing(context.Context, *SendPingRequest) (*SendPingResponse, error)
	mustEmbedUnimplementedHelloworldServiceServer()
}

// UnimplementedHelloworldServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedHelloworldServiceServer struct{}

func (UnimplementedHelloworldServiceServer) SendPing(context.Context, *SendPingRequest) (*SendPingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendPing not implemented")
}
func (UnimplementedHelloworldServiceServer) mustEmbedUnimplementedHelloworldServiceServer() {}
func (UnimplementedHelloworldServiceServer) testEmbeddedByValue()                           {}

// UnsafeHelloworldServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HelloworldServiceServer will
// result in compilation errors.
type UnsafeHelloworldServiceServer interface {
	mustEmbedUnimplementedHelloworldServiceServer()
}

func RegisterHelloworldServiceServer(s grpc.ServiceRegistrar, srv HelloworldServiceServer) {
	// If the following call pancis, it indicates UnimplementedHelloworldServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&HelloworldService_ServiceDesc, srv)
}

func _HelloworldService_SendPing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HelloworldServiceServer).SendPing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HelloworldService_SendPing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HelloworldServiceServer).SendPing(ctx, req.(*SendPingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HelloworldService_ServiceDesc is the grpc.ServiceDesc for HelloworldService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HelloworldService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.helloworld.v1.HelloworldService",
	HandlerType: (*HelloworldServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendPing",
			Handler:    _HelloworldService_SendPing_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/helloworld/v1/helloworld_service.proto",
}
