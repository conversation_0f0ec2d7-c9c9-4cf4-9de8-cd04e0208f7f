// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/helloworld/v1/echo_service.proto

package helloworldpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// request for echo
type EchoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// echo
	Echo          string `protobuf:"bytes,1,opt,name=echo,proto3" json:"echo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EchoRequest) Reset() {
	*x = EchoRequest{}
	mi := &file_backend_proto_helloworld_v1_echo_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EchoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoRequest) ProtoMessage() {}

func (x *EchoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_helloworld_v1_echo_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoRequest.ProtoReflect.Descriptor instead.
func (*EchoRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_helloworld_v1_echo_service_proto_rawDescGZIP(), []int{0}
}

func (x *EchoRequest) GetEcho() string {
	if x != nil {
		return x.Echo
	}
	return ""
}

// response for echo
type EchoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// echo
	Echo          string `protobuf:"bytes,1,opt,name=echo,proto3" json:"echo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EchoResponse) Reset() {
	*x = EchoResponse{}
	mi := &file_backend_proto_helloworld_v1_echo_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EchoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoResponse) ProtoMessage() {}

func (x *EchoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_helloworld_v1_echo_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoResponse.ProtoReflect.Descriptor instead.
func (*EchoResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_helloworld_v1_echo_service_proto_rawDescGZIP(), []int{1}
}

func (x *EchoResponse) GetEcho() string {
	if x != nil {
		return x.Echo
	}
	return ""
}

var File_backend_proto_helloworld_v1_echo_service_proto protoreflect.FileDescriptor

const file_backend_proto_helloworld_v1_echo_service_proto_rawDesc = "" +
	"\n" +
	".backend/proto/helloworld/v1/echo_service.proto\x12\x1bbackend.proto.helloworld.v1\"!\n" +
	"\vEchoRequest\x12\x12\n" +
	"\x04echo\x18\x01 \x01(\tR\x04echo\"\"\n" +
	"\fEchoResponse\x12\x12\n" +
	"\x04echo\x18\x01 \x01(\tR\x04echo2j\n" +
	"\vEchoService\x12[\n" +
	"\x04Echo\x12(.backend.proto.helloworld.v1.EchoRequest\x1a).backend.proto.helloworld.v1.EchoResponseBq\n" +
	"%com.moego.backend.proto.helloworld.v1P\x01ZFgithub.com/MoeGolibrary/moego/backend/proto/helloworld/v1;helloworldpbb\x06proto3"

var (
	file_backend_proto_helloworld_v1_echo_service_proto_rawDescOnce sync.Once
	file_backend_proto_helloworld_v1_echo_service_proto_rawDescData []byte
)

func file_backend_proto_helloworld_v1_echo_service_proto_rawDescGZIP() []byte {
	file_backend_proto_helloworld_v1_echo_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_helloworld_v1_echo_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_helloworld_v1_echo_service_proto_rawDesc), len(file_backend_proto_helloworld_v1_echo_service_proto_rawDesc)))
	})
	return file_backend_proto_helloworld_v1_echo_service_proto_rawDescData
}

var file_backend_proto_helloworld_v1_echo_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_helloworld_v1_echo_service_proto_goTypes = []any{
	(*EchoRequest)(nil),  // 0: backend.proto.helloworld.v1.EchoRequest
	(*EchoResponse)(nil), // 1: backend.proto.helloworld.v1.EchoResponse
}
var file_backend_proto_helloworld_v1_echo_service_proto_depIdxs = []int32{
	0, // 0: backend.proto.helloworld.v1.EchoService.Echo:input_type -> backend.proto.helloworld.v1.EchoRequest
	1, // 1: backend.proto.helloworld.v1.EchoService.Echo:output_type -> backend.proto.helloworld.v1.EchoResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_helloworld_v1_echo_service_proto_init() }
func file_backend_proto_helloworld_v1_echo_service_proto_init() {
	if File_backend_proto_helloworld_v1_echo_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_helloworld_v1_echo_service_proto_rawDesc), len(file_backend_proto_helloworld_v1_echo_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_helloworld_v1_echo_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_helloworld_v1_echo_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_helloworld_v1_echo_service_proto_msgTypes,
	}.Build()
	File_backend_proto_helloworld_v1_echo_service_proto = out.File
	file_backend_proto_helloworld_v1_echo_service_proto_goTypes = nil
	file_backend_proto_helloworld_v1_echo_service_proto_depIdxs = nil
}
