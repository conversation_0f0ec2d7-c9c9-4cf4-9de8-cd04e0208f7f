syntax = "proto3";

package backend.proto.helloworld.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/helloworld/v1;helloworldpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.helloworld.v1";

// EchoService defines the echo service.
service EchoService {
  // Echo returns exactly what is sent
  // (-- api-linter: core::0136::verb-noun=disabled
  //     aip.dev/not-precedent: echo 是常见的调试方法, 不需要带名词 --)
  rpc Echo(EchoRequest) returns (EchoResponse);
}

// request for echo
message EchoRequest {
    // echo
    string echo = 1;
}

// response for echo
message EchoResponse {
    // echo
    string echo = 1;
}
