// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/fulfillment_service.proto

package fulfillmentpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 不合法的参数
	ErrCode_ERR_CODE_INVALID ErrCode = 122000
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		122000: "ERR_CODE_INVALID",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":      0,
		"ERR_CODE_INVALID": 122000,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_fulfillment_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{0}
}

// ListFulfillmentRequest
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无效的parent定义 --)
//
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 不需要page-size --)
//
// (-- api-linter: core::0158::request-page_token-field=disabled
//
//	aip.dev/not-precedent: 不需要page_token --)
//
// (-- api-linter: core::0158::request-next_page_token-field=disabled
//
//	aip.dev/not-precedent: 不需要next_page_token --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要page-token --)
type ListFulfillmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 查询开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 查询结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 过滤条件
	// (-- api-linter: core::0132::request-field-types=disabled
	//
	//	aip.dev/not-precedent: 打平成string不利用协议理解 --)
	Filter *FulfillmentFilter `protobuf:"bytes,5,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序方式
	SortType SortType `protobuf:"varint,6,opt,name=sort_type,json=sortType,proto3,enum=backend.proto.fulfillment.v1.SortType" json:"sort_type,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentRequest) Reset() {
	*x = ListFulfillmentRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentRequest) ProtoMessage() {}

func (x *ListFulfillmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentRequest.ProtoReflect.Descriptor instead.
func (*ListFulfillmentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListFulfillmentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListFulfillmentRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListFulfillmentRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ListFulfillmentRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ListFulfillmentRequest) GetFilter() *FulfillmentFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListFulfillmentRequest) GetSortType() SortType {
	if x != nil {
		return x.SortType
	}
	return SortType_SORT_TYPE_UNSPECIFIED
}

func (x *ListFulfillmentRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListFulfillmentResponse
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要next-page-token --)
type ListFulfillmentResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 履约列表
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Fulfillments []*Fulfillment `protobuf:"bytes,1,rep,name=fulfillments,proto3" json:"fulfillments,omitempty"`
	// 分页信息
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// 是否最后一页
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	IsEnd bool `protobuf:"varint,3,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	// 总条数
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Total         int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentResponse) Reset() {
	*x = ListFulfillmentResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentResponse) ProtoMessage() {}

func (x *ListFulfillmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentResponse.ProtoReflect.Descriptor instead.
func (*ListFulfillmentResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListFulfillmentResponse) GetFulfillments() []*Fulfillment {
	if x != nil {
		return x.Fulfillments
	}
	return nil
}

func (x *ListFulfillmentResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListFulfillmentResponse) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

func (x *ListFulfillmentResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_backend_proto_fulfillment_v1_fulfillment_service_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc = "" +
	"\n" +
	"6backend/proto/fulfillment/v1/fulfillment_service.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a.backend/proto/fulfillment/v1/fulfillment.proto\x1a)backend/proto/fulfillment/v1/common.proto\x1a\x17validate/validate.proto\"\xb7\x03\n" +
	"\x16ListFulfillmentRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"businessId\x129\n" +
	"\n" +
	"start_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12G\n" +
	"\x06filter\x18\x05 \x01(\v2/.backend.proto.fulfillment.v1.FulfillmentFilterR\x06filter\x12C\n" +
	"\tsort_type\x18\x06 \x01(\x0e2&.backend.proto.fulfillment.v1.SortTypeR\bsortType\x12K\n" +
	"\n" +
	"pagination\x18\a \x01(\v2+.backend.proto.fulfillment.v1.PaginationRefR\n" +
	"pagination\"\xe2\x01\n" +
	"\x17ListFulfillmentResponse\x12M\n" +
	"\ffulfillments\x18\x01 \x03(\v2).backend.proto.fulfillment.v1.FulfillmentR\ffulfillments\x12K\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2+.backend.proto.fulfillment.v1.PaginationRefR\n" +
	"pagination\x12\x15\n" +
	"\x06is_end\x18\x03 \x01(\bR\x05isEnd\x12\x14\n" +
	"\x05total\x18\x04 \x01(\x05R\x05total*2\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x16\n" +
	"\x10ERR_CODE_INVALID\x10\x90\xb9\a2\x94\x01\n" +
	"\x12FulfillmentService\x12~\n" +
	"\x0fListFulfillment\x124.backend.proto.fulfillment.v1.ListFulfillmentRequest\x1a5.backend.proto.fulfillment.v1.ListFulfillmentResponseBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_fulfillment_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_fulfillment_v1_fulfillment_service_proto_goTypes = []any{
	(ErrCode)(0),                    // 0: backend.proto.fulfillment.v1.ErrCode
	(*ListFulfillmentRequest)(nil),  // 1: backend.proto.fulfillment.v1.ListFulfillmentRequest
	(*ListFulfillmentResponse)(nil), // 2: backend.proto.fulfillment.v1.ListFulfillmentResponse
	(*timestamppb.Timestamp)(nil),   // 3: google.protobuf.Timestamp
	(*FulfillmentFilter)(nil),       // 4: backend.proto.fulfillment.v1.FulfillmentFilter
	(SortType)(0),                   // 5: backend.proto.fulfillment.v1.SortType
	(*PaginationRef)(nil),           // 6: backend.proto.fulfillment.v1.PaginationRef
	(*Fulfillment)(nil),             // 7: backend.proto.fulfillment.v1.Fulfillment
}
var file_backend_proto_fulfillment_v1_fulfillment_service_proto_depIdxs = []int32{
	3, // 0: backend.proto.fulfillment.v1.ListFulfillmentRequest.start_time:type_name -> google.protobuf.Timestamp
	3, // 1: backend.proto.fulfillment.v1.ListFulfillmentRequest.end_time:type_name -> google.protobuf.Timestamp
	4, // 2: backend.proto.fulfillment.v1.ListFulfillmentRequest.filter:type_name -> backend.proto.fulfillment.v1.FulfillmentFilter
	5, // 3: backend.proto.fulfillment.v1.ListFulfillmentRequest.sort_type:type_name -> backend.proto.fulfillment.v1.SortType
	6, // 4: backend.proto.fulfillment.v1.ListFulfillmentRequest.pagination:type_name -> backend.proto.fulfillment.v1.PaginationRef
	7, // 5: backend.proto.fulfillment.v1.ListFulfillmentResponse.fulfillments:type_name -> backend.proto.fulfillment.v1.Fulfillment
	6, // 6: backend.proto.fulfillment.v1.ListFulfillmentResponse.pagination:type_name -> backend.proto.fulfillment.v1.PaginationRef
	1, // 7: backend.proto.fulfillment.v1.FulfillmentService.ListFulfillment:input_type -> backend.proto.fulfillment.v1.ListFulfillmentRequest
	2, // 8: backend.proto.fulfillment.v1.FulfillmentService.ListFulfillment:output_type -> backend.proto.fulfillment.v1.ListFulfillmentResponse
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_fulfillment_service_proto_init() }
func file_backend_proto_fulfillment_v1_fulfillment_service_proto_init() {
	if File_backend_proto_fulfillment_v1_fulfillment_service_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_fulfillment_proto_init()
	file_backend_proto_fulfillment_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_fulfillment_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_fulfillment_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_fulfillment_v1_fulfillment_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_fulfillment_service_proto = out.File
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_depIdxs = nil
}
