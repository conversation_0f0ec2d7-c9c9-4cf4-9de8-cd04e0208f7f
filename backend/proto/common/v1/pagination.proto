syntax = "proto3";

package backend.proto.common.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/common/v1;commonpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.common.v1";

import "validate/validate.proto";
// 
// 其他pb不应该引用该pb，因为分页令牌的格式是动态的，不应该使用字符串化的页码
// 并且Google AIP 的规范中 也不推荐使用嵌套分页
// 所以该pb 只是一个参考，实际上分页字段应该在Request中根据实际情况来定义
// 
// Pagination 表示分页信息。
// 用于控制页面大小和分页令牌，
// 必须在 List API 的请求中声明。
// 页面大小必须在 1 到 500 之间，分页令牌
// 必须是非空字符串，最大长度为 64。
// 如果还有更多结果，List API 的响应将包含下一页的令牌。
message Pagination {
    // 请求返回的最大条目数量
    // - 默认值：100
    // - 服务器强制最大值：1000
    // - 小于1的值将被拒绝
    // 控制每页返回的条目数量。
    // 服务器可能返回比请求更小的值。
    // 实际返回数量可能小于指定值。
    int32 page_size = 1 [
        (validate.rules).int32 = {
            gte: 1,
            lte: 1000
        }
    ];

    // 分页控制令牌，支持两种格式：
    // 1. 不透明的分页令牌（推荐）
    // 2. 字符串化的页码（简单场景）
    //
    // 使用页码模式需满足以下全部条件：
    // a) 静态数据集（分页期间无增删）
    // b) 固定分页大小（不可改变每页数量）
    // c) 小规模数据（<1万条）
    //
    // 客户端应将其视为不透明值，但简单场景下可解析为数字
    // 多格式分页控制令牌：
    // - 推荐模式：保持不透明字符串原样传递
    // - 简单模式：可解析为数字页码（需满足严格条件）
    // 警告：当底层数据变化时，页码模式可能导致数据不一致
    optional string page_token = 2 [
        (validate.rules).string = {
            min_len: 0,
            max_len: 64
        }
    ];
}

// PaginatedResponse 定义了分页响应的标准格式。
// 包含下一页的访问令牌和可选的总记录数。
// 用于所有支持分页的列表操作响应。
message PaginatedResponse {
    // 获取下一页的分页令牌
    // 两种格式：
    // - 推荐模式：token 字符串
    // - 简单模式：字符串化的下一个页码
    // 空值表示没有更多结果
    // 下一页访问令牌(双模式支持):
    // 1. 不透明令牌：视为随机字符串直接使用
    // 2. 页码模式：转换为整数后递增使用
    // 重要：当改变查询条件时，必须重置分页令牌
    string next_page_tokens = 1;

    // 所有页面的总条目数（仅在客户端请求时返回）
    // 总记录数（如果请求）
    // 大数据集时可能是近似值
    // 计算总数可能增加延迟
    // 动态数据下页码模式不可用此字段
    optional int64 total_size = 2;
}