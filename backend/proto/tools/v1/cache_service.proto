
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cache resources. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cache resources. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cache resources. --)

syntax = "proto3";

package backend.proto.tools.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.tools.v1";

import "backend/proto/tools/v1/resource_models.proto";
import "google/protobuf/struct.proto";
import "validate/validate.proto";


// CacheService
service CacheService {

    // register a cache
    rpc RegisterCache(RegisterCacheRequest) returns (RegisterCacheResponse);

    // list caches
    rpc ListCaches(ListCachesRequest) returns (ListCachesResponse);

    // get a cache
    rpc GetCache(GetCacheRequest) returns (Cache);

    // execute command in cache
    rpc ExecuteCacheCommand(ExecuteCacheCommandRequest) returns (ExecuteCacheCommandResponse);

}


// RegisterCacheRequest
message RegisterCacheRequest {
    // cache identifier
    PlatformIdentifier identifier = 1;
}

// RegisterCacheResponse
message RegisterCacheResponse {
    // cache
    optional Cache cache = 1;
}

// ListCachesRequest
message ListCachesRequest {
    // the platform where the cache is running
    optional string platform = 2 [(validate.rules).string = {max_len: 128}];
    // cache engine
    optional string engine = 3 [(validate.rules).string = {max_len: 128}];
    // cache labels
    map<string, string> labels = 4 [(validate.rules).map = {
        max_pairs: 256,
        keys: {
            string: {max_len: 255}
        },
        values: {
            string: {max_len: 63}
        }
    }];
}

// ListCachesResponse
message ListCachesResponse {
    // list of cache
    repeated Cache caches = 1;
}

// GetCacheRequest
message GetCacheRequest {
    // cache identifier
    PlatformIdentifier identifier = 1;
}

// ExecuteCacheCommandRequest
message ExecuteCacheCommandRequest {
    // cache identifier
    PlatformIdentifier identifier = 1;

    oneof commands_type {
        // general commands
        string commands = 2 [(validate.rules).string = {max_len: 65536}];
        // redis script
        RedisScript redis_script = 3;
    }

    // RedisScript
    message RedisScript{
        // redis script keys
        repeated string keys = 1;
        // redis script argv
        repeated string argv = 2;
        // redis script
        string script = 3 [(validate.rules).string = {max_len: 65536}];
    }
}

// ExecuteCacheCommandResponse
message ExecuteCacheCommandResponse {
    // database identifier
    PlatformIdentifier identifier = 1;
    // action outputs
    google.protobuf.Value output = 2;
}
