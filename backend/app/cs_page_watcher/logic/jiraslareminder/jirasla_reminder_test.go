package jiraslareminder

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
)

func TestSLAReminder_EvaluateJiraTickets(t *testing.T) {
	reminder := NewSLAReminder()

	now := time.Now()

	tests := []struct {
		name        string
		tickets     []*jira.Issue
		expected    []*SLAResult
		expectError bool
	}{
		{
			name: "P0 SLA Breached",
			tickets: []*jira.Issue{
				{
					Key:           "CS-1",
					IssuePriority: global.PriorityP0,
					Created:       now.Add(-4 * time.Hour),
					DevEngineer: []jira.UserInfo{
						{EmailAddress: "<EMAIL>"},
						{EmailAddress: "<EMAIL>"},
					},
					Assignee: jira.UserInfo{EmailAddress: "<EMAIL>"},
				},
			},
			expected: []*SLAResult{
				{
					JiraKey:          "CS-1",
					IsSLABreached:    true,
					TimeUntilBreach:  -1 * time.Hour,
					DevEngineerEmail: "<EMAIL>",
					AssigneeEmail:    "<EMAIL>",
				},
			},
			expectError: false,
		},
		{
			name: "P1 SLA Not Breached",
			tickets: []*jira.Issue{
				{
					Key:           "CS-2",
					IssuePriority: global.PriorityP1,
					Created:       now.Add(-1 * time.Hour),
					DevEngineer:   nil,
					Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				},
			},
			expected: []*SLAResult{
				{
					JiraKey:          "CS-2",
					IsSLABreached:    false,
					TimeUntilBreach:  23 * time.Hour,
					DevEngineerEmail: "",
					AssigneeEmail:    "<EMAIL>",
				},
			},
			expectError: false,
		},
		{
			name: "P2 SLA Breached",
			tickets: []*jira.Issue{
				{
					Key:           "CS-3",
					IssuePriority: global.PriorityP2,
					Created:       now.Add(-73 * time.Hour),
					DevEngineer: []jira.UserInfo{
						{EmailAddress: "<EMAIL>"},
					},
					Assignee: jira.UserInfo{EmailAddress: "<EMAIL>"},
				},
			},
			expected: []*SLAResult{
				{
					JiraKey:          "CS-3",
					IsSLABreached:    true,
					TimeUntilBreach:  -1 * time.Hour,
					DevEngineerEmail: "<EMAIL>",
					AssigneeEmail:    "<EMAIL>",
				},
			},
			expectError: false,
		},
		{
			name: "P3 SLA Not Breached",
			tickets: []*jira.Issue{
				{
					Key:           "CS-4",
					IssuePriority: global.PriorityP3,
					Created:       now.Add(-1 * time.Hour),
					DevEngineer:   nil,
					Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				},
			},
			expected: []*SLAResult{
				{
					JiraKey:          "CS-4",
					IsSLABreached:    false,
					TimeUntilBreach:  167 * time.Hour,
					DevEngineerEmail: "",
					AssigneeEmail:    "<EMAIL>",
				},
			},
			expectError: false,
		},
		{
			name: "P4 SLA Breached",
			tickets: []*jira.Issue{
				{
					Key:           "CS-5",
					IssuePriority: global.PriorityP4,
					Created:       now.Add(-361 * time.Hour),
					DevEngineer: []jira.UserInfo{
						{EmailAddress: "<EMAIL>"},
					},
					Assignee: jira.UserInfo{EmailAddress: "<EMAIL>"},
				},
			},
			expected: []*SLAResult{
				{
					JiraKey:          "CS-5",
					IsSLABreached:    true,
					TimeUntilBreach:  -1 * time.Hour,
					DevEngineerEmail: "<EMAIL>",
					AssigneeEmail:    "<EMAIL>",
				},
			},
			expectError: false,
		},
		{
			name: "P5 SLA Not Breached",
			tickets: []*jira.Issue{
				{
					Key:           "CS-6",
					IssuePriority: global.PriorityP5,
					Created:       now.Add(-1 * time.Hour),
					DevEngineer:   nil,
					Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				},
			},
			expected: []*SLAResult{
				{
					JiraKey:          "CS-6",
					IsSLABreached:    false,
					TimeUntilBreach:  719 * time.Hour,
					DevEngineerEmail: "",
					AssigneeEmail:    "<EMAIL>",
				},
			},
			expectError: false,
		},
		{
			name: "Unknown Priority",
			tickets: []*jira.Issue{
				{
					Key:           "CS-7",
					IssuePriority: "Unknown",
					Created:       now.Add(-1 * time.Hour),
					DevEngineer:   nil,
					Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				},
			},
			expected: []*SLAResult{
				{
					JiraKey:          "CS-7",
					IsSLABreached:    true,
					TimeUntilBreach:  -1 * time.Hour,
					DevEngineerEmail: "",
					AssigneeEmail:    "<EMAIL>",
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results, err := reminder.EvaluateJiraTickets(tt.tickets)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				for i, result := range results {
					assert.Equal(t, tt.expected[i].JiraKey, result.JiraKey)
					assert.Equal(t, tt.expected[i].IsSLABreached, result.IsSLABreached)
					assert.InDelta(t, tt.expected[i].TimeUntilBreach, result.TimeUntilBreach, float64(time.Second))
					assert.Equal(t, tt.expected[i].DevEngineerEmail, result.DevEngineerEmail)
					assert.Equal(t, tt.expected[i].AssigneeEmail, result.AssigneeEmail)
				}
			}
		})
	}
}
