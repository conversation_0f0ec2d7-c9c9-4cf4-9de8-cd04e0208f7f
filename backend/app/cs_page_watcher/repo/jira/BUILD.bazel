load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "jira",
    srcs = [
        "jira_client.go",
        "jira_issue.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/cs_page_watcher/global",
        "//backend/common/rpc/framework/log",
        "@com_github_andygrunwald_go_jira//:go-jira",
        "@com_github_samber_lo//:lo",
    ],
)

go_test(
    name = "jira_test",
    srcs = ["jira_client_test.go"],
    embed = [":jira"],
    deps = [
        "//backend/app/cs_page_watcher/configloader",
        "@com_github_samber_lo//:lo",
        "@com_github_stretchr_testify//assert",
    ],
)
