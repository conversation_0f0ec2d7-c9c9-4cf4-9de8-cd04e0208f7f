package pet

import (
	"context"
	"time"

	"github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/customer"
	"github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/pet"
	"github.com/MoeGolibrary/moego/backend/common/utils/pagination"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
)

type Logic struct {
	pet      pet.ReadWriter
	customer customer.ReadWriter
}

func NewLogic() *Logic {
	return &Logic{
		pet:      pet.New(),
		customer: customer.New(),
	}
}

func NewByParams(pet pet.ReadWriter,
	customer customer.ReadWriter) *Logic {
	return &Logic{
		pet:      pet,
		customer: customer,
	}
}

func (l *Logic) Create(ctx context.Context, params *CreatePetParams) (int64, error) {
	pet := &pet.CustomerPet{
		CustomerID: int(params.CustomerID),
		PetName:    params.PetName,
		PetTypeID:  uint(params.PetType),
		Gender:     int8(params.PetGender),
		Breed:      params.PetBreed,
		BreedMix:   params.GetMixed(),
		CompanyID:  int64(params.CompanyID),
		BusinessID: int(params.BusinessID),
		Status:     int8(petpb.Pet_ACTIVE.Number()),
		LifeStatus: int8(petpb.Pet_LIFE_STATE_ACTIVE),
		CreateTime: uint64(time.Now().Unix()),
		UpdateTime: uint64(time.Now().Unix()),
	}

	id, err := l.pet.Create(ctx, pet)
	if err != nil {
		return 0, err
	}

	return id, nil
}

func (l *Logic) BatchCreate(ctx context.Context, params []*CreatePetParams) ([]int64, error) {
	pets := make([]*pet.CustomerPet, 0, len(params))
	for _, param := range params {
		pets = append(pets, &pet.CustomerPet{
			CustomerID: int(param.CustomerID),
			PetName:    param.PetName,
			PetTypeID:  uint(param.PetType),
			Gender:     int8(param.PetGender),
			Breed:      param.PetBreed,
			BreedMix:   param.GetMixed(),
			CompanyID:  param.CompanyID,
			BusinessID: int(param.BusinessID),
			Status:     int8(petpb.Pet_ACTIVE.Number()),
			LifeStatus: int8(petpb.Pet_LIFE_STATE_ACTIVE),
			CreateTime: uint64(time.Now().Unix()),
			UpdateTime: uint64(time.Now().Unix()),
		})
	}

	ids, err := l.pet.BatchCreate(ctx, pets)
	if err != nil {
		return nil, err
	}

	return ids, nil
}

func (l *Logic) Update(ctx context.Context, params *UpdatePetParams) (int64, error) {
	pet := &pet.CustomerPet{
		ID:         uint(params.PetID),
		PetName:    params.PetName,
		PetTypeID:  uint(params.PetType.Number()),
		Gender:     int8(params.PetGender.Number()),
		Breed:      params.PetBreed,
		BreedMix:   params.GetMixed(),
		UpdateTime: uint64(time.Now().Unix()),
	}

	id, err := l.pet.Update(ctx, pet)
	if err != nil {
		return 0, err
	}

	return id, nil
}

func (l *Logic) Delete(ctx context.Context, petID int64) error {
	_, err := l.pet.Update(ctx, &pet.CustomerPet{
		ID:     uint(petID),
		Status: 2,
	})
	if err != nil {
		return err
	}
	return nil
}

func (l *Logic) List(ctx context.Context, params *ListPetParams) ([]*Pet, error) {
	dbPets, err := l.pet.List(ctx, &pet.Query{
		CustomerIDs: params.CustomerIDs,
		PetIDs:      params.PetIDs,
		CompanyIDs:  params.CompanyIDs,
	}, &pet.Filter{
		Status: 1,
	}, &pagination.Pagination{
		Offset:  (params.Page - 1) * params.PageSize,
		Limit:   params.PageSize,
		OrderBy: &params.OrderBy,
	})
	if err != nil {
		return nil, err
	}

	pets := make([]*Pet, 0, len(dbPets))
	for _, pet := range dbPets {
		pets = append(pets, &Pet{
			ID:         int64(pet.ID),
			PetName:    pet.PetName,
			PetType:    petpb.Pet_PetType(pet.PetTypeID),
			PetGender:  petpb.Pet_PetGender(pet.Gender),
			Breed:      pet.Breed,
			CompanyID:  pet.CompanyID,
			BusinessID: int64(pet.BusinessID),
			CustomerID: int64(pet.CustomerID),
			State:      petpb.Pet_State(pet.Status),
			Mixed:      pet.BreedMix == 1,
		})
	}

	return pets, nil
}
