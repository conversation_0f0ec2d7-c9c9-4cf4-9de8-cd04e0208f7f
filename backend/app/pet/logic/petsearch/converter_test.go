package petsearch_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/MoeGolibrary/moego/backend/app/pet/logic/petsearch"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

func TestConverter_ConvertToSearchPetTermRequest(t *testing.T) {
	// 创建测试用例
	tests := []struct {
		name      string
		companyID int64
		size      int32
		term      string
		expected  *searchpb.SearchDocumentRequest
	}{
		{
			name:      "基本搜索测试",
			companyID: 1,
			size:      10,
			term:      "测试宠物",
			expected: &searchpb.SearchDocumentRequest{
				PageSize: 10,
				Sort: []*searchpb.SearchDocumentRequest_Sort{
					{
						Field: "_score",
						Order: searchpb.SearchDocumentRequest_Sort_DESC,
					},
					{
						Field: "id",
						Order: searchpb.SearchDocumentRequest_Sort_ASC,
					},
				},
				Index: "erp-search-pets",
				Strategy: &searchpb.Strategy{
					Strategy: &searchpb.Strategy_Bool{
						Bool: &searchpb.BoolStrategy{
							Must: []*searchpb.Strategy{
								{
									Strategy: &searchpb.Strategy_Term{
										Term: &searchpb.TermStrategy{
											Field: "status",
											Value: structpb.NewNumberValue(1),
										},
									},
								},
								{
									Strategy: &searchpb.Strategy_Term{
										Term: &searchpb.TermStrategy{
											Field: "customer.status",
											Value: structpb.NewNumberValue(1),
										},
									},
								},
								{
									Strategy: &searchpb.Strategy_Term{
										Term: &searchpb.TermStrategy{
											Field: "company_id",
											Value: structpb.NewNumberValue(1),
										},
									},
								},
							},
							Should: &searchpb.BoolStrategy_BoolShouldStrategy{
								MinimumMatch: 1,
								Strategies: []*searchpb.Strategy{
									{
										Strategy: &searchpb.Strategy_Match{
											Match: &searchpb.MatchStrategy{
												Field:    "name",
												Query:    "测试宠物",
												Boost:    3,
												Operator: searchpb.MatchStrategy_OR,
											},
										},
									},
									{
										Strategy: &searchpb.Strategy_Match{
											Match: &searchpb.MatchStrategy{
												Field:    "customer.name",
												Query:    "测试宠物",
												Boost:    1,
												Operator: searchpb.MatchStrategy_OR,
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name:      "空搜索词测试",
			companyID: 2,
			size:      5,
			term:      "",
			expected: &searchpb.SearchDocumentRequest{
				PageSize: 5,
				Sort: []*searchpb.SearchDocumentRequest_Sort{
					{
						Field: "_score",
						Order: searchpb.SearchDocumentRequest_Sort_DESC,
					},
					{
						Field: "id",
						Order: searchpb.SearchDocumentRequest_Sort_ASC,
					},
				},
				Index: "erp-search-pets",
				Strategy: &searchpb.Strategy{
					Strategy: &searchpb.Strategy_Bool{
						Bool: &searchpb.BoolStrategy{
							Must: []*searchpb.Strategy{
								{
									Strategy: &searchpb.Strategy_Term{
										Term: &searchpb.TermStrategy{
											Field: "status",
											Value: structpb.NewNumberValue(1),
										},
									},
								},
								{
									Strategy: &searchpb.Strategy_Term{
										Term: &searchpb.TermStrategy{
											Field: "customer.status",
											Value: structpb.NewNumberValue(1),
										},
									},
								},
								{
									Strategy: &searchpb.Strategy_Term{
										Term: &searchpb.TermStrategy{
											Field: "company_id",
											Value: structpb.NewNumberValue(2),
										},
									},
								},
							},
							Should: &searchpb.BoolStrategy_BoolShouldStrategy{
								MinimumMatch: 1,
								Strategies: []*searchpb.Strategy{
									{
										Strategy: &searchpb.Strategy_Match{
											Match: &searchpb.MatchStrategy{
												Field:    "name",
												Boost:    3,
												Operator: searchpb.MatchStrategy_OR,
											},
										},
									},
									{
										Strategy: &searchpb.Strategy_Match{
											Match: &searchpb.MatchStrategy{
												Field:    "customer.name",
												Boost:    1,
												Operator: searchpb.MatchStrategy_OR,
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			converter := &petsearch.TermSearchParam{
				CompanyID: tt.companyID,
				Size:      tt.size,
				Term:      tt.term,
			}

			//
			result := converter.ConvertToSearchPetTermRequest(ctx)

			// 验证结果
			require.Equal(t, tt.expected.PageSize, result.PageSize)
			require.Equal(t, tt.expected.Sort[0].Field, result.Sort[0].Field)
			require.Equal(t, tt.expected.Sort[0].Order, result.Sort[0].Order)
			require.Equal(t, tt.expected.Index, result.Index)

			// 验证策略结构
			boolStrategy := result.Strategy.GetBool()
			require.NotNil(t, boolStrategy)

			// 验证Must条件
			require.Len(t, boolStrategy.Must, 3)
			require.Equal(t, "status", boolStrategy.Must[0].GetTerm().Field)
			require.Equal(t, float64(1), boolStrategy.Must[0].GetTerm().Value.GetNumberValue())
			require.Equal(t, "customer.status", boolStrategy.Must[1].GetTerm().Field)
			require.Equal(t, float64(1), boolStrategy.Must[1].GetTerm().Value.GetNumberValue())
			require.Equal(t, "company_id", boolStrategy.Must[2].GetTerm().Field)
			require.Equal(t, float64(tt.companyID), boolStrategy.Must[2].GetTerm().Value.GetNumberValue())

			// 验证Should条件
			shouldStrategy := boolStrategy.GetShould()
			require.NotNil(t, shouldStrategy)
			require.Equal(t, int32(1), shouldStrategy.MinimumMatch)
			require.Len(t, shouldStrategy.Strategies, 2)

			// 验证Match策略
			match1 := shouldStrategy.Strategies[0].GetMatch()
			require.NotNil(t, match1)
			require.Equal(t, "name", match1.Field)
			require.Equal(t, tt.term, match1.Query)
			require.Equal(t, float32(3), match1.Boost)
			require.Equal(t, searchpb.MatchStrategy_OR, match1.Operator)

			match2 := shouldStrategy.Strategies[1].GetMatch()
			require.NotNil(t, match2)
			require.Equal(t, "customer.name", match2.Field)
			require.Equal(t, tt.term, match2.Query)
			require.Equal(t, float32(1), match2.Boost)
			require.Equal(t, searchpb.MatchStrategy_OR, match2.Operator)
		})
	}
}
