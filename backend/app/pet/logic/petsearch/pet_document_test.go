package petsearch_test

import (
	"context"
	"errors"
	"strconv"
	"testing"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/structpb"
	ggorm "gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/pet/logic/petsearch"
	"github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/customer"
	customermock "github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/mock/customer"
	petmock "github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/mock/pet"
	petrepo "github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/pet"
	search "github.com/MoeGolibrary/moego/backend/app/pet/repo/search"
	searchmock "github.com/MoeGolibrary/moego/backend/app/pet/repo/search/mock"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

func NewMockPet(prw petrepo.ReadWriter,
	cwrw customerrepo.ReadWriter,
	srw search.ReadWriter) *petsearch.Logic {
	mock := petsearch.NewByParams(
		prw,
		cwrw,
		srw,
	)
	return mock
}

func TestNew(t *testing.T) {
	// 测试 NewLogic 函数是否能正常创建 Logic 实例
	t.Run("test NewLogic create instance", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 使用 Set 函数设置模拟的数据库连接
		mockDB := &ggorm.DB{} // 创建一个空的 gorm.DB 实例
		gorm.Set(mockDB)

		// 创建 Logic 实例
		logic := petsearch.NewLogic()

		// 验证实例不为空
		require.NotNil(t, logic, "Logic instance should not be nil")
	})
}

func TestIndexPetDocument(t *testing.T) {
	t.Run("test index pet document success", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		petIDs := []int64{1}
		customerID := int(1)
		petID := int64(1)
		petName := "test pet"
		firstName := "first"
		lastName := "last"

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			PetIDs: petIDs,
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return([]*petrepo.CustomerPet{
			{
				CustomerID: customerID,
				ID:         uint(petID),
				PetName:    petName,
			},
		}, nil)

		// mock customer repo
		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CustomerIDs: []int64{1},
		}).Return([]*customerrepo.BusinessCustomer{
			{
				ID:        uint(customerID),
				FirstName: firstName,
				LastName:  lastName,
			},
		}, nil)

		// mock search repo
		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().IndexPetDocument(gomock.Any(), gomock.Any()).Return(&searchpb.BulkDocumentResponse{
			Results: []*searchpb.BulkDocumentResponse_OperationResult{
				{
					DocumentId: "1",
				},
			},
		}, nil)

		mock := NewMockPet(prw, cwrw, srw)
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Pet_{
				Pet: &petpb.IndexPetDocumentRequest_Pet{
					PetIds: petIDs,
				},
			},
		}
		r, err := mock.IndexPetDocument(ctx, req)

		require.NoError(t, err)
		require.Equal(t, petIDs, r.SuccessIds)
	})

	t.Run("test index pet document company success", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerID := int(1)
		petID := int64(1)
		petName := "test pet"
		firstName := "first"
		lastName := "last"
		companyID := int64(1)

		// mock customer repo
		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CompanyIDs: []int64{companyID},
			Limit:      30000,
		}).Return([]*customerrepo.BusinessCustomer{
			{
				ID:        uint(customerID),
				FirstName: firstName,
				LastName:  lastName,
			},
		}, nil)

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			CustomerIDs: []int64{int64(customerID)},
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return([]*petrepo.CustomerPet{
			{
				CustomerID: customerID,
				ID:         uint(petID),
				PetName:    petName,
			},
		}, nil)

		// mock search repo
		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().IndexPetDocument(gomock.Any(), gomock.Any()).Return(&searchpb.BulkDocumentResponse{
			Results: []*searchpb.BulkDocumentResponse_OperationResult{
				{
					DocumentId: "1",
				},
			},
		}, nil)

		mock := NewMockPet(prw, cwrw, srw)
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Company_{
				Company: &petpb.IndexPetDocumentRequest_Company{
					CompanyIds: []int64{companyID},
				},
			},
		}
		r, err := mock.IndexPetDocument(ctx, req)

		require.NoError(t, err)
		require.Equal(t, []int64{1}, r.SuccessIds)
	})

	t.Run("test index pet document not company id", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		companyID := int64(1)

		// mock customer repo
		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CompanyIDs: []int64{companyID},
			Limit:      30000,
		}).Return([]*customerrepo.BusinessCustomer{}, nil)

		mock := NewMockPet(nil, cwrw, nil)
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Company_{
				Company: &petpb.IndexPetDocumentRequest_Company{
					CompanyIds: []int64{companyID},
				},
			},
		}
		r, err := mock.IndexPetDocument(ctx, req)

		require.NoError(t, err)
		require.Equal(t, []int64{}, r.SuccessIds)
	})

	t.Run("test index pet document when company list error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		companyID := int64(1)

		// mock customer repo
		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CompanyIDs: []int64{companyID},
			Limit:      30000,
		}).Return(nil, errors.New("customer list error"))

		mock := NewMockPet(nil, cwrw, nil)
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Company_{
				Company: &petpb.IndexPetDocumentRequest_Company{
					CompanyIds: []int64{companyID},
				},
			},
		}
		r, err := mock.IndexPetDocument(ctx, req)
		require.Error(t, err)
		require.Nil(t, r)
		require.Contains(t, err.Error(), "customer list error")
	})

	t.Run("test index pet document when pet list by customer ids error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		companyID := int64(1)
		customerID := uint(1)

		// mock customer repo
		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CompanyIDs: []int64{companyID},
			Limit:      30000,
		}).Return([]*customerrepo.BusinessCustomer{
			{
				ID:        customerID,
				FirstName: "first",
				LastName:  "last",
			},
		}, nil)

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			CustomerIDs: []int64{int64(customerID)},
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return(nil, errors.New("pet list error"))

		mock := NewMockPet(prw, cwrw, nil)
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Company_{
				Company: &petpb.IndexPetDocumentRequest_Company{
					CompanyIds: []int64{companyID},
				},
			},
		}
		r, err := mock.IndexPetDocument(ctx, req)
		require.Error(t, err)
		require.Nil(t, r)
		require.Contains(t, err.Error(), "pet list error")
	})

	t.Run("test index pet document when pet list error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		petIDs := []int64{1}

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			PetIDs: petIDs,
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return(nil, errors.New("pet list error"))

		mock := NewMockPet(prw, nil, nil)
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Pet_{
				Pet: &petpb.IndexPetDocumentRequest_Pet{
					PetIds: petIDs,
				},
			},
		}
		r, err := mock.IndexPetDocument(ctx, req)
		require.Error(t, err)
		require.Nil(t, r)

	})

	t.Run("test index pet document when customer list error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		petIDs := []int64{1}
		customerID := uint(1)
		petID := int64(1)
		petName := "test pet"

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			PetIDs: petIDs,
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return([]*petrepo.CustomerPet{
			{
				CustomerID: int(customerID),
				ID:         uint(petID),
				PetName:    petName,
			},
		}, nil)

		// mock customer repo
		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CustomerIDs: []int64{1},
		}).Return(nil, errors.New("customer list error"))

		mock := NewMockPet(prw, cwrw, nil)
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Pet_{
				Pet: &petpb.IndexPetDocumentRequest_Pet{
					PetIds: petIDs,
				},
			},
		}
		r, err := mock.IndexPetDocument(ctx, req)
		require.Error(t, err)
		require.Nil(t, r)
	})

	t.Run("test index pet document when index error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		petIDs := []int64{1}
		customerID := uint(1)
		petID := int64(1)
		petName := "test pet"
		firstName := "first"
		lastName := "last"

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			PetIDs: petIDs,
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return([]*petrepo.CustomerPet{
			{
				CustomerID: int(customerID),
				ID:         uint(petID),
				PetName:    petName,
			},
		}, nil)

		// mock customer repo
		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CustomerIDs: []int64{1},
		}).Return([]*customerrepo.BusinessCustomer{
			{
				ID:        customerID,
				FirstName: firstName,
				LastName:  lastName,
			},
		}, nil)

		// mock search repo
		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().IndexPetDocument(gomock.Any(), gomock.Any()).Return(nil,
			errors.New("index error"))

		mock := NewMockPet(prw, cwrw, srw)
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Pet_{
				Pet: &petpb.IndexPetDocumentRequest_Pet{
					PetIds: petIDs,
				},
			},
		}
		r, err := mock.IndexPetDocument(ctx, req)
		require.Error(t, err)
		require.Nil(t, r)
	})

	t.Run("test index pet document when convert to proto struct error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		petIDs := []int64{1}
		customerID := uint(1)
		petID := int64(1)
		petName := "test pet"
		firstName := "first"
		lastName := "last"

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			PetIDs: petIDs,
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return([]*petrepo.CustomerPet{
			{
				CustomerID: int(customerID),
				ID:         uint(petID),
				PetName:    petName,
			},
		}, nil)

		// mock customer repo
		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CustomerIDs: []int64{1},
		}).Return([]*customerrepo.BusinessCustomer{
			{
				ID:        customerID,
				FirstName: firstName,
				LastName:  lastName,
			},
		}, nil)

		// mock search repo
		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().IndexPetDocument(gomock.Any(), gomock.Any()).Return(nil, errors.New("convert error"))

		mock := NewMockPet(prw, cwrw, srw)
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Pet_{
				Pet: &petpb.IndexPetDocumentRequest_Pet{
					PetIds: petIDs,
				},
			},
		}
		r, err := mock.IndexPetDocument(ctx, req)
		require.Error(t, err)
		require.Nil(t, r)
		require.Contains(t, err.Error(), "convert error")
	})
	t.Run("test index pet document when pet list is empty", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		petIDs := []int64{123}

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			PetIDs: petIDs,
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return([]*petrepo.CustomerPet{}, nil)

		mock := NewMockPet(prw, nil, nil)
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Pet_{
				Pet: &petpb.IndexPetDocumentRequest_Pet{
					PetIds: petIDs,
				},
			},
		}
		r, err := mock.IndexPetDocument(ctx, req)
		require.Error(t, err)
		require.Nil(t, r)
		require.Contains(t, err.Error(), "pet list is empty")
	})

	t.Run("test index pet duplicate customer id", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		petIDs := []int64{123, 123}

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			PetIDs: petIDs,
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return([]*petrepo.CustomerPet{
			{
				CustomerID: 123,
				ID:         123,
				PetName:    "test pet",
			},
		}, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CustomerIDs: []int64{123},
		}).Return([]*customerrepo.BusinessCustomer{
			{
				ID:        123,
				FirstName: "first",
				LastName:  "last",
			},
		}, nil)

		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().IndexPetDocument(gomock.Any(), gomock.Any()).Return(&searchpb.BulkDocumentResponse{
			Results: []*searchpb.BulkDocumentResponse_OperationResult{
				{
					DocumentId: "123",
				},
			},
		}, nil)

		mock := NewMockPet(prw, cwrw, srw)
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Pet_{
				Pet: &petpb.IndexPetDocumentRequest_Pet{
					PetIds: petIDs,
				},
			},
		}
		r, err := mock.IndexPetDocument(ctx, req)
		require.NoError(t, err)
		require.Equal(t, len(r.SuccessIds), 1)
		require.Equal(t, r.SuccessIds[0], petIDs[0])
	})
	t.Run("test index pet document when customer id not found", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		petIDs := []int64{123, 123, 1234, 12345}

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			PetIDs: petIDs,
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return([]*petrepo.CustomerPet{
			{
				CustomerID: 123,
				ID:         123,
				PetName:    "test pet",
			},
			{
				CustomerID: 1234,
				ID:         1234,
				PetName:    "test pet",
			},
			{
				CustomerID: 12345,
				ID:         12345,
				PetName:    "test pet",
			},
			{
				CustomerID: 12346,
				ID:         12345,
				PetName:    "test pet",
			},
		}, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CustomerIDs: []int64{123, 1234, 12345, 12346},
		}).Return([]*customerrepo.BusinessCustomer{
			{
				ID:        123,
				FirstName: "first",
				LastName:  "last",
			},
			{
				ID:        1234,
				FirstName: "first",
				LastName:  "last",
			},
			{
				ID:        12345,
				FirstName: "first",
				LastName:  "last",
			},
		}, nil)

		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().IndexPetDocument(gomock.Any(), gomock.Any()).Return(&searchpb.BulkDocumentResponse{
			Results: []*searchpb.BulkDocumentResponse_OperationResult{
				{
					DocumentId: "123",
				},
				{
					DocumentId: "123",
				},
				{
					DocumentId: "1234",
				},
				{
					DocumentId: "12345",
				},
			},
		}, nil)

		mock := NewMockPet(prw, cwrw, srw)
		r, err := mock.IndexPetDocument(ctx, &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Pet_{
				Pet: &petpb.IndexPetDocumentRequest_Pet{
					PetIds: petIDs,
				},
			},
		})
		require.NoError(t, err)
		require.Equal(t, petIDs, r.GetSuccessIds())
	})

	t.Run("test index pet document when search response has error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		petIDs := []int64{123, 123, 1234, 12345}

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			PetIDs: petIDs,
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return([]*petrepo.CustomerPet{
			{
				CustomerID: 123,
				ID:         123,
				PetName:    "test pet",
			},
			{
				CustomerID: 123,
				ID:         123,
				PetName:    "test pet",
			},
			{
				CustomerID: 1234,
				ID:         1234,
				PetName:    "test pet",
			},
			{
				CustomerID: 12345,
				ID:         12345,
				PetName:    "test pet",
			},
			{
				CustomerID: 12346,
				ID:         12345,
				PetName:    "test pet",
			},
		}, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CustomerIDs: []int64{123, 1234, 12345, 12346},
		}).Return([]*customerrepo.BusinessCustomer{
			{
				ID:        123,
				FirstName: "first",
				LastName:  "last",
			},
			{
				ID:        1234,
				FirstName: "first",
				LastName:  "last",
			},
			{
				ID:        12345,
				FirstName: "first",
				LastName:  "last",
			},
		}, nil)

		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().IndexPetDocument(gomock.Any(), gomock.Any()).Return(&searchpb.BulkDocumentResponse{
			Results: []*searchpb.BulkDocumentResponse_OperationResult{
				{
					DocumentId: "123",
				},
				{
					DocumentId: "123",
				},
				{
					DocumentId: "1234",
				},
				{
					DocumentId: "12345",
					Error: &searchpb.BulkDocumentResponse_Error{
						ErrorType:   "error type",
						ErrorReason: "error reason",
					},
				},
			},
			HasErrors: true,
		}, nil)

		mock := NewMockPet(prw, cwrw, srw)
		r, err := mock.IndexPetDocument(ctx, &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Pet_{
				Pet: &petpb.IndexPetDocumentRequest_Pet{
					PetIds: petIDs,
				},
			},
		})
		require.NoError(t, err)
		require.Equal(t, len(r.GetSuccessIds()), 3)
		require.Equal(t, len(r.GetErrors()), 1)
		require.Equal(t, r.GetErrors()[0].GetId(), petIDs[3])
	})

	t.Run("test index pet document when search response invalid id", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		petIDs := []int64{123, 123, 1234, 12345}

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			PetIDs: petIDs,
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return([]*petrepo.CustomerPet{
			{
				CustomerID: 123,
				ID:         123,
				PetName:    "test pet",
			},
			{
				CustomerID: 123,
				ID:         123,
				PetName:    "test pet",
			},
		}, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CustomerIDs: []int64{123},
		}).Return([]*customerrepo.BusinessCustomer{
			{
				ID:        123,
				FirstName: "first",
				LastName:  "last",
			},
		}, nil)

		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().IndexPetDocument(gomock.Any(), gomock.Any()).Return(&searchpb.BulkDocumentResponse{
			Results: []*searchpb.BulkDocumentResponse_OperationResult{
				{
					DocumentId: "invalid_id",
				},
			},
		}, nil)

		mock := NewMockPet(prw, cwrw, srw)
		r, err := mock.IndexPetDocument(ctx, &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Pet_{
				Pet: &petpb.IndexPetDocumentRequest_Pet{
					PetIds: petIDs,
				},
			},
		})
		require.Error(t, err)
		require.Nil(t, r)

		require.Contains(t, err.Error(), "strconv.ParseInt:")
	})
}

func TestSearchPetByTerm(t *testing.T) {
	t.Run("test search pet by term success", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 模拟搜索请求和响应
		term := "test"
		companyID := int64(1)
		size := int32(10)
		petID := int64(1)
		customerID := int64(2)
		petName := "test pet"
		firstName := "first"
		lastName := "last"

		// 模拟搜索服务返回的结果
		searchResponse := &searchpb.SearchDocumentResponse{
			Hits: []*searchpb.SearchDocumentResponse_Hit{
				{
					Source: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"id": {
								Kind: &structpb.Value_NumberValue{
									NumberValue: float64(petID),
								},
							},
						},
					},
				},
			},
			NextPageToken: "W10=",
		}

		// 模拟宠物数据
		pets := []*petrepo.CustomerPet{
			{
				ID:         uint(petID),
				CustomerID: int(customerID),
				PetName:    petName,
				PetTypeID:  1,
				Breed:      "Labrador",
				Weight:     "20kg",
				HairLength: "short",
			},
		}

		// 模拟客户数据
		customers := []*customerrepo.BusinessCustomer{
			{
				ID:        uint(customerID),
				FirstName: firstName,
				LastName:  lastName,
			},
		}

		// 设置mock
		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().SearchDocument(gomock.Any(), gomock.Any()).Return(searchResponse, nil)

		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			CompanyIDs: []int64{companyID},
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return(pets, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CustomerIDs: []int64{customerID},
		}).Return(customers, nil)

		// 创建测试实例
		mock := NewMockPet(prw, cwrw, srw)

		// 执行测试
		response, err := mock.SearchPetByTerm(ctx, &petsearch.TermSearchParam{
			CompanyID: companyID,
			Size:      size,
			Term:      term,
		})

		// 验证结果
		require.NoError(t, err)
		require.NotNil(t, response)
		require.Equal(t, "W10=", response.NextPageToken)
		require.Len(t, response.Pets, 1)

		pet := response.Pets[0]
		require.Equal(t, petID, pet.Id)
		require.Equal(t, customerID, pet.CustomerId)
		require.Equal(t, petName, pet.Name)
		require.Equal(t, int32(1), pet.PetType)
		require.Equal(t, "Labrador", pet.Breed)
		require.Equal(t, "20kg", pet.Weight)
		require.Equal(t, "short", pet.CoatType)

		client := pet.Client
		require.Equal(t, customerID, client.Id)
		require.Equal(t, firstName+" "+lastName, client.Name)
		require.Equal(t, firstName, client.GivenName)
		require.Equal(t, lastName, client.FamilyName)
	})

	t.Run("test search pet by term when search error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		term := "test"
		companyID := int64(1)
		size := int32(10)

		// 模拟搜索错误
		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().SearchDocument(gomock.Any(), gomock.Any()).Return(nil, errors.New("search error"))

		mock := NewMockPet(nil, nil, srw)

		response, err := mock.SearchPetByTerm(ctx, &petsearch.TermSearchParam{
			CompanyID: companyID,
			Size:      size,
			Term:      term,
		})

		require.Error(t, err)
		require.Nil(t, response)
		require.Contains(t, err.Error(), "search error")
	})

	t.Run("test search pet by term when unmarshal error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		term := "test"
		companyID := int64(1)
		size := int32(10)

		// 模拟无效的搜索响应
		searchResponse := &searchpb.SearchDocumentResponse{
			Hits: []*searchpb.SearchDocumentResponse_Hit{
				{
					Source: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"invalid_field": {
								Kind: &structpb.Value_StringValue{
									StringValue: "invalid",
								},
							},
						},
					},
				},
			},
		}
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			CompanyIDs: []int64{companyID},
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return(nil, errors.New("pet list error"))

		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().SearchDocument(gomock.Any(), gomock.Any()).Return(searchResponse, nil)

		mock := NewMockPet(prw, nil, srw)

		response, err := mock.SearchPetByTerm(ctx, &petsearch.TermSearchParam{
			CompanyID: companyID,
			Size:      size,
			Term:      term,
		})

		require.Error(t, err)
		require.Nil(t, response)
	})

	t.Run("test search pet by term when pet list error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		term := "test"
		companyID := int64(1)
		size := int32(10)
		petID := int64(1)

		// 模拟搜索响应
		searchResponse := &searchpb.SearchDocumentResponse{
			Hits: []*searchpb.SearchDocumentResponse_Hit{
				{
					Source: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"id": {
								Kind: &structpb.Value_NumberValue{
									NumberValue: float64(petID),
								},
							},
						},
					},
				},
			},
		}

		// 模拟宠物列表错误
		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().SearchDocument(gomock.Any(), gomock.Any()).Return(searchResponse, nil)

		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			CompanyIDs: []int64{companyID},
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return(nil, errors.New("pet list error"))

		mock := NewMockPet(prw, nil, srw)

		response, err := mock.SearchPetByTerm(ctx, &petsearch.TermSearchParam{
			CompanyID: companyID,
			Size:      size,
			Term:      term,
		})

		require.Error(t, err)
		require.Nil(t, response)
		require.Contains(t, err.Error(), "pet list error")
	})

	t.Run("test search pet by term when customer list error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		term := "test"
		size := int32(10)
		petID := int64(1)
		companyID := int64(1)
		customerID := int64(2)

		// 模拟搜索响应
		searchResponse := &searchpb.SearchDocumentResponse{
			Hits: []*searchpb.SearchDocumentResponse_Hit{
				{
					Source: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"id": {
								Kind: &structpb.Value_NumberValue{
									NumberValue: float64(petID),
								},
							},
						},
					},
				},
			},
		}

		// 模拟宠物数据
		pets := []*petrepo.CustomerPet{
			{
				ID:         uint(petID),
				CustomerID: int(customerID),
				PetName:    "test pet",
			},
		}

		// 模拟客户列表错误
		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().SearchDocument(gomock.Any(), gomock.Any()).Return(searchResponse, nil)

		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			CompanyIDs: []int64{companyID},
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return(pets, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CustomerIDs: []int64{customerID},
		}).Return(nil, errors.New("customer list error"))

		mock := NewMockPet(prw, cwrw, srw)

		response, err := mock.SearchPetByTerm(ctx, &petsearch.TermSearchParam{
			CompanyID: companyID,
			Size:      size,
			Term:      term,
		})

		require.Error(t, err)
		require.Nil(t, response)
		require.Contains(t, err.Error(), "customer list error")
	})

	t.Run("test MatchSyncStrategy with company flag", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		prw := petmock.NewMockReadWriter(ctrl)
		cwrw := customermock.NewMockReadWriter(ctrl)
		srw := searchmock.NewMockReadWriter(ctrl)

		mock := NewMockPet(prw, cwrw, srw)

		// 测试公司策略
		companyIDs := []int64{1, 2}
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Company_{
				Company: &petpb.IndexPetDocumentRequest_Company{
					CompanyIds: companyIDs,
				},
			},
		}

		strategy, err := mock.MatchSyncStrategy(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, strategy)

		// 验证返回的是正确的策略类型
		_, ok := strategy.(*petsearch.SyncCompanyStrategy)
		require.True(t, ok, "应该返回SyncCompanyStrategy类型")
	})

	t.Run("test MatchSyncStrategy with invalid flag", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		prw := petmock.NewMockReadWriter(ctrl)
		cwrw := customermock.NewMockReadWriter(ctrl)
		srw := searchmock.NewMockReadWriter(ctrl)

		mock := NewMockPet(prw, cwrw, srw)

		// 创建一个没有设置Flags的请求
		req := &petpb.IndexPetDocumentRequest{}

		strategy, err := mock.IndexPetDocument(ctx, req)
		require.Error(t, err)
		require.Nil(t, strategy)
		require.Contains(t, err.Error(), "invalid index pet document request")
	})

	t.Run("test MatchSyncStrategy with pet flag", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		prw := petmock.NewMockReadWriter(ctrl)
		cwrw := customermock.NewMockReadWriter(ctrl)
		srw := searchmock.NewMockReadWriter(ctrl)

		mock := NewMockPet(prw, cwrw, srw)

		// 测试宠物策略
		petIDs := []int64{1, 2, 3}
		req := &petpb.IndexPetDocumentRequest{
			Flags: &petpb.IndexPetDocumentRequest_Pet_{
				Pet: &petpb.IndexPetDocumentRequest_Pet{
					PetIds: petIDs,
				},
			},
		}

		strategy, err := mock.MatchSyncStrategy(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, strategy)

		// 验证返回的是正确的策略类型
		_, ok := strategy.(*petsearch.SyncPetStrategy)
		require.True(t, ok, "应该返回SyncPetStrategy类型")
	})

	t.Run("test SearchPetByTerm success", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		petID := int64(1)
		customerID := uint(1)
		size := int32(10)
		term := "test"
		companyID := int64(1)
		// 创建模拟的搜索结果
		hitSource, _ := structpb.NewStruct(map[string]interface{}{
			"id":   petID,
			"name": "TestPet",
			"customers": map[string]interface{}{
				"id":   int(customerID),
				"name": "Test Customer",
			},
		})

		// mock search repo
		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().SearchDocument(gomock.Any(), gomock.Any()).Return(&searchpb.SearchDocumentResponse{
			Hits: []*searchpb.SearchDocumentResponse_Hit{
				{
					Id:     strconv.FormatInt(petID, 10),
					Source: hitSource,
				},
			},
		}, nil)

		// mock pet repo
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			CompanyIDs: []int64{companyID},
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return([]*petrepo.CustomerPet{
			{
				ID:         uint(petID),
				CustomerID: int(customerID),
				PetName:    "TestPet",
				AvatarPath: "avatar/path",
				PetTypeID:  1,
				Breed:      "TestBreed",
				Weight:     "5.5kg",
				HairLength: "short",
			},
		}, nil)

		// mock customer repo
		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CustomerIDs: []int64{int64(customerID)},
		}).Return([]*customerrepo.BusinessCustomer{
			{
				ID:        customerID,
				FirstName: "First",
				LastName:  "Last",
			},
		}, nil)

		mock := NewMockPet(prw, cwrw, srw)
		resp, err := mock.SearchPetByTerm(ctx, &petsearch.TermSearchParam{
			CompanyID: companyID,
			Size:      size,
			Term:      term,
		})

		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.Pets, 1)
		require.Equal(t, petID, resp.Pets[0].Id)
		require.Equal(t, "TestPet", resp.Pets[0].Name)
		require.Equal(t, "First Last", resp.Pets[0].Client.Name)
	})

	t.Run("test SearchPetByTerm success", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		size := int32(10)
		term := "test"
		companyID := int64(1)

		// mock search repo
		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().SearchDocument(gomock.Any(), gomock.Any()).Return(&searchpb.SearchDocumentResponse{}, nil)

		mock := NewMockPet(nil, nil, srw)
		resp, err := mock.SearchPetByTerm(ctx, &petsearch.TermSearchParam{
			CompanyID:   companyID,
			Size:        size,
			Term:        term,
			SearchAfter: []float64{1.0, 2.0},
		})

		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Len(t, resp.Pets, 0)
	})

	t.Run("test search pet by term when pet id not found in database", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		term := "test"
		size := int32(10)
		petID1 := int64(1)
		petID2 := int64(2) // 这个ID将在搜索结果中存在但在数据库中不存在
		customerID := int64(3)
		companyID := int64(1)

		// 模拟搜索响应，返回两个宠物ID
		searchResponse := &searchpb.SearchDocumentResponse{
			Hits: []*searchpb.SearchDocumentResponse_Hit{
				{
					Source: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"id": {
								Kind: &structpb.Value_NumberValue{
									NumberValue: float64(petID1),
								},
							},
						},
					},
				},
				{
					Source: &structpb.Struct{
						Fields: map[string]*structpb.Value{
							"id": {
								Kind: &structpb.Value_NumberValue{
									NumberValue: float64(petID2),
								},
							},
						},
					},
				},
			},
		}

		// 模拟宠物数据，只返回petID1，不返回petID2
		pets := []*petrepo.CustomerPet{
			{
				ID:         uint(petID1),
				CustomerID: int(customerID),
				PetName:    "test pet",
			},
		}

		// 模拟客户数据
		customers := []*customerrepo.BusinessCustomer{
			{
				ID:        uint(customerID),
				FirstName: "first",
				LastName:  "last",
			},
		}

		// 设置mock
		srw := searchmock.NewMockReadWriter(ctrl)
		srw.EXPECT().SearchDocument(gomock.Any(), gomock.Any()).Return(searchResponse, nil)

		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().List(gomock.Any(), &petrepo.Query{
			CompanyIDs: []int64{companyID},
		}, &petrepo.Filter{
			Status: 1,
		}, nil).Return(pets, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)
		cwrw.EXPECT().List(gomock.Any(), &customerrepo.ListParams{
			CustomerIDs: []int64{customerID},
		}).Return(customers, nil)

		mock := NewMockPet(prw, cwrw, srw)

		// 执行测试
		response, err := mock.SearchPetByTerm(ctx, &petsearch.TermSearchParam{
			CompanyID:   companyID,
			Size:        size,
			Term:        term,
			SearchAfter: []float64{1.0, 2.0},
		})

		// 验证结果
		require.NoError(t, err)
		require.NotNil(t, response)
		require.Len(t, response.Pets, 1)              // 应该只有一个宠物，因为petID2不存在于数据库中
		require.Equal(t, petID1, response.Pets[0].Id) // 确认返回的是petID1
	})

}
