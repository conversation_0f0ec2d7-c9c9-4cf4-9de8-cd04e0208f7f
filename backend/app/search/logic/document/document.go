package document

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

type Logic struct {
	opensearch opensearch.OpenSearch
	converter  *Converter
}

func New() *Logic {
	return &Logic{
		opensearch: opensearch.New(),
		converter:  &Converter{},
	}
}

func NewMock(opensearch opensearch.OpenSearch) *Logic {
	return &Logic{
		opensearch: opensearch,
		converter:  &Converter{},
	}
}

func (l *Logic) Bulk(ctx context.Context, req *searchpb.BulkDocumentRequest) (*searchpb.BulkDocumentResponse, error) {
	bulkreq, err := l.converter.ConvertBulkRequest(ctx, req)
	if err != nil {
		return nil, err
	}
	resp, err := l.opensearch.Bulk(ctx, bulkreq)
	if err != nil {
		return nil, err
	}
	return l.converter.BuildBulkResponse(ctx, resp)
}
