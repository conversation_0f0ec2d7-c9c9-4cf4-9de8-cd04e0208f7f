load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = ["search.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/search/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/search/logic/document",
        "//backend/app/search/logic/search",
        "//backend/common/rpc/framework/log",
        "//backend/proto/search/v1:search",
        "@org_uber_go_zap//:zap",
    ],
)
