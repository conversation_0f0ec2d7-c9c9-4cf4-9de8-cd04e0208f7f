package googleadssetting

import (
	"time"

	"github.com/lib/pq"
)

// GoogleAdsSetting 对应数据库表 google_ads_setting
type GoogleAdsSetting struct {
	ID             int64         `gorm:"column:id;primary_key"`
	CompanyID      int64         `gorm:"column:company_id;not null"`
	BusinessID     int64         `gorm:"column:business_id;not null"`
	AdsCustomerIDs pq.Int64Array `gorm:"column:ads_customer_ids;type:bigint[];not null"`
	CreatedAt      time.Time     `gorm:"column:created_at;not null"`
	UpdatedAt      time.Time     `gorm:"column:updated_at;not null"`
	DeletedAt      *time.Time    `gorm:"column:deleted_at;index"`
	CreatedBy      int64         `gorm:"column:created_by;not null"`
	UpdatedBy      int64         `gorm:"column:updated_by;not null"`
	DeletedBy      *int64        `gorm:"column:deleted_by"`
}

// TableName 表名
func (GoogleAdsSetting) TableName() string {
	return "google_ads_setting"
}
