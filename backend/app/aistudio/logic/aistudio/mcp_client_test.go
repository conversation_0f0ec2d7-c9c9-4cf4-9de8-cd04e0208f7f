package aistudio

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"reflect"
	"testing"
)

// RoundTripFunc .
type RoundTripFunc func(req *http.Request) *http.Response

// RoundTrip .
func (f RoundTripFunc) RoundTrip(req *http.Request) (*http.Response, error) {
	return f(req), nil
}

// NewTestClient returns a new http Client with the provided RoundTripFunc
func NewTestClient(fn RoundTripFunc) *http.Client {
	return &http.Client{
		Transport: RoundTripFunc(fn),
	}
}

func TestMCPClient_String(t *testing.T) {
	mcpConfig := NewMCPConfig(
		"mcp-atlassian",
		"uvx",
		"stdio",
		[]string{
			"mcp-atlassian",
			"--jira-url=https://moego.atlassian.net",
			"--jira-username=<EMAIL>",
			"--jira-token=ATATT3xFfGF02xghAYSPSRN6DQMAjhoacPIPP_vp2MoDFQevHEhTBdq1t4SgmE9yoIim6pZdC",
		},
		map[string]string{},
		map[string]string{},
	)

	mcpClient := NewMCPClient(
		"gemini-2.0-flash",
		`This is a multi-step task, please complete it in order:
1. Please first Get all Jira Tasks or Stories, condition: assignee or <NAME_EMAIL>, all projects, up to 10 items. 
2. Please optimize the summaries of these tasks from the user perspective, outputs, and outcomes. A good example is: As a user role, I hope to do outputs and achieve outcomes. 
3. outputs the jira key, summary of the original jira task and the optimized summary(convert it to Chinese) in the previous step.`,
		"AIzaSyBFjCEU8GBqUJCLtNZ4gfg4aXn",
		map[string]*MCPConfig{
			mcpConfig.GetMCPName(): mcpConfig,
		},
	)

	expected := `{"model_type":"gemini-2.0-flash-001","init_prompt":"This is a multi-step task, please complete it in order:\n1. Please first Get all Jira Tasks or Stories, condition: assignee or <NAME_EMAIL>, all projects, up to 10 items. \n2. Please optimize the summaries of these tasks from the user perspective, outputs, and outcomes. A good example is: As a user role, I hope to do outputs and achieve outcomes. \n3. outputs the jira key, summary of the original jira task and the optimized summary(convert it to Chinese) in the previous step.","mcp_servers_config":{"mcp-atlassian":{"timeout":60,"command":"uvx","args":["mcp-atlassian","--jira-url=https://moego.atlassian.net","--jira-username=<EMAIL>","--jira-token=ATATT3xFfGF02xghAYSPSRN6DQMAjhoacPIPP_vp2MoDFQevHEhTBdq1t4SgmE9yoIim6pZdC"],"env":{},"transportType":"stdio"}},"api_key":"AIzaSyBFjCEU8GBqUJCLtNZ4gfg4aXn"}`
	actual, _ := json.Marshal(mcpClient)

	if string(actual) != expected {
		t.Errorf("String() = %s, want %s", actual, expected)
	}
}

func TestMCPClient_Send2Gemini(t *testing.T) {
	tests := []struct {
		name          string
		mockHTTPResp  *http.Response
		mockHTTPError error
		expectedResp  *GeminiResp
		wantErr       bool
		expectedErr   string
	}{
		{
			name: "Success",
			mockHTTPResp: &http.Response{
				StatusCode: http.StatusOK,
				Body:       io.NopCloser(bytes.NewBufferString(`{"result":"OK","dialogues":["Hello from Gemini"]}`)),
			},
			expectedResp: &GeminiResp{
				Result:    "OK",
				Dialogues: []string{"Hello from Gemini"},
			},
			wantErr: false,
		},
		{
			name: "HTTP Error",
			mockHTTPResp: &http.Response{
				StatusCode: http.StatusInternalServerError,
				Body:       io.NopCloser(bytes.NewBufferString(`{"error":"Internal Server Error"}`)),
			},
			expectedResp: &GeminiResp{}, // Expect empty GeminiResp on HTTP error
			wantErr:      true,
			expectedErr:  "unexpected status code: 500",
		},
		{
			name: "Invalid JSON Response",
			mockHTTPResp: &http.Response{
				StatusCode: http.StatusOK,
				Body:       io.NopCloser(bytes.NewBufferString(`invalid json`)),
			},
			expectedResp: nil,
			wantErr:      true,
			expectedErr:  "invalid character 'i' looking for beginning of value",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Override the http.Client in mcp_client.go for testing
			originalClient := http.DefaultClient
			defer func() { http.DefaultClient = originalClient }() // Restore original client after test

			http.DefaultClient = NewTestClient(func(req *http.Request) *http.Response {
				if tt.mockHTTPError != nil {
					t.Fatal(tt.mockHTTPError) // This will cause the test to fail if an error is expected
				}
				return tt.mockHTTPResp
			})

			// Create a dummy MCPClient for testing
			client := NewMCPClient("model", "prompt", "key", nil)

			resp, err := client.Send2Gemini(context.Background())

			if (err != nil) != tt.wantErr {
				t.Errorf("Send2Gemini() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				if err == nil || err.Error() != tt.expectedErr {
					t.Errorf("Send2Gemini() error = %v, expectedErr %v", err, tt.expectedErr)
				}
			} else {
				if !reflect.DeepEqual(resp, tt.expectedResp) {
					t.Errorf("Send2Gemini() got = %v, want %v", resp, tt.expectedResp)
				}
			}
		})
	}
}
