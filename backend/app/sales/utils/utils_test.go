package utils

import (
	"testing"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func TestParseStructJSONTagNames(t *testing.T) {
	// 测试用例1: 带有JSON标签的结构体
	type testStruct struct {
		Name string `json:"name"`
		Age  int    `json:"age,omitempty"`
		ID   string // 没有json标签
	}

	fields := ParseStructJSONTagNames(testStruct{})
	expected := []string{"name", "age"}
	if len(fields) != len(expected) {
		t.<PERSON><PERSON><PERSON>("Expected %d fields, got %d", len(expected), len(fields))
	} else {
		for i, field := range fields {
			if field != expected[i] {
				t.<PERSON><PERSON><PERSON>("Expected %s at position %d, got %s", expected[i], i, field)
			}
		}
	}

	// 测试用例2: 匿名结构体
	type anonymousStruct struct {
		Field1 string `json:"field1"`
	}

	fields = ParseStructJSONTagNames(anonymousStruct{})
	expected = []string{"field1"}
	if len(fields) != len(expected) {
		t.<PERSON><PERSON><PERSON>("Expected %d fields, got %d", len(expected), len(fields))
	} else {
		for i, field := range fields {
			if field != expected[i] {
				t.Errorf("Expected %s at position %d, got %s", expected[i], i, field)
			}
		}
	}
}

func TestNewUUID(t *testing.T) {
	uuid := NewUUID()
	if len(uuid) != 32 {
		t.Errorf("Expected UUID length of 32, got %d, %s", len(uuid), uuid)
	}

	log.Infof("uuid: %s", uuid)
}
