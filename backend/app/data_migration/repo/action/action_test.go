package action

import (
	"testing"
)

type mockAction struct {
	name    string
	depends []string
}

func (m *mockAction) Prepare() error { return nil }

func (m *mockAction) Import() (map[string]string, error) { return nil, nil }

func (m *mockAction) Name() string { return m.name }

func (m *mockAction) DependOn() []string { return m.depends }

func TestSortActions(t *testing.T) {
	t.Run("Normal dependency sort", func(t *testing.T) {
		a := &mockAction{name: "A"}
		b := &mockAction{name: "B", depends: []string{"A"}}
		c := &mockAction{name: "C", depends: []string{"B"}}
		actions := []Action{c, b, a}
		sorted, err := SortActions(actions)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}
		order := []string{sorted[0].Name(), sorted[1].Name(), sorted[2].Name()}
		want := []string{"A", "B", "C"}
		for i := range want {
			if order[i] != want[i] {
				t.<PERSON><PERSON><PERSON>("order[%d]=%s, want %s", i, order[i], want[i])
			}
		}
	})

	t.Run("No dependency", func(t *testing.T) {
		a := &mockAction{name: "A"}
		b := &mockAction{name: "B"}
		c := &mockAction{name: "C"}
		actions := []Action{a, b, c}
		sorted, err := SortActions(actions)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}
		// 顺序不唯一，只要都在即可
		found := map[string]bool{}
		for _, act := range sorted {
			found[act.Name()] = true
		}
		for _, n := range []string{"A", "B", "C"} {
			if !found[n] {
				t.Errorf("missing action %s", n)
			}
		}
	})

	t.Run("Cyclic dependency", func(t *testing.T) {
		a := &mockAction{name: "A", depends: []string{"C"}}
		b := &mockAction{name: "B", depends: []string{"A"}}
		c := &mockAction{name: "C", depends: []string{"B"}}
		actions := []Action{a, b, c}
		_, err := SortActions(actions)
		if err == nil {
			t.Fatal("expect error for cyclic dependency, got nil")
		}
	})

	t.Run("Complex dependency", func(t *testing.T) {
		//     E ──┐
		//         ▼
		// A → B → C → D
		//         ▼   ▲
		//         F ──┘
		a := &mockAction{name: "A"}
		b := &mockAction{name: "B", depends: []string{"A"}}
		c := &mockAction{name: "C", depends: []string{"B", "E"}}
		d := &mockAction{name: "D", depends: []string{"C", "F"}}
		e := &mockAction{name: "E"}
		f := &mockAction{name: "F", depends: []string{"C"}}
		actions := []Action{a, b, c, d, e, f}
		sorted, err := SortActions(actions)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}
		pos := map[string]int{}
		for i, act := range sorted {
			pos[act.Name()] = i
		}
		// A->B->C->D
		if !(pos["A"] < pos["B"] && pos["B"] < pos["C"] && pos["C"] < pos["D"]) {
			t.Errorf("excepted: A->B->C->D, actual: %v", pos)
		}
		// E->C->F->D
		if !(pos["E"] < pos["C"] && pos["C"] < pos["F"] && pos["F"] < pos["D"]) {
			t.Errorf("excepted: E->C->F->D, actual: %v", pos)
		}
	})
}
