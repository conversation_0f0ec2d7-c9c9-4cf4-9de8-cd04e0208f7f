load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "excel",
    srcs = [
        "entity.go",
        "excel.go",
        "template.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/data_migration/repo/excel",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/membership/v1:membership",
        "@com_github_xuri_excelize_v2//:excelize",
        "@org_golang_google_genproto//googleapis/type/calendarperiod",
        "@org_golang_google_genproto//googleapis/type/dayofweek",
        "@org_golang_google_protobuf//reflect/protoreflect",
    ],
)

go_test(
    name = "excel_test",
    srcs = [
        "excel_test.go",
        "template_test.go",
    ],
    embed = [":excel"],
)
