server:
  filter:
    - opentelemetry
    - debuglog
    - recovery
    - validation
  service:
    - name: backend.proto.helloworld.v1.Greeter
      ip: 127.0.0.1
      port: 9090
      protocol: grpc
      timeout: 5000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 5000
  service:
    - callee: greeter_cutomized_name
      target: ip://127.0.0.1:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: postgres.xxxx
      target: dsn://postgresql://moego_developer_240310_eff7a0dc:<EMAIL>:40132/moego_subscription?sslmode=disable
      protocol: gorm
      transport: gorm
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: mysql.xxxx
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
        - name: postgres.xxxx
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  auth:
    validation:
      enable_error_log: false
