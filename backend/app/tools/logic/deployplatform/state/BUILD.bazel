load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "state",
    srcs = [
        "phase_canary.go",
        "phase_ci.go",
        "phase_full_deployment.go",
        "phase_task_factory.go",
        "state.go",
        "task_loop.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/tools/logic/deployplatform/state",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/tools/repo/deployplatform",
        "//backend/app/tools/repo/deployplatform/entity",
        "//backend/common/rpc/framework/log",
        "@com_github_samber_lo//:lo",
    ],
)

go_test(
    name = "state_test",
    srcs = [
        "phase_canary_test.go",
        "phase_ci_test.go",
        "phase_full_deployment_test.go",
        "phase_task_factory_test.go",
        "task_loop_test.go",
    ],
    embed = [":state"],
    deps = [
        "//backend/app/tools/repo/deployplatform",
        "//backend/app/tools/repo/deployplatform/entity",
        "//backend/app/tools/repo/deployplatform/mocks",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//suite",
    ],
)
