package state

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/mocks"
)

type PhaseTaskFactoryTestSuite struct {
	suite.Suite
}

func TestPhaseTaskFactorySuite(t *testing.T) {
	suite.Run(t, new(PhaseTaskFactoryTestSuite))
}

func (t *PhaseTaskFactoryTestSuite) TestNewPhaseTask_CI() {
	manager := &mocks.ReadWriter{}
	deployTask := &entity.DeployTask{
		ID:           112233,
		State:        deployplatform.Init,
		CurrentPhase: deployplatform.PhaseCI,
		Name:         "test",
		Title:        "123456",
		CreatedBy:    "test",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		Parameters:   entity.JSONB(`{}`),
	}

	factory := &PhaseTaskFactory{}

	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Return(deployTask, nil)
	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Return([]*entity.DeployPhase{}, []*entity.DeployLog{}, nil)

	task, err := factory.NewPhaseTask(manager, deployTask)
	assert.Nil(t.T(), err)
	assert.IsType(t.T(), &CIPhase{}, task)
}

func (t *PhaseTaskFactoryTestSuite) TestNewPhaseTask_Canary() {
	manager := &mocks.ReadWriter{}
	deployTask := &entity.DeployTask{
		ID:           112233,
		State:        deployplatform.Init,
		CurrentPhase: deployplatform.PhaseCanary,
		Name:         "test",
		Title:        "123456",
		CreatedBy:    "test",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		Parameters:   entity.JSONB(`{}`),
	}

	factory := &PhaseTaskFactory{}

	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Return(deployTask, nil)
	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Return([]*entity.DeployPhase{}, []*entity.DeployLog{}, nil)

	task, err := factory.NewPhaseTask(manager, deployTask)
	assert.Nil(t.T(), err)
	assert.IsType(t.T(), &CanaryPhase{}, task)
}

func (t *PhaseTaskFactoryTestSuite) TestNewPhaseTask_FullDeployment() {
	manager := &mocks.ReadWriter{}
	deployTask := &entity.DeployTask{
		ID:           112233,
		State:        deployplatform.Init,
		CurrentPhase: deployplatform.PhaseFullDeployment,
		Name:         "test",
		Title:        "123456",
		CreatedBy:    "test",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		Parameters:   entity.JSONB(`{}`),
	}

	factory := &PhaseTaskFactory{}

	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Return(deployTask, nil)
	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Return([]*entity.DeployPhase{}, []*entity.DeployLog{}, nil)

	task, err := factory.NewPhaseTask(manager, deployTask)
	assert.Nil(t.T(), err)
	assert.IsType(t.T(), &FullDeploymentPhase{}, task)
}

func (t *PhaseTaskFactoryTestSuite) TestNewPhaseTask_UnknownPhase() {
	manager := &mocks.ReadWriter{}
	deployTask := &entity.DeployTask{
		ID:           112233,
		State:        deployplatform.Init,
		CurrentPhase: "unknown",
		Name:         "test",
		Title:        "123456",
		CreatedBy:    "test",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		Parameters:   entity.JSONB(`{}`),
	}

	factory := &PhaseTaskFactory{}

	task, err := factory.NewPhaseTask(manager, deployTask)
	assert.NotNil(t.T(), err)
	assert.Nil(t.T(), task)
}
