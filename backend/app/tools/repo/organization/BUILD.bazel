load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "organization",
    srcs = [
        "country.go",
        "organization.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/tools/repo/organization",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/tools/repo/account/entity",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/utils/pointer",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/organization/v1:organization",
    ],
)
