-- customer table - 客户核心信息表
CREATE TABLE customer
(
    id                BIGSERIAL PRIMARY KEY,
    organization_type VARCHAR(50),
    organization_id   BIGINT,
    given_name        VARCHAR(100),
    family_name       VARCHAR(100),
    customer_type     VARCHAR(50) DEFAULT 'LEAD',
    state             VARCHAR(50) DEFAULT 'ACTIVE',

    custom_fields     JSONB       DEFAULT '{}',

    life_cycle_id     BIGINT,
    owner_staff_id    BIGINT,

    deleted_time      TIMESTAMP,
    created_time      TIMESTAMP   DEFAULT CURRENT_TIMESTAMP,
    updated_time      TIMESTAMP   DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_customer_type CHECK (customer_type IN ('LEAD', 'CUSTOMER')),
    CONSTRAINT chk_customer_state CHECK (state IN ('ACTIVE', 'INACTIVE', 'DELETED'))
);

-- 索引
CREATE INDEX idx_customer_owner_staff_id ON customer (owner_staff_id);
CREATE INDEX idx_customer_custom_fields ON customer USING GIN (custom_fields);
CREATE INDEX idx_customer_organization ON customer (organization_type, organization_id);

-- 注释
COMMENT ON TABLE customer IS '客户核心信息表';
COMMENT ON COLUMN customer.organization_type IS '组织数据类型';
COMMENT ON COLUMN customer.organization_id IS '组织数据ID';
COMMENT ON COLUMN customer.given_name IS '名';
COMMENT ON COLUMN customer.family_name IS '姓';
COMMENT ON COLUMN customer.customer_type IS '客户类型：LEAD线索, CUSTOMER客户';
COMMENT ON COLUMN customer.state IS '状态：ACTIVE活跃, INACTIVE非活跃, DELETED已删除';
COMMENT ON COLUMN customer.custom_fields IS '自定义字段JSON存储';
COMMENT ON COLUMN customer.life_cycle_id IS '生命周期ID';
COMMENT ON COLUMN customer.owner_staff_id IS '负责人员工ID';
COMMENT ON COLUMN customer.deleted_time IS '删除时间';
COMMENT ON COLUMN customer.created_time IS '创建时间';
COMMENT ON COLUMN customer.updated_time IS '更新时间';

-- 触发器
CREATE TRIGGER update_customer_updated_time
    BEFORE UPDATE
    ON customer
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();


-- contact table - 联系人核心信息表
CREATE TABLE contact
(
    id                BIGSERIAL PRIMARY KEY,
    customer_id       BIGINT,
    given_name        VARCHAR(255),
    family_name       VARCHAR(255),
    email             VARCHAR(255),
    phone             VARCHAR(255),
    state             VARCHAR(50) DEFAULT 'ACTIVE',

    custom_fields     JSONB       DEFAULT '{}',

    deleted_time      TIMESTAMP,
    created_time      TIMESTAMP   DEFAULT CURRENT_TIMESTAMP,
    updated_time      TIMESTAMP   DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_contact_state CHECK (state IN ('ACTIVE', 'INACTIVE', 'DELETED'))
);

-- 索引
CREATE INDEX idx_contact_customer_id ON contact (customer_id);
CREATE INDEX idx_contact_email ON contact (email);
CREATE INDEX idx_contact_phone ON contact (phone);
CREATE INDEX idx_contact_custom_fields ON contact USING GIN (custom_fields);

-- 注释
COMMENT ON TABLE contact IS '联系人核心信息表';
COMMENT ON COLUMN contact.customer_id IS '客户ID';
COMMENT ON COLUMN contact.given_name IS '名';
COMMENT ON COLUMN contact.family_name IS '姓';
COMMENT ON COLUMN contact.email IS '邮箱地址';
COMMENT ON COLUMN contact.phone IS '电话号码';
COMMENT ON COLUMN contact.state IS '状态：ACTIVE活跃, INACTIVE非活跃, DELETED已删除';
COMMENT ON COLUMN contact.custom_fields IS '自定义字段JSON存储';
COMMENT ON COLUMN contact.deleted_time IS '删除时间';
COMMENT ON COLUMN contact.created_time IS '创建时间';
COMMENT ON COLUMN contact.updated_time IS '更新时间';

-- 触发器
CREATE TRIGGER update_contact_updated_time
    BEFORE UPDATE
    ON contact
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TABLE public.contact_tag
(
    id                BIGSERIAL PRIMARY KEY,
    organization_type varchar(255) NOT NULL,
    organization_id   BIGINT       NOT NULL,

    name              VARCHAR(255) NOT NULL,
    color             VARCHAR(255) NOT NULL,
    sort              INTEGER      NOT NULL DEFAULT 0,
    description       TEXT,

    created_time      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_time      TIMESTAMP
);
CREATE INDEX idx_organization ON contact_tag (organization_id, organization_type);
CREATE TRIGGER update_contact_tag_updated_time
    BEFORE UPDATE
    ON contact_tag
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TABLE public.contact_tag_rel
(
    id                BIGSERIAL PRIMARY KEY,
    organization_type varchar(255) NOT NULL,
    organization_id   BIGINT       NOT NULL,
    contact_id        BIGINT       NOT NULL,
    tag_id            BIGINT       NOT NULL,

    created_time      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_time      TIMESTAMP
);
CREATE INDEX idx_contact_tag_contact_id ON contact_tag_rel (contact_id);
CREATE INDEX idx_contact_tag_tag_id ON contact_tag_rel (tag_id);
CREATE UNIQUE INDEX uniq_contact_tag_id_contact_id ON contact_tag_rel (contact_id, tag_id) WHERE deleted_time IS NULL;
CREATE TRIGGER update_contact_tag_rel_updated_time
    BEFORE UPDATE
    ON contact_tag_rel
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();


-- address table - 地址表
CREATE TABLE address
(
    id                  BIGSERIAL PRIMARY KEY,
    customer_id         BIGINT,
    revision            VARCHAR(20),
    region_code         VARCHAR(10),
    language_code       VARCHAR(10),
    organization        VARCHAR(255),
    postal_code         VARCHAR(20),
    sorting_code        VARCHAR(20),
    administrative_area VARCHAR(100),
    locality            VARCHAR(100),
    sublocality         VARCHAR(100),
    address_lines       TEXT[],         -- 多行地址
    recipients          TEXT[],         -- 收件人
    latitude            DOUBLE PRECISION, -- 纬度
    longitude           DOUBLE PRECISION, -- 经度
    state               VARCHAR(50) DEFAULT 'ACTIVE',
    custom_fields       JSONB DEFAULT '{}',
    deleted_time        TIMESTAMP,
    created_time        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT chk_address_state CHECK (state IN ('ACTIVE', 'INACTIVE', 'DELETED'))
);

-- 索引
CREATE INDEX idx_address_customer_id ON address (customer_id);
CREATE INDEX idx_address_custom_fields ON address USING GIN (custom_fields);

-- 注释
COMMENT ON TABLE address IS '地址表';
COMMENT ON COLUMN address.customer_id IS '客户ID';
COMMENT ON COLUMN address.revision IS '版本';
COMMENT ON COLUMN address.region_code IS '地区代码';
COMMENT ON COLUMN address.language_code IS '语言代码';
COMMENT ON COLUMN address.organization IS '组织';
COMMENT ON COLUMN address.postal_code IS '邮政编码';
COMMENT ON COLUMN address.sorting_code IS '排序代码';
COMMENT ON COLUMN address.administrative_area IS '行政区域';
COMMENT ON COLUMN address.locality IS '地点';
COMMENT ON COLUMN address.sublocality IS '子地点';
COMMENT ON COLUMN address.address_lines IS '地址行';
COMMENT ON COLUMN address.recipients IS '收件人';
COMMENT ON COLUMN address.latitude IS '纬度';
COMMENT ON COLUMN address.longitude IS '经度';
COMMENT ON COLUMN address.state IS '状态：ACTIVE活跃, INACTIVE非活跃, DELETED已删除';
COMMENT ON COLUMN address.custom_fields IS '自定义字段JSON存储';
COMMENT ON COLUMN address.deleted_time IS '删除时间';
COMMENT ON COLUMN address.created_time IS '创建时间';
COMMENT ON COLUMN address.updated_time IS '更新时间';

-- 触发器
CREATE TRIGGER update_address_updated_time
    BEFORE UPDATE
    ON address
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();


-- custom_field_definition table - 自定义字段定义表
CREATE TABLE custom_field_definition (
                                         id BIGSERIAL PRIMARY KEY,
                                         organization_type VARCHAR(50) NOT NULL, -- 组织类型
                                         organization_id BIGINT NOT NULL,       -- 组织ID
                                         association_type VARCHAR(50) NOT NULL,
                                         field_name VARCHAR(100) NOT NULL,
                                         field_label VARCHAR(200) NOT NULL,
                                         field_type VARCHAR(50) NOT NULL,
                                         is_required BOOLEAN DEFAULT FALSE,
                                         default_value JSONB,
                                         validation_rules JSONB DEFAULT '{}',
                                         display_order INTEGER DEFAULT 0,
                                         help_text TEXT,
                                         deleted_time TIMESTAMP,
                                         state VARCHAR(50) DEFAULT 'ACTIVE',
                                         created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                         updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                         CONSTRAINT chk_custom_field_entity_type
                                             CHECK (association_type IN ('CUSTOMER', 'LEAD')),
                                         CONSTRAINT chk_custom_field_type
                                             CHECK (field_type IN ('TEXT', 'NUMBER', 'DATE', 'BOOLEAN', 'CURRENCY', 'SELECT', 'MULTI_SELECT', 'RELATION', 'MONEY')),
                                         CONSTRAINT chk_custom_field_state
                                             CHECK (state IN ('ACTIVE', 'DELETED')),
                                         UNIQUE(organization_type, organization_id, association_type, field_name)
);

-- 索引
CREATE INDEX idx_custom_field_entity ON custom_field_definition(association_type);
CREATE INDEX idx_custom_field_organization ON custom_field_definition(organization_type, organization_id);
CREATE INDEX idx_custom_field_display_order ON custom_field_definition(display_order);


-- 注释
COMMENT ON TABLE custom_field_definition IS '自定义字段定义表';
COMMENT ON COLUMN custom_field_definition.organization_type IS '组织类型：BUSINESS、COMPANY、ENTERPRISE等';
COMMENT ON COLUMN custom_field_definition.organization_id IS '组织ID';
COMMENT ON COLUMN custom_field_definition.association_type IS '实体类型：contact联系人, lead潜在客户, opportunity销售机会, activity_log活动记录';
COMMENT ON COLUMN custom_field_definition.field_name IS '字段名称(代码)';
COMMENT ON COLUMN custom_field_definition.field_label IS '字段标签(显示名称)';
COMMENT ON COLUMN custom_field_definition.field_type IS '字段类型：text文本, number数字, date日期, boolean布尔, select单选, multi_select多选';
COMMENT ON COLUMN custom_field_definition.is_required IS '是否必填';
COMMENT ON COLUMN custom_field_definition.default_value IS '默认值';
COMMENT ON COLUMN custom_field_definition.validation_rules IS '验证规则(JSON对象)';
COMMENT ON COLUMN custom_field_definition.display_order IS '显示顺序';
COMMENT ON COLUMN custom_field_definition.help_text IS '帮助文本';
COMMENT ON COLUMN custom_field_definition.deleted_time IS '删除时间';
COMMENT ON COLUMN custom_field_definition.created_time IS '创建时间';
COMMENT ON COLUMN custom_field_definition.updated_time IS '更新时间';
COMMENT ON COLUMN custom_field_definition.state IS '状态：ACTIVE活跃, DELETED已删除';



-- custom_field_option table - 自定义字段选项表
CREATE TABLE custom_field_option (
                                     id BIGSERIAL PRIMARY KEY, -- 选项ID
                                     field_id BIGINT NOT NULL, -- custom_field_definition.id 外键
                                     value_string VARCHAR(255), -- 字符串值
                                     value_int64 BIGINT,        -- 整数值
                                     value_double DOUBLE PRECISION, -- 浮点值
                                     value_bool BOOLEAN,        -- 布尔值
                                     value_money JSONB,         -- 金额（如需支持复杂结构）
                                     value_relation JSONB,      -- 关系型（如 {"entity": "customer", "id": 123}）
                                     label VARCHAR(200) NOT NULL, -- 选项标签
                                     sort_order INTEGER DEFAULT 0, -- 排序
                                     state VARCHAR(50) DEFAULT 'ACTIVE', -- 状态，保持与 definition 表一致
                                     created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
                                     updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
                                     deleted_time TIMESTAMP,
                                     CONSTRAINT chk_option_state CHECK (state IN ('ACTIVE', 'DELETED'))
);

-- 索引
CREATE INDEX idx_option_field_id ON custom_field_option(field_id);
CREATE INDEX idx_option_value_string ON custom_field_option(value_string);
CREATE INDEX idx_option_value_int64 ON custom_field_option(value_int64);
CREATE INDEX idx_option_value_double ON custom_field_option(value_double);
CREATE INDEX idx_option_state ON custom_field_option(state);

-- 注释
COMMENT ON TABLE custom_field_option IS '自定义字段选项表';
COMMENT ON COLUMN custom_field_option.field_id IS '自定义字段定义ID';
COMMENT ON COLUMN custom_field_option.value_string IS '字符串值';
COMMENT ON COLUMN custom_field_option.value_int64 IS '整数值';
COMMENT ON COLUMN custom_field_option.value_double IS '浮点值';
COMMENT ON COLUMN custom_field_option.value_bool IS '布尔值';
COMMENT ON COLUMN custom_field_option.value_money IS '金额类型值(JSON)';
COMMENT ON COLUMN custom_field_option.value_relation IS '关系型值(JSON)';
COMMENT ON COLUMN custom_field_option.label IS '选项标签';
COMMENT ON COLUMN custom_field_option.sort_order IS '排序顺序';
COMMENT ON COLUMN custom_field_option.state IS '状态：ACTIVE活跃, DELETED已删除';
COMMENT ON COLUMN custom_field_option.created_time IS '创建时间';
COMMENT ON COLUMN custom_field_option.updated_time IS '更新时间';
COMMENT ON COLUMN custom_field_option.deleted_time IS '删除时间';

-- 触发器
CREATE TRIGGER update_custom_field_option_updated_time
    BEFORE UPDATE ON custom_field_option
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
