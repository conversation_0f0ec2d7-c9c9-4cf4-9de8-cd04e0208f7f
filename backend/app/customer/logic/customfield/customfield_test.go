package customfield

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/genproto/googleapis/type/money"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	repocf "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customfield"
	customfieldmock "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customfield/mock"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

func newTestDefinitionWithOptions() *DefinitionWithOptions {
	return &DefinitionWithOptions{
		Definition: &Definition{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   1001,
			AssociationType:  customerpb.CustomField_CUSTOMER,
			FieldName:        "test_field",
			FieldLabel:       "测试字段",
			FieldType:        customerpb.CustomField_TEXT,
			IsRequired:       true,
			DefaultValue:     "default",
			ValidationRules:  map[string]any{"min": 1},
			DisplayOrder:     1,
			HelpText:         "帮助文本",
			State:            customerpb.CustomField_ACTIVE,
			CreatedTime:      time.Now(),
			UpdatedTime:      time.Now(),
		},
		Options: []*Option{
			{
				ID:          11,
				FieldID:     1,
				Label:       "选项1",
				SortOrder:   1,
				State:       customerpb.CustomField_ACTIVE,
				CreatedTime: time.Now(),
				UpdatedTime: time.Now(),
			},
		},
	}
}

func TestNew(t *testing.T) {
	t.Run("New方法", func(t *testing.T) {
		postgres.SetDB(&gorm.DB{})
		logic := New()
		require.NotNil(t, logic)
	})
}

func TestLogic_Create(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	repo := customfieldmock.NewMockRepository(ctrl)
	logic := NewByParams(repo)

	t.Run("创建成功", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, dwo *repocf.DefinitionWithOptions) (*repocf.DefinitionWithOptions, error) {
				// 验证传入参数的关键字段
				require.NotNil(t, dwo)
				require.NotNil(t, dwo.Definition)
				require.Equal(t, "test_field", dwo.Definition.FieldName)
				require.Equal(t, "测试字段", dwo.Definition.FieldLabel)
				require.Len(t, dwo.Options, 1)
				require.Equal(t, "选项1", dwo.Options[0].Label)
				return toRepoDwoForTest(in), nil
			},
		)
		out, err := logic.Create(context.Background(), in)
		require.NoError(t, err)
		require.NotNil(t, out)
		require.Equal(t, in.Definition.FieldName, out.Definition.FieldName)
	})

	t.Run("repo返回错误", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, errors.New("db error"))
		out, err := logic.Create(context.Background(), in)
		require.Error(t, err)
		require.Nil(t, out)
	})

	t.Run("toRepo error", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		// 让 DefaultValue 为不能序列化的类型，触发 toRepo 返回 error
		in.Definition.DefaultValue = make(chan int)
		out, err := logic.Create(context.Background(), in)
		require.Error(t, err)
		require.Nil(t, out)
	})
}

func TestLogic_Update(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	repo := customfieldmock.NewMockRepository(ctrl)
	logic := NewByParams(repo)

	t.Run("更新成功", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, dwo *repocf.DefinitionWithOptions) (*repocf.DefinitionWithOptions, error) {
				// 验证更新参数
				require.NotNil(t, dwo)
				require.NotNil(t, dwo.Definition)
				require.Equal(t, "test_field", dwo.Definition.FieldName)
				return toRepoDwoForTest(in), nil
			},
		)
		out, err := logic.Update(context.Background(), in)
		require.NoError(t, err)
		require.NotNil(t, out)
		require.Equal(t, in.Definition.FieldName, out.Definition.FieldName)
	})

	t.Run("repo返回错误", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil, errors.New("db error"))
		out, err := logic.Update(context.Background(), in)
		require.Error(t, err)
		require.Nil(t, out)
	})

	t.Run("toRepo error", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		// 让 DefaultValue 为不能序列化的类型，触发 toRepo 返回 error
		in.Definition.DefaultValue = make(chan int)
		out, err := logic.Update(context.Background(), in)
		require.Error(t, err)
		require.Nil(t, out)
	})
}

func TestLogic_Get(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	repo := customfieldmock.NewMockRepository(ctrl)
	logic := NewByParams(repo)

	t.Run("获取成功", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repo.EXPECT().Get(gomock.Any(), int64(1)).DoAndReturn(
			func(ctx context.Context, id int64) (*repocf.DefinitionWithOptions, error) {
				// 验证传入的ID
				require.Equal(t, int64(1), id)
				return toRepoDwoForTest(in), nil
			},
		)
		out, err := logic.Get(context.Background(), &GetDefinitionParams{ID: 1})
		require.NoError(t, err)
		require.NotNil(t, out)
		require.Equal(t, in.Definition.FieldName, out.Definition.FieldName)
	})

	t.Run("repo返回错误", func(t *testing.T) {
		repo.EXPECT().Get(gomock.Any(), int64(1)).Return(nil, errors.New("db error"))
		out, err := logic.Get(context.Background(), &GetDefinitionParams{ID: 1})
		require.Error(t, err)
		require.Nil(t, out)
	})

	t.Run("记录不存在", func(t *testing.T) {
		repo.EXPECT().Get(gomock.Any(), int64(1)).Return(nil, gorm.ErrRecordNotFound)
		out, err := logic.Get(context.Background(), &GetDefinitionParams{ID: 1})
		require.Error(t, err)
		require.Nil(t, out)
		// 检查是否返回了正确的错误码（检查错误码120101）
		require.Contains(t, err.Error(), "120101")
	})
}

func TestLogic_List(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	repo := customfieldmock.NewMockRepository(ctrl)
	logic := NewByParams(repo)

	t.Run("列表成功", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, filter *repocf.DefinitionListFilter, pagination *repocf.Pagination, orderBy *repocf.DefinitionOrderBy) (*repocf.DefinitionCursorResult, error) {
				// 验证传入的参数
				require.NotNil(t, filter)
				require.Equal(t, customerpb.OrganizationRef_BUSINESS, filter.OrganizationType)
				require.Equal(t, int64(1001), filter.OrganizationID)
				require.Equal(t, customerpb.CustomField_CUSTOMER, filter.AssociationType)
				require.NotNil(t, pagination)
				require.Equal(t, int32(10), pagination.PageSize)
				require.NotNil(t, orderBy)

				return &repocf.DefinitionCursorResult{
					Data:       []*repocf.DefinitionWithOptions{toRepoDwoForTest(in)},
					HasNext:    false,
					TotalCount: nil,
				}, nil
			},
		)
		params := &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   1001,
				AssociationType:  customerpb.CustomField_CUSTOMER,
			},
			Pagination: &ListDefinitionsPagination{
				PageSize: 10,
			},
			OrderBy: &ListDefinitionsOrderBy{
				Field:     customerpb.ListCustomFieldsRequest_Sorting_FIELD_UNSPECIFIED,
				Direction: customerpb.ListCustomFieldsRequest_Sorting_DIRECTION_UNSPECIFIED,
			},
		}
		out, err := logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Len(t, out.Definitions, 1)
		require.Equal(t, in.Definition.FieldName, out.Definitions[0].Definition.FieldName)
	})

	t.Run("列表成功-有下一页和总数", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		totalCount := int64(100)
		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{toRepoDwoForTest(in)},
			HasNext:    true,
			TotalCount: &totalCount,
		}, nil)
		params := &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   1001,
				AssociationType:  customerpb.CustomField_CUSTOMER,
			},
			Pagination: &ListDefinitionsPagination{
				PageSize: 10,
			},
		}
		out, err := logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Len(t, out.Definitions, 1)
		require.True(t, out.HasNext)
		require.NotNil(t, out.TotalSize)
		require.Equal(t, int64(100), *out.TotalSize)
		require.NotEmpty(t, out.NextToken)
	})

	t.Run("repo返回错误", func(t *testing.T) {
		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("db error"))
		params := &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   1001,
				AssociationType:  customerpb.CustomField_CUSTOMER,
			},
		}
		out, err := logic.List(context.Background(), params)
		require.Error(t, err)
		require.Nil(t, out)
	})

	t.Run("toLogic error", func(t *testing.T) {
		// 构造一个会导致toLogic错误的情况
		in := newTestDefinitionWithOptions()
		repoDwo := toRepoDwoForTest(in)
		// 创建一个会导致转换错误的option
		repoDwo.Options = append(repoDwo.Options, &repocf.Option{
			ID:      999,
			FieldID: 1,
			Label:   "错误选项",
		})

		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{repoDwo},
			HasNext:    false,
			TotalCount: nil,
		}, nil)

		params := &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   1001,
			},
		}
		out, err := logic.List(context.Background(), params)
		// 由于toLogic不会返回错误（会吞掉错误），这里应该成功
		require.NoError(t, err)
		require.NotNil(t, out)
	})
}

func TestEntityConvert(t *testing.T) {
	t.Run("toRepo/toLogic全流程", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repoDwo, err := toRepo(in)
		require.NoError(t, err)
		logicDwo := toLogic(repoDwo)
		require.Equal(t, in.Definition.FieldName, logicDwo.Definition.FieldName)
		// 检查 option
		require.Equal(t, in.Options[0].Label, logicDwo.Options[0].Label)
	})

	t.Run("toRepoDef/toLogicDef序列化反序列化", func(t *testing.T) {
		def := &Definition{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   1001,
			AssociationType:  customerpb.CustomField_CUSTOMER,
			FieldName:        "test_field",
			FieldLabel:       "测试字段",
			FieldType:        customerpb.CustomField_TEXT,
			IsRequired:       true,
			DefaultValue:     map[string]any{"foo": "bar"},
			ValidationRules:  map[string]any{"min": 1},
			DisplayOrder:     1,
			HelpText:         "帮助文本",
			State:            customerpb.CustomField_ACTIVE,
			CreatedTime:      time.Now(),
			UpdatedTime:      time.Now(),
		}
		repoDef, err := toRepoDef(def)
		require.NoError(t, err)
		logicDef := toLogicDef(repoDef)
		require.Equal(t, def.FieldName, logicDef.FieldName)
		// 检查 DefaultValue/ValidationRules 反序列化
		require.NotNil(t, logicDef.DefaultValue)
		require.NotNil(t, logicDef.ValidationRules)
	})

	t.Run("toRepoOpt/toLogicOpt序列化反序列化", func(t *testing.T) {
		opt := &Option{
			ID:         1,
			FieldID:    1,
			Label:      "选项1",
			SortOrder:  1,
			State:      customerpb.CustomField_ACTIVE,
			ValueMoney: map[string]any{"amount": 100},
		}
		repoOpt, err := toRepoOpt(opt)
		require.NoError(t, err)
		logicOpt := toLogicOpt(repoOpt)
		require.Equal(t, opt.Label, logicOpt.Label)
		require.NotNil(t, logicOpt.ValueMoney)
	})

	t.Run("toRepoDef/toLogicDef nil安全", func(t *testing.T) {
		def, err := toRepoDef(nil)
		require.NoError(t, err)
		require.Nil(t, def)
		logicDef := toLogicDef(nil)
		require.Nil(t, logicDef)
	})
}

func TestEntityConvert_Errors(t *testing.T) {
	t.Run("toRepoDef marshal error", func(t *testing.T) {
		def := &Definition{
			DefaultValue: make(chan int), // 不能序列化
		}
		_, err := toRepoDef(def)
		require.Error(t, err)
	})

	t.Run("toLogicDef unmarshal error", func(t *testing.T) {
		repoDef := &repocf.Definition{
			DefaultValue: []byte("{invalid json"),
		}
		// toLogicDef不再返回错误，无效JSON会被忽略
		logicDef := toLogicDef(repoDef)
		require.NotNil(t, logicDef)
	})

	t.Run("toRepoOpt marshal error", func(t *testing.T) {
		opt := &Option{
			ValueMoney: make(chan int), // 不能序列化
		}
		_, err := toRepoOpt(opt)
		require.Error(t, err)
	})

	t.Run("toRepoOpt ValueRelation marshal error", func(t *testing.T) {
		opt := &Option{
			ValueRelation: make(chan int), // 不能序列化
		}
		_, err := toRepoOpt(opt)
		require.Error(t, err)
	})

	t.Run("toRepo nil", func(t *testing.T) {
		out, err := toRepo(nil)
		require.NoError(t, err)
		require.Nil(t, out)
	})

	t.Run("toLogic nil", func(t *testing.T) {
		out := toLogic(nil)
		require.Nil(t, out)
	})

	// toRepo: dwo.Definition 不能序列化
	t.Run("toRepo definition marshal error", func(t *testing.T) {
		in := &DefinitionWithOptions{
			Definition: &Definition{DefaultValue: make(chan int)},
			Options:    []*Option{},
		}
		out, err := toRepo(in)
		require.Error(t, err)
		require.Nil(t, out)
	})

	// toRepo: dwo.Options 里有一个 option 不能序列化
	t.Run("toRepo option marshal error", func(t *testing.T) {
		in := &DefinitionWithOptions{
			Definition: &Definition{FieldName: "f"},
			Options: []*Option{
				{Label: "ok"},
				{Label: "bad", ValueMoney: make(chan int)},
			},
		}
		out, err := toRepo(in)
		require.Error(t, err)
		require.Nil(t, out)
	})

	// toLogic: dwo.Definition 反序列化失败
	t.Run("toLogic definition unmarshal error", func(t *testing.T) {
		dwo := &repocf.DefinitionWithOptions{
			Definition: &repocf.Definition{DefaultValue: []byte("{invalid json")},
			Options:    []*repocf.Option{},
		}
		out := toLogic(dwo)
		require.NotNil(t, out) // toLogic不再返回错误
	})
}

func TestEntityConvert_DeepBranches(t *testing.T) {
	t.Run("toRepo with empty options", func(t *testing.T) {
		in := &DefinitionWithOptions{Definition: &Definition{FieldName: "f"}}
		out, err := toRepo(in)
		require.NoError(t, err)
		require.NotNil(t, out)
		require.Empty(t, out.Options)
	})

	t.Run("toRepo with nil option in slice", func(t *testing.T) {
		in := &DefinitionWithOptions{
			Definition: &Definition{FieldName: "f"},
			Options:    []*Option{nil},
		}
		out, err := toRepo(in)
		require.NoError(t, err)
		require.NotNil(t, out)
		require.Len(t, out.Options, 1)
		require.Nil(t, out.Options[0])
	})

	t.Run("toLogic with nil option in slice", func(t *testing.T) {
		repoDwo := &repocf.DefinitionWithOptions{
			Definition: &repocf.Definition{FieldName: "f"},
			Options:    []*repocf.Option{nil},
		}
		out := toLogic(repoDwo)
		require.NotNil(t, out)
		require.Len(t, out.Options, 1)
		require.Nil(t, out.Options[0])
	})

	t.Run("toLogicDef with empty DefaultValue/ValidationRules", func(t *testing.T) {
		repoDef := &repocf.Definition{
			DefaultValue:    []byte(""),
			ValidationRules: []byte(""),
		}
		out := toLogicDef(repoDef)
		require.NotNil(t, out)
	})

	t.Run("toLogicOpt with empty ValueMoney/ValueRelation", func(t *testing.T) {
		repoOpt := &repocf.Option{
			ValueMoney:    []byte(""),
			ValueRelation: []byte(""),
		}
		out := toLogicOpt(repoOpt)
		require.NotNil(t, out)
	})

	t.Run("toRepoDef with nil ValidationRules", func(t *testing.T) {
		def := &Definition{FieldName: "f", ValidationRules: nil}
		out, err := toRepoDef(def)
		require.NoError(t, err)
		require.NotNil(t, out)
	})

	t.Run("toRepoOpt with ValidationRules", func(t *testing.T) {
		out, err := toRepoOpt(&Option{Label: "l", ValueMoney: map[string]any{"amount": 100}})
		require.NoError(t, err)
		require.NotNil(t, out)
	})

	t.Run("toRepoOpt with error ValidationRules", func(t *testing.T) {
		out, err := toRepoOpt(&Option{Label: "l", ValueMoney: make(chan int)})
		require.Error(t, err)
		require.Nil(t, out)
	})

	t.Run("toRepoOpt with nil ValueMoney/ValueRelation", func(t *testing.T) {
		opt := &Option{Label: "l", ValueMoney: nil, ValueRelation: nil}
		out, err := toRepoOpt(opt)
		require.NoError(t, err)
		require.NotNil(t, out)
	})

	t.Run("toLogicDef with partial fields", func(t *testing.T) {
		repoDef := &repocf.Definition{FieldName: "f"}
		out := toLogicDef(repoDef)
		require.NotNil(t, out)
	})

	t.Run("toLogicOpt with partial fields", func(t *testing.T) {
		repoOpt := &repocf.Option{Label: "l"}
		out := toLogicOpt(repoOpt)
		require.NotNil(t, out)
	})

	t.Run("toRepoOpt with error ValueRelation", func(t *testing.T) {
		out, err := toRepoOpt(&Option{Label: "l", ValueRelation: make(chan int)})
		require.Error(t, err)
		require.Nil(t, out)
	})
}

func TestLogic_NilParams(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	repo := customfieldmock.NewMockRepository(ctrl)
	logic := NewByParams(repo)

	t.Run("Create nil", func(t *testing.T) {
		out, err := logic.Create(context.Background(), nil)
		require.Error(t, err)
		require.Contains(t, err.Error(), "definition with options is nil")
		require.Nil(t, out)
	})

	t.Run("Update nil", func(t *testing.T) {
		out, err := logic.Update(context.Background(), nil)
		require.Error(t, err)
		require.Contains(t, err.Error(), "definition with options is nil")
		require.Nil(t, out)
	})

	t.Run("Get nil params", func(t *testing.T) {
		out, err := logic.Get(context.Background(), nil)
		require.Error(t, err)
		require.Contains(t, err.Error(), "get params is nil")
		require.Nil(t, out)
	})

	t.Run("List nil params", func(t *testing.T) {
		out, err := logic.List(context.Background(), nil)
		require.Error(t, err)
		require.Contains(t, err.Error(), "list params is nil")
		require.Nil(t, out)
	})

	t.Run("repo返回nil", func(t *testing.T) {
		repo.EXPECT().Get(gomock.Any(), int64(2)).Return(nil, nil)
		out, err := logic.Get(context.Background(), &GetDefinitionParams{ID: 2})
		require.NoError(t, err)
		require.Nil(t, out)
	})
}

// ToPB方法测试 - 这是之前缺失的主要覆盖率
func TestEntityToPB(t *testing.T) {
	t.Run("Option.ToPB - 完整测试", func(t *testing.T) {
		now := time.Now()

		// 测试空值
		var nilOpt *Option
		require.Nil(t, nilOpt.ToPB())

		// 测试字符串值
		opt := &Option{
			ID:          1,
			FieldID:     10,
			ValueString: "test_value",
			Label:       "测试标签",
			SortOrder:   1,
			State:       customerpb.CustomField_ACTIVE,
			CreatedTime: now,
			UpdatedTime: now,
		}
		pb := opt.ToPB()
		require.NotNil(t, pb)
		require.NotNil(t, pb.Value)
		require.Equal(t, "test_value", pb.Value.GetString_())
		require.Equal(t, "测试标签", pb.Label)
		require.Equal(t, int32(1), pb.SortOrder)
		require.Equal(t, customerpb.CustomField_ACTIVE, pb.State)

		// 测试整数值
		opt.ValueString = ""
		opt.ValueInt64 = 123
		pb = opt.ToPB()
		require.Equal(t, int64(123), pb.Value.GetInt64())

		// 测试浮点值
		opt.ValueInt64 = 0
		opt.ValueDouble = 123.45
		pb = opt.ToPB()
		require.Equal(t, 123.45, pb.Value.GetDoubleValue())

		// 测试布尔值
		opt.ValueDouble = 0
		opt.ValueBool = true
		pb = opt.ToPB()
		require.True(t, pb.Value.GetBool())

		// 测试Money值
		opt.ValueBool = false
		opt.ValueMoney = map[string]any{
			"currency_code": "USD",
			"units":         float64(100),
			"nanos":         float64(500000000),
		}
		pb = opt.ToPB()
		require.NotNil(t, pb.Value.GetMoney())
		require.Equal(t, "USD", pb.Value.GetMoney().CurrencyCode)
		require.Equal(t, int64(100), pb.Value.GetMoney().Units)
		require.Equal(t, int32(500000000), pb.Value.GetMoney().Nanos)

		// 测试Relation值
		opt.ValueMoney = nil
		opt.ValueRelation = map[string]any{
			"entity": customerpb.CustomField_Value_Relation_CUSTOMER.String(),
			"id":     float64(456),
		}
		pb = opt.ToPB()
		require.NotNil(t, pb.Value.GetRelation())
		require.Equal(t, customerpb.CustomField_Value_Relation_CUSTOMER, pb.Value.GetRelation().Entity)
		require.Equal(t, int64(456), pb.Value.GetRelation().Id)

		// 测试LEAD类型的Relation
		opt.ValueRelation = map[string]any{
			"entity": customerpb.CustomField_Value_Relation_LEAD.String(),
			"id":     float64(789),
		}
		pb = opt.ToPB()
		require.Equal(t, customerpb.CustomField_Value_Relation_LEAD, pb.Value.GetRelation().Entity)
		require.Equal(t, int64(789), pb.Value.GetRelation().Id)

		// 测试没有任何值的情况
		optEmpty := &Option{
			Label:       "空选项",
			SortOrder:   1,
			State:       customerpb.CustomField_ACTIVE,
			CreatedTime: now,
			UpdatedTime: now,
		}
		pbEmpty := optEmpty.ToPB()
		require.NotNil(t, pbEmpty)
		require.Nil(t, pbEmpty.Value)
		require.Equal(t, "空选项", pbEmpty.Label)
	})

	t.Run("Definition.ToPB - 完整测试", func(t *testing.T) {
		now := time.Now()
		deletedTime := time.Now().Add(time.Hour)

		// 测试空值
		var nilDef *Definition
		require.Nil(t, nilDef.ToPB())

		// 测试完整定义
		def := &Definition{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   1001,
			AssociationType:  customerpb.CustomField_CUSTOMER,
			FieldName:        "test_field",
			FieldLabel:       "测试字段",
			FieldType:        customerpb.CustomField_TEXT,
			IsRequired:       true,
			DefaultValue:     "default_value",
			ValidationRules:  map[string]any{"min": 1, "max": 100},
			DisplayOrder:     5,
			HelpText:         "帮助文本",
			State:            customerpb.CustomField_ACTIVE,
			DeletedTime:      &deletedTime,
			CreatedTime:      now,
			UpdatedTime:      now,
		}

		pb := def.ToPB()
		require.NotNil(t, pb)
		require.Equal(t, int64(1), pb.Id)
		require.Equal(t, customerpb.OrganizationRef_BUSINESS, pb.Organization.Type)
		require.Equal(t, int64(1001), pb.Organization.Id)
		require.Equal(t, customerpb.CustomField_CUSTOMER, pb.AssociationType)
		require.Equal(t, "test_field", pb.Key)
		require.Equal(t, "测试字段", pb.Label)
		require.Equal(t, customerpb.CustomField_TEXT, pb.Type)
		require.True(t, pb.IsRequired)
		require.Equal(t, int32(5), pb.DisplayOrder)
		require.Equal(t, "帮助文本", pb.HelpText)
		require.Equal(t, customerpb.CustomField_ACTIVE, pb.State)

		// 检查默认值
		require.NotNil(t, pb.DefaultValue)
		require.Equal(t, "default_value", pb.DefaultValue.GetString_())

		// 检查验证规则
		require.NotNil(t, pb.ValidationRules)

		// 检查时间戳
		require.NotNil(t, pb.CreateTime)
		require.NotNil(t, pb.UpdateTime)
		require.NotNil(t, pb.DeleteTime)

		// 测试不同类型的默认值
		// 整数默认值
		def.DefaultValue = int64(123)
		pb = def.ToPB()
		require.Equal(t, int64(123), pb.DefaultValue.GetInt64())

		// 浮点默认值
		def.DefaultValue = float64(123.45)
		pb = def.ToPB()
		require.Equal(t, 123.45, pb.DefaultValue.GetDoubleValue())

		// 布尔默认值
		def.DefaultValue = true
		pb = def.ToPB()
		require.True(t, pb.DefaultValue.GetBool())

		// Money默认值
		moneyObj := &money.Money{
			CurrencyCode: "USD",
			Units:        100,
			Nanos:        500000000,
		}
		def.DefaultValue = moneyObj
		pb = def.ToPB()
		require.Equal(t, moneyObj, pb.DefaultValue.GetMoney())

		// Relation默认值
		relationObj := &customerpb.CustomField_Value_Relation{
			Entity: customerpb.CustomField_Value_Relation_CUSTOMER,
			Id:     456,
		}
		def.DefaultValue = relationObj
		pb = def.ToPB()
		require.Equal(t, relationObj, pb.DefaultValue.GetRelation())

		// Map类型默认值（Money）
		def.DefaultValue = map[string]any{
			"currency_code": "EUR",
			"units":         float64(200),
			"nanos":         float64(250000000),
		}
		pb = def.ToPB()
		require.NotNil(t, pb.DefaultValue.GetMoney())
		require.Equal(t, "EUR", pb.DefaultValue.GetMoney().CurrencyCode)
		require.Equal(t, int64(200), pb.DefaultValue.GetMoney().Units)
		require.Equal(t, int32(250000000), pb.DefaultValue.GetMoney().Nanos)

		// Map类型默认值（Relation）
		def.DefaultValue = map[string]any{
			"entity": customerpb.CustomField_Value_Relation_LEAD.String(),
			"id":     float64(789),
		}
		pb = def.ToPB()
		require.NotNil(t, pb.DefaultValue.GetRelation())
		require.Equal(t, customerpb.CustomField_Value_Relation_LEAD, pb.DefaultValue.GetRelation().Entity)
		require.Equal(t, int64(789), pb.DefaultValue.GetRelation().Id)

		// 时间类型默认值
		timeVal := time.Now()
		def.DefaultValue = timeVal
		pb = def.ToPB()
		require.NotNil(t, pb.DefaultValue.GetTimestampTime())
		require.Equal(t, timeVal.Unix(), pb.DefaultValue.GetTimestampTime().Seconds)

		// 测试没有删除时间的情况
		def.DeletedTime = nil
		pb = def.ToPB()
		require.Nil(t, pb.DeleteTime)

		// 测试没有默认值的情况
		def.DefaultValue = nil
		pb = def.ToPB()
		require.Nil(t, pb.DefaultValue)

		// 测试没有验证规则的情况
		def.ValidationRules = nil
		pb = def.ToPB()
		require.Nil(t, pb.ValidationRules)
	})

	t.Run("DefinitionWithOptions.ToPB - 完整测试", func(t *testing.T) {
		now := time.Now()

		// 测试空值
		var nilDwo *DefinitionWithOptions
		require.Nil(t, nilDwo.ToPB())

		// 测试没有定义的情况
		dwo := &DefinitionWithOptions{}
		require.Nil(t, dwo.ToPB())

		// 测试完整的DefinitionWithOptions
		dwo = &DefinitionWithOptions{
			Definition: &Definition{
				ID:               1,
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   1001,
				AssociationType:  customerpb.CustomField_CUSTOMER,
				FieldName:        "test_select",
				FieldLabel:       "测试选择字段",
				FieldType:        customerpb.CustomField_SELECT,
				IsRequired:       true,
				DefaultValue:     "option1",
				ValidationRules:  map[string]any{"required": true},
				DisplayOrder:     1,
				HelpText:         "选择帮助",
				State:            customerpb.CustomField_ACTIVE,
				CreatedTime:      now,
				UpdatedTime:      now,
			},
			Options: []*Option{
				{
					ID:          11,
					FieldID:     1,
					ValueString: "option1",
					Label:       "选项1",
					SortOrder:   1,
					State:       customerpb.CustomField_ACTIVE,
					CreatedTime: now,
					UpdatedTime: now,
				},
				{
					ID:          12,
					FieldID:     1,
					ValueString: "option2",
					Label:       "选项2",
					SortOrder:   2,
					State:       customerpb.CustomField_ACTIVE,
					CreatedTime: now,
					UpdatedTime: now,
				},
			},
		}

		pb := dwo.ToPB()
		require.NotNil(t, pb)
		require.Equal(t, "test_select", pb.Key)
		require.Equal(t, "测试选择字段", pb.Label)
		require.Equal(t, customerpb.CustomField_SELECT, pb.Type)

		// 检查选项
		require.Len(t, pb.Options, 2)
		require.Equal(t, "选项1", pb.Options[0].Label)
		require.Equal(t, "option1", pb.Options[0].Value.GetString_())
		require.Equal(t, int32(1), pb.Options[0].SortOrder)
		require.Equal(t, "选项2", pb.Options[1].Label)
		require.Equal(t, "option2", pb.Options[1].Value.GetString_())
		require.Equal(t, int32(2), pb.Options[1].SortOrder)

		// 测试空选项的情况
		dwo.Options = nil
		pb = dwo.ToPB()
		require.Empty(t, pb.Options)

		// 测试有nil选项的情况
		dwo.Options = []*Option{nil, {Label: "valid"}}
		pb = dwo.ToPB()
		require.Len(t, pb.Options, 1) // nil的选项会被过滤掉
		require.Equal(t, "valid", pb.Options[0].Label)

		// 测试Definition.ToPB返回nil的情况（这种情况实际不会发生，但为了覆盖所有分支）
		dwoBroken := &DefinitionWithOptions{Definition: nil}
		require.Nil(t, dwoBroken.ToPB())
	})
}

// 添加更多边界情况的测试
func TestEntityToPB_EdgeCases(t *testing.T) {
	t.Run("Option ToPB with invalid Money map", func(t *testing.T) {
		opt := &Option{
			Label:      "测试",
			ValueMoney: "not a map", // 不是map类型
		}
		pb := opt.ToPB()
		require.NotNil(t, pb)
		require.Nil(t, pb.Value) // 应该没有值因为类型不匹配
	})

	t.Run("Option ToPB with invalid Relation map", func(t *testing.T) {
		opt := &Option{
			Label:         "测试",
			ValueRelation: "not a map", // 不是map类型
		}
		pb := opt.ToPB()
		require.NotNil(t, pb)
		require.Nil(t, pb.Value) // 应该没有值因为类型不匹配
	})

	t.Run("Definition ToPB with invalid map types", func(t *testing.T) {
		def := &Definition{
			ID:         1,
			FieldName:  "test",
			FieldLabel: "测试",
			FieldType:  customerpb.CustomField_TEXT,
			DefaultValue: map[string]any{
				"unknown_field": "value", // 不是Money也不是Relation的map
			},
		}
		pb := def.ToPB()
		require.NotNil(t, pb)
		require.Nil(t, pb.DefaultValue) // 无法识别的map类型不会转换
	})
}

// 添加更多的转换函数测试以达到100%覆盖率
func TestConversionFunctions_CompleteCoverage(t *testing.T) {
	t.Run("List函数的所有分支", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customfieldmock.NewMockRepository(ctrl)
		logic := NewByParams(repo)

		// 测试没有过滤器的情况
		repo.EXPECT().List(gomock.Any(), nil, gomock.Any(), gomock.Any()).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{},
			HasNext:    false,
			TotalCount: nil,
		}, nil)

		params := &ListDefinitionsParams{
			Filter: nil, // 没有过滤器
			Pagination: &ListDefinitionsPagination{
				PageSize: 10,
			},
		}
		out, err := logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Empty(t, out.Definitions)

		// 测试没有分页的情况
		repo.EXPECT().List(gomock.Any(), gomock.Any(), nil, gomock.Any()).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{},
			HasNext:    false,
			TotalCount: nil,
		}, nil)

		params = &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
			},
			Pagination: nil, // 没有分页
		}
		out, err = logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Empty(t, out.Definitions)

		// 测试没有排序的情况
		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{},
			HasNext:    false,
			TotalCount: nil,
		}, nil)

		params = &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
			},
			Pagination: &ListDefinitionsPagination{
				PageSize: 10,
			},
			OrderBy: nil, // 没有排序
		}
		out, err = logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Empty(t, out.Definitions)
	})

	t.Run("toRepoDef的所有分支", func(t *testing.T) {
		// 测试空的ValidationRules
		def := &Definition{
			FieldName:       "test",
			ValidationRules: nil,
		}
		repoDef, err := toRepoDef(def)
		require.NoError(t, err)
		require.NotNil(t, repoDef)
		require.Nil(t, repoDef.ValidationRules)

		// 测试空的DefaultValue
		def.DefaultValue = nil
		repoDef, err = toRepoDef(def)
		require.NoError(t, err)
		require.NotNil(t, repoDef)
		require.Nil(t, repoDef.DefaultValue)
	})

	t.Run("toLogic的所有分支", func(t *testing.T) {
		// 测试dwo.Definition为nil的情况（虽然实际不会发生）
		repoDwo := &repocf.DefinitionWithOptions{
			Definition: nil,
			Options:    []*repocf.Option{},
		}
		out := toLogic(repoDwo)
		require.NotNil(t, out)
		require.Nil(t, out.Definition)
	})

	t.Run("toLogicOpt和toRepoOpt的nil情况", func(t *testing.T) {
		// 测试toLogicOpt的nil情况
		logicOpt := toLogicOpt(nil)
		require.Nil(t, logicOpt)

		// 测试toRepoOpt的nil情况
		repoOpt, err := toRepoOpt(nil)
		require.NoError(t, err)
		require.Nil(t, repoOpt)
	})

	t.Run("List函数HasNext但数据为空的情况", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customfieldmock.NewMockRepository(ctrl)
		logic := NewByParams(repo)

		// 测试HasNext为true但数据为空的情况（不应该生成NextToken）
		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{}, // 空数据
			HasNext:    true,                              // 但HasNext为true
			TotalCount: nil,
		}, nil)

		params := &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
			},
			Pagination: &ListDefinitionsPagination{
				PageSize: 10,
			},
		}
		out, err := logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Empty(t, out.Definitions)
		require.True(t, out.HasNext)
		require.Empty(t, out.NextToken) // 数据为空时不应该生成NextToken
	})

	t.Run("toLogic转换错误的情况", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customfieldmock.NewMockRepository(ctrl)
		logic := NewByParams(repo)

		// 构造一个会导致toLogic失败的Definition（虽然实际上toLogic不会失败）
		// 我们可以通过传入nil Definition来测试这个分支，但实际上toLogicDef会处理nil情况
		// 这里我们构造一个正常的情况，因为toLogic实际上不会返回错误
		in := newTestDefinitionWithOptions()
		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{toRepoDwoForTest(in)},
			HasNext:    false,
			TotalCount: nil,
		}, nil)

		params := &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
			},
		}
		out, err := logic.List(context.Background(), params)
		require.NoError(t, err)
		require.NotNil(t, out)
	})
}

// toRepoDwoForTest 用于 mock repo 层返回
func toRepoDwoForTest(in *DefinitionWithOptions) *repocf.DefinitionWithOptions {
	repoDef, _ := toRepoDef(in.Definition)
	var repoOpts []*repocf.Option
	for _, opt := range in.Options {
		repoOpt, _ := toRepoOpt(opt)
		repoOpts = append(repoOpts, repoOpt)
	}
	return &repocf.DefinitionWithOptions{
		Definition: repoDef,
		Options:    repoOpts,
	}
}
