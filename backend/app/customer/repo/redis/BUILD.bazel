load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "redis",
    srcs = ["redis.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/redis",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/config/redis",
        "//backend/common/rpc/framework/log",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)
