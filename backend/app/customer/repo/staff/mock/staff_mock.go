// Code generated by MockGen. DO NOT EDIT.
// Source: ./staff/staff.go
//
// Generated by this command:
//
//	mockgen -source=./staff/staff.go -destination=./staff/mock/staff_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// GetStaffDetail mocks base method.
func (m *MockReadWriter) GetStaffDetail(ctx context.Context, staffID, companyID, enterpriseID int64) (*organizationpb.StaffModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStaffDetail", ctx, staffID, companyID, enterpriseID)
	ret0, _ := ret[0].(*organizationpb.StaffModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffDetail indicates an expected call of GetStaffDetail.
func (mr *MockReadWriterMockRecorder) GetStaffDetail(ctx, staffID, companyID, enterpriseID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffDetail", reflect.TypeOf((*MockReadWriter)(nil).GetStaffDetail), ctx, staffID, companyID, enterpriseID)
}
