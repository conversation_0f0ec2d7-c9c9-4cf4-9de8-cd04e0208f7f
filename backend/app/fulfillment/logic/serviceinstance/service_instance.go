package serviceinstance

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	service_instance "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

const (
	defaultLimit = 200
)

func New() *Logic {
	return &Logic{
		serviceInstanceCli: service_instance.New(), // TODO: 需要传入数据库连接
	}
}

type Logic struct {
	serviceInstanceCli service_instance.ReadWriter
}

func (l *Logic) ListServiceInstance(ctx context.Context, req *offeringpb.ListServiceInstanceRequest) (
	*offeringpb.ListServiceInstanceResponse, error) {
	// 校验请求参数
	if err := verifyListServiceInstanceRequest(req); err != nil {
		return nil, err
	}

	// 设置默认分页参数
	limit := int(req.GetPagination().GetLimit())
	if limit == 0 {
		limit = defaultLimit
	}

	// 构建查询参数
	baseParam := &service_instance.BaseParam{
		BusinessID: int32(req.GetBusinessId()),
		CompanyID:  int32(req.GetCompanyId()),
		PaginationInfo: &service_instance.PaginationInfo{
			Offset: req.GetPagination().GetOffset(),
			Limit:  int32(limit),
		},
	}

	// 设置时间范围
	if req.GetStartTime() != nil {
		baseParam.StartTime = req.GetStartTime().AsTime()
	}
	if req.GetEndTime() != nil {
		baseParam.EndTime = req.GetEndTime().AsTime()
	}

	// 构建过滤条件
	filter := buildServiceInstanceFilter(req)

	// 查询数据
	instances, err := l.serviceInstanceCli.List(ctx, baseParam, filter)
	if err != nil {
		return nil, err
	}

	// 转换为proto响应
	var pbInstances []*offeringpb.ServiceInstance
	for _, instance := range instances {
		pbInstances = append(pbInstances, convertToProtoServiceInstance(instance))
	}

	// 构建响应
	return &offeringpb.ListServiceInstanceResponse{
		ServiceInstances: pbInstances,
		Pagination:       req.GetPagination(),
		IsEnd:            len(pbInstances) < limit,
		Total:            int32(len(pbInstances)), // TODO: 需要实现Count方法获取总数
	}, nil
}

func (l *Logic) GetServiceInstanceByIDs(ctx context.Context, req *offeringpb.GetServiceInstanceByIDsRequest) (
	*offeringpb.GetServiceInstanceByIDsResponse, error) {
	// 校验请求参数
	if err := verifyGetServiceInstanceByIDsRequest(req); err != nil {
		return nil, err
	}

	// 查询数据
	instances, err := l.serviceInstanceCli.GetByIDs(ctx, req.GetServiceInstanceIds())
	if err != nil {
		return nil, err
	}

	// 转换为proto响应
	var pbInstances []*offeringpb.ServiceInstance
	for _, instance := range instances {
		pbInstances = append(pbInstances, convertToProtoServiceInstance(instance))
	}

	return &offeringpb.GetServiceInstanceByIDsResponse{
		ServiceInstances: pbInstances,
	}, nil
}

// 辅助函数
func verifyListServiceInstanceRequest(req *offeringpb.ListServiceInstanceRequest) error {
	if req.GetBusinessId() <= 0 {
		return status.Error(codes.InvalidArgument, "business_id must be greater than 0")
	}
	if req.GetCompanyId() <= 0 {
		return status.Error(codes.InvalidArgument, "company_id must be greater than 0")
	}
	if req.GetStartTime() != nil && req.GetEndTime() != nil {
		startTime := req.GetStartTime().AsTime()
		endTime := req.GetEndTime().AsTime()
		if startTime.After(endTime) {
			return status.Error(codes.InvalidArgument, "start_time must be before end_time")
		}
	}
	return nil
}

func verifyGetServiceInstanceByIDsRequest(req *offeringpb.GetServiceInstanceByIDsRequest) error {
	if len(req.GetServiceInstanceIds()) == 0 {
		return status.Error(codes.InvalidArgument, "service_instance_ids cannot be empty")
	}
	return nil
}

func buildServiceInstanceFilter(req *offeringpb.ListServiceInstanceRequest) *service_instance.Filter {
	if req.GetFilter() == nil {
		return nil
	}

	filter := &service_instance.Filter{}

	// 转换宠物ID列表
	if req.GetFilter().GetPetIds() != nil {
		filter.PetIDs = req.GetFilter().GetPetIds()
	}

	// 转换客户ID列表
	if req.GetFilter().GetCustomerIds() != nil {
		filter.CustomerIDs = req.GetFilter().GetCustomerIds()
	}

	// 转换护理类型列表
	if req.GetFilter().GetCareTypes() != nil {
		careTypes := make([]int32, 0, len(req.GetFilter().GetCareTypes()))
		for _, careType := range req.GetFilter().GetCareTypes() {
			careTypes = append(careTypes, int32(careType))
		}
		filter.CareTypes = careTypes
	}

	// 转换日期类型列表
	if req.GetFilter().GetDateTypes() != nil {
		dateTypes := make([]int32, 0, len(req.GetFilter().GetDateTypes()))
		for _, dateType := range req.GetFilter().GetDateTypes() {
			dateTypes = append(dateTypes, int32(dateType))
		}
		filter.DateTypes = dateTypes
	}

	// 转换根服务实例ID列表
	if req.GetFilter().GetRootIds() != nil {
		rootIDs := make([]int32, 0, len(req.GetFilter().GetRootIds()))
		for _, rootID := range req.GetFilter().GetRootIds() {
			rootIDs = append(rootIDs, int32(rootID))
		}
		filter.RootIDs = rootIDs
	}

	return filter
}

func convertToProtoServiceInstance(instance *service_instance.ServiceInstance) *offeringpb.ServiceInstance {
	return &offeringpb.ServiceInstance{
		Id:               int64(instance.ID),
		BusinessId:       int64(instance.BusinessID),
		CustomerId:       int64(instance.CustomerID),
		CompanyId:        int64(instance.CompanyID),
		AppointmentId:    int64(instance.AppointmentID),
		PetId:            int64(instance.PetID),
		CareType:         offeringpb.CareType(instance.CareType),
		DateType:         offeringpb.DateType(instance.DateType),
		ServiceFactoryId: int64(instance.ServiceFactoryID),
		ParentId:         int64(instance.ParentID),
		RootId:           int64(instance.RootID),
		StartDate:        timestamppb.New(instance.StartDate),
		EndDate:          timestamppb.New(instance.EndDate),
		CreatedAt:        timestamppb.New(instance.CreatedAt),
		UpdatedAt:        timestamppb.New(instance.UpdatedAt),
	}
}
