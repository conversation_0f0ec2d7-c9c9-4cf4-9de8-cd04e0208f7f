load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "serviceinstance",
    srcs = [
        "create.go",
        "entity.go",
        "query.go",
        "service_instance.go",
        "update.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db",
        "//backend/common/rpc/framework/log",
        "@io_gorm_gorm//:gorm",
    ],
)
