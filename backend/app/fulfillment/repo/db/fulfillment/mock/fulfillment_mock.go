// Code generated by MockGen. DO NOT EDIT.
// Source: ./fulfillment.go
//
// Generated by this command:
//
//	mockgen -source=./fulfillment.go -destination=./mock/fulfillment_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	fulfillment "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockReadWriter) BatchCreate(ctx context.Context, fulfillments []*fulfillment.Fulfillment) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, fulfillments)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockReadWriterMockRecorder) BatchCreate(ctx, fulfillments any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockReadWriter)(nil).BatchCreate), ctx, fulfillments)
}

// Count mocks base method.
func (m *MockReadWriter) Count(ctx context.Context, param *fulfillment.BaseParam, filter *fulfillment.Filter) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Count", ctx, param, filter)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockReadWriterMockRecorder) Count(ctx, param, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockReadWriter)(nil).Count), ctx, param, filter)
}

// DeleteByServiceInstanceID mocks base method.
func (m *MockReadWriter) DeleteByServiceInstanceID(ctx context.Context, serviceInstanceID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByServiceInstanceID", ctx, serviceInstanceID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByServiceInstanceID indicates an expected call of DeleteByServiceInstanceID.
func (mr *MockReadWriterMockRecorder) DeleteByServiceInstanceID(ctx, serviceInstanceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByServiceInstanceID", reflect.TypeOf((*MockReadWriter)(nil).DeleteByServiceInstanceID), ctx, serviceInstanceID)
}

// GetByServiceInstanceID mocks base method.
func (m *MockReadWriter) GetByServiceInstanceID(ctx context.Context, serviceInstanceID int64) ([]*fulfillment.Fulfillment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByServiceInstanceID", ctx, serviceInstanceID)
	ret0, _ := ret[0].([]*fulfillment.Fulfillment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByServiceInstanceID indicates an expected call of GetByServiceInstanceID.
func (mr *MockReadWriterMockRecorder) GetByServiceInstanceID(ctx, serviceInstanceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByServiceInstanceID", reflect.TypeOf((*MockReadWriter)(nil).GetByServiceInstanceID), ctx, serviceInstanceID)
}

// List mocks base method.
func (m *MockReadWriter) List(ctx context.Context, param *fulfillment.BaseParam, filter *fulfillment.Filter) ([]*fulfillment.Fulfillment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, param, filter)
	ret0, _ := ret[0].([]*fulfillment.Fulfillment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockReadWriterMockRecorder) List(ctx, param, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockReadWriter)(nil).List), ctx, param, filter)
}

// Update mocks base method.
func (m *MockReadWriter) Update(ctx context.Context, arg1 *fulfillment.Fulfillment) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReadWriterMockRecorder) Update(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReadWriter)(nil).Update), ctx, arg1)
}
