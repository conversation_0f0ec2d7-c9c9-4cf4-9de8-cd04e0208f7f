package com.moego.api.thirdparty.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class CreateContactBodyParam {
    private CreateContactProperties properties;

    @Data
    @Builder
    public static class CreateContactProperties {
        private String firstname;
        private String lastname;
        private String email;

        @JsonProperty("lead_source")
        private String leadSource;
    }
}
