package com.moego.api.thirdparty;

import com.moego.api.thirdparty.param.CreateContactBodyParam;
import com.moego.api.thirdparty.param.QueryContactBodyParam;
import com.moego.api.thirdparty.param.UpdateContactBodyParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "hubspot-client", url = "https://api.hubapi.com/", contextId = "IHubspotClient")
public interface IHubspotClient {
    @PostMapping(
            value = "/crm/v3/objects/contacts",
            headers = {"Authorization=Bearer ${hubspot.authToken}", "Content-Type=application/json"})
    String createContact(@RequestBody CreateContactBodyParam bodyParam);

    @PostMapping(
            value = "/crm/v3/objects/contacts/batch/read",
            headers = {"Authorization=Bearer ${hubspot.authToken}", "Content-Type=application/json"})
    String queryContactByEmail(@RequestBody QueryContactBodyParam bodyParam);

    @GetMapping(
            value = "/crm/v3/objects/contacts/{id}?properties=est__monthly_rev_from_sales",
            headers = {"Authorization=Bearer ${hubspot.authToken}", "Content-Type=application/json"})
    String queryEstRevFromSalesContactById(@RequestParam(required = false) String id);

    @PatchMapping(
            value = "/crm/v3/objects/contacts/{id}",
            headers = {"Authorization=Bearer ${hubspot.authToken}", "Content-Type=application/json"})
    String updateContactById(@RequestParam(required = false) String id, @RequestBody UpdateContactBodyParam bodyParam);
}
