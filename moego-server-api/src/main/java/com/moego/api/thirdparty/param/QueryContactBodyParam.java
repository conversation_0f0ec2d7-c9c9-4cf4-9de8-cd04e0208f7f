package com.moego.api.thirdparty.param;

import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
public class QueryContactBodyParam {
    private String idProperty;
    private List<QueryInputsParam> inputs;

    @Data
    @Builder
    public static class QueryInputsParam {
        private String id;
    }

    public QueryContactBodyParam(String email) {
        idProperty = "email";
        inputs = List.of(QueryInputsParam.builder().id(email).build());
    }
}
