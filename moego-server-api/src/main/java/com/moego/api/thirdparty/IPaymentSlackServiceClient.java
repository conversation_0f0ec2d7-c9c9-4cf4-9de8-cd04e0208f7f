package com.moego.api.thirdparty;

import com.moego.server.payment.params.SlackPostBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2022/11/02
 */
@FeignClient(
        name = "payment-slack-service-client",
        url = "${notification.slackServicesBaseUrl}",
        contextId = "IPaymentSlackServiceClient")
public interface IPaymentSlackServiceClient {
    @PostMapping("${notification.slackNotification.firstUpgradeUrl}")
    void sendScriptNotificationToClack(@RequestBody SlackPostBody params);
}
