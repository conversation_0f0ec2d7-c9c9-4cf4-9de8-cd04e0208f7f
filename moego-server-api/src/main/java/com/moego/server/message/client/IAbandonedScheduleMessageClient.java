package com.moego.server.message.client;

import com.moego.server.message.api.IAbandonedScheduleMessageService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-message-server",
        url = "${moego.server.url.message}",
        contextId = "IAbandonedScheduleMessageClient")
public interface IAbandonedScheduleMessageClient extends IAbandonedScheduleMessageService {}
