package com.moego.server.message.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class InviteStaffLinkParams {

    @NotBlank
    @Schema(description = "邀请码")
    private String inviteCode;

    @NotBlank
    @Schema(description = "接收人邮箱")
    private String recipientEmail;

    /**
     * @see com.moego.server.message.enums.MessageDetailEnum
     * 1: message
     * 2: email
     */
    @NotNull
    @Schema(description = "发送类型，1：message 2：email")
    private Integer methodType;
}
