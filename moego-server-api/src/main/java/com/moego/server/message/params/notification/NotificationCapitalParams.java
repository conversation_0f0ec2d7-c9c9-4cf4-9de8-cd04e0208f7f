package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/6/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationCapitalParams extends NotificationParams {
    private String title = "Capital request approved";
    private String type = NotificationEnum.TYPE_ACTIVITY_CAPITAL_REQUEST_APPROVED;
    private Boolean isNotifyBusinessOwner = true;
    private NotificationExtraCapitalRequestApproved webPushDto;
    private String mobilePushTitle = "Capital request approved";
    private String mobilePushBody = "Your capital request of %s has been approved and deposited into your account.";
}
