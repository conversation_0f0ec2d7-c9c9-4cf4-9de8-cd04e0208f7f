package com.moego.server.message.params;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UpdateCustomerParams {

    private Integer customerId;
    private Integer businessId;
    private String firstName;
    private String lastName;
    private String avatar;
    private Integer openStatus;
    private Integer deleteTime;
    //    private Integer status;
    //    private String lastMessageText;
    //    private Integer lastMessageTime;
    private Boolean isDelete;
}
