package com.moego.server.message.api;

import com.moego.common.dto.notificationDto.SearchNotificationTemplatesDTO;
import com.moego.common.dto.notificationDto.StaffNotificationTotalDTOV2;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.vo.GetStaffsUnreadCountByBusinessListVo;
import com.moego.server.message.dto.NotificationCampaignRecordDTO;
import com.moego.server.message.dto.NotificationClientUpdateCustomerParams;
import com.moego.server.message.dto.NotificationClientUpdatePetParams;
import com.moego.server.message.dto.NotificationPageDto;
import com.moego.server.message.params.CreateNotificationTemplateParams;
import com.moego.server.message.params.OnlineBookWaitingNotifyParams;
import com.moego.server.message.params.UpdateNotificationTemplateParams;
import com.moego.server.message.params.notification.GetNotificationsParams;
import com.moego.server.message.params.notification.NotificationAgreementSignedParams;
import com.moego.server.message.params.notification.NotificationApptAssignedParams;
import com.moego.server.message.params.notification.NotificationApptCancelledByClientParams;
import com.moego.server.message.params.notification.NotificationApptCancelledParams;
import com.moego.server.message.params.notification.NotificationApptConfirmedByClientParams;
import com.moego.server.message.params.notification.NotificationApptRescheduledParams;
import com.moego.server.message.params.notification.NotificationAssignedTaskParams;
import com.moego.server.message.params.notification.NotificationCapitalParams;
import com.moego.server.message.params.notification.NotificationClientUpdateAppointmentParams;
import com.moego.server.message.params.notification.NotificationClockInParams;
import com.moego.server.message.params.notification.NotificationDisputeParams;
import com.moego.server.message.params.notification.NotificationGoogleReserveGeoMatchedParams;
import com.moego.server.message.params.notification.NotificationGoogleReserveGeoUnmatchedParams;
import com.moego.server.message.params.notification.NotificationInvoicePaidParams;
import com.moego.server.message.params.notification.NotificationNewAbandonedBookingsParams;
import com.moego.server.message.params.notification.NotificationNewIntakeFormParams;
import com.moego.server.message.params.notification.NotificationOBRequestParams;
import com.moego.server.message.params.notification.NotificationParams;
import com.moego.server.message.params.notification.NotificationRenewEndDateParam;
import com.moego.server.message.params.notification.NotificationReviewSubmittedParams;
import com.moego.server.message.params.notification.NotificationWaitlistAvailableParams;
import com.moego.server.message.params.notification.NotificationWaitlistSignedUpParams;
import com.moego.server.message.params.notification.ReadAllNotificationParams;
import com.moego.server.message.params.notification.SearchNotificationTemplatesParams;
import com.moego.server.message.params.notification.StartNotificationCampaignParams;
import com.moego.server.message.params.notification.SystemA2pFailedParams;
import com.moego.server.message.params.notification.SystemA2pRequiredParams;
import com.moego.server.message.params.notification.SystemSMSAmountLowParams;
import com.moego.server.message.params.notification.SystemStripeIsRestrictedParams;
import com.moego.server.message.params.notification.SystemSubPaymentBackupSuccessParams;
import com.moego.server.message.params.notification.SystemSubPaymentFailedParams;
import com.moego.server.message.params.notification.SystemTwilioFineParams;
import com.moego.server.message.params.notification.ob.NotificationOBRequestCanceledParams;
import com.moego.server.message.params.notification.ob.NotificationOBRequestReceivedParams;
import com.moego.server.message.params.notification.ob.NotificationOBRequestRescheduledParams;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface INotificationService {

    // 通用发送消息接口
    @PostMapping("/service/message/notification/sendNotification")
    Boolean sendNotification(@RequestBody NotificationParams params);

    @PostMapping("/service/message/notification/apptAssigned/send")
    Boolean sendNotificationApptAssigned(@RequestBody NotificationApptAssignedParams apptAssignedParams);

    @PostMapping("/service/message/notification/apptCancelled/byClient/send")
    Boolean sendApptCancelledByClient(@RequestBody NotificationApptCancelledByClientParams apptCancelledByClientParams);

    @PostMapping("/service/message/notification/apptConfirmed/byClient/send")
    Boolean sendApptConfirmedByClient(@RequestBody NotificationApptConfirmedByClientParams apptConfirmedByClientParams);

    @PostMapping("/service/message/notification/apptCancelled/send")
    Boolean sendNotificationApptCancelled(@RequestBody NotificationApptCancelledParams apptCancelledParams);

    @PostMapping("/service/message/notification/apptRescheduled/send")
    Boolean sendNotificationApptRescheduled(@RequestBody NotificationApptRescheduledParams apptRescheduledParams);

    @PostMapping("/service/message/notification/invoicePaid/send")
    Boolean sendNotificationInvoicePaid(@RequestBody NotificationInvoicePaidParams invoicePaidParams);

    @PostMapping("/service/message/notification/reviewSubmitted/send")
    Boolean sendNotificationReviewSubmitted(@RequestBody NotificationReviewSubmittedParams reviewSubmittedParams);

    @PostMapping("/service/message/notification/agreementSigned/send")
    Boolean sendNotificationAgreementSigned(@RequestBody NotificationAgreementSignedParams agreementSignedParams);

    // use sendNotificationOBRequestReceived instead
    @PostMapping("/service/message/notification/obRequest/send")
    @Deprecated
    Boolean sendNotificationOBRequest(@RequestBody NotificationOBRequestParams obRequestParams);

    @PostMapping("/service/message/notification/obRequestCanceled/send")
    Boolean sendNotificationOBRequestCanceled(@RequestBody NotificationOBRequestCanceledParams obRequestCanceledParams);

    @PostMapping("/service/message/notification/obRequestReceived/send")
    Boolean sendNotificationOBRequestReceived(@RequestBody NotificationOBRequestReceivedParams obRequestReceivedParams);

    @PostMapping("/service/message/notification/obRequestRescheduled/send")
    Boolean sendNotificationOBRequestRescheduled(
            @RequestBody NotificationOBRequestRescheduledParams obRequestRescheduledParams);

    @PostMapping("/service/message/notification/newIntakeForm/send")
    Boolean sendNotificationNewIntakeForm(@RequestBody NotificationNewIntakeFormParams intakeFormParams);

    @PostMapping("/service/message/notification/clockIn/send")
    Boolean sendNotificationClockIn(@RequestBody NotificationClockInParams clockInParams);

    @PostMapping("/service/message/notification/system/subPaymentFail/send")
    void sendNotificationSubPaymentFailed(@RequestBody SystemSubPaymentFailedParams params);

    @PostMapping("/service/message/notification/system/subPaymentSuccess/send")
    void sendNotificationSubPaymentSuccess(@RequestBody SystemSubPaymentBackupSuccessParams params);

    @PostMapping("/service/message/notification/system/a2pFail/send")
    void sendNotificationA2pFail(@RequestBody SystemA2pFailedParams params);

    @PostMapping("/service/message/notification/system/a2pRequired/send")
    void sendNotificationA2pRequired(@RequestBody SystemA2pRequiredParams params);

    @PostMapping("/service/message/notification/system/sendNotificationTwilioFine")
    void sendNotificationTwilioFine(@RequestBody SystemTwilioFineParams params);

    /**
     * admin 组装复杂参数比较繁琐，这里提供简化后的内部接口来调用
     *
     * @param companyId
     * @param businessId
     * @param failReason
     */
    @PostMapping("/service/message/notification/system/a2pFail/forAdmin/send")
    void sendNotificationA2pFailForAdmin(
            @RequestParam("companyId") Integer companyId,
            @RequestParam("businessId") Integer businessId,
            @RequestParam("failReason") String failReason);

    @PostMapping("/service/message/notification/system/a2pRequired/forAdmin/send")
    void sendNotificationA2pRequiredForAdmin(
            @RequestParam("companyId") Integer companyId, @RequestParam("businessId") Integer businessId);

    @PostMapping("/service/message/notification/system/SMSAmountLow/send")
    void sendNotificationSMSAmountLow(@RequestBody SystemSMSAmountLowParams params);

    @PostMapping("/service/message/notification/system/stripeRestricted/send")
    void sendNotificationStripeRestricted(@RequestBody SystemStripeIsRestrictedParams params);

    /**
     * ob的email和message发送
     * 内部调用，只会发送accept和submit通知
     *
     * @param onlineBookWaitingNotifyParams
     * @return
     */
    @PostMapping("/service/message/notification/onlineBooking/automatic/notify")
    void bookOnlineNotify(@RequestBody OnlineBookWaitingNotifyParams onlineBookWaitingNotifyParams);

    @PostMapping("/service/message/notification/staffUnreadCount")
    Map<Integer, Integer> getStaffUnreadCount(@RequestBody List<MoeStaffDto> staffList);

    /**
     * @param vos
     * @return
     */
    @PostMapping("/service/message/notification/companyStaffUnreadCount")
    Map<Integer /* company id */, Integer> getStaffsUnreadCountByBusinessList(
            @RequestBody List<GetStaffsUnreadCountByBusinessListVo> vos);

    /**
     * Send abandoned booking received notification.
     */
    @PostMapping("/service/message/notification/sendAbandonedBookingReceivedNotification")
    boolean sendAbandonedBookingReceivedNotification(@RequestBody NotificationNewAbandonedBookingsParams params);

    /**
     * Sending renew OB end date notification.
     *
     * @param param {@link NotificationRenewEndDateParam}
     * @see com.moego.common.dto.notificationDto.NotificationEnum#TYPE_ACTIVITY_RENEW_ONLINE_BOOKING_END_DATE
     */
    @PostMapping("/service/message/notification/renewOBEndDate/send")
    Boolean sendRenewOBEndDateNotification(@RequestBody NotificationRenewEndDateParam param);

    /**
     * Sending google reserve geo unmatched notification.
     *
     * @see <a href="https://moego.atlassian.net/browse/ERP-7492">ERP-7492</a>
     */
    @PostMapping("/service/message/notification/sendGoogleReserveGeoUnmatchedNotification")
    boolean sendGoogleReserveGeoUnmatchedNotification(@RequestBody NotificationGoogleReserveGeoUnmatchedParams param);

    /**
     * Sending google reserve geo matched notification.
     *
     * @see <a href="https://moego.atlassian.net/browse/ERP-7492">ERP-7492</a>
     */
    @PostMapping("/service/message/notification/sendGoogleReserveGeoMatchedNotification")
    boolean sendGoogleReserveGeoMatchedNotification(@RequestBody NotificationGoogleReserveGeoMatchedParams param);

    /**
     * Sending platform marketing campaign notification.
     *
     * @see <a href="https://moego.atlassian.net/browse/TECH-681">TECH-681</a>
     */
    @PostMapping("/service/message/notification/startNotificationCampaign")
    List<NotificationCampaignRecordDTO> startNotificationCampaign(@RequestBody StartNotificationCampaignParams params);

    /**
     * Create platform marketing campaign notification template
     *
     * @see <a href="https://moego.atlassian.net/browse/TECH-681">TECH-681</a>
     */
    @PostMapping("/service/message/notification/createNotificationTemplate")
    boolean createNotificationTemplate(@RequestBody CreateNotificationTemplateParams params);
    /**
     * Update platform marketing campaign notification template
     *
     * @see <a href="https://moego.atlassian.net/browse/TECH-681">TECH-681</a>
     */
    @PostMapping("/service/message/notification/updateNotificationTemplate")
    boolean updateNotificationTemplate(@RequestBody UpdateNotificationTemplateParams params);
    /**
     * Delete platform marketing campaign notification template
     *
     * @see <a href="https://moego.atlassian.net/browse/TECH-681">TECH-681</a>
     */
    @PostMapping("/service/message/notification/deleteNotificationTemplate")
    boolean deleteNotificationTemplate(@RequestParam("templateId") Long templateId);
    /**
     * Search platform marketing campaign notification template
     *
     * @see <a href="https://moego.atlassian.net/browse/TECH-681">TECH-681</a>
     */
    @PostMapping("/service/message/notification/searchNotificationTemplates")
    SearchNotificationTemplatesDTO searchNotificationTemplates(@RequestBody SearchNotificationTemplatesParams params);

    @PostMapping("/service/message/notification/getNotifications")
    NotificationPageDto getNotifications(@RequestBody GetNotificationsParams params);

    @PostMapping("/service/message/notification/unreadCount")
    List<StaffNotificationTotalDTOV2> getUnreadCount(
            @RequestParam("businessIds") List<Integer> businessIds, @RequestParam("staffId") Integer staffId);

    @PostMapping("/service/message/notification/read")
    Boolean readNotification(
            @RequestParam("companyId") Long companyId,
            @RequestParam("staffId") Integer staffId,
            @RequestParam("notificationId") Integer notificationId,
            @RequestParam("source") String source);

    @PostMapping("/service/message/notification/readAll")
    Integer readAllNotification(@RequestBody ReadAllNotificationParams params);

    @PostMapping("/service/message/notification/dimiss")
    Boolean dismissNotification(
            @RequestParam("companyId") Long companyId,
            @RequestParam("staffId") Integer staffId,
            @RequestParam("notificationId") Integer notificationId);
    /**
     * 根据发给某个 staff 的 notification record 的 id，找到对应的 notification，并把这个
     * notification 发给所有 staff 的 record 给 dismiss 了
     * @param companyId company id
     * @param id id of moe_notification_record
     * @return success
     */
    @PostMapping("/service/message/notification/dismissSameNotification")
    Boolean dismissSameNotification(@RequestParam("companyId") Long companyId, @RequestParam("id") Integer id);

    @PostMapping("/service/message/notification/dispute/send")
    Boolean sendNotificationDispute(@RequestBody NotificationDisputeParams disputeParams);

    @PostMapping("/service/message/notification/capital/request/approved")
    Boolean sendNotificationCapitalRequestApproved(@RequestBody NotificationCapitalParams params);

    @PostMapping("/service/message/notification/sendNotificationAssignedTask")
    Boolean sendNotificationAssignedTask(@RequestBody NotificationAssignedTaskParams params);

    @PostMapping("/service/message/notification/sendNotificationWaitlistAvailable")
    void sendNotificationWaitlistAvailable(@RequestBody NotificationWaitlistAvailableParams params);

    @PostMapping("/service/message/notification/sendClientUpdateAppointmentNotification")
    boolean sendClientUpdateAppointmentNotification(@RequestBody NotificationClientUpdateAppointmentParams params);

    @PostMapping("/service/message/notification/sendNotificationWaitlistSignedUp")
    void sendNotificationWaitlistSignedUp(@RequestBody NotificationWaitlistSignedUpParams params);

    /**
     * Send client update pet notification.
     *
     * @return dummy value, always true
     */
    @PostMapping("/service/message/notification/sendClientUpdatePetNotification")
    boolean sendClientUpdatePetNotification(@RequestBody NotificationClientUpdatePetParams params);

    /**
     * Send client update customer notification.
     *
     * @return dummy value, always true
     */
    @PostMapping("/service/message/notification/sendClientUpdateCustomerNotification")
    boolean sendClientUpdateCustomerNotification(@RequestBody NotificationClientUpdateCustomerParams params);
}
