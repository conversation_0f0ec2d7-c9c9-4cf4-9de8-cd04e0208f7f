package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @see <a href="https://www.figma.com/file/qGCiknaG6wZh7XrzOuzwsq/OB---master-file?type=design&node-id=3069-43317&mode=design&t=iErMtPeuFbhHN45o-0">OB advanced configuration 设计稿</a>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationRenewEndDateParam extends NotificationParams {
    private String title = "Renew Online Booking end date";
    private String body = "Time to renew your Online Booking — it's just around the corner!";
    private String type = NotificationEnum.TYPE_ACTIVITY_RENEW_ONLINE_BOOKING_END_DATE;
    private Boolean isNotifyBusinessAllStaff = true;
    private String mobilePushTitle = "Renew Online Booking end date";
    private String mobilePushBody = "Time to renew your Online Booking — it's just around the corner!";

    private Extra webPushDto = new Extra();

    @Data
    @Accessors(chain = true)
    public static class Extra {
        private String bookingRangeEndDate;
    }
}
