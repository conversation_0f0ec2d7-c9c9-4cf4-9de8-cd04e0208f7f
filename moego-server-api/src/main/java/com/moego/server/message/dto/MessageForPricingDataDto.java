package com.moego.server.message.dto;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageForPricingDataDto {

    private Map<Integer, Integer> smsAmountMap;
    private Map<Integer, BusinessLineDto> businessLineConfigMap;

    private Map<Integer, MessageReportItem> currentMessageReportMap;
    private Map<Integer, EmailReportItem> currentEmailReportMap;
}
