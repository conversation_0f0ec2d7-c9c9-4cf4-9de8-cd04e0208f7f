package com.moego.server.message.api;

import com.moego.server.message.dto.DescribeMessageDetailsDTO;
import com.moego.server.message.dto.LastContactDetailDTO;
import com.moego.server.message.dto.MessageDetailCountDTO;
import com.moego.server.message.dto.MessageDetailDTO;
import com.moego.server.message.params.LastContactSearchParam;
import com.moego.server.message.params.MessageDetailParams;
import com.moego.server.message.params.MoeMessageTargetQueryParams;
import com.moego.server.message.params.SearchMessageDetailParam;
import com.moego.server.message.vo.DescribeMessageDetailsVO;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2022/12/6
 */
public interface IMessageDetailService {

    /**
     * Insert a message detail.
     *
     * <p> {@link MessageDetailDTO#phoneNumber} 可以带或者不带国家码，如果不带国家码，会自动加上国家码（使用 business 对应的国家码）。
     * <p> {@link MessageDetailDTO#companyId} 如果为 null 或者 0，会自动填充为 business 对应的 companyId。
     *
     * @param dto {@link MessageDetailDTO}
     * @return inserted id
     */
    @PostMapping("/service/message/detail/insert")
    int insert(@RequestBody MessageDetailDTO dto);

    @PostMapping("/service/message/detail/count")
    List<MessageDetailCountDTO> countByMessageText(@RequestBody List<MessageDetailParams> paramsList);

    /**
     * Search message detail by criteria.
     */
    @PostMapping("/service/message/message-details/search")
    List<MessageDetailDTO> searchMessageDetail(@RequestBody SearchMessageDetailParam param);

    @PostMapping("/service/message/message-details/queryByTargetTypeAndId")
    List<MessageDetailDTO> queryMessageDetailByTargetTypeAndId(@RequestBody MoeMessageTargetQueryParams param);

    /**
     * Describe message details
     */
    @PostMapping("/service/message/detail/describeMessageDetails")
    DescribeMessageDetailsDTO describeMessageDetails(@RequestBody @Valid DescribeMessageDetailsVO vo);

    @PostMapping("/service/message/detail/contacted")
    List<LastContactDetailDTO> getLastContactedTime(@RequestBody List<LastContactSearchParam> params);
}
