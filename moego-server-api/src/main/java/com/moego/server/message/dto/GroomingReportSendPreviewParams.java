package com.moego.server.message.dto;

import com.moego.server.grooming.params.groomingreport.GroomingReportPreviewParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class GroomingReportSendPreviewParams {

    private GroomingReportPreviewParams previewParams;

    @Schema(description = "email title")
    private String emailSubject;

    @Schema(description = "recipient email")
    @NotNull
    private String recipientEmail;
}
