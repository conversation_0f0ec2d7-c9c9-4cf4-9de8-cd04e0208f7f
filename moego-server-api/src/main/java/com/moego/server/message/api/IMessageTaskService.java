/*
 * @since 2023-11-29 16:43:19
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.message.api;

import com.moego.server.message.dto.ExportMessageUsageReportsDTO;
import com.moego.server.message.params.ExportBusinessSmsUsageReportParams;
import com.moego.server.message.params.ExportMessageUsageReportsParams;
import com.moego.server.message.params.ReSendPriorityAppAutoMsgParams;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Message 模块的内部定时任务先统一放这里
 */
public interface IMessageTaskService {
    @PostMapping("/service/message/task/batchGetMessageUsageReport")
    ExportMessageUsageReportsDTO exportMessageUsageReports(@RequestBody @Valid ExportMessageUsageReportsParams params);

    @PostMapping("/service/message/task/exportBusinessSmsUsageReport")
    Long /* fileId */ exportBusinessSmsUsageReport(@RequestBody @Valid ExportBusinessSmsUsageReportParams params);

    @PostMapping("/service/message/task/resendPriorityAppAutoMsg")
    Boolean resendPriorityAppAutoMsg(@RequestBody @Valid ReSendPriorityAppAutoMsgParams params);
}
