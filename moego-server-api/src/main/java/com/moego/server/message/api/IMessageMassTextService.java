package com.moego.server.message.api;

import com.moego.server.message.dto.DescribeMassTextsDTO;
import com.moego.server.message.vo.DescribeMassTextsVO;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IMessageMassTextService {
    @PostMapping("/service/message/massText/task")
    void beginSendMassText();

    /**
     * describe mass texts
     */
    @PostMapping("/service/message/massText/describeMassTexts")
    DescribeMassTextsDTO describeMassTexts(@RequestBody @Valid DescribeMassTextsVO vo);
}
