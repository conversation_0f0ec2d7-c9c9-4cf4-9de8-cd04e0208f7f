package com.moego.server.message.params;

import com.moego.common.enums.hardware.BundleSaleEnum;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/26
 */
@Data
public class HardwareOrderSummaryParams {
    // demo link: https://go.moego.pet/care/contract?careCode=XXXX
    String contractLink;

    @Email
    @NotEmpty
    String recipientEmail;

    String recipientName;

    @NotEmpty
    String companyName;

    // order created time(date in LA Zone)
    @NotEmpty
    String companyType;

    @NotEmpty
    String dueDate;

    // orderAmount = subscriptionTotalPrice + m2Amount + smartReaderAmount + shippingFee + hardwareTax -
    // hardwareDiscount - subscriptionDiscount
    @NotEmpty
    String orderAmount;

    @NotEmpty
    List<BundleSaleItem> bundleSaleItemList;

    @NotEmpty
    List<FeeCouponItem> feeCouponItemList;

    BundleSaleEnum saleType;

    @Data
    @Builder
    public static class BundleSaleItem {
        String name;
        // should > 1
        String quantity;
        // demo: $149
        String price;
        // unit price
        String unitPrice;
    }

    @Data
    @Builder
    public static class FeeCouponItem {
        // Code (Subscription - 50% OFF)
        String itemName;
        // $10, or -$74.50
        String amount;
    }
}
