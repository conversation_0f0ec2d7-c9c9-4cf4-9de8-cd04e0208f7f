package com.moego.server.message.params;

import lombok.Data;

@Data
public class TwilioReceiveVoiceCallbackParams {

    // handel data
    private Byte status;
    private Byte callHandleType;
    private String callPhoneNumber;
    private Integer businessId;
    private Integer customerId;

    // voice webhook params
    // e.g.:AC426f9d098219020db0785ee68fd472f7
    private String AccountSid;
    // e.g.:2010-04-01
    private String ApiVersion;
    // e.g.:CAe729811aabc41589dd4ef4300a3f40d0
    private String CallSid;
    // e.g.:ringing
    private String CallStatus;

    private String CallToken;
    // e.g.:+***********
    private String Called;
    // e.g.:
    private String CalledCity;
    // e.g.:US
    private String CalledCountry;
    // e.g.:
    private String CalledState;
    // e.g.:
    private String CalledZip;
    // e.g.:+***********
    private String Caller;
    // e.g.:LOS ANGELES
    private String CallerCity;
    // e.g.:US
    private String CallerCountry;
    // e.g.:CA
    private String CallerState;
    // e.g.:90065
    private String CallerZip;
    // e.g.:inbound
    private String Direction;
    // e.g.:+***********
    private String From;
    // e.g.:LOS ANGELES
    private String FromCity;
    // e.g.:US
    private String FromCountry;
    // e.g.:CA
    private String FromState;
    // e.g.:90065
    private String FromZip;
    private String StirPassportToken;
    private String StirVerstat;
    // e.g.:+***********
    private String To;
    // e.g.:
    private String ToCity;
    // e.g.:US
    private String ToCountry;
    // e.g.:
    private String ToState;
    // e.g.:
    private String ToZip;

    // voice status webhook extra params
    // Thu, 31 Mar 2022 02:37:55 +0000
    private String Timestamp;
    // call-progress-events
    private String CallbackSource;
    // 0
    private String SequenceNumber;
    // 1
    private Integer Duration;
    // 5
    private Integer CallDuration;
}
