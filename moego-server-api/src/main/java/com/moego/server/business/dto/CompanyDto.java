package com.moego.server.business.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/3/22 9:50 PM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class CompanyDto {

    private String name;
    private Integer accountId;
    private String country;
    private Integer locationNum;
    private Integer vansNum;
    private Integer staffNum;

    private Integer id;
    private Long createTime;
    private Long updateTime;
    private Integer level;
    private Integer isNewPricing;
    private Byte enableSquare;

    /**
     * this field has spelling mistake, please use enableStripeReader
     */
    @Deprecated
    private Byte enableStripReader;

    private Byte enableStripeReader;

    private Integer enterpriseId;
}
