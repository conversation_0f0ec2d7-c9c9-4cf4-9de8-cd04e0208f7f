package com.moego.server.business.api;

import com.moego.common.dto.StaffPermissions;
import com.moego.common.dto.StaffRolePermissions;
import com.moego.common.response.ResponseResult;
import com.moego.server.business.dto.DescribeStaffsDTO;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.MoeWorkingDailyDTO;
import com.moego.server.business.dto.ServiceAreaInsideBatchResultV2;
import com.moego.server.business.dto.StaffAccountBusinessDto;
import com.moego.server.business.dto.StaffInfoWithNotificationDto;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.dto.WorkingTimeAndAreaDto;
import com.moego.server.business.params.DescribeStaffsParams;
import com.moego.server.business.params.InitCompanyIdForStaffRequest;
import com.moego.server.business.params.ServiceAreaInsideBatchRequestV2;
import com.moego.server.business.params.StaffCACDBatchRequest;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.business.params.WorkingDailyQueryRangeVo;
import com.moego.server.business.vo.InitCompanyIdForStaffResponse;
import com.moego.server.grooming.api.IBookOnlineAvailableStaffService;
import jakarta.annotation.Nonnull;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2021/8/25 2:45 PM
 */
public interface IBusinessStaffService {

    @PostMapping("/service/business/staff/getStaffNames")
    Map<Integer, MoeStaffDto> getStaffNames(@RequestBody StaffIdListParams staffIdListParams);

    /**
     * TODO(account structure): 迁移前返回 business owner 的 staffId，迁移后返回 company owner 的 staffId
     */
    @GetMapping("/service/business/staff/getOwnerStaffId")
    Integer getOwnerStaffId(@RequestParam("businessId") @Validated @Nonnull @Min(1) Integer businessId);

    /**
     * 获取指定员工的每周工作时间
     * return Map<weekDayIndex, Map<staffId, timeRange>>
     *
     * @param businessId
     * @return
     */
    @GetMapping("/service/business/staff/getBusinessStaffWorkingHour")
    Map<Integer, Map<Integer, TimeRangeDto>> getBusinessStaffWorkingHour(
            @RequestParam("businessId") Integer businessId, @RequestParam("staffIds") List<Integer> staffIds);

    /**
     * 获取指定员工在一段日期范围内的工作时间
     *
     * @param businessId
     * @param staffIdList
     * @param startDate
     * @param endDate
     * @return Map<weekDayIndex, Map < staffId, List < timeRange>>>
     */
    @GetMapping("/service/business/staff/getBusinessDateStaffWorkingHour/date")
    Map<Integer, Map<LocalDate, List<TimeRangeDto>>> getStaffWorkingHourWithOverrideDate(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("staffIdList") List<Integer> staffIdList,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    /**
     * 获取商家一段时间内员工的 override date 设置
     *
     * @param businessId
     * @param staffIdList
     * @param startDate
     * @param endDate
     * @return Map<staffId, List < date>>
     */
    @GetMapping("/service/business/staff/getStaffOverrideDateList")
    Map<Integer, List<String>> getStaffOverrideDateList(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("staffIdList") List<Integer> staffIdList,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    /**
     * 获取指定员工在一段日期范围内的实际工作时间
     * 结合计算了 business working hour、staff working hour、override date、close date
     *
     * @return Map<staffId, Map < date, List < timeRange>>>
     */
    @PostMapping("/service/business/staff/queryRangeV2")
    Map<Integer, Map<String, List<TimeRangeDto>>> queryStaffWorkingHourRange(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("staffIdList") List<Integer> staffIdList,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);

    @PostMapping("/service/business/staff/isLocationInsideAreaBatchV2")
    ServiceAreaInsideBatchResultV2 isLocationInsideAreaBatchV2(
            @RequestParam("businessId") Integer businessId, @RequestBody ServiceAreaInsideBatchRequestV2 request);

    /**
     * 批量计算多个地址的 CACD 结果
     *
     * @return Map<LocationId, Map < date, Map < staffId, List < WorkingTimeAndAreaDto>>>>
     */
    @PostMapping("/service/business/staff/staffCACDBatch")
    Map<Long, Map<String, Map<Integer, List<WorkingTimeAndAreaDto>>>> queryStaffCACDBatch(
            @RequestBody StaffCACDBatchRequest request);

    @PostMapping("/service/business/staff/getStaff")
    MoeStaffDto getStaff(@RequestBody StaffIdParams staffIdParams);

    /**
     * staffId不能为null，businessId可以
     *
     * @param staffIdParams
     * @return
     */
    @PostMapping("/service/business/staff/getStaffWithAccount")
    StaffAccountBusinessDto getStaffWithAccount(@RequestBody StaffIdParams staffIdParams);

    /**
     * DONE(account structure): 查 company staff(modify by Zhang Dong)
     * 根据businessId或staffIdList获取所有的staff信息
     * businessId不能为空，staffIdList为空获取所有的staff
     *
     * @param staffIdListParams
     * @return
     */
    @PostMapping("/service/business/staff/getStaffList")
    List<MoeStaffDto> getStaffList(@RequestBody StaffIdListParams staffIdListParams);

    /**
     * 根据staffIdList获取所有的staff信息
     * 忽略business id
     *
     * @param staffIdListParams
     * @return
     */
    @PostMapping("/service/business/staff/getStaffListV2")
    List<MoeStaffDto> getStaffListV2(@RequestBody StaffIdListParams staffIdListParams);

    @PostMapping("/service/business/staff/getStaffListForCalendar")
    List<MoeStaffDto> getStaffListForCalendar(
            @RequestParam("businessId") Integer businessId, @RequestParam("staffId") Integer staffId);

    /**
     * Done(account structure) 迁移前查business staff 迁移后查 working location 里有 businessId 的 staff
     *
     * @param businessId
     * @param withDeleted
     * @return
     */
    @PostMapping("/service/business/staff/getStaffListByBusinessId")
    List<MoeStaffDto> getStaffListByBusinessId(
            @RequestParam("businessId") Integer businessId, @RequestParam("withDeleted") Boolean withDeleted);

    @PostMapping("/service/business/staff/queryRange")
    ResponseResult<List<StaffWorkingRangeDto>> queryRange(
            @RequestParam("businessId") Integer businessId,
            @RequestParam(value = "staffId", required = false) Integer staffId,
            @RequestBody WorkingDailyQueryRangeVo rangeVo);

    /**
     * ShowCalendarStaffAll 为 true 的 staff
     * account structure 迁移前 ，需要查询 business 所有 ShowOnCalendar 为 true 的 staff
     * 迁移后需要查询 working in business 的 ShowOnCalendar 为 true 的 staff
     * ShowCalendarStaffAll 为 false 的 staff
     * 查 moe_staff_appointment 表
     */
    @GetMapping("/service/business/staff/showCalenderStaff")
    List<Integer> showCalenderStaff(
            @RequestParam("businessId") Integer businessId, @RequestParam("staffId") Integer staffId);

    @PostMapping("/service/business/staff/query")
    ResponseResult<List<MoeWorkingDailyDTO>> query(
            @RequestParam("businessId") Integer businessId, @RequestBody MoeWorkingDailyDTO moeWorkingDailyDTO);

    @PostMapping("/service/business/staff/getBusinessRoleByStaffId")
    StaffPermissions getBusinessRoleByStaffId(@RequestParam("staffId") Integer staffId);

    @GetMapping("/service/business/staff/queryStaffByIdList")
    Map<Integer, StaffInfoWithNotificationDto> queryStaffByIdList(@RequestParam("businessId") Integer businessId);

    /**
     * @deprecated by Freeman since 2024/8/9, use {@link IBookOnlineAvailableStaffService#getAvailableStaffListInAvailabilityType(Integer)} instead
     */
    @Deprecated(since = "2024/8/9")
    @GetMapping("/service/business/staff/queryBookOnlineAvailableStaff")
    List<MoeStaffDto> queryBookOnlineAvailableStaff(@RequestParam("businessId") Integer businessId);

    @PutMapping("/service/business/staff/updateStaff")
    void updateStaff(@RequestBody MoeStaffDto moeStaffDto);

    /**
     * Search staffs
     */
    @PostMapping("/service/business/staff/describeStaffs")
    DescribeStaffsDTO describeStaffs(@RequestBody @Valid DescribeStaffsParams params);

    /**
     * Create staff
     */
    @PostMapping("/service/business/staff/createStaff")
    MoeStaffDto createStaff(@RequestBody @Validated MoeStaffDto staff);

    @PostMapping("/service/business/staff/initCompanyIdForStaff")
    InitCompanyIdForStaffResponse initCompanyIdForStaff(@RequestBody InitCompanyIdForStaffRequest params);

    @PostMapping("/service/business/staff/getStaffRolePermissionsByBusinessIds")
    List<StaffRolePermissions> getStaffRolePermissionsByBusinessIds(
            @RequestBody @NotNull @Valid List<Integer> businessIds);
}
