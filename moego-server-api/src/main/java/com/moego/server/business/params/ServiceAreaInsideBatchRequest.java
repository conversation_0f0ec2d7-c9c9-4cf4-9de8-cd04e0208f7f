package com.moego.server.business.params;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ServiceAreaInsideBatchRequest {
    private Integer businessId;

    @NotNull
    private Integer staffId;

    @NotNull
    private Double lat;

    @NotNull
    private Double lng;

    // include
    @NotNull
    private String startDate;

    // include
    @NotNull
    private String endDate;
}
