package com.moego.server.business.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.StringJoiner;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MoeBusinessDto implements Serializable {

    private static final long serialVersionUID = -7145452232431463904L;

    private Integer id;
    private Integer companyId;
    private String businessName;
    private String avatarPath;
    private String phoneNumber;
    private String website;
    private String address;
    private String address1;
    private String address2;
    private String addressCity;
    private String addressState;
    private String addressZipcode;
    private String addressCountry;
    private String addressLat;
    private String addressLng;
    private String country;
    private String countryAlpha2Code;
    private String currencySymbol;
    private String currencyCode;
    private Byte timeFormatType;
    private String facebook;
    private String instagram;
    private String timeFormat;
    private String unitOfWeight;
    private String calendarFormat;
    private String dateFormat;
    private Byte unitOfWeightType;
    private String timezoneName;
    private Integer timezoneSeconds;
    private Byte dateFormatType;
    private Byte calendarFormatType;
    private Byte numberFormatType;
    private String numberFormat;
    private Byte appType;
    private Byte source;
    private Byte clockInOutEnable;
    private Byte clockInOutNotify;
    private Byte isEnableAccessCode;
    private Integer smartScheduleMaxDist;
    private Integer smartScheduleMaxTime;
    /**
     * move to moe_business_book_online.book_online_name
     */
    @Deprecated
    private String bookOnlineName;

    private Long createTime;
    private Long updateTime;

    @Deprecated
    private String countryCode;

    /**
     * 增加business表已有属性
     */
    private Byte primaryPayType;

    private String smartScheduleStartLat;
    private String smartScheduleStartLng;
    private String smartScheduleEndLat;
    private String smartScheduleEndLng;
    private Integer smartScheduleServiceRange;
    private String smartScheduleStartAddr;
    private String smartScheduleEndAddr;
    private String knowFrom;
    private Byte locations;
    private Byte staffMembers;

    private String ownerEmail;

    private Boolean needSendCode;

    private Byte unitOfDistanceType;

    private Byte retailEnable;
    private String contactEmail;

    private String unitOfDistance;
    private Byte serviceAreaEnable;
    private String google;
    private String yelp;
    private Byte moveFrom;

    public static String getBusinessAddress(MoeBusinessDto e) {
        StringJoiner sj = new StringJoiner(", ");

        if (StringUtils.hasText(e.getAddress1())) {
            sj.add(e.getAddress1());
        }
        if (StringUtils.hasText(e.getAddress2())) {
            sj.add(e.getAddress2());
        }
        if (StringUtils.hasText(e.getAddressCity())) {
            sj.add(e.getAddressCity());
        }
        if (StringUtils.hasText(e.getAddressState())) {
            sj.add(e.getAddressState());
        }
        if (StringUtils.hasText(e.getCountry())) {
            sj.add(e.getCountry());
        }
        if (StringUtils.hasText(e.getAddressZipcode())) {
            sj.add(e.getAddressZipcode());
        }
        return sj.toString();
    }
}
