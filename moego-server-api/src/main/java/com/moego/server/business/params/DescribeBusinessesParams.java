package com.moego.server.business.params;

import com.moego.common.utils.Pagination;
import jakarta.annotation.Nonnull;
import java.util.Set;
import lombok.Builder;

@Builder(toBuilder = true)
public record DescribeBusinessesParams(
        Set<Integer> ids,
        Set<Integer> companyIds,
        Set<String> companyNames,
        Set<Integer> ownerIds,
        Set<String> countries,
        String nameLike,
        @Nonnull Pagination pagination) {}
