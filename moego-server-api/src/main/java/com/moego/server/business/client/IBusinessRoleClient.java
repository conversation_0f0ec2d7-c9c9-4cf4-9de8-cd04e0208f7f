/*
 * @since 2023-12-04 10:07:11
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.business.client;

import com.moego.server.business.api.IBusinessRoleService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(value = "moego-business-server", url = "${moego.server.url.business}", contextId = "IBusinessRoleClient")
public interface IBusinessRoleClient extends IBusinessRoleService {}
