package com.moego.server.payment.api;

import com.moego.server.payment.dto.PreAuthDTO;
import com.moego.server.payment.params.AppointmentEventParams;
import com.moego.server.payment.params.BatchQueryPreAuthParams;
import com.moego.server.payment.params.ListPreAuthParams;
import com.moego.server.payment.params.UpdateInvoiceAndCaptureParams;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IPaymentPreAuthService {

    /**
     * 创建一个 preAuth 记录，参考 {@link com.moego.server.payment.listener.mq.PreAuthActiveMQEventListener#onAppointmentMsg(org.apache.activemq.command.ActiveMQTextMessage)}
     *
     * <p> preauth 严重依赖老的 appointment 模型，实在重构不动，提供一个接口创建 preauth 记录，不依赖之前的事件处理模型（还会引入 activeMQ 依赖）:)
     *
     * <p> 不要尝试复用这个方法！！！
     *
     * @param param {@link AppointmentEventParams}
     * @return dummy return, always true
     */
    @PostMapping("/service/payment/preauth/create")
    boolean create(@RequestBody AppointmentEventParams param);

    // calendar view
    @PostMapping("/service/payment/preauth/batch/query")
    List<PreAuthDTO> batchQueryByTicketIds(@RequestBody @Valid BatchQueryPreAuthParams params);

    @PostMapping("/service/payment/preauth/listPreAuthRecords")
    List<PreAuthDTO> listPreAuthRecords(@RequestBody @Valid ListPreAuthParams params);

    @GetMapping("/service/payment/preauth/query")
    PreAuthDTO queryByTicketId(@RequestParam Integer businessId, @RequestParam Integer ticketId);
    // no show invoice
    @PostMapping("/service/payment/preauth/updateInvoiceAndCapture")
    void updateInvoiceAndCapture(@RequestBody @Valid UpdateInvoiceAndCaptureParams params);

    // every 10min check need preauth
    @PostMapping("/service/payment/preauth/task")
    void taskForPreAuth();
    // every day check need capture preauth
    @PostMapping("/service/payment/preauth/autCapture/task")
    void taskForPreAuthAutoCapture();

    @PostMapping("/service/payment/preauth/captureByInvoiceId")
    void capturePreAuth(@RequestParam Integer businessId, @RequestParam Integer invoiceId);

    @PostMapping("/service/payment/preauth/message/task")
    @Deprecated
    void taskForPreAuthMessageDailyCheck();

    @GetMapping("/service/payment/preauth/query/with/fees")
    @Deprecated
    PreAuthDTO queryPreAuthInfoWithFeesByTicketId(@RequestParam Integer businessId, @RequestParam Integer ticketId);
}
