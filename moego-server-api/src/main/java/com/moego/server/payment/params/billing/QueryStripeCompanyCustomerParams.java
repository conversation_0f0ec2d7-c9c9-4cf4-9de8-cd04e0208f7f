package com.moego.server.payment.params.billing;

import com.moego.common.utils.Pagination;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryStripeCompanyCustomerParams {
    @Min(value = 0, message = "stripe id must gt 0")
    // database primary key id
    private Integer id;

    @Min(value = 0, message = "stripe company id must gt 0")
    // the company id in the account
    private Integer companyId;

    @Size(min = 1, message = "stripe customer id size must > 0")
    // merchant's customer id registered with stripe
    private String stripeCustomerId;

    @Size(min = 1, message = "the length of email must be > 0")
    // the email in account, support fuzzy matching
    String email;

    /**
     * status indicator used to query the situation of deleted data, 1-normal, 0-deleted.
     */
    @Builder.Default
    Byte status = 1;

    /**
     * whether to sort or not Default is 0 No sorting, 1 - desc
     */
    @Builder.Default
    Byte ordered = 0;

    Pagination pagination;
}
