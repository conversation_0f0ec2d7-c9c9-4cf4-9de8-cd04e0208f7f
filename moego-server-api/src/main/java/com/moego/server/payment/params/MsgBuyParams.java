package com.moego.server.payment.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/3/15 5:45 PM
 */
@Data
@Schema(description = "短信包购买")
public class MsgBuyParams {

    @Schema(description = "公司主键")
    @NotNull
    Integer companyId;

    @Schema(description = "需要购买的短信包id")
    @NotNull
    private Integer msgPlanId;

    @Schema(description = "是否自动购买：  0：不自动购买  1：自动购买")
    private Byte autoReload;

    private Integer accountId;
}
