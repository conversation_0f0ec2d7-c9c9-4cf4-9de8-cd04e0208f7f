package com.moego.server.payment.api;

import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2021/9/15 5:19 PM
 */
public interface IPaymentSyncService {
    @PostMapping("/service/payment/sync/processingFee")
    void syncProcessingFee();

    /**
     * 检查所有stripe customer 有没有credit card
     */
    @PostMapping("/service/payment/sync/stripe/card")
    void checkStripeCard();

    /**
     * 检查2023/01/01（**********）之后的所有支付记录，补扣所有processing fee，另需要满足以下条件：
     * 1. 补扣金额1$ 以上
     * 2. 只补扣美国商家（United States）
     * 2.1 另有50个非美国商家（Australia、United Kingdom、Canada）因金额较小/货币转换问题等综合考虑，暂不补扣
     */
    @PostMapping("/service/payment/charge/fee")
    void chargeConnectedAccountFee(@RequestParam(required = false, defaultValue = "false") Boolean chargeNow);

    @PostMapping("/service/payment/connected/negative")
    void tureOnConnectedAccountDebitNegative();

    @PostMapping("/service/payment/sync/stripeAccountStatement")
    void syncStripeAccountStatementFromBusinessName();

    @PostMapping("/service/payment/sync/all/stripeCustomerCards")
    void syncAllStripeCustomerCreditCard();

    @PostMapping("/service/payment/sync/payout")
    Integer syncSpecifiedPayout(
            @RequestParam(name = "payoutId", required = false) String payoutId,
            @RequestParam(name = "startCreatedDate", required = false) String startCreatedDate,
            @RequestParam(name = "endCreatedDate", required = false) String endCreatedDate,
            @RequestParam(name = "businessId") Integer businessId);

    @PostMapping("/service/payment/sync/payDetail")
    Integer syncPayDetail(@RequestBody List<Integer> paymentIdList);

    @PostMapping("/service/payment/connected/tPlus2")
    void rollBackAllUsConnectedAccountToPlus2();

    @PostMapping("/service/payment/square/tips")
    void syncTips(@RequestParam(name = "paymentId", required = false) Integer paymentId);
}
