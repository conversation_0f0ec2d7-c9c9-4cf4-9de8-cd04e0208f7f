package com.moego.server.payment.params;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/10/8
 */
@Data
@Accessors(chain = true)
public class InsertCompanyFeatureRelationParams {
    // company id
    @NotNull
    @Min(0)
    Integer companyId;
    // code
    String code;
    // enable
    Byte enable;
    // note
    String note;
}
