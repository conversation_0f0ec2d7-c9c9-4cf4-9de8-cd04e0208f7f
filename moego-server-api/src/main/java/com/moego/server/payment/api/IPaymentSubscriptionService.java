package com.moego.server.payment.api;

import com.moego.server.business.params.CommonIdsParams;
import com.moego.server.payment.dto.CompanyPermissionStateDto;
import com.moego.server.payment.dto.CouponDTO;
import com.moego.server.payment.dto.EnterpriseSubscriptionConfigDTO;
import com.moego.server.payment.dto.SubscriptionPlanDTO;
import com.moego.server.payment.params.BatchUpdateCouponParams;
import com.moego.server.payment.params.CancelSubParams;
import com.moego.server.payment.params.CouponParams;
import com.moego.server.payment.params.CreateEnterpriseSubscriptionConfigParams;
import com.moego.server.payment.params.SubscriptionParams;
import com.moego.server.payment.params.SubscriptionQueryParams;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2021/11/15 11:43 AM
 */
public interface IPaymentSubscriptionService {
    @PostMapping("/service/payment/subscription/downgrade")
    Boolean downgradeImmediately(@RequestParam("companyId") Long companyId);

    @GetMapping("/service/payment/subscription/permissionState/by/companyId")
    CompanyPermissionStateDto getPermissionStateByCompanyId(@RequestParam("companyId") Integer companyId);

    @PostMapping("/service/payment/subscription/permissionState/by/companyIdList")
    List<CompanyPermissionStateDto> getPermissionStateByCompanyIdList(@RequestBody CommonIdsParams commonIdsParams);

    @GetMapping("/service/payment/subscription/record")
    Integer countRecordListByCompanyId(@RequestParam("companyId") Integer companyId);

    @PostMapping("/service/payment/subscription/msg/autoBuy")
    Integer autoReload(@RequestParam("companyId") Integer companyId);

    /**
     * 修改套餐
     *
     * @param subscriptionParams
     * @return
     */
    @PostMapping("/service/payment/subscription/admin/switchSubscription")
    @Operation(summary = "切换套餐")
    void switchSubscriptionForAdmin(@RequestBody SubscriptionParams subscriptionParams);

    @PostMapping("/service/payment/subscription/admin/changePlan")
    @Operation(summary = "切换new pricing plan")
    void changPlan(@RequestBody SubscriptionParams subscriptionParams);

    @PostMapping("/service/payment/subscription/v2/admin/changePlan")
    @Operation(summary = "切换new pricing plan")
    Boolean changPlanWithResult(@RequestBody SubscriptionParams subscriptionParams);

    @PostMapping("/service/payment/subscription/admin/buyPlan")
    @Operation(summary = "free 用户购买旧套餐，不需要admin 前端confirm")
    void buyPlan(@RequestBody SubscriptionParams subscriptionParams);

    /**
     * 修改套餐
     *
     * @param companyId
     * @param cancelSubParams
     */
    @PostMapping("/service/payment/subscription/admin/cancelSubscriptionForAdmin")
    @Operation(summary = "取消套餐")
    void cancelSubscriptionForAdmin(
            @RequestParam("accountId") Integer accountId,
            @RequestParam("companyId") Integer companyId,
            @RequestBody CancelSubParams cancelSubParams);

    @PostMapping("/service/payment/subscription/createCoupon")
    CouponDTO createCoupon(@RequestBody CouponParams couponParams);

    @GetMapping("/service/payment/subscription/allCoupons")
    List<CouponDTO> getCoupons();

    @PostMapping("/service/payment/subscription/refreshCoupon")
    void refreshCoupons(@RequestBody BatchUpdateCouponParams params);

    /**
     * Create or update company subscription
     */
    @PostMapping("/service/payment/subscription/createOrUpdateCompanyPermissionState")
    CompanyPermissionStateDto createOrUpdateCompanyPermissionState(
            @RequestBody @Validated CompanyPermissionStateDto vo);

    @PostMapping("/service/payment/subscription/admin/initFranchisee")
    @Operation(summary = "初始化 franchisee 套餐")
    void initFranchisee(@RequestBody SubscriptionParams subscriptionParams);

    @PostMapping("/service/payment/subscription/admin/createEnterpriseSubscriptionConfig")
    EnterpriseSubscriptionConfigDTO createEnterpriseSubscriptionConfig(
            @RequestBody CreateEnterpriseSubscriptionConfigParams params);

    @GetMapping("/service/payment/subscription/enterprise/config")
    EnterpriseSubscriptionConfigDTO getEnterpriseSubscriptionConfig(@RequestParam("enterpriseId") Long enterpriseId);

    // used by enterprise-api-v1, do not make any breaking change
    @PostMapping("/service/payment/subscription/enterprise/changeFranchiseePlan")
    void changeFranchiseePlan(@RequestBody SubscriptionParams subscriptionParams);

    @PostMapping("/service/payment/subscription/plans")
    List<SubscriptionPlanDTO> getPlans(@RequestBody SubscriptionQueryParams SubscriptionQueryParams);

    @GetMapping("/service/payment/subscription/plan")
    SubscriptionPlanDTO getPlan(@RequestParam("stripPlanId") String stripPlanId);

    @PostMapping("/service/payment/subscription/clearDismissedStaffIds")
    Integer clearDismissedStaffIds();
}
