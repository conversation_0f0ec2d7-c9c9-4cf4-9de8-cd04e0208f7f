package com.moego.server.payment.params;

import com.moego.common.utils.Pagination;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/9/8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyCustomFeeQueryParams {
    @Min(0)
    Integer companyId;

    @Min(0)
    Long enterpriseId;

    Pagination pagination;
}
