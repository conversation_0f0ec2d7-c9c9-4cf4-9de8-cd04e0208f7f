package com.moego.server.payment.dto;

import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/11/27
 */
@Data
@Builder
public class StripeCustomerInfo {
    String customerId;
    String email;
    String name;
    Long invoiceBalance;
    String defaultSource;
    Long created;
    String description;
    String meta;
    List<StripeBalanceTransaction> balanceTransactions;
}
