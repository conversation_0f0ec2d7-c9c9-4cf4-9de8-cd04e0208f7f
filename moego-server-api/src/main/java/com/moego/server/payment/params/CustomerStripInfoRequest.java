package com.moego.server.payment.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CustomerStripInfoRequest {

    @NotNull
    private Integer customerId;

    @NotNull
    private String chargeToken;

    @JsonIgnore
    private Integer businessId;

    private boolean ignoreDuplicateCard = true;

    private boolean addAch;
}
