package com.moego.server.payment.dto.billing;

import com.moego.server.payment.enums.CouponBusinessCategoryEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/12/6
 */
@Builder(toBuilder = true)
@Getter
@Setter
public class CouponDetailView {
    private Integer id;

    private String code;

    private String stripeCouponId;

    private BigDecimal percentOff;

    private BigDecimal amountOff;

    private Integer maxRedemptions;

    private Integer timesRedeemed;

    private LocalDateTime redeemBy;

    private Integer valid;

    private Integer status;

    private CouponBusinessCategoryEnum businessCategory;

    private Integer validMonth;

    private Long createdTime;
}
