package com.moego.server.payment.dto.stripe;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/1/12
 */
@Getter
@Setter
@AllArgsConstructor
@Builder(toBuilder = true)
public class StripeAccountDTO {
    @Schema(description = "Business information about the account.")
    BusinessProfile businessProfile;

    @Schema(
            description =
                    "The business type One of {@code company}, {@code government_entity}, {@code individual}, or {@code  non_profit}")
    String businessType;

    @Schema(
            description =
                    "A hash containing the set of capabilities that was requested for this account and their associated states."
                            + " Keys are names of capabilities. You can see the full list here. Values may be active, inactive, or pending.")
    Capabilities capabilities;

    @Schema(description = " Whether the account can create live charges.")
    Boolean chargesEnabled;

    @Schema(description = "company")
    Company company;

    @Schema(description = "controller")
    Controller controller;

    @Schema(description = "The account's country.")
    String country;

    @Schema(description = "Time at which the account was connected. Measured in seconds since the Unix epoch.")
    Long created;

    @Schema(description = "Three-letter ISO currency code representing the default currency for the account. ")
    String defaultCurrency;

    @Schema(description = " Always true for a deleted object.")
    Boolean deleted;

    @Schema(
            description =
                    "Whether account details have been submitted. Standard accounts cannot receive payouts before this is true.")
    Boolean detailsSubmitted;

    @Schema(
            description =
                    "An email address associated with the account. You can treat this as metadata: it is not used for authentication or messaging account holders.")
    String email;

    @Schema(description = "External accounts (bank accounts and debit cards) currently attached to this account.")
    List<Object> externalAccounts;

    @Schema(description = "future_requirements")
    FutureRequirements futureRequirements;

    @Schema(description = "Unique identifier for the object.")
    String id;

    @Schema(description = "This is an object representing a person associated with a Stripe account.")
    List<StripePersonDTO> individual;

    @Schema(description = "metadata")
    Map<String, String> metadata;

    @Schema(
            description =
                    "String representing the object's type. Objects of the same type share the same value. Equal to {@code account}.")
    String object;

    @Schema(description = "Whether Stripe can send payouts to this account.")
    Boolean payoutsEnabled;

    @Schema(description = "requirements")
    Requirements requirements;

    @Schema(description = "Options for customizing how the account functions within Stripe.")
    Settings settings;

    @Schema(description = "tos_acceptance")
    TosAcceptance tosAcceptance;

    @Schema(description = "The Stripe account type. Can be {@code standard}, {@code express}, or {@code custom}.")
    String type;

    @Data
    @Builder(toBuilder = true)
    public static class BusinessProfile {
        /**
         * <a href="https://stripe.com/docs/connect/setting-mcc">The merchant category code for the
         * account</a>.
         */
        @Schema(description = "MCCs are used to classify businesses based on the goods or services they provide.")
        String mcc;
        /**
         * .
         */
        @Schema(description = "The customer-facing business name")
        String name;

        @Schema(
                description =
                        "Internal-only description of the product sold or service provided by the business. It's used by Stripe for risk and underwriting purposes.")
        String productDescription;

        //
        @Schema(description = "A publicly available mailing address for sending support issues to.")
        StripeAddressDTO supportAddress;

        @Schema(description = "A publicly available email address for sending support issues to.")
        String supportEmail;

        @Schema(description = " A publicly available phone number to call with support issues.")
        String supportPhone;

        @Schema(description = "A publicly available website for handling support issues.")
        String supportUrl;

        @Schema(description = "The business's publicly available website.")
        String url;
    }

    @Data
    @Builder(toBuilder = true)
    public static class Capabilities {
        @Schema(
                description =
                        "The status of the Canadian pre-authorized debits payments capability of the account. One of active, inactive, or pending.")
        String acssDebitPayments;

        @Schema(
                description =
                        "The status of the Affirm capability of the account. One of active, inactive, or pending.")
        String affirmPayments;

        @Schema(
                description =
                        "The status of the Afterpay Clearpay capability of the account. One of active, inactive, or pending.")
        String afterpayClearpayPayments;

        @Schema(
                description =
                        "The status of the BECS Direct Debit (AU) payments capability of the account. One of active, inactive, or pending.")
        String auBecsDebitPayments;

        @Schema(
                description =
                        "The status of the Bacs Direct Debits payments capability of the account. One of active, inactive, or pending.")
        String bacsDebitPayments;

        @Schema(
                description =
                        "The status of the Bancontact payments capability of the account. One of active, inactive, or pending.")
        String bancontactPayments;

        @Schema(
                description =
                        "The status of the customer_balance payments capability of the account. One of active, inactive, or pending.")
        String bankTransferPayments;

        @Schema(
                description =
                        "The status of the blik payments capability of the account. One of active, inactive, or pending.")
        String blikPayments;

        @Schema(
                description =
                        "The status of the boleto payments capability of the account. One of active, inactive, or pending.")
        String boletoPayments;

        @Schema(
                description =
                        "The status of the card issuing capability of the account. One of active, inactive, or pending.")
        String cardIssuing;

        @Schema(
                description =
                        "The status of the card payments capability of the account. One of active, inactive, or pending.")
        String cardPayments;

        @Schema(
                description =
                        "The status of the Cartes Bancaires payments capability of the account. One of active, inactive, or pending.")
        String cartesBancairesPayments;

        @Schema(
                description =
                        "The status of the EPS payments capability of the account. One of active, inactive, or pending.")
        String epsPayments;

        @Schema(
                description =
                        "The status of the FPX payments capability of the account. One of active, inactive, or pending.")
        String fpxPayments;

        @Schema(
                description =
                        "The status of the giropay payments capability of the account. One of active, inactive, or pending.")
        String giropayPayments;

        @Schema(
                description =
                        "The status of the GrabPay payments capability of the account. One of active, inactive, or pending.")
        String grabpayPayments;

        @Schema(
                description =
                        "The status of the iDEAL payments capability of the account. One of active, inactive, or pending.")
        String idealPayments;

        @Schema(
                description =
                        "The status of the JCB payments capability of the account (Japan only). One of active, inactive, or pending.")
        String jcbPayments;

        @Schema(
                description =
                        "The status of the Klarna payments capability of the account. One of active, inactive, or pending.")
        String klarnaPayments;

        @Schema(
                description =
                        "The status of the konbini payments capability of the account. One of active, inactive, or pending.")
        String konbiniPayments;

        @Schema(
                description =
                        "The status of the legacy payments capability of the account. One of active, inactive, or pending.")
        String legacyPayments;

        @Schema(
                description =
                        "The status of the link_payments capability of the account. One of active, inactive, or pending.")
        String linkPayments;

        @Schema(
                description =
                        "The status of the OXXO payments capability of the account. One of active, inactive, or pending.")
        String oxxoPayments;

        @Schema(
                description =
                        "The status of the P24 payments capability of the account. One of active, inactive, or pending.")
        String p24Payments;

        @Schema(
                description =
                        "The status of the paynow payments capability of the account. One of active, inactive, or pending.")
        String paynowPayments;

        @Schema(
                description =
                        "The status of the promptpay payments capability of the account. One of active, inactive, or pending.")
        String promptpayPayments;

        @Schema(
                description =
                        "The status of the SEPA Direct Debits payments capability of the account. One of active, inactive, or pending.")
        String sepaDebitPayments;

        @Schema(
                description =
                        "The status of the Sofort payments capability of the account. One of active, inactive, or pending.")
        String sofortPayments;

        @Schema(
                description =
                        "The status of the tax reporting 1099-K (US) capability of the account. One of active, inactive, or pending.")
        String taxReportingUs1099K;

        @Schema(
                description =
                        "The status of the tax reporting 1099-MISC (US) capability of the account. One of active, inactive, or pending.")
        String taxReportingUs1099Misc;

        @Schema(
                description =
                        "The status of the transfers capability of the account. One of active, inactive, or pending.")
        String transfers;

        @Schema(
                description =
                        "The status of the banking capability, or whether the account can have bank accounts. One of active, inactive, or pending.")
        String treasury;

        @Schema(
                description =
                        "The status of the US bank account ACH payments capability of the account. One of active, inactive, or pending.")
        String usBankAccountAchPayments;
    }

    @Data
    @Builder(toBuilder = true)
    public static class Company {
        @Schema(description = "address")
        StripeAddressDTO address;
        /**
         * The Kana variation of the company's primary address (Japan only).
         */
        @Schema(description = "address_kana")
        AddressKana addressKana;
        /**
         * The Kanji variation of the company's primary address (Japan only).
         */
        @Schema(description = "address_kanji")
        AddressKanji addressKanji;
        /**
         * Whether the company's directors have been provided. This Boolean will be {@code true} if
         * you've manually indicated that all directors are provided via <a
         * href="https://stripe.com/docs/api/accounts/update#update_account-company-directors_provided">the
         * {@code directors_provided} parameter</a>.
         */
        @Schema(description = "directors_provided")
        Boolean directorsProvided;
        /**
         * Whether the company's executives have been provided. This Boolean will be {@code true} if
         * you've manually indicated that all executives are provided via <a
         * href="https://stripe.com/docs/api/accounts/update#update_account-company-executives_provided">the
         * {@code executives_provided} parameter</a>, or if Stripe determined that sufficient executives
         * were provided.
         */
        @Schema(description = "executives_provided")
        Boolean executivesProvided;
        /**
         * The company's legal name.
         */
        @Schema(description = "name")
        String name;
        /**
         * The Kana variation of the company's legal name (Japan only).
         */
        @Schema(description = "name_kana")
        String nameKana;
        /**
         * The Kanji variation of the company's legal name (Japan only).
         */
        @Schema(description = "name_kanji")
        String nameKanji;
        /**
         * Whether the company's owners have been provided. This Boolean will be {@code true} if you've
         * manually indicated that all owners are provided via <a
         * href="https://stripe.com/docs/api/accounts/update#update_account-company-owners_provided">the
         * {@code owners_provided} parameter</a>, or if Stripe determined that sufficient owners were
         * provided. Stripe determines ownership requirements using both the number of owners provided
         * and their total percent ownership (calculated by adding the {@code percent_ownership} of each
         * owner together).
         */
        @Schema(description = "owners_provided")
        Boolean ownersProvided;
        /**
         * This hash is used to attest that the beneficial owner information provided to Stripe is both
         * current and correct.
         */
        @Schema(description = "ownership_declaration")
        OwnershipDeclaration ownershipDeclaration;
        /**
         * The company's phone number (used for verification).
         */
        @Schema(description = "phone")
        String phone;
        /**
         * The category identifying the legal structure of the company or legal entity. See <a
         * href="https://stripe.com/docs/connect/identity-verification#business-structure">Business
         * structure</a> for more details.
         *
         * <p>One of {@code free_zone_establishment}, {@code free_zone_llc}, {@code
         * government_instrumentality}, {@code governmental_unit}, {@code incorporated_non_profit},
         * {@code limited_liability_partnership}, {@code llc}, {@code multi_member_llc}, {@code
         * private_company}, {@code private_corporation}, {@code private_partnership}, {@code
         * public_company}, {@code public_corporation}, {@code public_partnership}, {@code
         * single_member_llc}, {@code sole_establishment}, {@code sole_proprietorship}, {@code
         * tax_exempt_government_instrumentality}, {@code unincorporated_association}, or {@code
         * unincorporated_non_profit}.
         */
        @Schema(description = "structure")
        String structure;
        /**
         * Whether the company's business ID number was provided.
         */
        @Schema(description = "tax_id_provided")
        Boolean taxIdProvided;
        /**
         * The jurisdiction in which the {@code tax_id} is registered (Germany-based companies only).
         */
        @Schema(description = "tax_id_registrar")
        String taxIdRegistrar;
        /**
         * Whether the company's business VAT number was provided.
         */
        @Schema(description = "vat_id_provided")
        Boolean vatIdProvided;
        /**
         * Information on the verification state of the company.
         */
        @Schema(description = "verification")
        Verification verification;

        @Data
        @Builder(toBuilder = true)
        public static class AddressKana {
            /**
             * City/Ward.
             */
            @Schema(description = "city")
            String city;
            /**
             * Two-letter country code (<a href="https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2">ISO
             * 3166-1 alpha-2</a>).
             */
            @Schema(description = "country")
            String country;
            /**
             * Block/Building number.
             */
            @Schema(description = "line1")
            String line1;
            /**
             * Building details.
             */
            @Schema(description = "line2")
            String line2;
            /**
             * ZIP or postal code.
             */
            @Schema(description = "postal_code")
            String postalCode;
            /**
             * Prefecture.
             */
            @Schema(description = "state")
            String state;
            /**
             * Town/cho-me.
             */
            @Schema(description = "town")
            String town;
        }

        @Data
        @Builder(toBuilder = true)
        public static class AddressKanji {
            /**
             * City/Ward.
             */
            @Schema(description = "city")
            String city;
            /**
             * Two-letter country code (<a href="https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2">ISO
             * 3166-1 alpha-2</a>).
             */
            @Schema(description = "country")
            String country;
            /**
             * Block/Building number.
             */
            @Schema(description = "line1")
            String line1;
            /**
             * Building details.
             */
            @Schema(description = "line2")
            String line2;
            /**
             * ZIP or postal code.
             */
            @Schema(description = "postal_code")
            String postalCode;
            /**
             * Prefecture.
             */
            @Schema(description = "state")
            String state;
            /**
             * Town/cho-me.
             */
            @Schema(description = "town")
            String town;
        }

        @Data
        @Builder(toBuilder = true)
        public static class OwnershipDeclaration {
            /**
             * The Unix timestamp marking when the beneficial owner attestation was made.
             */
            @Schema(description = "date")
            Long date;
            /**
             * The IP address from which the beneficial owner attestation was made.
             */
            @Schema(description = "ip")
            String ip;
            /**
             * The user-agent string from the browser where the beneficial owner attestation was made.
             */
            @Schema(description = "user_agent")
            String userAgent;
        }

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Verification {
            @Schema(description = "document")
            Document document;

            @Data
            @Builder(toBuilder = true)
            public static class Document {
                /**
                 * The back of a document returned by a <a
                 * href="https://stripe.com/docs/api#create_file">file upload</a> with a {@code purpose}
                 * value of {@code additional_verification}.
                 */
                @Schema(description = "back")
                String back;
                /**
                 * A user-displayable string describing the verification state of this document.
                 */
                @Schema(description = "details")
                String details;
                /**
                 * One of {@code document_corrupt}, {@code document_expired}, {@code document_failed_copy},
                 * {@code document_failed_greyscale}, {@code document_failed_other}, {@code
                 * document_failed_test_mode}, {@code document_fraudulent}, {@code document_incomplete},
                 * {@code document_invalid}, {@code document_manipulated}, {@code document_not_readable},
                 * {@code document_not_uploaded}, {@code document_type_not_supported}, or {@code
                 * document_too_large}. A machine-readable code specifying the verification state for this
                 * document.
                 */
                @Schema(description = "details_code")
                String detailsCode;
                /**
                 * The front of a document returned by a <a
                 * href="https://stripe.com/docs/api#create_file">file upload</a> with a {@code purpose}
                 * value of {@code additional_verification}.
                 */
                @Schema(description = "front")
                String front;
            }
        }
    }

    @Data
    @Builder(toBuilder = true)
    public static class Controller {
        /**
         * {@code true} if the Connect application retrieving the resource controls the account and can
         * therefore exercise <a
         * href="https://stripe.com/docs/connect/platform-controls-for-standard-accounts">platform
         * controls</a>. Otherwise, this field is null.
         */
        @Schema(description = "is_controller")
        Boolean isController;
        /**
         * The controller type. Can be {@code application}, if a Connect application controls the
         * account, or {@code account}, if the account controls itself.
         */
        @Schema(description = "type")
        String type;
    }

    @Data
    @Builder(toBuilder = true)
    public static class FutureRequirements {
        /**
         * Fields that are due and can be satisfied by providing the corresponding alternative fields
         * instead.
         */
        @Schema(description = "alternatives")
        List<FutureRequirements.Alternative> alternatives;
        /**
         * Date on which {@code future_requirements} merges with the main {@code requirements} hash and
         * {@code future_requirements} becomes empty. After the transition, {@code currently_due}
         * requirements may immediately become {@code past_due}, but the account may also be given a
         * grace period depending on its enablement state prior to transitioning.
         */
        @Schema(description = "current_deadline")
        Long currentDeadline;
        /**
         * Fields that need to be collected to keep the account enabled. If not collected by {@code
         * future_requirements[current_deadline]}, these fields will transition to the main {@code
         * requirements} hash.
         */
        @Schema(description = "currently_due")
        List<String> currentlyDue;
        /**
         * This is typed as a string for consistency with {@code requirements.disabled_reason}, but it
         * safe to assume {@code future_requirements.disabled_reason} is empty because fields in {@code
         * future_requirements} will never disable the account.
         */
        @Schema(description = "disabled_reason")
        String disabledReason;
        /**
         * Fields that are {@code currently_due} and need to be collected again because validation or
         * verification failed.
         */
        @Schema(description = "errors")
        List<FutureRequirements.Errors> errors;
        /**
         * Fields that need to be collected assuming all volume thresholds are reached. As they become
         * required, they appear in {@code currently_due} as well.
         */
        @Schema(description = "eventually_due")
        List<String> eventuallyDue;
        /**
         * Fields that weren't collected by {@code requirements.current_deadline}. These fields need to
         * be collected to enable the capability on the account. New fields will never appear here;
         * {@code future_requirements.past_due} will always be a subset of {@code
         * requirements.past_due}.
         */
        @Schema(description = "past_due")
        List<String> pastDue;
        /**
         * Fields that may become required depending on the results of verification or review. Will be
         * an empty array unless an asynchronous verification is pending. If verification fails, these
         * fields move to {@code eventually_due} or {@code currently_due}.
         */
        @Schema(description = "pending_verification")
        List<String> pendingVerification;

        @Data
        @Builder(toBuilder = true)
        public static class Alternative {
            /**
             * Fields that can be provided to satisfy all fields in {@code original_fields_due}.
             */
            @Schema(description = "alternative_fields_due")
            List<String> alternativeFieldsDue;
            /**
             * Fields that are due and can be satisfied by providing all fields in {@code
             * alternative_fields_due}.
             */
            @Schema(description = "original_fields_due")
            List<String> originalFieldsDue;
        }

        @Data
        @Builder(toBuilder = true)
        public static class Errors {
            /**
             * The code for the type of error.
             *
             * <p>One of {@code invalid_address_city_state_postal_code}, {@code invalid_dob_age_under_18},
             * {@code invalid_representative_country}, {@code invalid_street_address}, {@code
             * invalid_tos_acceptance}, {@code invalid_value_other}, {@code
             * verification_document_address_mismatch}, {@code verification_document_address_missing},
             * {@code verification_document_corrupt}, {@code verification_document_country_not_supported},
             * {@code verification_document_dob_mismatch}, {@code verification_document_duplicate_type},
             * {@code verification_document_expired}, {@code verification_document_failed_copy}, {@code
             * verification_document_failed_greyscale}, {@code verification_document_failed_other}, {@code
             * verification_document_failed_test_mode}, {@code verification_document_fraudulent}, {@code
             * verification_document_id_number_mismatch}, {@code verification_document_id_number_missing},
             * {@code verification_document_incomplete}, {@code verification_document_invalid}, {@code
             * verification_document_issue_or_expiry_date_missing}, {@code
             * verification_document_manipulated}, {@code verification_document_missing_back}, {@code
             * verification_document_missing_front}, {@code verification_document_name_mismatch}, {@code
             * verification_document_name_missing}, {@code verification_document_nationality_mismatch},
             * {@code verification_document_not_readable}, {@code verification_document_not_signed},
             * {@code verification_document_not_uploaded}, {@code verification_document_photo_mismatch},
             * {@code verification_document_too_large}, {@code verification_document_type_not_supported},
             * {@code verification_failed_address_match}, {@code verification_failed_business_iec_number},
             * {@code verification_failed_document_match}, {@code verification_failed_id_number_match},
             * {@code verification_failed_keyed_identity}, {@code verification_failed_keyed_match}, {@code
             * verification_failed_name_match}, {@code verification_failed_other}, {@code
             * verification_failed_residential_address}, {@code verification_failed_tax_id_match}, {@code
             * verification_failed_tax_id_not_issued}, {@code verification_missing_executives}, {@code
             * verification_missing_owners}, or {@code
             * verification_requires_additional_memorandum_of_associations}.
             */
            @Schema(description = "code")
            String code;
            /**
             * An informative message that indicates the error type and provides additional details about
             * the error.
             */
            @Schema(description = "reason")
            String reason;

            @Schema(description = "requirement")
            String requirement;
        }
    }

    @Data
    @Builder(toBuilder = true)
    public static class Requirements {
        /**
         * Fields that are due and can be satisfied by providing the corresponding alternative fields
         * instead.
         */
        @Schema(description = "alternatives")
        List<Requirements.Alternative> alternatives;
        /**
         * Date by which the fields in {@code currently_due} must be collected to keep the account
         * enabled. These fields may disable the account sooner if the next threshold is reached before
         * they are collected.
         */
        @Schema(description = "current_deadline")
        Long currentDeadline;
        /**
         * Fields that need to be collected to keep the account enabled. If not collected by {@code
         * current_deadline}, these fields appear in {@code past_due} as well, and the account is
         * disabled.
         */
        @Schema(description = "currently_due")
        List<String> currentlyDue;
        /**
         * If the account is disabled, this string describes why. Can be {@code requirements.past_due},
         * {@code requirements.pending_verification}, {@code listed}, {@code platform_paused}, {@code
         * rejected.fraud}, {@code rejected.listed}, {@code rejected.terms_of_service}, {@code
         * rejected.other}, {@code under_review}, or {@code other}.
         */
        @Schema(description = "disabled_reason")
        String disabledReason;
        /**
         * Fields that are {@code currently_due} and need to be collected again because validation or
         * verification failed.
         */
        @Schema(description = "errors")
        List<Requirements.Errors> errors;
        /**
         * Fields that need to be collected assuming all volume thresholds are reached. As they become
         * required, they appear in {@code currently_due} as well, and {@code current_deadline} becomes
         * set.
         */
        @Schema(description = "eventually_due")
        List<String> eventuallyDue;
        /**
         * Fields that weren't collected by {@code current_deadline}. These fields need to be collected
         * to enable the account.
         */
        @Schema(description = "past_due")
        List<String> pastDue;
        /**
         * Fields that may become required depending on the results of verification or review. Will be
         * an empty array unless an asynchronous verification is pending. If verification fails, these
         * fields move to {@code eventually_due}, {@code currently_due}, or {@code past_due}.
         */
        @Schema(description = "pending_verification")
        List<String> pendingVerification;

        @Data
        @Builder(toBuilder = true)
        public static class Alternative {
            /**
             * Fields that can be provided to satisfy all fields in {@code original_fields_due}.
             */
            @Schema(description = "alternative_fields_due")
            List<String> alternativeFieldsDue;
            /**
             * Fields that are due and can be satisfied by providing all fields in {@code
             * alternative_fields_due}.
             */
            @Schema(description = "original_fields_due")
            List<String> originalFieldsDue;
        }

        @Data
        @Builder(toBuilder = true)
        public static class Errors {
            /**
             * The code for the type of error.
             *
             * <p>One of {@code invalid_address_city_state_postal_code}, {@code invalid_dob_age_under_18},
             * {@code invalid_representative_country}, {@code invalid_street_address}, {@code
             * invalid_tos_acceptance}, {@code invalid_value_other}, {@code
             * verification_document_address_mismatch}, {@code verification_document_address_missing},
             * {@code verification_document_corrupt}, {@code verification_document_country_not_supported},
             * {@code verification_document_dob_mismatch}, {@code verification_document_duplicate_type},
             * {@code verification_document_expired}, {@code verification_document_failed_copy}, {@code
             * verification_document_failed_greyscale}, {@code verification_document_failed_other}, {@code
             * verification_document_failed_test_mode}, {@code verification_document_fraudulent}, {@code
             * verification_document_id_number_mismatch}, {@code verification_document_id_number_missing},
             * {@code verification_document_incomplete}, {@code verification_document_invalid}, {@code
             * verification_document_issue_or_expiry_date_missing}, {@code
             * verification_document_manipulated}, {@code verification_document_missing_back}, {@code
             * verification_document_missing_front}, {@code verification_document_name_mismatch}, {@code
             * verification_document_name_missing}, {@code verification_document_nationality_mismatch},
             * {@code verification_document_not_readable}, {@code verification_document_not_signed},
             * {@code verification_document_not_uploaded}, {@code verification_document_photo_mismatch},
             * {@code verification_document_too_large}, {@code verification_document_type_not_supported},
             * {@code verification_failed_address_match}, {@code verification_failed_business_iec_number},
             * {@code verification_failed_document_match}, {@code verification_failed_id_number_match},
             * {@code verification_failed_keyed_identity}, {@code verification_failed_keyed_match}, {@code
             * verification_failed_name_match}, {@code verification_failed_other}, {@code
             * verification_failed_residential_address}, {@code verification_failed_tax_id_match}, {@code
             * verification_failed_tax_id_not_issued}, {@code verification_missing_executives}, {@code
             * verification_missing_owners}, or {@code
             * verification_requires_additional_memorandum_of_associations}.
             */
            @Schema(description = "code")
            String code;
            /**
             * An informative message that indicates the error type and provides additional details about
             * the error.
             */
            @Schema(description = "reason")
            String reason;

            @Schema(description = "requirement")
            String requirement;
        }
    }

    @Data
    @Builder(toBuilder = true)
    public static class Settings {
        @Schema(description = "bacs_debit_payments")
        BacsDebitPayments bacsDebitPayments;

        @Schema(description = "branding")
        Branding branding;

        @Schema(description = "card_issuing")
        CardIssuing cardIssuing;

        @Schema(description = "card_payments")
        CardPayments cardPayments;

        @Schema(description = "dashboard")
        Dashboard dashboard;

        @Schema(description = "payments")
        Payments payments;

        @Schema(description = "payouts")
        Payouts payouts;

        @Schema(description = "sepa_debit_payments")
        SepaDebitPayments sepaDebitPayments;

        @Schema(description = "treasury")
        Treasury treasury;

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class BacsDebitPayments {
            /**
             * The Bacs Direct Debit Display Name for this account. For payments made with Bacs Direct
             * Debit, this will appear on the mandate, and as the statement descriptor.
             */
            @Schema(description = "display_name")
            String displayName;
        }

        @Data
        @Builder(toBuilder = true)
        public static class Branding {
            /**
             * (ID of a <a href="https://stripe.com/docs/guides/file-upload">file upload</a>) An icon for
             * the account. Must be square and at least 128px x 128px.
             */
            @Schema(description = "icon")
            String icon;
            /**
             * (ID of a <a href="https://stripe.com/docs/guides/file-upload">file upload</a>) A logo for
             * the account that will be used in Checkout instead of the icon and without the account's
             * name next to it if provided. Must be at least 128px x 128px.
             */
            @Schema(description = "logo")
            String logo;
            /**
             * A CSS hex color value representing the primary branding color for this account.
             */
            @Schema(description = "primary_color")
            String primaryColor;
            /**
             * A CSS hex color value representing the secondary branding color for this account.
             */
            @Schema(description = "secondary_color")
            String secondaryColor;
        }

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class CardIssuing {
            @Schema(description = "tos_acceptance")
            TosAcceptance tosAcceptance;

            @Data
            @Builder(toBuilder = true)
            public static class TosAcceptance {
                /**
                 * The Unix timestamp marking when the account representative accepted the service
                 * agreement.
                 */
                @Schema(description = "date")
                Long date;
                /**
                 * The IP address from which the account representative accepted the service agreement.
                 */
                @Schema(description = "ip")
                String ip;

                @Schema(description = "user_agent")
                String userAgent;
            }
        }

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class CardPayments {
            @Schema(description = "decline_on")
            DeclineOn declineOn;
            /**
             * The default text that appears on credit card statements when a charge is made. This field
             * prefixes any dynamic {@code statement_descriptor} specified on the charge. {@code
             * statement_descriptor_prefix} is useful for maximizing descriptor space for the dynamic
             * portion.
             */
            @Schema(description = "statement_descriptor_prefix")
            String statementDescriptorPrefix;
            /**
             * The Kana variation of the default text that appears on credit card statements when a charge
             * is made (Japan only). This field prefixes any dynamic {@code
             * statement_descriptor_suffix_kana} specified on the charge. {@code
             * statement_descriptor_prefix_kana} is useful for maximizing descriptor space for the dynamic
             * portion.
             */
            @Schema(description = "statement_descriptor_prefix_kana")
            String statementDescriptorPrefixKana;
            /**
             * The Kanji variation of the default text that appears on credit card statements when a
             * charge is made (Japan only). This field prefixes any dynamic {@code
             * statement_descriptor_suffix_kanji} specified on the charge. {@code
             * statement_descriptor_prefix_kanji} is useful for maximizing descriptor space for the
             * dynamic portion.
             */
            @Schema(description = "statement_descriptor_prefix_kanji")
            String statementDescriptorPrefixKanji;

            @Data
            @Builder(toBuilder = true)
            public static class DeclineOn {
                /**
                 * Whether Stripe automatically declines charges with an incorrect ZIP or postal code. This
                 * setting only applies when a ZIP or postal code is provided and they fail bank
                 * verification.
                 */
                @Schema(description = "avs_failure")
                Boolean avsFailure;

                @Schema(description = "cvc_failure")
                Boolean cvcFailure;
            }
        }

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Dashboard {
            /**
             * The display name for this account. This is used on the Stripe Dashboard to differentiate
             * between accounts.
             */
            @Schema(description = "display_name")
            String displayName;
            /**
             * The timezone used in the Stripe Dashboard for this account. A list of possible time zone
             * values is maintained at the <a href="http://www.iana.org/time-zones">IANA Time Zone
             * Database</a>.
             */
            @Schema(description = "timezone")
            String timezone;
        }

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Payments {
            /**
             * The default text that appears on credit card statements when a charge is made. This field
             * prefixes any dynamic {@code statement_descriptor} specified on the charge.
             */
            @Schema(description = "statement_descriptor")
            String statementDescriptor;
            /**
             * The Kana variation of the default text that appears on credit card statements when a charge
             * is made (Japan only).
             */
            @Schema(description = "statement_descriptor_kana")
            String statementDescriptorKana;
            /**
             * The Kanji variation of the default text that appears on credit card statements when a
             * charge is made (Japan only).
             */
            @Schema(description = "statement_descriptor_kanji")
            String statementDescriptorKanji;
            /**
             * The Kana variation of the default text that appears on credit card statements when a charge
             * is made (Japan only). This field prefixes any dynamic {@code
             * statement_descriptor_suffix_kana} specified on the charge. {@code
             * statement_descriptor_prefix_kana} is useful for maximizing descriptor space for the dynamic
             * portion.
             */
            @Schema(description = "statement_descriptor_prefix_kana")
            String statementDescriptorPrefixKana;
            /**
             * The Kanji variation of the default text that appears on credit card statements when a
             * charge is made (Japan only). This field prefixes any dynamic {@code
             * statement_descriptor_suffix_kanji} specified on the charge. {@code
             * statement_descriptor_prefix_kanji} is useful for maximizing descriptor space for the
             * dynamic portion.
             */
            @Schema(description = "statement_descriptor_prefix_kanji")
            String statementDescriptorPrefixKanji;
        }

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Payouts {
            /**
             * A Boolean indicating if Stripe should try to reclaim negative balances from an attached
             * bank account. See our <a
             * href="https://stripe.com/docs/connect/account-balances">Understanding Connect Account
             * Balances</a> documentation for details. Default value is {@code false} for Custom accounts,
             * otherwise {@code true}.
             */
            @Schema(description = "debit_negative_balances")
            Boolean debitNegativeBalances;

            @Schema(description = "schedule")
            Settings.Payouts.Schedule schedule;
            /**
             * The text that appears on the bank account statement for payouts. If not set, this defaults
             * to the platform's bank descriptor as set in the Dashboard.
             */
            @Schema(description = "statement_descriptor")
            String statementDescriptor;

            @Data
            @Builder(toBuilder = true)
            public static class Schedule {
                /**
                 * The number of days charges for the account will be held before being paid out.
                 */
                @Schema(description = "delay_days")
                Long delayDays;
                /**
                 * How frequently funds will be paid out. One of {@code manual} (payouts only created via
                 * API call), {@code daily}, {@code weekly}, or {@code monthly}.
                 */
                @Schema(description = "interval")
                String interval;
                /**
                 * The day of the month funds will be paid out. Only shown if {@code interval} is monthly.
                 * Payouts scheduled between the 29th and 31st of the month are sent on the last day of
                 * shorter months.
                 */
                @Schema(description = "monthly_anchor")
                Long monthlyAnchor;

                @Schema(description = "weekly_anchor")
                String weeklyAnchor;
            }
        }

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class SepaDebitPayments {
            /**
             * SEPA creditor identifier that identifies the company making the payment.
             */
            @Schema(description = "creditor_id")
            String creditorId;
        }

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Treasury {
            @Schema(description = "tos_acceptance")
            Settings.Treasury.TosAcceptance tosAcceptance;

            @Data
            @Builder(toBuilder = true)
            @NoArgsConstructor
            @AllArgsConstructor
            public static class TosAcceptance {
                /**
                 * The Unix timestamp marking when the account representative accepted the service
                 * agreement.
                 */
                @Schema(description = "date")
                Long date;
                /**
                 * The IP address from which the account representative accepted the service agreement.
                 */
                @Schema(description = "ip")
                String ip;

                @Schema(description = "user_agent")
                String userAgent;
            }
        }
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TosAcceptance {
        /**
         * The Unix timestamp marking when the account representative accepted their service agreement.
         */
        @Schema(description = "date")
        Long date;
        /**
         * The IP address from which the account representative accepted their service agreement.
         */
        @Schema(description = "ip")
        String ip;
        /**
         * The user's service agreement type.
         */
        @Schema(description = "service_agreement")
        String serviceAgreement;
        /**
         * The user agent of the browser from which the account representative accepted their service
         * agreement.
         */
        @Schema(description = "user_agent")
        String userAgent;
    }
}
