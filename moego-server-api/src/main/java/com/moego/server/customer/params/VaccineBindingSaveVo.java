package com.moego.server.customer.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VaccineBindingSaveVo {

    @NotNull
    private Integer petId;

    @NotNull
    private Integer vaccineId;

    @NotNull
    private String expirationDate;

    @Schema(description = "The length of a single photo upload to s3 is 151")
    @Size(max = 10, message = "The maximum number of uploaded documents is 10")
    private List<String> documentUrls;

    private Byte type;
}
