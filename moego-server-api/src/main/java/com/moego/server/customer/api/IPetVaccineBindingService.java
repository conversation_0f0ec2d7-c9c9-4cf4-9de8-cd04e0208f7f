package com.moego.server.customer.api;

import com.moego.server.customer.dto.PetVaccineBindingDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface IPetVaccineBindingService {
    /**
     * DONE(account structure): 无需修改
     */
    @GetMapping("/service/customer/pet-vaccine-binding/getVaccineBindingById")
    PetVaccineBindingDTO getPetVaccineBindingById(@RequestParam("petVaccineBindingId") int petVaccineBindingId);
}
