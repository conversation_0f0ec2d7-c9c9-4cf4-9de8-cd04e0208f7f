package com.moego.server.customer.params;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * add agreement parameters for unsigned agreement or ob agreement
 * <AUTHOR>  v1
 * <AUTHOR>  v2
 */
@Data
@ToString
public class ClientCommitSignParams {

    @NotNull
    private String signature;

    @NotNull
    private String unsignedId;

    private String rawId;
    private Long rawCreateTime;
    private Long rawUpdateTime;

    private List<String> inputs;
}
