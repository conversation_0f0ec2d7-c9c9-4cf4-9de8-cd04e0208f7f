package com.moego.server.customer.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * BeanUtils Copy with AppointmentReportRecord
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReportWebApptCustomerInfo {

    private Integer clientId;
    private String firstName;
    private String lastName;
    private String primaryNumber;
    private String email;

    private Integer addressId;
    private String address1;
    private String address2;
    private String city;
    private String country;
    private String state;
    private String zipcode;

    private Byte isRecurring;
}
