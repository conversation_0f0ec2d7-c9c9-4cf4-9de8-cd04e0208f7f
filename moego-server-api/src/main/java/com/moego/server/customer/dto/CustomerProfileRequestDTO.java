package com.moego.server.customer.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/6/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomerProfileRequestDTO {

    // FIXME: add @NotNull when company id is ready
    private Long companyId;

    @NotNull
    private Integer businessId;

    @NotNull
    private Integer customerId;

    /**
     * Client profile
     */
    @Valid
    private ClientProfileDTO client;

    /**
     * Pet profile
     */
    @Valid
    private List<PetProfileDTO> pets;

    /**
     * Selected address detail.
     *
     * @deprecated by <PERSON> since 2024/2/21, 不再使用这个字段，address 的 update info 单独放在 profile_request_address 表中
     */
    @Deprecated
    private CustomerAddressDto address;

    @Data
    @Accessors(chain = true)
    public static class ClientProfileDTO {

        private String firstName;
        private String lastName;
        private String phoneNumber;
        private String email;
        private Integer referralSourceId;
        private Integer preferredGroomerId;
        private Byte preferredFrequencyType;
        private Integer preferredFrequencyDay;
        private Integer[] preferredDay;
        private Integer[] preferredTime;
        private Map<String, Object> customQuestions;
        // CRM-3555
        private AdditionalContactDTO emergencyContact;

        // CRM-3671
        private AdditionalContactDTO pickupContact;
        private LocalDateTime birthday;
    }

    @Data
    @Accessors(chain = true)
    public static class PetProfileDTO {

        @NotNull
        private Integer petId;

        private String avatarPath;
        private String petName;
        private String breed;
        private Byte breedMix;
        private Integer petTypeId;
        private Byte gender;
        private String birthday;
        private String weight;
        private String fixed;
        private String behavior;
        private String hairLength;
        private String vetName;
        private String vetPhone;
        private String vetAddress;
        private String emergencyContactName;
        private String emergencyContactPhone;
        private String healthIssues;
        private List<VaccineBindingRecordDto> vaccineList;
        private Map<String, Object> customQuestions;
        /**
         * MoeGo platform pet id
         */
        private Long platformPetId;
    }
}
