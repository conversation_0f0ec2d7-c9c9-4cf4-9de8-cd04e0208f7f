package com.moego.server.customer.dto;

import java.util.Map;
import lombok.Data;

@Data
public class CustomerMigrationResult {
    private Map<Integer, Integer> customerIdMapping;
    private Map<Integer, Integer> customerContactIdMapping;
    private Map<Integer, Integer> customerAddressIdMapping;
    private Map<Integer, Integer> customerReferralSourceIdMapping;
    private Map<Integer, Integer> petIdMapping;
    private Map<Integer, Integer> petBreedIdMapping;
    private Map<Integer, Integer> petSizeIdMapping;
    private Map<Integer, Integer> petCoatTypeIdMapping;
    private Map<Integer, Integer> petVaccineIdMapping;
}
