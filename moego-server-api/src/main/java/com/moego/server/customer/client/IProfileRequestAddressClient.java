package com.moego.server.customer.client;

import com.moego.server.customer.api.IProfileRequestAddressService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-customer-server",
        url = "${moego.server.url.customer}",
        contextId = "IProfileRequestAddressClient")
public interface IProfileRequestAddressClient extends IProfileRequestAddressService {}
