package com.moego.server.customer.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2020-09-21 22:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MoeBusinessCustomerDTO extends CustomerBasicDTO {

    /**
     * for Intake Form create profile
     */
    private Boolean isPhoneConflict = false;
}
