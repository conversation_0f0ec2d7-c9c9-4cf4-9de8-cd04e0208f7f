package com.moego.server.customer.api;

import com.moego.server.customer.dto.SearchPetResult;
import com.moego.server.customer.params.SearchPetParams;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Validated
public interface IPetSearchService {
    @PostMapping("/service/customer/pet/search")
    @Deprecated // use v2
    SearchPetResult searchPet(@RequestBody @Valid SearchPetParams params);

    @PostMapping("/service/customer/pet/search/v2")
    SearchPetResult searchPetV2(@RequestBody @Valid SearchPetParams params);
}
