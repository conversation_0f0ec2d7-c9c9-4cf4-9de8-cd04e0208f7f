package com.moego.server.grooming.api;

import com.moego.common.dto.CustomerPaymentSummary;
import com.moego.common.params.CustomerIdsParams;
import com.moego.common.response.ResponseResult;
import com.moego.server.grooming.dto.InvoiceSummaryDTO;
import com.moego.server.grooming.dto.PackageServiceDTO;
import com.moego.server.grooming.dto.TipSplitDetailsDTO;
import com.moego.server.grooming.params.CommonIdsParams;
import com.moego.server.grooming.params.InvoiceAmountVo;
import com.moego.server.grooming.params.SaveTipSplitParams;
import com.moego.server.grooming.params.SetPaymentParams;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IGroomingInvoiceService {
    @PostMapping("/service/grooming/invoice/set-payment-result")
    Boolean setPaymentResult(@RequestBody SetPaymentParams params);

    @GetMapping("/service/grooming/invoice/detailWithPayment")
    ResponseResult<InvoiceSummaryDTO> getWithPaymentById(@RequestParam("invoiceId") Integer invoiceId);

    /**
     * DONE(account structure): company 迁移后，查询 company 维度的数据
     */
    @PostMapping("/service/grooming/invoice/get-customer-payment")
    ResponseResult<List<CustomerPaymentSummary>> queryCustomerPaymentSummary(
            @RequestBody CustomerIdsParams customerIdsParams);

    @PostMapping("/service/grooming/invoice/set-tips")
    @Deprecated
    ResponseResult<InvoiceSummaryDTO> setTips(@RequestBody InvoiceAmountVo param);

    @PostMapping("/service/grooming/invoice/save-tip-split-record")
    Boolean saveTipSplitRecord(@RequestBody SaveTipSplitParams params);

    @PostMapping("/service/grooming/invoice/list-by-ids")
    ResponseResult<List<InvoiceSummaryDTO>> selectListByIds(@RequestBody CommonIdsParams params);

    /**
     * 校验获invoice guid 并获取invoice 信息
     *
     * @param guid
     * @return
     */
    @GetMapping("/service/grooming/invoice/check")
    InvoiceSummaryDTO checkInvoiceGuid(@RequestParam("guid") String guid);

    /**
     * for payment/list
     */
    @PostMapping("/service/grooming/invoice/listInvoices")
    List<InvoiceSummaryDTO> selectInvoicesByIds(@RequestBody CommonIdsParams params);

    @PostMapping("/service/grooming/invoice/tipsSplitDetails")
    TipSplitDetailsDTO getTipsSplitDetails(@RequestBody List<Long> orderIds);

    @PostMapping("/service/grooming/invoice/client/url")
    String getInvoiceClientUrl(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("invoiceId") Integer invoiceId,
            @RequestParam(value = "requiredProcessingFee", required = false) Boolean requiredProcessingFee);

    @PostMapping("/service/grooming/invoice/uuid")
    String getInvoiceClientUrlByOrderId(
            @RequestParam("businessId") Integer businessId, @RequestParam("orderId") Integer orderId);

    /**
     * invoice数据修正，仅内部调用
     */
    @PostMapping("/service/grooming/invoice/amount/fix")
    Integer fixAmountDeviation();

    /**
     * 仅用于获取 invoice 对应的 apply package 信息。不包含 service price
     */
    @PostMapping("/service/grooming/invoice/listApplyPackages")
    List<PackageServiceDTO> listApplyPackages(@RequestBody List<Long> invoiceIds);

    @PostMapping("/service/grooming/invoice/usePackage")
    Boolean usePackageForGrooming(
            @RequestParam("businessId") Integer businessId, @RequestParam("orderId") Integer orderId);
}
