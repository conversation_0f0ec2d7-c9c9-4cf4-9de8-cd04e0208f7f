package com.moego.server.grooming.params;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class EditIdParams {

    private Integer id;
    private Integer businessId;
    private Integer accountId;

    private Byte isPaid;

    @Schema(description = "内部同步参数，标记是否为定金")
    private Boolean isDeposit;

    private Boolean isFromOB;

    private BigDecimal nowShowFee;

    private Boolean isCalledByTaskModule = false;
}
