/*
 * @since 2024-09-17 18:06:32
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.grooming.client;

import com.moego.server.grooming.api.ISmartSchedulingService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "ISmartSchedulingClient")
public interface ISmartSchedulingClient extends ISmartSchedulingService {}
