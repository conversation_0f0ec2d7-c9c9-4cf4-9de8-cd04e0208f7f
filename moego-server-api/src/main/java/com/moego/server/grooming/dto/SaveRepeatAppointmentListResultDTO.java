package com.moego.server.grooming.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SaveRepeatAppointmentListResultDTO {

    public static final Byte NOTIFY_TYPE_CREATED = 1;
    public static final Byte NOTIFY_TYPE_RESCHEDULED = 2;

    private Integer updatedCount;
    private Integer notifyAppointmentId;
    private Byte notifyType;
}
