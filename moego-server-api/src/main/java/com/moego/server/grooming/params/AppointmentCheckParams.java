package com.moego.server.grooming.params;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AppointmentCheckParams {

    @NotNull
    private Integer staffId;

    @NotNull
    private Integer startTime;

    @NotNull
    private Integer duration;

    @NotNull
    private String appointmentTime;

    private String appointmentDate;

    private Integer businessId;

    /**
     * 查询冲突的员工
     */
    private Integer tokenStaffId;

    private Integer groomingId;
}
