package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.groomingreport.GroomingReportDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportPreviewDataDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSettingDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO;
import com.moego.server.grooming.params.groomingreport.GetGroomingReportCardListParams;
import com.moego.server.grooming.params.groomingreport.GetGroomingReportSummaryInfoParams;
import com.moego.server.grooming.params.groomingreport.GroomingIdListParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportPreviewParams;
import com.moego.server.grooming.params.groomingreport.UpdateGroomingReportStatusParams;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Grooming report 内部接口
 */
public interface IGroomingGroomingReportService {
    @GetMapping("/service/grooming/grooming-report/getGroomingReportSetting")
    GroomingReportSettingDTO getGroomingReportSetting(@RequestParam("businessId") Integer businessId);

    @PostMapping("/service/grooming/grooming-report/getGroomingReportPreviewData")
    GroomingReportPreviewDataDTO getGroomingReportPreviewData(
            @RequestParam("businessId") Integer businessId,
            @RequestBody(required = false) GroomingReportPreviewParams params);

    @PostMapping("/service/grooming/grooming-report/getGroomingReportSummaryInfoList")
    List<GroomingReportSummaryInfoDTO> getGroomingReportSummaryInfoList(
            @Valid @RequestBody GetGroomingReportSummaryInfoParams params);

    @PutMapping("/service/grooming/grooming-report/updateGroomingReportSentStatus")
    Boolean updateGroomingReportSentStatus(@Valid @RequestBody UpdateGroomingReportStatusParams params);

    @GetMapping("/service/grooming/grooming-report/getGroomingReportSummaryInfoByUuid")
    GroomingReportSummaryInfoDTO getGroomingReportSummaryInfoByUuid(
            @RequestParam("uuid") String uuid, @RequestParam(value = "reportId", required = false) Integer reportId);

    @PostMapping("/service/grooming/grooming-report/getGroomingReportList")
    List<GroomingReportDTO> getGroomingReportList(@RequestBody GroomingIdListParams idListParams);

    @PostMapping("/service/grooming/grooming-report/getGroomingReportListForClientApp")
    List<GroomingReportDTO> getGroomingReportListForClientApp(@RequestParam Integer appointmentId);

    @PostMapping("/service/grooming/grooming-report/listGroomingReportCardByFilter")
    List<GroomingReportDTO> listGroomingReportCardByFilter(@RequestBody GetGroomingReportCardListParams params);

    @PostMapping("/service/grooming/grooming-report/batchDeleteGroomingReportCard")
    Boolean batchDeleteGroomingReportCard(
            @RequestParam("businessId") Integer businessId, @RequestBody List<Integer> reportCardIds);

    @PostMapping("/service/grooming/grooming-report/batchGenerateGroomingReportUuid")
    Boolean batchGenerateGroomingReportUuid(
            @RequestParam("businessId") Integer businessId, @RequestBody List<Integer> reportCardIds);
}
