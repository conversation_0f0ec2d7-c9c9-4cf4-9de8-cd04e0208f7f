package com.moego.server.grooming.params.appointment;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

@Builder(toBuilder = true)
public record WaitListRescheduleParams(
        @Schema(description = "business id", hidden = true) Integer businessId,
        @Schema(description = "token staff id", hidden = true) Integer tokenStaffId,
        @Schema(description = "waiting list id") Long waitListId,
        @Schema(description = "staff id") @NotNull @Min(1) Integer staffId,
        @Schema(description = "start date, YYYY-MM-DD") @NotNull String startDate,
        @Schema(description = "start time, 当天的分钟数") @NotNull @Min(0) @Max(1440) Integer startTime) {}
