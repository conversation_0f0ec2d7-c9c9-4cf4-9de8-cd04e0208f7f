package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class GroomingTicketWindowDetailDTO {

    private Integer id;
    private Integer businessId;
    private Long companyId;
    private Integer repeatId;
    private Integer customerId;
    private Integer status;
    private Byte bookOnlineStatus;
    private Byte isWaitingList;
    private Long checkInTime;
    private Long checkOutTime;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private String appointmentEndDate;
    private String colorCode;
    private Integer createdById;
    private Long createTime;

    private String alertNotes;
    private String ticketComments;
    private String additionalNote;

    private String oldAppointmentDate;
    private Integer oldAppointmentStartTime;
    private Integer oldAppointmentEndTime;
    private String arrivalWindowTimeString;

    private List<GroomingPetDetailDTO> groomingPetDetails;
    private List<EvaluationServiceDetailDTO> evaluationServiceDetails;

    private Integer source;

    private Boolean noStartTime;

    private Boolean isAutoAccept;
    private AutoAssignDTO autoAssign;

    private BigDecimal estimatedTotalPrice;
}
