package com.moego.server.grooming.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class EditRepeatParams {

    @NotNull
    private Integer repeatId;

    private Integer businessId;
    private Integer staffId;
    private Integer times;
    private String endsOn;

    @NotNull
    @Schema(description = "1：次数 \t 2：设置结束时间")
    private String type;
}
