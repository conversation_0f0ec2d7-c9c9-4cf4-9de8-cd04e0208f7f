package com.moego.server.grooming.dto;

import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CustomerLastApptForBookOnline {

    private Integer id;
    private Integer customerId;
    private String orderId;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;
    private String address;
    private Integer contactsAddressId;
    private CustomerInfoForOnlineBooking customer;
    private String note;
    private Map<Integer, PetInfoForOnlineBooking> pets;
}
