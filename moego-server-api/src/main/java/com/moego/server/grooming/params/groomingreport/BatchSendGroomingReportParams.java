package com.moego.server.grooming.params.groomingreport;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class BatchSendGroomingReportParams {

    @Schema(description = "company id")
    private Integer companyId;

    @Schema(description = "business id")
    private Integer businessId;

    @Schema(description = "need to send report ids")
    private List<Integer> reportIds;

    @Schema(description = "send method: 1-sms, 2-email")
    private Byte sendMethod;

    @Schema(description = "staff id")
    private Integer staffId;
}
