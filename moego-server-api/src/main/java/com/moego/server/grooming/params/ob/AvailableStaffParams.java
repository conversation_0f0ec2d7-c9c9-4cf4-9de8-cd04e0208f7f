package com.moego.server.grooming.params.ob;

import com.moego.server.grooming.dto.ob.SelectedPetServiceDTO;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/2
 */
@Data
public class AvailableStaffParams {

    @NotNull
    private Integer businessId;

    private Integer customerId;

    @NotEmpty
    private List<@NotNull SelectedPetServiceDTO> petServiceList;
}
