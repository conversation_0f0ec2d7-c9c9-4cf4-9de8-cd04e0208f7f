package com.moego.server.grooming.dto.ob;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OBTimeSlotQueryDTO {

    @NotNull
    private OBTimeSlotDTO timeSlotDTO;

    @NotNull
    private List<String> needQueryDays;

    @NotNull
    private Set<Integer> availableStaffIds;
}
