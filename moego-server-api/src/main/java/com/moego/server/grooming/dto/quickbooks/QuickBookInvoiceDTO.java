package com.moego.server.grooming.dto.quickbooks;

import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 * @since 2024/10/30
 */
@Builder(toBuilder = true)
public class QuickBookInvoiceDTO {

    private Integer id;
    private Integer businessId;
    private Long companyId;
    private Integer invoiceId;
    private String invoiceType;
    private String groomingId;
    private String qbInvoiceId;
    private String qbInvoiceStatus;
    private BigDecimal totalAmount;
    private BigDecimal payAmount;
    private Byte payStatus;
    private Long createTime;
    private Long updateTime;

    private List<QuickBookPaymentDTO> associationPayments;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Integer getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getGroomingId() {
        return groomingId;
    }

    public void setGroomingId(String groomingId) {
        this.groomingId = groomingId;
    }

    public String getQbInvoiceId() {
        return qbInvoiceId;
    }

    public void setQbInvoiceId(String qbInvoiceId) {
        this.qbInvoiceId = qbInvoiceId;
    }

    public String getQbInvoiceStatus() {
        return qbInvoiceStatus;
    }

    public void setQbInvoiceStatus(String qbInvoiceStatus) {
        this.qbInvoiceStatus = qbInvoiceStatus;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public Byte getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Byte payStatus) {
        this.payStatus = payStatus;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public List<QuickBookPaymentDTO> getAssociationPayments() {
        return associationPayments;
    }

    public void setAssociationPayments(List<QuickBookPaymentDTO> associationPayments) {
        this.associationPayments = associationPayments;
    }
}
