package com.moego.server.grooming.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LineDiscountDTO {

    @Schema(description = "记录id")
    private Long id;

    @Schema(description = "订单id")
    private Long orderId;

    @Schema(description = " item id")
    private Long orderItemId;

    @Schema(description = "生效类型：all-订单级别，service - service级别，product - product级别 ，item-单个item")
    private String applyType;

    @Schema(description = "是否删除，正常删除的不会下发")
    private Boolean isDeleted;

    @Schema(description = "discount类型：percentage/amount")
    private String discountType;

    @Schema(description = "discount金额")
    private BigDecimal discountAmount;

    @Schema(description = "discount比例")
    private BigDecimal discountRate;

    @Schema(description = "添加的staff id")
    private Long applyBy;

    @Schema(description = "添加顺序，添加多个的时候会决定计算顺序")
    private Integer applySequence;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "折扣代码 id")
    private Long discountCodeId;
}
