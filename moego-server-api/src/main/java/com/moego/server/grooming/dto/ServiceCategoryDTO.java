package com.moego.server.grooming.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class ServiceCategoryDTO implements Cloneable {

    private Integer id;
    private Integer businessId;
    private String name;
    private Byte type;
    private Integer sort;
    private List<PetServiceDTO> petServices;

    @Override
    public ServiceCategoryDTO clone() {
        ServiceCategoryDTO result = null;
        try {
            result = (ServiceCategoryDTO) super.clone();
        } catch (CloneNotSupportedException e) {
            log.error("clone object failed", e);
        }
        if (petServices != null) {
            List<PetServiceDTO> newPetService = new ArrayList<>();
            petServices.forEach(srv -> {
                newPetService.add(srv.clone());
            });
            result.setPetServices(newPetService);
        }
        return result;
    }
}
