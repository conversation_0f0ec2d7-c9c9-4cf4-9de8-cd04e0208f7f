package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportWebSale {

    private String employeeName;
    private String apptDateTime;
    private String ownerName;
    private Integer petNum;
    private BigDecimal revenue;
    private BigDecimal tips;
    private BigDecimal tax;
    private BigDecimal discount;
    private BigDecimal collectedRevenue;
    private BigDecimal totalPayment;
    private BigDecimal totalRefund;
    private BigDecimal unpaidRevenue;
    private List<String> services;
    private List<String> addOns;
    private List<String> products; // product item names
    private String clientType;
    private String apptStatus;
    private String paymentStatus;
    private String clientAddress;
    private BigDecimal packageUsage;

    // extra internal use
    private Integer staffId;
    private String apptDate;
    private Integer apptTime;

    // sale by week, by month, by client
    private Integer apptId;
    private Integer businessId;
    private Integer customerId;
    private String appointmentDate;
    private Integer invoiceId;
    private BigDecimal paymentAmount;
    private BigDecimal convenienceFee;
    private Set<Integer> petIds;

    // sale by geography
    private Integer totalNewClients;
    private Integer totalExistingClients;

    private String city;
    private String zipcode;
    private List<String> serviceAreas;

    // ERP-1053 新增字段
    private BigDecimal collectedServicePrice;
    private BigDecimal collectedTips;
    private BigDecimal collectedTax;

    // 净实收收入 = payment - convenienceFee - tax - tips - refund
    private BigDecimal netSaleRevenue;
}
