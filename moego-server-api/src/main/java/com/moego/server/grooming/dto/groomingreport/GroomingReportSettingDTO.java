package com.moego.server.grooming.dto.groomingreport;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * Grooming report setting 记录，moe_grooming_report_setting 表对象
 */
@Data
public class GroomingReportSettingDTO {

    private Integer id;
    private Integer businessId;

    @Schema(description = "sending type: 1-manually, 2-automatically")
    private Integer sendingType;

    @Schema(description = "sending method: 1-sms, 2-email")
    private List<Byte> sendingMethodList;

    @Schema(description = "template published time")
    private Long templatePublishTime;

    private Integer updateBy;
    private Long createTime;
    private Long updateTime;
}
