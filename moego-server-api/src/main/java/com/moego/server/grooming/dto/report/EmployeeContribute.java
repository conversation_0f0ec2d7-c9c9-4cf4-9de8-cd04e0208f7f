package com.moego.server.grooming.dto.report;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeContribute {

    private String name;
    private Integer id;

    @Builder.Default
    private Integer totalAppts = 0;

    @Builder.Default
    private Integer totalPets = 0;

    @Builder.Default
    private BigDecimal collectedRevenue = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal unpaidRevenue = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal totalTips = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal totalTaxes = BigDecimal.ZERO;
}
