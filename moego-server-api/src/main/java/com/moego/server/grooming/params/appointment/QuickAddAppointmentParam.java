package com.moego.server.grooming.params.appointment;

import com.moego.server.grooming.params.PreAuthParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Builder;

@Builder(toBuilder = true)
public record QuickAddAppointmentParam(
        @Schema(description = "business id", hidden = true) Integer businessId,
        @Schema(description = "company id", hidden = true) Long companyId,
        @Schema(description = "token staff id", hidden = true) Integer tokenStaffId,
        @NotNull @Min(1) Long customerId,
        @Schema(description = "start date, YYYY-MM-DD") @NotNull String startDate,
        @Schema(description = "end date, YYYY-MM-DD") String endDate,
        @Schema(description = "start time, 当天的分钟数") @NotNull @Min(0) @Max(1440) Integer startTime,
        @Schema(description = "end time, 当天的分钟数") @Min(0) @Max(1440) Integer endTime,
        String alertNotes,
        @Valid @Size(min = 1) List<PetParams> petList,
        Boolean allPetsStartAtSameTime,
        String ticketComment,
        @NotNull String colorCode,
        @NotNull Integer source,
        PreAuthParams preAuthParams) {}
