package com.moego.server.grooming.dto;

import java.util.List;
import lombok.Data;

@Data
public class GroomingAppointmentWaitingListDTO {

    private Integer ticketId;

    private String appointmentDate;
    private Integer appointmentStartTime;

    private Integer customerId;
    private Integer customerAddressId;

    // client full name
    private String customerLastName;
    private String customerFirstName;
    // Client phone number
    private String clientPhoneNumber;

    // client full address(包含 city zipcode)
    private String address1;
    private String address2;
    private String country;
    private String state;
    // City
    private String city;
    // Zipcode
    private String zipcode;

    private String clientFullAddress;

    private Long createTime;
    private Long updateTime;

    private List<WaitingListPetDetailDTO> waitingListPetDetails;
}
