package com.moego.server.grooming.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import java.util.List;
import lombok.Data;

@Data
public class SaveRepeatAppointmentParams {

    @JsonIgnore
    private Integer companyId;

    @JsonIgnore
    private Integer businessId;

    @JsonIgnore
    private Integer staffId;

    @Positive
    private Integer appointmentId;

    @NotNull
    @Positive
    private Integer customerId;

    @NotNull
    @Schema(description = "预约来源: 22018-web, 17216-android, 17802-ios, 22168-OB, 19826-google calendar, 23426-DM")
    private Integer source;

    @Pattern(regexp = "^(\\d{4}-\\d{2}-\\d{2})$", message = "Invalid date format, valid example: 2022-02-08")
    private String appointmentDateString;

    @PositiveOrZero
    private Integer appointmentStartTime;

    @Schema(description = "预约类型: 1-普通预约, 2-SS repeat 预约")
    private Byte scheduleType;

    @NotEmpty
    private List<@Valid PetDetailParams> serviceList;

    private Boolean allPetsStartAtSameTime;

    @NotNull
    private String colorCode;

    private Integer customerAddressId;

    private String alertNotes;

    private String ticketComments;

    private Integer createdById;

    private PreAuthParams preAuthParams;

    private String endDate;
}
