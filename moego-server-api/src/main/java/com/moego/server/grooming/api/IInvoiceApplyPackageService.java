package com.moego.server.grooming.api;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2024/9/23
 */
public interface IInvoiceApplyPackageService {

    /**
     * Update applied package for order.
     */
    @PostMapping("/service/grooming/invoiceApplyPackage/updateAppliedPackageForOrder")
    UpdateForOrderResult updateAppliedPackageForOrder(@RequestBody @NotNull UpdateForOrderParam param);

    @Builder(toBuilder = true)
    record UpdateForOrderParam(long orderId) {}

    @Builder(toBuilder = true)
    record UpdateForOrderResult() {}

    /**
     * Update applied package for order.
     */
    @PostMapping("/service/grooming/invoiceApplyPackage/removeAllPackages")
    RemoveAllPackagesResult removeAllPackages(@RequestBody @NotNull RemoveAllPackagesParam param);

    @Builder(toBuilder = true)
    record RemoveAllPackagesParam(long orderId, boolean checkRefund) {}

    @Builder(toBuilder = true)
    record RemoveAllPackagesResult() {}
}
