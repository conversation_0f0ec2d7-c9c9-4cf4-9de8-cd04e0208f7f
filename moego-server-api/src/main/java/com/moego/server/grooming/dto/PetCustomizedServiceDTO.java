package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PetCustomizedServiceDTO {

    private Integer id;
    private Long companyId;
    private Integer businessId;
    private Integer customerId;
    private Integer createBy;
    private Integer petId;
    private Integer serviceId;
    private String serviceName;
    private Integer serviceType;
    private Integer categoryId;
    private Integer serviceTime;
    private BigDecimal serviceFee;
    private Byte saveType;
    private Byte status;
    private Long createTime;
    private Long updateTime;
    private String serviceDetail;
}
