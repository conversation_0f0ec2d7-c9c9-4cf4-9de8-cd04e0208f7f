package com.moego.server.grooming.params;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AppointmentAgreementParams {

    @NotNull
    private Integer groomingId; // 预约id

    @NotNull
    private Integer agreementId; // agreement id

    @NotNull
    private Integer agreementRequiredType; // agreement 类型

    private Long lastRequiredTime; // 当agreement_required_type为1时，判断customer是否sign，需要限定这个时间之后
}
