package com.moego.server.grooming.dto.printcard;

import java.util.List;
import lombok.Data;

@Data
public class StayCardDetail {

    private Integer groomingId;

    private Integer petId;

    private PrintCardPet pet;

    private PrintCardCustomer customer;

    private StayCardAppointment appointment;

    private List<FeedingInstruction> feedingInstructions;

    private List<MedicationInstruction> medicationInstructions;

    private List<PetBelonging> petBelongings;

    private List<String> addOns;

    private List<ExtraServiceDetail> extraServiceDetails;
}
