package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AppointmentServiceInfo {

    private Integer serviceId;
    private Integer petDetailId;
    private String serviceName;
    private Integer serviceType;
    private Integer startTime; // minutes
    private Integer serviceTime; // duration
    private Integer petId;
    private Integer staffId;
    private BigDecimal servicePrice;
    private Integer serviceItemType;
}
