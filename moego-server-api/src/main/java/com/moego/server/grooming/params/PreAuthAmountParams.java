package com.moego.server.grooming.params;

import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

@Data
public class PreAuthAmountParams {

    @Valid
    private BookOnlineCustomerParams customerData;

    @NotNull
    @Valid
    private List<@NotNull @Valid BookOnlinePetParams> petData;

    @Size(min = 4, max = 20)
    private String discountCode;

    @Nullable
    private Integer staffId;
}
