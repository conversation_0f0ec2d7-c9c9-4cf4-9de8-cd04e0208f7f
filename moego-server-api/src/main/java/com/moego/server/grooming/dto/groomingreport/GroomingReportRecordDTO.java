package com.moego.server.grooming.dto.groomingreport;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * 预约中单只 pet 的 Grooming report 状态和发送记录封装
 */
@Data
public class GroomingReportRecordDTO {

    @Schema(description = "grooming report pet id")
    private Integer petId;

    @Schema(description = "pet name")
    private String petName;

    @Schema(description = "pet type id: 1-dog,2-cat,3-other")
    private Integer petTypeId;

    @Schema(description = "grooming report used template published time")
    private Long templatePublishTime;

    @Schema(description = "grooming report id")
    private Integer reportId;

    @Schema(description = "grooming report status")
    private String reportStatus;

    @Schema(description = "系统当前设置的 sending type: 1-manually 2-automatically")
    private Integer sendingType;

    @Schema(description = "系统当前设置的 sending method: 1-sms, 2-email")
    private List<Byte> sendingMethodList;

    @Schema(description = "最近一次发送选择的 sending method: 1-sms, 2-email")
    private List<Byte> lastSendingMethodList;

    @Schema(description = "最近一次的发送记录")
    private GroomingReportSendRecord sentRecords;

    public record GroomingReportSendRecord(GroomingReportSendLog sms, GroomingReportSendLog email) {}

    public record GroomingReportSendLog(Byte sentStatus, Integer errorCode, String errorMessage) {}
}
