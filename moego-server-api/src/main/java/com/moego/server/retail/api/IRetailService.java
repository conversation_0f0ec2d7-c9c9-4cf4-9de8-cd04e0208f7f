package com.moego.server.retail.api;

import com.moego.server.retail.param.RetailInitDataParams;
import com.moego.server.retail.param.TaxIdsParams;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IRetailService {
    @Operation(summary = "检查 tax 是否存在")
    @PostMapping("/service/retail/retail/isTaxExist")
    Boolean isTaxExist(@RequestBody TaxIdsParams params);

    @Operation(summary = "用于商户创建，初始化retail模块数据")
    @PostMapping("/service/retail/retail/initData")
    Boolean initData(@RequestBody RetailInitDataParams initDataParam);

    @PostMapping("/service/retail/retail/remove")
    Boolean removeInvoice(
            @RequestParam("invoiceItemId") Integer invoiceItemId, @RequestParam("staffId") Integer staffId);
}
