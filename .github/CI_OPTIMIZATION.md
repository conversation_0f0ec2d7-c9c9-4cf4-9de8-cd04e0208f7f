# CI/CD 优化：使用自定义Docker镜像加速构建

## 概述

为了解决GitHub Actions CI流水线每次都需要安装大量工具导致的构建缓慢问题，我们引入了预构建的Docker镜像方案。这个优化可以将CI构建时间减少5-10分钟。

## 优化前 vs 优化后

### 优化前
- 每个job都需要重新安装：Go、Node.js、Bazel、Buf、yq、AWS CLI等工具
- 工具安装时间：3-5分钟
- 总的CI时间：15-25分钟

### 优化后
- 使用预构建镜像，所有工具已就绪
- 工具"安装"时间：10-30秒（下载镜像时间）
- 总的CI时间：8-15分钟
- **节省时间：40-60%**

## 文件结构

```
.github/
├── docker/
│   └── ci-builder.Dockerfile    # CI构建镜像定义
└── workflows/
    ├── build-ci-image.yml       # 构建CI镜像的workflow
    └── app-ci.yaml              # 主CI流程（已优化）
```

## 使用的Docker镜像

**镜像名称：** `ghcr.io/moegolibrary/moego-ci-builder:latest`

**包含的工具：**
- Go 1.24.4
- Node.js 23 + npm + yarn
- Bazelisk
- Buf
- yq
- AWS CLI
- Docker CLI
- api-linter
- goimports-reviser
- protoc相关工具
- 其他CI必需工具

## 本地开发命令

```bash
# 构建CI镜像
make ci-image-build

# 测试CI镜像
make ci-image-test

# 构建并测试
make ci-image

# 推送到GitHub Container Registry
make ci-image-push
```

## 镜像自动更新

CI镜像会在以下情况自动重新构建：
- `.github/docker/ci-builder.Dockerfile` 文件变更
- `.github/workflows/build-ci-image.yml` 文件变更
- 手动触发构建

## 故障排除

### 镜像不存在错误
如果遇到镜像拉取失败，可能是因为：
1. 镜像还未构建 - 先推送Dockerfile变更触发构建
2. 权限问题 - 检查GitHub Token权限

### VCS状态检查错误
如果遇到 `error obtaining VCS status: exit status 128` 错误：
- 已在Dockerfile中设置 `GOFLAGS=-buildvcs=false` 环境变量
- 已在本地脚本中添加相应的环境变量设置
- 注意：`-buildvcs=false` 是Go命令的参数，不是 `goimports-reviser` 的参数

### Git历史检查错误
如果遇到 `Could not access 'HEAD^'` 错误：
- 已在check-proto-changes job中添加健壮的Git历史检查
- 自动检测是否有父提交，如果没有则使用main分支作为比较基准
- 适用于首次提交或浅克隆的情况

如果遇到 `Could not access 'origin/main'` 错误：
- 已添加智能的Git fetch逻辑，避免fetch到当前分支
- 自动检测当前分支，如果不在main分支上才进行fetch
- 如果main分支不存在，会检查所有可用的变更

### 本地测试
使用以下命令本地测试CI镜像：
```bash
# 进入镜像环境
docker run -it --rm ghcr.io/moegolibrary/moego-ci-builder:latest /bin/bash

# 测试特定工具
docker run --rm ghcr.io/moegolibrary/moego-ci-builder:latest go version
```

## 性能监控

可以通过GitHub Actions的执行时间来监控优化效果：
- 关注job的总执行时间
- 对比优化前后的构建日志

## 注意事项

1. **镜像大小**：当前镜像约2-3GB，在CI环境中首次下载需要一些时间
2. **工具版本**：所有工具版本固定在Dockerfile中，需要更新时修改Dockerfile
3. **缓存策略**：GitHub Actions会缓存镜像，但定期会重新下载
4. **VCS问题**：已通过环境变量和命令行参数解决Go工具的VCS状态检查问题

## 未来改进

1. **多阶段构建**：减少最终镜像大小
2. **工具版本自动更新**：通过脚本自动检测和更新工具版本
3. **多架构支持**：支持ARM64架构（如果需要） 