# CI Builder镜像 - 包含所有构建和测试工具
FROM ubuntu:24.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV GO_VERSION=1.24.4
ENV NODE_VERSION=23
ENV BUF_VERSION=1.48.0
ENV BAZELISK_VERSION=1.24.0
ENV YQ_VERSION=4.44.2

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    unzip \
    wget \
    ca-certificates \
    gnupg \
    lsb-release \
    software-properties-common \
    lcov \
    gcc \
    g++ \
    make \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 安装Go
RUN curl -fsSL https://dl.google.com/go/go${GO_VERSION}.linux-amd64.tar.gz | tar -xzC /usr/local \
    && ln -s /usr/local/go/bin/* /usr/local/bin/

# 安装Node.js和yarn
RUN curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g yarn npm-cli-login @bufbuild/protoc-gen-es

# 安装Bazelisk
RUN curl -L "https://github.com/bazelbuild/bazelisk/releases/download/v${BAZELISK_VERSION}/bazelisk-linux-amd64" -o /usr/local/bin/bazel \
    && chmod +x /usr/local/bin/bazel \
    && ln -s /usr/local/bin/bazel /usr/local/bin/bazelisk

# 安装Buf
RUN curl -sSL "https://github.com/bufbuild/buf/releases/download/v${BUF_VERSION}/buf-Linux-x86_64" -o /usr/local/bin/buf \
    && chmod +x /usr/local/bin/buf

# 安装yq
RUN curl -L "https://github.com/mikefarah/yq/releases/download/v${YQ_VERSION}/yq_linux_amd64" -o /usr/local/bin/yq \
    && chmod +x /usr/local/bin/yq

# 安装AWS CLI
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" \
    && unzip awscliv2.zip \
    && ./aws/install \
    && rm -rf awscliv2.zip aws

# 安装Docker CLI (用于构建镜像)
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce-cli \
    && rm -rf /var/lib/apt/lists/*

# 设置Go环境
ENV GOPATH=/go
ENV PATH=$GOPATH/bin:/usr/local/go/bin:$PATH
ENV CGO_ENABLED=0
ENV GOFLAGS=-buildvcs=false
RUN mkdir -p "$GOPATH/src" "$GOPATH/bin" && chmod -R 777 "$GOPATH"

# 预安装常用的Go工具（禁用CGO避免编译问题）
RUN go install github.com/googleapis/api-linter/cmd/api-linter@v1.69.2 \
    && go install github.com/incu6us/goimports-reviser/v3@latest \
    && go install google.golang.org/protobuf/cmd/protoc-gen-go@latest \
    && go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest \
    && go install github.com/envoyproxy/protoc-gen-validate@latest \
    && go install go.uber.org/mock/mockgen@latest

# 创建工作目录
WORKDIR /workspace

# 设置Git安全目录（避免权限问题）
RUN git config --global --add safe.directory '*'

# 预热Bazel缓存目录
RUN mkdir -p /root/.cache/bazel

# 验证安装
RUN echo "=== 工具版本信息 ===" \
    && go version \
    && node --version \
    && npm --version \
    && yarn --version \
    && bazel version \
    && buf --version \
    && yq --version \
    && aws --version \
    && docker --version \
    && api-linter --version \
    && goimports-reviser --help | head -1 \
    && echo "=== 所有工具安装完成 ==="